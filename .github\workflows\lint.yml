name: Lint Check

on:
  push:
    branches: [ "develop" ]
  pull_request:
    branches: [ "develop" ]

jobs:
  lint:
    runs-on: [ self-hosted, linux, X64, CA, OVH ]
    steps:
      - name: Clean workspace
        run: rm -rf ${{ github.workspace }}/*

      - uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: gradle

      - name: Set up Android SDK
        uses: android-actions/setup-android@v3
        with:
          api-level: 30
          build-tools: 30.0.3

      - name: <PERSON> execute permission for gradlew
        run: chmod +x ./gradlew

      - name: Run lint check
        run: ./gradlew lint

      - name: Upload lint results
        if: failure()
        uses: actions/upload-artifact@v2
        with:
          name: lint-results
          path: app/build/reports/lint-results-prodDebug.html
