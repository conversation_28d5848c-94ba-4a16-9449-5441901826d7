name: Package to AppTester Test Environment

on:
  push:
    branches: [ "develop" ]
  pull_request:
    branches: [ "develop" ]

jobs:
  build:
    runs-on: [ self-hosted, linux, X64, CA, OVH ]
    if: "contains(github.event.head_commit.message, 'build')"
    steps:
      - name: clean workspace before job
        run: rm -rf ${{ github.workspace }}/*

      - uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: gradle

      - name: Set up Android SDK
        uses: android-actions/setup-android@v3
        with:
          api-level: 30
          build-tools: 30.0.3

      - name: Grant execute permission for gradlew
        run: chmod +x ./gradlew

      - name: Assemble Release
        env:
          ANDROID_HOME: ${{ steps.setup-android.outputs.android-home }}
        run: ./gradlew assembleRelease

      - name: Upload to Firebase App Distribution
        env:
          ANDROID_HOME: ${{ steps.setup-android.outputs.android-home }}
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        run: ./gradlew appDistributionUploadQaRelease

  notify:
    needs: [ build ]
    runs-on: [ self-hosted, linux, X64, CA, OVH ]
    steps:
      - name: PUSHPLUS推送通知
        uses: xhnmt/pushplus-action@v1.0.0
        with:
          token: ${{ secrets.PUSHPLUS_TOKEN }}
          title: "APK build ${{ needs.build.result }}"
          content: "Android test ${{ github.event.head_commit.message }}: APK build ${{ needs.build.result }}."