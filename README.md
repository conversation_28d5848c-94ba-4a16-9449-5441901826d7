## [HouseSigma](https://housesigma.com/) for Android

[![API](https://img.shields.io/badge/API-23%2B-brightgreen.svg?style=flat)](https://android-arsenal.com/api?level=23)

[![Package to AppTester Test Environment](https://github.com/housesigma/housesigma-android-native/actions/workflows/test.yml/badge.svg)](https://github.com/housesigma/housesigma-android-native/actions/workflows/test.yml)

[![Package to AppTester Staging Environment](https://github.com/housesigma/housesigma-android-native/actions/workflows/staging.yml/badge.svg)](https://github.com/housesigma/housesigma-android-native/actions/workflows/staging.yml)

[![Lint Check](https://github.com/housesigma/housesigma-android-native/actions/workflows/lint.yml/badge.svg)](https://github.com/housesigma/housesigma-android-native/actions/workflows/lint.yml)

<a href="https://play.google.com/store/apps/details?id=com.housesigma.android"><img src="https://play.google.com/intl/en_us/badges/images/generic/en-play-badge.png" height="48">

### PREREQUISITES

- JDK 17
- Android SDK (API Level 23 or higher)

## Getting Started

To get a local copy up and running follow these simple steps:

1. Clone the repository
```git clone https://github.com/housesigma/housesigma-android-native.git```
2. Open the project in Android Studio Koala | 2024.1.1 or later.
3. Sync the project with Gradle files.
4. Build and run the application.

## Deployment

Continuous Integration and Deployment is set up with GitHub Actions, packaging the app for the Test, Staging environments.

## Lint Workflow

This project includes an automated linting workflow, defined in `.github/workflows/lint.yml`, to ensure code quality and consistency.

**Triggers:**
- On `push` to `develop` branch.
- On pull requests to `develop` branch.

## Resources

- [Google Play Dashboard](https://play.google.com/console/u/0/developers/7205498896070778554/app/4974300260903203027/app-dashboard?timespan=thirtyDays)
- [Firebase Release Monitoring](https://console.firebase.google.com/project/housesigma-appv2/releasemonitoring)
- [App Distribution](https://console.firebase.google.com/project/housesigma-appv2/appdistribution/app/android:com.housesigma.android/releases)
- GA DebugView for different environments (PRD, Staging, Test)
- [GA DebugView (PRD)](https://console.firebase.google.com/project/housesigma-appv2/analytics/app/android:com.housesigma.android/debugview)
- [GA DebugView (Staging)](https://console.firebase.google.com/project/hs-ga4-dev/analytics/app/android:com.housesigma.android.staging/debugview)
- [GA DebugView (Test)](https://console.firebase.google.com/project/hs-ga4-test/analytics/app/android:com.housesigma.android.test/debugview)





