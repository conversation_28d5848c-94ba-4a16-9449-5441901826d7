plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
    id "com.google.firebase.appdistribution"
}

static def releaseTime() {
    return new Date().format("yyyyMMdd", TimeZone.getTimeZone("GMT+08:00"))
}
android {
    namespace "com.housesigma.android"
    compileSdk 34
    bundle {
        language {
            enableSplit = false
        }
        density {
            enableSplit = false
        }
    }
    testOptions {
        unitTests {
            returnDefaultValues = true
        }
    }
    defaultConfig {
        applicationId "com.housesigma.android"
        minSdk 23
        targetSdk 34
        versionCode 1827
        versionName "7.25.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        buildConfigField "String","ONE_TAP_WEB_CLIENT_ID","\"591784314269-2f6vb4vpuuqksuel32006c1uconqjsne.apps.googleusercontent.com\""
        //DEV-3106 需要把libRSSupport.so、librsjni.so、librsjni_androidx.so 这三个.so兼容库一并打包到apk中
        renderscriptTargetApi 18
        renderscriptSupportModeEnabled true
        
        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a'
        }
    }

    signingConfigs {
        release {
            storeFile file(KEY_PATH)
            storePassword KEY_PASS
            keyAlias ALIAS_NAME
            keyPassword ALIAS_PASS
            firebaseAppDistribution {
                releaseNotesFile="updateNotes"
                groups="housesigma"
            }
        }
    }
    buildTypes {
        release {
            shrinkResources true
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            firebaseCrashlytics {
                mappingFileUploadEnabled true
            }
        }
        debug{
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
            firebaseCrashlytics {
                mappingFileUploadEnabled false
            }
        }
    }
    flavorDimensions "version"
    productFlavors {
        qa {
            dimension "version"
            applicationIdSuffix '.test'
            resValue  "string", "app_name", 'Native Test'
            buildConfigField("String", "API_DOMAIN","\"https://test-3p31a3.housesigma.com\"")
            buildConfigField("String", "COLLECT_API_DOMAIN","\"https://dgw.test.housesigma.com\"")
            buildConfigField("String", "WEBVIEW_URL_PREFIX","\"https://test-3p31a3.housesigma.com\"")
            buildConfigField("String", "APPLINKS_HOST","\"test-3p31a3.housesigma.com\"")
            buildConfigField("String", "Clarity_PROJECT_ID","\"l83psdujy4\"")
            buildConfigField("String", "LiveChat_License","\"18127824\"")
            buildConfigField("String", "PUBLIC_KEY","\"-----BEGIN PUBLIC KEY-----MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDc2aWgkx7Yu0bNP8v5pbV412nYFc/+XPZ0iC9Pbsbxxu47qJ69oLRW1RaW1aEVCrVi3mWY+eJJx9UjUGlvFUlZ85Rkp4TddRKNtZgkECnW6ilgIuLW3QagmyDi0oY13SbQrPFzJV+il/MJ/NWNK0QecGri/khgbGAqqpMhzaS+dQIDAQAB-----END PUBLIC KEY-----\"")
        }
        staging {
            dimension "version"
            applicationIdSuffix '.staging'
            resValue  "string", "app_name", 'Native Staging'
            buildConfigField("String", "API_DOMAIN","\"https://dev-a0e5d8.housesigma.com\"")
            buildConfigField("String", "COLLECT_API_DOMAIN","\"https://dgw.housesigma.com\"")
            buildConfigField("String", "WEBVIEW_URL_PREFIX","\"https://dev-a0e5d8.housesigma.com\"")
            buildConfigField("String", "APPLINKS_HOST","\"dev-a0e5d8.housesigma.com\"")
            buildConfigField("String", "Clarity_PROJECT_ID","\"l83phokonf\"")
            buildConfigField("String", "LiveChat_License","\"18150093\"")
            buildConfigField("String", "PUBLIC_KEY","\"-----BEGIN PUBLIC KEY-----MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDQlOcjEbqprurl2xjoEP0QdjGIrZhLVn5vzwCorG4+2AtSi4AAHjghSXM//ljqE5rA13gfTc58JvM6I75Dmqr5r5Vvo57CAbxBXHsXu5ojtgvb5rOd2lrZeckwJL0Z7euvRsA/FjbFdGMcGeSJ8JoePq+H0RFOt285bSb8hVq0LQIDAQAB-----END PUBLIC KEY-----\"")
        }
        prod {
            dimension "version"
            resValue  "string", "app_name", 'HouseSigma'
            buildConfigField("String", "API_DOMAIN","\"https://api.housesigma.com\"")
            buildConfigField("String", "COLLECT_API_DOMAIN","\"https://dgw.housesigma.com\"")
            buildConfigField("String", "WEBVIEW_URL_PREFIX","\"https://housesigma.com\"")
            buildConfigField("String", "APPLINKS_HOST","\"housesigma.com\"")
            buildConfigField("String", "Clarity_PROJECT_ID","\"l83p3nna52\"")
            buildConfigField("String", "LiveChat_License","\"18150093\"")
            buildConfigField("String", "PUBLIC_KEY","\"-----BEGIN PUBLIC KEY-----MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDQlOcjEbqprurl2xjoEP0QdjGIrZhLVn5vzwCorG4+2AtSi4AAHjghSXM//ljqE5rA13gfTc58JvM6I75Dmqr5r5Vvo57CAbxBXHsXu5ojtgvb5rOd2lrZeckwJL0Z7euvRsA/FjbFdGMcGeSJ8JoePq+H0RFOt285bSb8hVq0LQIDAQAB-----END PUBLIC KEY-----\"")
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    buildFeatures {
        viewBinding true
        buildConfig true
    }

    android.applicationVariants.all { variant ->
        variant.outputs.all {
            outputFileName = "hs_${variant.productFlavors[0].name}_v${variant.versionName}_${releaseTime()}.apk"
        }
    }

}

dependencies {

    implementation 'androidx.core:core-ktx:1.9.0'
    implementation 'androidx.appcompat:appcompat:1.5.1'
    implementation 'com.google.android.material:material:1.8.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'

    implementation 'androidx.vectordrawable:vectordrawable:1.1.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.5.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'

    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1")

    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'

    implementation 'com.geyifeng.immersionbar:immersionbar:3.2.2'
    implementation 'com.geyifeng.immersionbar:immersionbar-ktx:3.2.2'
    implementation 'com.geyifeng.immersionbar:immersionbar-components:3.2.2'

    implementation 'com.github.bumptech.glide:glide:4.14.2'

    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.4'

    implementation 'com.tencent:mmkv:1.3.7'

    implementation 'me.jessyan:autosize:1.2.1'

    implementation "com.github.permissions-dispatcher:permissionsdispatcher:4.9.2"
    kapt "com.github.permissions-dispatcher:permissionsdispatcher-processor:4.9.2"


    implementation('com.github.ihsanbal:LoggingInterceptor:3.1.0') {
        exclude group: 'org.json', module: 'json'
    }

    implementation 'com.youth.banner:banner:2.1.0'

    implementation 'com.github.donkingliang:LabelsView:1.6.5'

    implementation 'com.github.ihgoo:glide-transformations-fix:4.3.2'

//    双头seekbar
    implementation 'com.github.Jay-Goo:RangeSeekBar:v3.0.0'

    testImplementation 'junit:junit:4.13.2'
    testImplementation 'io.mockk:mockk:1.13.9'
    
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'


    implementation  'io.github.scwang90:refresh-layout-kernel:2.0.5'
    implementation  'io.github.scwang90:refresh-header-classics:2.0.5'
    implementation  'io.github.scwang90:refresh-footer-classics:2.0.5'

//    https://github.com/li-xiaojun/XPopup
    implementation 'com.github.li-xiaojun:XPopup:2.10.0'

    implementation 'org.maplibre.gl:android-sdk:11.7.1'
    implementation 'org.maplibre.gl:android-plugin-annotation-v9:3.0.2'
    implementation 'org.maplibre.gl:android-plugin-markerview-v9:3.0.2'
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'


//    implementation 'com.shizhefei:ViewPagerIndicator:1.1.9.androidx'
    implementation 'com.github.getActivity:ToastUtils:12.2'
    implementation("org.greenrobot:eventbus:3.3.1")


// room orm
//    implementation "androidx.room:room-runtime:2.4.1"
//    annotationProcessor "androidx.room:room-compiler:2.4.1"


    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.14'


    // Import the Firebase BoM
    implementation platform('com.google.firebase:firebase-bom:32.8.1')
    // When using the BoM, you don't specify versions in Firebase library dependencies
    // Declare the dependency for the Firebase SDK for Google Analytics
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-messaging'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'

    // Declare the dependency for the Firebase Authentication library
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation 'com.google.firebase:firebase-auth-ktx'
    // Also declare the dependency for the Google Play services library and specify its version
    implementation 'com.google.android.gms:play-services-auth:20.2.0'
    implementation 'com.google.android.play:review-ktx:2.0.1'


    implementation 'com.google.android.gms:play-services-tagmanager:18.0.1'

    //Rxjava
    implementation 'io.reactivex:rxandroid:1.2.0'
    implementation 'io.reactivex:rxjava:1.2.0'


    implementation 'com.github.chrisbanes:PhotoView:2.3.0'
    implementation 'com.github.lihangleo2:ShadowLayout:3.3.2'


    implementation 'com.google.firebase:firebase-config-ktx'


    implementation 'com.getkeepsafe.relinker:relinker:1.4.5'

    // location之所以使用20.0.0版本，是因为mapbox对21版本有兼容性的bug尚未修复
    implementation 'com.google.android.gms:play-services-location:20.0.0'

    implementation 'com.github.ihgoo:chat-window-android:v2.2.3'

    implementation "androidx.biometric:biometric:1.1.0"

    // To use Kotlin annotation processing tool (kapt)
    kapt "androidx.room:room-compiler:2.6.1"
    implementation "androidx.room:room-ktx:2.6.1"
}

tasks.withType(Test) {
    jvmArgs '-Dnet.bytebuddy.experimental=true'
}