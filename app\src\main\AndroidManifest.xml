<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <uses-permission android:name="android.permission.VIBRATE"/>

    <application
        android:name=".HSApp"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:largeHeap="true"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:theme="@style/Theme.HouseSigma">

<!--        <activity android:name=".AppLinksEntryActivity"-->
<!--            android:exported="true">-->

<!--        </activity>-->

        <!--适配华为（huawei）刘海屏-->
        <meta-data
            android:name="android.notch_support"
            android:value="true"/>
        <!--适配小米（xiaomi）刘海屏-->
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />

        <meta-data
            android:name="design_width_in_dp"
            android:value="390" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="900" />

        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" />

        <meta-data
            android:name="android.webkit.WebView.EnableSafeBrowsing"
            android:value="false" />

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyCigvqDdagUsv-HlsJbdE-ZBPlAdaDgbNI" />

        <!--停用GA屏幕浏览跟踪，改用手动埋ScreenView -->
        <meta-data android:name="google_analytics_automatic_screen_reporting_enabled" android:value="false" />

        <activity
            android:name=".ui.splash.SplashActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="housesigma" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:scheme="https"
                    android:host="@string/applinks_host" />
                <data
                    android:pathPrefix="/app/" />

                <data
                    android:pathPrefix="/on/" />

                <data
                    android:pathPrefix="/bc/" />

                <data
                    android:pathPrefix="/ab/" />

                <data
                    android:pathPrefix="/home/" />

                <data
                    android:pathPrefix="/user" />

                <data
                    android:pathPrefix="/listings/" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.main.MainActivity"
            android:windowSoftInputMode="adjustPan"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait">


        </activity>
        <activity
            android:name=".ui.map.agent.AgentExperienceMapActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.onboard.OnBoardActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.onboard.OnBoardFilterActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.main.PersonalizeListingsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.watched.WatchedListingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.search.SearchActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.signup.SignUpActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.forgot.ForgotPasswordVerifyCodeActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.recommended.RecommendedCommunityActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.signup.SignUpTermsOfUseActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize"></activity>
        <activity
            android:name=".ui.webview.WebViewActivity"
            android:hardwareAccelerated="true"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.signup.SignUpVerifyCodeActivity"
            android:windowSoftInputMode="adjustPan"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.forgot.ForgotPasswordActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.map.MapActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.map.precon.PreconMapActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.account.MyProfileActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.account.AboutActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.account.NotificationActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.account.ChangePasswordActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.account.ChangeNameActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.account.ChangeContactActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.account.FeedbackActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.account.ChangeAccountActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity android:name=".ui.account.MyAgentActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.account.DelAccountActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.home.ListingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.watched.AddWatchCommunityActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.account.ReferralCodeActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>

        <activity
            android:name=".ui.map.housephoto.HousePhotoListDetailActivity"
            android:theme="@style/Theme.ImagePreview"
            android:screenOrientation="user"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"></activity>

        <activity
            android:name=".ui.recommended.RecommendedCommunityListActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>

        <activity
            android:name=".ui.recommended.RecommendedStartActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>

        <activity
            android:name=".ui.watcharea.WatchedAreaActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>

        <activity
            android:name=".ui.watched.WatchedTrendListingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>


        <activity
            android:name=".ui.map.housephoto.PreconHousePhotoListActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.map.housephoto.HousePhotoListActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>

        <activity
            android:name=".ui.watched.WatchedPropertiesActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>

        <activity
            android:name=".ui.onetap.OneTapActivity"
            android:windowSoftInputMode="adjustPan"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>
        <activity
            android:name=".ui.account.AgentInfoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>


        <service
            android:name=".service.MessagingService"
            android:enabled="true"
            android:foregroundServiceType="dataSync"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>


        <!--  Add preview activity. -->
        <activity
            android:name="com.google.android.gms.tagmanager.TagManagerPreviewActivity"
            android:noHistory="true"
            android:exported="true"> <!-- optional, removes previewActivity from activity stack. -->
            <intent-filter>
                <data android:scheme="tagmanager.c.com.housesigma.android.test" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE"/>
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.chat.HSChatWindowActivity"
            android:theme="@style/HSChat.Theme.HouseSigma"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"></activity>


    </application>

</manifest>