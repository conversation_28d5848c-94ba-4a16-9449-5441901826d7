//package com.housesigma.android;
//
//import android.content.Intent;
//import android.net.Uri;
//import android.os.Bundle;
//import android.util.Log;
//
//import androidx.appcompat.app.AppCompatActivity;
//
//import com.housesigma.android.ui.main.MainActivity;
//
//public class AppLinksEntryActivity extends AppCompatActivity {
//
//    @SuppressWarnings("FieldCanBeLocal")
//    private final String TAG = "hkj";
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        // ATTENTION: This was auto-generated to handle app links.
//        Intent appLinkIntent = getIntent();
//        String appLinkAction = appLinkIntent.getAction();
//        Uri appLinkData = appLinkIntent.getData();
//
//        Log.d(TAG, "[applinks] " + appLinkAction + " " + appLinkData);
//
//        if (!"android.intent.action.VIEW".equals(appLinkAction) || appLinkData == null) {
//            return;
//        }
//
//        Bundle bundle = new Bundle();
//        bundle.putString("type", "applinks");
//        bundle.putString("uri", appLinkData.toString());
//
//        Intent startMainActivity = new Intent(getApplicationContext(), MainActivity.class);
//        startMainActivity.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
//        startMainActivity.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
//        startMainActivity.putExtra("data", bundle);
//        startActivity(startMainActivity);
//        this.finish();
//    }
//}
