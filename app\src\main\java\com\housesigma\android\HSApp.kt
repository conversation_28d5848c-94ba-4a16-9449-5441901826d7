package com.housesigma.android

import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.os.Bundle
import android.view.Gravity
import com.hjq.toast.Toaster
import com.housesigma.android.model.InitApp
import com.housesigma.android.model.Secret
import com.housesigma.android.model.User
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSLog
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.LanguageUtils
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.ScreenUtils
import com.housesigma.android.utils.log.AndroidLogAdapter
import com.housesigma.android.utils.log.FormatStrategy
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.utils.log.PrettyFormatStrategy
import me.jessyan.autosize.AutoSize
import me.jessyan.autosize.AutoSizeConfig
import me.jessyan.autosize.onAdaptListener


class HSApp : AbsSuperApplication() {

    override fun onCreate() {
        super.onCreate()
        appContext = this
        val formatStrategy: FormatStrategy = PrettyFormatStrategy.newBuilder()
            .methodCount(2) // 显示的方法行数
            .tag("hkj") // 每个日志的全局标记. 默认 PRETTY_LOGGER
            .build()
        Logger.addLogAdapter(object : AndroidLogAdapter(formatStrategy) {
            override fun isLoggable(priority: Int, tag: String?): Boolean {
                return !"prod".equals(BuildConfig.FLAVOR)
            }
        })



        MMKVUtils.initMMKV(this)
        Toaster.init(this)
        Toaster.setView(R.layout.toast_custom_view)
        Toaster.setGravity(Gravity.TOP,0, ScreenUtils.dpToPx(20f).toInt())

        Logger.e("HSUtil.isPad(this) "+HSUtil.isPad(this))
        if (!HSUtil.isPad(this)) {
            AutoSize.initCompatMultiProcess(this)
            AutoSizeConfig.getInstance()
                .setCustomFragment(true)
                .setExcludeFontScale(true)
                .setOnAdaptListener(object : onAdaptListener{
                    override fun onAdaptBefore(target: Any?, activity: Activity) {
                        AutoSizeConfig.getInstance().screenHeight = ScreenUtils.getDeviceHeight(activity)
                        AutoSizeConfig.getInstance().screenWidth = ScreenUtils.getDeviceWidth(activity)
                    }
                    override fun onAdaptAfter(target: Any?, activity: Activity?) {
                    }
                })
        }

        LanguageUtils().setAppLanguage(this)


        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}

            override fun onActivityStarted(activity: Activity) {}

            override fun onActivityResumed(activity: Activity) {}

            override fun onActivityPaused(activity: Activity) {
                HSLog.sendUserEngagement()
            }

            override fun onActivityStopped(activity: Activity) {
                // This can be used to detect when an activity goes to the background
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}

            override fun onActivityDestroyed(activity: Activity) {}
        })
    }

    companion object {
        var appContext: Context? = null

        var user: User? = null

        var initApp: InitApp? = null

        var hybridUser: String? = null

        var hybridInitApp: String? = null

        var isShowedOneTap: Boolean = false

        var secret: Secret? = null
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        LanguageUtils().setAppLanguage(this)
    }

}