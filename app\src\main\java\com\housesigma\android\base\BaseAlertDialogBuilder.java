package com.housesigma.android.base;

import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.Resources;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;

import me.jessyan.autosize.AutoSizeCompat;

/**
 * 解决autoSize框架屏幕适配关于弹窗屏幕适配失效的问题
 * https://github.com/JessYanCoding/AndroidAutoSize/issues/1
 */
public class BaseAlertDialogBuilder extends MaterialAlertDialogBuilder {


    public BaseAlertDialogBuilder(@NonNull Context context) {
        super(adjustAutoSize(context));
    }

    public BaseAlertDialogBuilder(@NonNull Context context, int themeResId) {
        super(adjustAutoSize(context), themeResId);
    }

    private static Context adjustAutoSize(Context context) {
        return new ContextWrapper(context) {
            private Resources mResources;

            {
                Resources oldResources = super.getResources();
                mResources = new Resources(oldResources.getAssets(), oldResources.getDisplayMetrics(), oldResources.getConfiguration());
            }

            @Override
            public Resources getResources() {
                AutoSizeCompat.autoConvertDensityOfGlobal((super.getResources()));
                return mResources;
            }
        };
    }
}

