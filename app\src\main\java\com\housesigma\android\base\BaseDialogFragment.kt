package com.housesigma.android.base

import android.content.DialogInterface
import android.view.Gravity
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.gyf.immersionbar.ktx.destroyImmersionBar
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.LoadingDialog

open class BaseDialogFragment : DialogFragment() {

    private var isShowFragment = false

    private val loadingDialog: LoadingDialog? by lazy {
        activity?.let { LoadingDialog(it).setDimAmount(0.7f) }
    }

    fun showLoadingDialog() {
        loadingDialog?.show()
    }

    fun dismissLoadingDialog() {
        loadingDialog?.let {
            if (it.isShowing) {
                it.dismiss()
            }
        }
    }

    override fun show(manager: FragmentManager, tag: String?) {
        // 解决bug：Android java.lang.IllegalStateException: Fragment already added
        if (isShowFragment) {
            return
        }
        isShowFragment = true
        super.show(manager, tag)
    }

    override fun dismiss() {
        if (!isShowFragment) {
            return
        }
        isShowFragment = false
        if (fragmentManager==null) {
            Logger.e("dismiss: $this not associated with a fragment manager.")
        } else {
            super.dismiss();
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        isShowFragment = false
    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        val windowParams = window?.attributes
        windowParams?.dimAmount = 0.7f // 遮罩效果消失
        window?.setLayout(-1, -2)
        windowParams?.gravity = Gravity.BOTTOM
        window?.attributes = windowParams
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        getDialog()?.let { destroyImmersionBar(it) }
        activity?.let {
            loadingDialog?.dismiss()
        }
    }
}