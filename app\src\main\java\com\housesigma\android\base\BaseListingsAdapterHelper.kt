package com.housesigma.android.base

import android.content.Context
import android.graphics.Bitmap
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.Transformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.HSApp
import com.housesigma.android.R
import com.housesigma.android.model.HouseDetail
import com.housesigma.android.utils.Constants
import com.housesigma.android.utils.HSHtmlImageGetter
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.ScreenUtils
import com.housesigma.android.views.HSHtmlTextView
import jp.wasabeef.glide.transformations.BlurTransformation
import jp.wasabeef.glide.transformations.RoundedCornersTransformation

class BaseListingsAdapterHelper {


    companion object {

         fun canJumpListingDetail(item: HouseDetail): Boolean {
            if (item.isNotAvailable() || item.isAgreementRequired() || item.isNeedReLogin() || item.isLoginRequired() || item.isPasswordExpired()) {
                return true
            }
            return false
        }


         fun handleImageMaskView(
             context: Context,
             item: HouseDetail,
             holder: BaseViewHolder
        ) {
            val multi: MultiTransformation<Bitmap> =
                updateMaskViewVisibility(context,item, holder)
            Glide.with(context)
                .load(item.photo_url)
                .transform(multi)
                .error(R.drawable.shape_pic_place_holder)
                .placeholder(R.drawable.shape_pic_place_holder)
                .into(holder.getView(R.id.iv_house_pic))
        }

        private fun updateMaskViewVisibility(
            context: Context,
            item: HouseDetail,
            holder: BaseViewHolder
        ): MultiTransformation<Bitmap> {
            val topCorner = ScreenUtils.dpToPx(16f)

            // Set all views to gone by default
            holder.setGone(R.id.tv_not_available, true)
            holder.setGone(R.id.tv_agreement_required, true)
            holder.setGone(R.id.tv_login_required, true)
            holder.setGone(R.id.ll_text, true)
            holder.setGone(R.id.tv_trreb_expired_header, true)
            holder.setGone(R.id.tv_trreb_expired_body, true)

            var blurEffect = false
            if (item.isNotAvailable()) {
                holder.setVisible(R.id.tv_not_available, true)
                blurEffect = true
            } else if (item.isAgreementRequired()) {
                holder.setVisible(R.id.tv_agreement_required, true)
                blurEffect = true
            } else if (item.isLoginRequired() || item.isPasswordExpired()) {
                holder.setVisible(R.id.tv_login_required, true)
                var string =  "Login Required"
                if (item.isPasswordExpired()){
                    string = "Password Expired"
                }
                holder.setText(R.id.tv_login_required, string)
                blurEffect = true
            } else if (item.isNeedReLogin()) {
                val tvTrrebExpiredBody = holder.getView<TextView>(R.id.tv_trreb_expired_body)
                val explainTitle = HSApp.initApp?.timeout_explain?.explain_title?:""
                val explainText = HSApp.initApp?.timeout_explain?.explain_text?:""

                holder.setVisible(R.id.tv_trreb_expired_header, true)
                holder.setVisible(R.id.tv_trreb_expired_body, true)
                holder.setVisible(R.id.tv_login_required, true)
                holder.setText(R.id.tv_trreb_expired_header, explainTitle)
                HSHtmlTextView.setHtml(context, explainText,tvTrrebExpiredBody)
                holder.setText(R.id.tv_login_required, Constants.TEXT_VIEW_TRREB_TIMEOUT)

                blurEffect = true
            } else {
                holder.setVisible(R.id.ll_text, true)
            }

            if (blurEffect) {
                holder.setGone(R.id.tv_tag, true)
                holder.getViewOrNull<LinearLayout>(R.id.ll_left_label)?.visibility = View.GONE
            }

            // Create the multi transformation
            val transformations = ArrayList<Transformation<Bitmap>>()
            if (blurEffect) {
                if (item.isNeedReLogin()) {
                    transformations.add(BlurTransformation(2,30))
                } else {
                    transformations.add(BlurTransformation(2,10))
                }
            }
            transformations.add(CenterCrop())
            transformations.add(
                RoundedCornersTransformation(
                    topCorner.toInt(),
                    0,
                    RoundedCornersTransformation.CornerType.ALL
                )
            )

            return MultiTransformation(transformations)
        }
    }

}