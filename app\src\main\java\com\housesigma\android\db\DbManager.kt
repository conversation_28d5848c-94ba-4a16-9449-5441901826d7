import android.util.Log
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.sqlite.db.SupportSQLiteDatabase
import com.housesigma.android.HSApp
import com.housesigma.android.db.HSRoomDB


object DbManager {

    private const val DB_NAME: String = "hs_db"

    val db: HSRoomDB? by lazy {
        HSApp.appContext?.let {
            Room.databaseBuilder(
                it, HSRoomDB::class.java, DB_NAME
            ).allowMainThreadQueries()
                .addCallback(DbCreateCallBack)
                .addMigrations()
                .build()
        }
    }

    private object DbCreateCallBack : RoomDatabase.Callback() {
        //第一次创建数据库时调用
        override fun onCreate(db: SupportSQLiteDatabase) {
            super.onCreate(db)
            Log.e("TAG", "first onCreate db version: " + db.version)
        }
    }
}