package com.housesigma.android.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.Query
import com.housesigma.android.model.HSCollectData

@Dao
interface HSCollectDataDao {
    @Query("SELECT * FROM HSCollectData")
    fun selectAll(): List<HSCollectData>

    @Insert
    fun insert(vararg data: HSCollectData)

    @Delete
    fun delete(user: HSCollectData)

    @Delete
    fun deleteList(users: List<HSCollectData>)
}