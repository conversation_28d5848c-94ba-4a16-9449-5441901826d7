package com.housesigma.android.helper

import android.content.Context
import android.content.Intent
import com.housesigma.android.ui.home.ListingActivity
import com.housesigma.android.ui.map.MapActivity
import com.housesigma.android.ui.map.agent.AgentExperienceMapActivity
import com.housesigma.android.ui.map.agent.AgentExperienceMapStatus
import com.housesigma.android.ui.map.precon.PreconMapActivity
import com.housesigma.android.ui.map.housephoto.HousePhotoListActivity
import com.housesigma.android.ui.map.housephoto.PreconHousePhotoListActivity
import com.housesigma.android.ui.map.precon.PreconProjectStatus
import com.housesigma.android.ui.recommended.RecommendedStartActivity
import com.housesigma.android.ui.watched.WatchedListingActivity
import com.housesigma.android.ui.webview.WebViewActivity
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.MapUtils

class JumpHelper {

    companion object {


        fun jumpHousePhotoListActivity(context: Context, id_listing: String) {
            val intent = Intent(context, HousePhotoListActivity::class.java)
            intent.putExtra("id_listing", id_listing)
            context.startActivity(intent)
        }

        fun jumpPreconPhotoListActivity(context: Context, id_project: String) {
            val intent = Intent(context, PreconHousePhotoListActivity::class.java)
            intent.putExtra("id_project", id_project)
            context.startActivity(intent)
        }


        fun jumpWebViewActivity(context: Context, url: String) {
            val intent = Intent(context, WebViewActivity::class.java)
            intent.putExtra(
                WebViewHelper.WEB_VIEW_URL,
                url
            )
            //          intent.putExtra(WebViewActivity.WEB_VIEW_TITLE, data.text)
            context.startActivity(intent)
        }

        fun jumpListingActivity(context: Context, type: Int) {
            val intent = Intent(context, ListingActivity::class.java)
            intent.putExtra("type", type)
            context.startActivity(intent)
        }

        fun jumpWatchListActivity(context: Context, id_user_watchlist: String,isWatchList:Boolean) {
            val intent = Intent(context, WatchedListingActivity::class.java)
            intent.putExtra("id_user_watchlist",id_user_watchlist)
            intent.putExtra("is_watch_list",isWatchList)
            context.startActivity(intent)
        }


        fun jumpRecommendStartActivity(context: Context) {
            val intent = Intent(context, RecommendedStartActivity::class.java)
            context.startActivity(intent)
        }


        fun jumpMapActivityFromSearch(
            context: Context,
            lastLat: Double? = 0.0,
            lastLon: Double? = 0.0,
            zoom: Double? = 0.0,
            map_type: ArrayList<String>,
            municipality: String,
            id_community: String?=null
        ) {
            val intent = Intent(context, MapActivity::class.java)
//            "map_type":["for-sale","sold","de-listed-5",
//                "for-lease","leased","de-listed-6"],
            if (map_type.contains("for-sale") || map_type.contains("sold") || map_type.contains("de-listed-5")) {
                intent.putExtra("is_sale", true)
            }
            if (map_type.contains("for-lease") || map_type.contains("leased") || map_type.contains("de-listed-6")) {
                intent.putExtra("is_sale", false)
            }

            intent.putExtra("map_type", map_type)

            // DEV-3993 地图页，list-view模式，跳转时候带municipality_id字段，面包屑有2级
            intent.putExtra("municipality_id" , municipality)
            intent.putExtra("id_community" , id_community)

            intent.putExtra("marker_center_lat", lastLat)
            intent.putExtra("marker_center_lon", lastLon)

            if (lastLat != 0.0 && lastLat != null) {
                MMKVUtils.saveDouble("last_lat", lastLat)
            }

            if (zoom != 0.0 && zoom != null) {
                MapUtils.setCurrentMapZoom(zoom)
            }

            if (lastLon != 0.0 && lastLon != null) {
                MMKVUtils.saveDouble("last_lon", lastLon)
            }
            context.startActivity(intent)
        }

        fun jumpMapWithParamActivity(
            context: Context,
            idListing: String
        ) {
            val intent = Intent(context, MapActivity::class.java)
            intent.putExtra("is_sale", true)
            intent.putExtra("map_type", arrayListOf("for-sale"))
            intent.putExtra("id_listing", idListing)
            context.startActivity(intent)
        }


        fun jumpMapWithParamActivity(
            context: Context,
            lastLat: Double? = 0.0,
            lastLon: Double? = 0.0,
            zoom: Double? = 0.0,
            map_type: ArrayList<String>,
            view: String ? = "map",
            municipality:String? = ""
        ) {
            val intent = Intent(context, MapActivity::class.java)
//            "map_type":["for-sale","sold","de-listed-5",
//                "for-lease","leased","de-listed-6"],
            if (map_type.contains("for-sale") || map_type.contains("sold") || map_type.contains("de-listed-5")) {
                intent.putExtra("is_sale", true)
            }
            if (map_type.contains("for-lease") || map_type.contains("leased") || map_type.contains("de-listed-6")) {
                intent.putExtra("is_sale", false)
            }
            intent.putExtra("view_type", view)
            intent.putExtra("municipality_id", municipality)
            intent.putExtra("map_type", map_type)
            if (lastLat != 0.0 && lastLat != null) {
                MMKVUtils.saveDouble("last_lat", lastLat)
            }

            if (zoom != 0.0 && zoom != null) {
                MapUtils.setCurrentMapZoom(zoom)
            }

            if (lastLon != 0.0 && lastLon != null) {
                MMKVUtils.saveDouble("last_lon", lastLon)
            }
            context.startActivity(intent)
        }


        fun jumpPreconMapWithParamActivity(
            context: Context,
            lastLat: Double? = 0.0,
            lastLon: Double? = 0.0,
            zoom: Double? = 0.0,
            map_type: ArrayList<String>,
            id_project:String? = ""
        ) {
            val intent = Intent(context, PreconMapActivity::class.java)
            val projectStatuses = ArrayList<PreconProjectStatus>()
            if (map_type.contains("selling-now")) {
                projectStatuses.add(PreconProjectStatus.SELLING_NOW)
            }
            if (map_type.contains("upcoming")) {
                projectStatuses.add(PreconProjectStatus.REGISTRATION)
            }
            if (map_type.contains("sold-out")) {
                projectStatuses.add(PreconProjectStatus.SOLD_OUT)
            }
            if (projectStatuses.size==0) {
                projectStatuses.add(PreconProjectStatus.SELLING_NOW)
            }
            intent.putExtra("map_type", projectStatuses)
            intent.putExtra("is_sale", true)
            intent.putExtra("id_listing", id_project)
            if (lastLat != 0.0 && lastLat != null) {
                MMKVUtils.saveDouble("last_lat", lastLat)
            }

            if (zoom != 0.0 && zoom != null) {
                MapUtils.setCurrentMapZoom(zoom)
            }

            if (lastLon != 0.0 && lastLon != null) {
                MMKVUtils.saveDouble("last_lon", lastLon)
            }
            context.startActivity(intent)
        }

        fun jumpAgentMapActivity(
            context: Context,
            filterType: ArrayList<String>? = null,
            slug: String? = null,
            id: String? = null
        ) {
            val intent = Intent(context, AgentExperienceMapActivity::class.java)
            val status = ArrayList<AgentExperienceMapStatus>()
            if (filterType?.contains("bought") == true) {
                status.add(AgentExperienceMapStatus.Bought)
            }
            if (filterType?.contains("listed") == true) {
                status.add(AgentExperienceMapStatus.Listed)
            }
            if (filterType?.contains("toured") == true) {
                status.add(AgentExperienceMapStatus.Toured)
            }
            if (status.size==0) {
                status.add(AgentExperienceMapStatus.Bought)
            }
            intent.putExtra("map_type", status)
            intent.putExtra("agent_slug", slug)
            intent.putExtra("agent_id", id)
            context.startActivity(intent)
        }

        fun jumpPreconMapForSaleActivity(
            context: Context,
            lastLat: Double? = 0.0,
            lastLon: Double? = 0.0,
        ) {
            val intent = Intent(context, PreconMapActivity::class.java)
            intent.putExtra("is_sale", true)
            intent.putExtra("map_type",arrayListOf(PreconProjectStatus.SELLING_NOW))
            if (lastLat != 0.0 && lastLat != null) {
                MMKVUtils.saveDouble("last_lat", lastLat)
            }

            if (lastLon != 0.0 && lastLon != null) {
                MMKVUtils.saveDouble("last_lon", lastLon)
            }

            context.startActivity(intent)
        }

        fun jumpMapForSaleActivity(
            context: Context,
            lastLat: Double? = 0.0,
            lastLon: Double? = 0.0,
            isShowWatchHint: Boolean? = false,
        ) {
            val intent = Intent(context, MapActivity::class.java)
            intent.putExtra("is_sale", true)
            val map_type = ArrayList<String>()
//            map_type.add("for-sale")
//            intent.putExtra("map_type", map_type)
            if (lastLat != 0.0 && lastLat != null) {
                MMKVUtils.saveDouble("last_lat", lastLat)
            }

            if (lastLon != 0.0 && lastLon != null) {
                MMKVUtils.saveDouble("last_lon", lastLon)
            }

            intent.putExtra("is_show_watch_hint", isShowWatchHint)

            context.startActivity(intent)
        }

        fun jumpMapForRentalActivity(
            context: Context,
            lastLat: Double? = 0.0,
            lastLon: Double? = 0.0
        ) {
            val intent = Intent(context, MapActivity::class.java)
            intent.putExtra("is_sale", false)
            val map_type = ArrayList<String>()
//            map_type.add("for-lease")
//            intent.putExtra("map_type", map_type)
            if (lastLat != 0.0 && lastLat != null) {
                MMKVUtils.saveDouble("last_lat", lastLat)
            }

            if (lastLon != 0.0 && lastLon != null) {
                MMKVUtils.saveDouble("last_lon", lastLon)
            }
            context.startActivity(intent)
        }

    }
}