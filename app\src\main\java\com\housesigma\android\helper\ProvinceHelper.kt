package com.housesigma.android.helper

import android.text.TextUtils
import com.google.gson.Gson
import com.housesigma.android.model.InitApp
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.MMKVUtils

class ProvinceHelper {
    companion object {
        /**
         * 通过缩略省份获取完整省份名称
         */
        fun getFullStringFromAbbreviation(abbreviation:String?): String? {
            var fullStringLocation :String?= null
            if ("ON".equals(abbreviation)) {
                fullStringLocation = "Ontario"
            } else if ("BC".equals(abbreviation)) {
                fullStringLocation = "British Columbia"
            } else if ("AB".equals(abbreviation)) {
                fullStringLocation = "Alberta"
            }
            if (TextUtils.isEmpty(fullStringLocation)) {
                val initApp = HSUtil.getInitApp()
                initApp?.let {
                    for (province in initApp.provinces) {
                        if (province.id.equals(abbreviation)) {
                            return province.name
                        }
                    }
                }
            }
            return fullStringLocation
        }

        /**
         * 通过缩略省份获取完整省份名称
         */
        fun getFullStringFromAbbreviation(abbreviation:String?,default:String): String {
            var fullStringLocation = ""
            // ON省是缺省的省份，如果init_app接口还未返回结果，则显示该缺省省份，以保证首页右上角的UI不会出问题
            if ("ON".equals(abbreviation)) {
                fullStringLocation = "Ontario"
            }
            if (TextUtils.isEmpty(fullStringLocation)) {
                val initApp = HSUtil.getInitApp()
                initApp?.let {
                    for (province in initApp.provinces) {
                        if (province.id.equals(abbreviation)) {
                            return province.name
                        }
                    }
                }
            }

            if (TextUtils.isEmpty(fullStringLocation)){
               fullStringLocation = default
            }
            return fullStringLocation
        }

        /**
         * 通过缓存拿完整省份名称，
         * 如：Ontario
         */
        fun getFullStringFromCache(): String? {
            val abbreviation = getAbbreviationFromCache()
            val provinceFullName = getFullStringFromAbbreviation(abbreviation)
            return provinceFullName
        }

        fun getFullStringFromCache(default:String): String {
            val abbreviation = getAbbreviationFromCache()
            val provinceFullName = getFullStringFromAbbreviation(abbreviation, default)
            return provinceFullName
        }

        fun getAbbreviationFromCache():String?{
            val abbreviation = MMKVUtils.getStr(LoginFragment.PROVINCE)
            return  abbreviation
        }

        fun getAbbreviationFromCache(defaultAbbr:String):String{
            val abbreviation = MMKVUtils.getStr(LoginFragment.PROVINCE)?:defaultAbbr
            return abbreviation
        }

    }


}