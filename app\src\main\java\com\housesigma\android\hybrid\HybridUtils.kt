package com.housesigma.android.hybrid

import com.google.gson.Gson
import com.housesigma.android.HSApp
import com.housesigma.android.model.InitApp
import com.housesigma.android.model.User
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.MMKVUtils

object HybridUtils {

    /**
     * 保存并解析初始化应用数据
     * @param any 需要保存的数据对象
     * @return 解析后的 InitApp 对象
     */
    fun saveAndParseInitAppData(any:Any): InitApp? {
        val json = Gson().toJson(any)

        // 磁盘缓存中放一份，hybrid使用
        MMKVUtils.saveStr("init_app", json?:"")

        // 内存缓存中放置一份，hybrid使用
        HSApp.hybridInitApp = json

        // 内存缓存中放置一份，native使用
        val initAppModel = Gson().fromJson(json, InitApp::class.java)
        HSApp.initApp = initAppModel


        return initAppModel
    }

    /**
     * 获取初始化应用数据供 Hybrid 使用
     *
     * @return 返回存储中的 init_app 数据字符串，如果不存在则返回空字符串
     */
    fun getInitAppForHybrid(): String {
        if (HSApp.hybridInitApp != null) {
            // 在内存中命中缓存
            return HSApp.hybridInitApp?:""
        }
        return MMKVUtils.getStr("init_app")?:""
    }

    /**
     * 保存并解析初始化应用数据
     * @param any 需要保存的数据对象
     * @return 解析后的 User 对象
     */
    fun saveAndParseUserData(any:Any?): User? {
        val json = Gson().toJson(any)

        // 磁盘缓存中放一份，hybrid使用
        MMKVUtils.saveStr(LoginFragment.USER_JSON, json?:"")

        // 内存缓存中放置一份，hybrid使用
        HSApp.hybridUser = json

        // 内存缓存中放置一份，native使用
        val user = Gson().fromJson(json, User::class.java)
        HSApp.user = user

        return user
    }

    fun getUserForHybrid(): String {
        if (HSApp.hybridUser != null) {
            // 在内存中命中缓存
            return HSApp.hybridUser?:""
        }
        return MMKVUtils.getStr(LoginFragment.USER_JSON)?:""
    }

}