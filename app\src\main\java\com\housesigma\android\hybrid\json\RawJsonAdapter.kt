package com.housesigma.android.hybrid.json

import com.google.gson.*
import java.lang.reflect.Type

/**
 * 自定义的TypeAdapter，用于处理JSON序列化和反序列化过程中不进行转义
 * 在APP里，主要提供给Hybrid使用
 */
class RawJsonAdapter : JsonSerializer<String>, JsonDeserializer<String> {

    /**
     * 序列化方法，将字符串转换为JsonElement
     *
     * @param src 要序列化的字符串
     * @param typeOfSrc 字符串的类型
     * @param context 序列化上下文
     * @return JsonElement 转换后的JsonElement
     */
    override fun serialize(src: String?, typeOfSrc: Type?, context: JsonSerializationContext?): JsonElement {
        return JsonParser.parseString(src)
    }

    /**
     * 反序列化方法，将JsonElement转换为字符串
     *
     * @param json 要反序列化的JsonElement
     * @param typeOfT 目标类型
     * @param context 反序列化上下文
     * @return String 转换后的字符串
     */
    override fun deserialize(json: JsonElement?, typeOfT: Type?, context: JsonDeserializationContext?): String {
        return json?.toString() ?: ""
    }

}