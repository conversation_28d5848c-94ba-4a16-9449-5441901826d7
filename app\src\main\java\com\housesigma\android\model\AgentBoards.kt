package com.housesigma.android.model

import com.housesigma.android.views.selectlistdialog.SimpleSelectListItem

data class AgentBoards(
    val provinces: List<AgentBoard>
)

data class AgentBoard(
    val boards: List<Board>,
    val province: String,
    val province_text:String,
): SimpleSelectListItem() {
    override fun toString(): String {
        return "$province_text"
    }
}

data class Board(
    val text: String,
    val value: String
): SimpleSelectListItem() {
    override fun toString(): String {
        return "$text"
    }
}