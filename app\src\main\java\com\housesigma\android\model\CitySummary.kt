package com.housesigma.android.model
import com.google.gson.annotations.SerializedName


data class CitySummary(
    @SerializedName("current")
    val current: Current? = null,
    @SerializedName("filter_active")
    val filterActive: FilterActive? = null,
    @SerializedName("message")
    val message: String? = null,
    @SerializedName("municipalities")
    val municipalities: List<CitySummaryMunicipality>
)

data class Current(
    @SerializedName("list_active")
    val listActive: String? = null,
    @SerializedName("list_days")
    val listDays: String? = null,
    @SerializedName("list_live")
    val listLive: Any? = null,
    @SerializedName("list_new")
    val listNew: String? = null,
    @SerializedName("month")
    val month: String? = null,
    @SerializedName("period")
    val period: String? = null,
    @SerializedName("period_name")
    val periodName: String? = null,
    @SerializedName("price_sold")
    val priceSold: String? = null,
    @SerializedName("price_sold_change_10_years")
    val priceSoldChange10Years: String? = null,
    @SerializedName("price_sold_change_5_years")
    val priceSoldChange5Years: String? = null,
    @SerializedName("price_sold_change_month")
    val priceSoldChangeMonth: String? = null,
    @SerializedName("price_sold_change_year")
    val priceSoldChangeYear: String? = null,
    @SerializedName("price_sold_median_90")
    val priceSoldMedian90: String? = null,
    @SerializedName("sold_count")
    val soldCount: String? = null,
    @SerializedName("year")
    val year: String? = null
)

data class FilterActive(
    @SerializedName("community_id")
    val communityId: String? = null,
    @SerializedName("community_name")
    val communityName: String? = null,
    @SerializedName("current_province")
    val currentProvince: String? = null,
    @SerializedName("current_province_name")
    val currentProvinceName: String? = null,
    @SerializedName("house_type")
    val houseType: String? = null,
    @SerializedName("house_type_name")
    val houseTypeName: String? = null,
    @SerializedName("municipality_id")
    val municipalityId: String? = null,
    @SerializedName("municipality_name")
    val municipalityName: String? = null
)

data class CitySummaryMunicipality(
    @SerializedName("location")
    val location: Location? = null,
    @SerializedName("municipality_id")
    val municipalityId: String? = null,
    @SerializedName("municipality_name")
    val municipalityName: String? = null,
    @SerializedName("seo_municipality")
    val seoMunicipality: String? = null,
    @SerializedName("url_name")
    val urlName: String? = null
)
//
//data class Location(
//    @SerializedName("lat")
//    val lat: Double? = null,
//    @SerializedName("lon")
//    val lon: Double? = null
//)