package com.housesigma.android.model

data class Community(
    val community_name: String = "",
    val municipality_name: String = "",
    val province_abbr: String = "",
//    val updatedOn: String = "",
    val community_plus: String = "",
    val house_type: String = "",
    val house_type_name: String = "",
    val id: Int = 0,
    val id_community: String = "",
    val id_municipality: String = "",
//    val id_watch_community: String = "",
    val municipality_plus: String = "",
    val new_listing_count: String? = "",
    val period: String = "",
    val pics: List<String> = ArrayList(),
    val price_sold_median: String = "",
    val sold_count: String? = "",
    var watch_types: List<String> = ArrayList(),
    val coordinate:Location = Location(),
    var map: Location = Location()
)
