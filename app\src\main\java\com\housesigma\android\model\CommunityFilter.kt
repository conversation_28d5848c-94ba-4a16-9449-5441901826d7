package com.housesigma.android.model

data class CommunityFilter(
    val default_municipality_id: Int = 0,
    val default_region_id: String = "",

    val community_filter: List<CommunityFilterX> = ArrayList(),
    val housetype_filter: List<CommunityFilterHouseTypeFilter> = ArrayList(),
    val municipality_filter: List<MunicipalityFilter> = ArrayList(),
    val region_filter: List<RegionFilter> = ArrayList()
)


data class CommunityFilterX(
    val id_municipality: Int = 0,
    val list: List<CommunityFilterItem> = ArrayList(),
)

data class CommunityFilterHouseTypeFilter(
    val id: String = "",
    val name: String = "",
)

data class MunicipalityFilter(
    val municipality_list: List<Municipality> = ArrayList(),
    val region: Region = Region(),
)

data class RegionFilter(
    val id: String = "",
    val name: String = "",
    var checked: Boolean = false
)

data class CommunityFilterItem(
    val id: Int = 0,
    val id_municipality: Int = 0,
    val map: Location,
    val name: String = "",
    var checked: Boolean = false
)

data class Municipality(
    val id: Int = 0,
    val name: String = "",
    var checked: Boolean = false
)

data class Region(
    val id: String = "",
    val name: String = "",
)