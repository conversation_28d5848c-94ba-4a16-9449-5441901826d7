package com.housesigma.android.model

data class CustomizedFilter(
    val active_filter: ActiveFilter,
//    val all_house_type_id: List<String>,
//    val all_municipality_id: List<Int>,
    val house_type_filter: List<HouseTypeFilter>,
//    val message: String,
    val municipality_filter: List<CustomizedMunicipalityFilter>
)

data class ActiveFilter(
//    val default: Boolean,
    val house_type: List<String>,
    val municipality_id: List<Int>,
    val price_max: Int,
    val price_min: Int,
    val province: String
)

data class HouseTypeFilter(
    val id: String,
    val name: String,
    var isSelect: Boolean = false // 本地label选择后就是true，默认false
)

data class CustomizedMunicipalityFilter(
    var isSelect: Boolean = false, //本地label全选，反选
    val list: List<MunicipalityItemFilter>,
    val title: String,
)

data class MunicipalityItemFilter(
    val id: Int,
    val name: String,
    var isSelect: Boolean = false// 本地lable选择后就是true，默认false
)