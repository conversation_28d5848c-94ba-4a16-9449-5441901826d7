package com.housesigma.android.model

import com.google.gson.Gson
import com.google.gson.annotations.Expose
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.MMKVUtils
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

class HSUserData(
    // `en`: The name of the event being tracked.
    // Example events could be "page_view", "click", "user_engagement".
    val en: String?=null,

    // `dt`: The title of the page or the screen name.
    // This helps identify where the event took place.
    val dt: String?=null,

    // `et`: The engagement time, in milliseconds, that the user spent on a particular event or page.
    val et: Long? = null,

    // `ep`: A map of custom parameters associated with the event.
    // Example: ep["id_listing"] = "xLkv3V6jzve3DBNr"
    var ep: HashMap<String, Any>?=null,

    var up: HashMap<String, String>?=null,

    // `sr`: The screen resolution of the user's device.
    // Example: "1080x1920".
    var sr: String?=null,

    // `vp`: The size of the viewport, which is the visible area of a web page on the user's device.
    // Example: "1080x1920".
    var vp: String?=null,

    // `lang`: The language setting of the user's browser or device.
    // Example: "en" for English.
    var lang: String?=null,
) {
    // `ts`: A timestamp of when the event was created, represented in milliseconds since epoch.
    val ts: Long = System.currentTimeMillis()

    // `chk`: A checksum used to verify the integrity of the data.
    // It's generated by hashing the combination of a token, timestamp, event name, and salt.
    val chk: String = generateChk()

    /**
     * Generates a checksum using MD5 hashing of the concatenated string:
     * {token} + {ts} + {en} + {salt}
     */
    private fun generateChk(): String {
        val token = MMKVUtils.getStr(LoginFragment.LOGIN_TOKEN)?:""
        val salt = "%P6bQFDV15qeQKUV^JAr"
        val md5Input = token + ts + en + salt
//        Logger.i("GA Log userData md5Input: $md5Input")
//        Logger.i("GA Log userData md5Output: "+ md5(md5Input))
        return md5(md5Input)
    }

    /**
     * Creates an MD5 hash of the input string.
     * You might need to implement this method or use a library to perform the hashing.
     */
    fun md5(input: String): String {
        try {
            //获取md5加密对象
            val instance: MessageDigest = MessageDigest.getInstance("MD5")
            //对字符串加密，返回字节数组
            val digest:ByteArray = instance.digest(input.toByteArray())
            var sb : StringBuffer = StringBuffer()
            for (b in digest) {
                //获取低八位有效值
                var i :Int = b.toInt() and 0xff
                //将整数转化为16进制
                var hexString = Integer.toHexString(i)
                if (hexString.length < 2) {
                    //如果是一位的话，补0
                    hexString = "0" + hexString
                }
                sb.append(hexString)
            }
            return sb.toString()

        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        }

        return ""
    }

    override fun toString(): String {
        return Gson().toJson(this)
    }
}
