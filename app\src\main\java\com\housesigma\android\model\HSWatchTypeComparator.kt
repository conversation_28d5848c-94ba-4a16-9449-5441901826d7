package com.housesigma.android.model

class HSWatchTypeComparator : Comparator<String> {

    // 实现 compare() 方法
    override fun compare(o1: String, o2: String): Int {
        // 按照 "New"、"Sold"、"Delised" 的顺序进行排序
        return when {
            o1 == "New" -> -1
            o2 == "New" -> 1
            o1 == "Sold" -> -1
            o2 == "Sold" -> 1
            o1 == "Delised" -> -1
            o2 == "Delised" -> 1

            o1 == "new" -> -1
            o2 == "new" -> 1
            o1 == "sold" -> -1
            o2 == "sold" -> 1
            o1 == "delised" -> -1
            o2 == "delised" -> 1
            else -> 0
        }
    }
}
