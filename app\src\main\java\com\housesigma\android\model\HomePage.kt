package com.housesigma.android.model

data class HomePage(
    val chart: List<Chart>? = ArrayList(),
//    val chart_axis: ChartAxis,
//    val message: String,
    val notification: Notification = Notification(),
    /**
     * 1为显示onboard页面，直接跳转onboard
     */
    val show_onboard: Int = 0,

    val chart_title: String? = "",
    val chart_tips: String? = "",
    val chart_subtitle :String?= "",
    val bottom_slogan: String? = "",
)

data class Chart(
//    val list_count: Int = 0,
    val period: String = "",
    val price_sold: String ? = null,
    val sold_count: String ? = null,
)

//data class ChartAxis(
//    val list_count_max: Int = 0,
//    val list_count_min: Int = 0,
//    val price_sold_max: Int = 0,
//    val price_sold_min: Int = 0,
//    val sold_count_max: Int = 0,
//    val sold_count_min: Int = 0
//)

data class Notification(
    val native: List<NativeRouter> = ArrayList()
)

data class NativeRouter(
    val text: String = "",
//    val button: String = "",
    val ga_event: String = "",
    val url: NativeUrl = NativeUrl()
)

data class NativeUrl(
    val action: String = "",
    val params: HashMap<String, Any>? = null,
)
