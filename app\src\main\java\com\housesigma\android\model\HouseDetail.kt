package com.housesigma.android.model

import android.text.TextUtils

class HouseDetail(
    val brokerage_text: String, //brokerage on thumbnail数据源上都有这个字段了
    val scores: Scores,
    val address: String,
//     val address_navigation: String,
//     val apt_num: String,
//     val bedroom: Int,
//     val bedroom_plus: String,
    val bedroom_string: String?=null,
    val community_name: String,
//     val data_source: String,
//     val data_source_raw: String,
//     val data_source_text: String,
//     val date_added: String,
//     val date_added_days: Int,
//     val date_end: String,
//     val date_end_days: Int,
//     val date_start: String,
//     val date_start_days: String,
//     val date_start_month: Int,
//     val date_update: String,
//     val feature_header: String,
//     val house_area: HouseArea,
//     val house_style: String,
//    val house_type: String,
    val house_type_name: String,
    val id_listing: String,
//     val land: Land,
//     val list_days: String,
    val list_status: HouseListStatus,
    val map: Location,
    val ml_num: String,
//     val ml_num_merge: String,
    val municipality_name: String,
//     val open_house_date: List<Any>,
    val parking: Parking,
//     val photo_source: Any,
    val photo_url: String,
    val price: String,
//     val price_abbr: String,
//     val price_change_yearly: Double,
    val price_change_yearly_text: String,
//     val price_int: Any,
//     val price_origin: Any,
    val price_sold: String,
//     val price_sold_int: Any,
//    val province: String,
//     val province_name: String,
//     val raw: Raw,
//     val scores: Scores,
    val seo_suffix: String,
//     val sold_month: String,
//     val tags: List<String>,
    val text: Text,
    val tos_source: String,
    val washroom: String?=null,


    var isEdit: Boolean,//watched里面的编辑note的
    var note: String,//watched里面才有的 note字段
) {
    fun isForSellType(): Boolean {
        return list_status.live == 1
    }

    fun isWatched(): Boolean {
        return list_status.watched == 1
    }

    /**
     * sold for显示条件
     */
    fun isSoldType(): Boolean {
        return list_status.sold == 1 && !TextUtils.isEmpty(price_sold)
    }

    fun isAgreementRequired(): Boolean {
        return list_status.public == -5
    }

    /**
     * DEV-5474 TRREB Compliance for Agent | 根据用户是否为地产经纪显示不同内容
     */
    fun isNotAvailable(): Boolean {
        return list_status.public == -7
    }

    fun isLoginRequired(): Boolean {
        return list_status.public == 0
    }

    // public = -1 时，trreb不活跃超时
    fun isNeedReLogin(): Boolean {
        return list_status.public == -1
    }


    // DEV-6977 password expire 90 days
    fun isPasswordExpired(): Boolean {
        return list_status.public == -8
    }
}