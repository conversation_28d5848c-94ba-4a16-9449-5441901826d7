package com.housesigma.android.model

data class HousePhotosInfo(
    val picture: Picture = Picture(),
    val contact_message: String? = "",
    val schedule_message: String? = "",
    val list_status: HouseListStatus,
    val agent_user: AgentUser? = null,
    val bind_agent_user:BindAgent?=null,
    val ml_num: String? = "",
    val municipality_name: String,
    val meta: PhotoMeta?=null,
    val show_trustpilot: Boolean ?= false,
    val transactions: Transactions? = null,
) {
    fun agentAvailable(): Boolean {
        return agent_user != null || transactions?.agent != null
    }
    fun isForSellType(): Boolean {
        return list_status?.isForSellType()?:false
    }
}


data class Transactions(
    val contact_agent_message: String?,
    val contact_agent_tag: String?,
    val agent: TransactionsAgent?
)


data class TransactionsAgent(
    val id: Int?,
    val name: String?,
    val phone: String?,
    val email: String?,
    val picture: String?,
    val slug: String?,
    val province: String?
)

data class PhotoMeta(
    val title_share: String = "",
    val url_share_android: String = ""
)

data class Picture(
    val photo_list: List<String> = ArrayList(),
    val thumb_photo_list: List<String> = ArrayList(),
//    val photo_source: Any,
    val photo_url: String = "",
    val virtual_tour: String = ""
)

data class AgentUser(
    val id: Int? = null,
)