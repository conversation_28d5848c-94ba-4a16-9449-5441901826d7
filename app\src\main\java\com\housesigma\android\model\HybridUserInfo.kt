package com.housesigma.android.model

import com.google.gson.annotations.JsonAdapter
import com.housesigma.android.BuildConfig
import com.housesigma.android.hybrid.json.RawJsonAdapter

data class HybridUserInfo(
    var access_token: String?,
    val is_hide_header: Int,
    val is_sign_in: Int,
    val lang: String = "en_US",
    val province: String = "ON",
    @JsonAdapter(RawJsonAdapter::class)
    val user: String?,
    @JsonAdapter(RawJsonAdapter::class)
    val app: String?,
    val version: String = BuildConfig.VERSION_NAME,
    var secret: Secret? = null,
)