package com.housesigma.android.model

import com.google.gson.annotations.SerializedName

data class InitApp(
//    val apiUrl: String = "",
    val force_update: Boolean = false,
//    val geoip: InitAppGeoip = InitAppGeoip(),
//    val message: String = "",
    val meta: Meta = Meta(),
    val provinces: List<Province>,
//    val qr_code: QrCode = QrCode(),
//    val siteUrl: String = "",
//    val ua_detection: UaDetection = UaDetection(),
    val update: Boolean = false,
    val update_message: String = "",
    val update_url: String = "",
    val tos: HashMap<String, Term> = HashMap(),
    val native_config: NativeConfig? = NativeConfig(),
    val timeout_explain: TimeoutExplain? = TimeoutExplain(),

    // DEV-6977 TRREB compliance - password expire 90 days
    val revalidate_explain: TimeoutExplain? = TimeoutExplain(),

    // DEV-7480 API - add properties for show/hide RAE tips
    //  1 - 显示，0 - 隐藏 null 隐藏
    val show_rae_map_tips:Int?,

    // DEV-8471 API collect user location data close to the touring event
    //  1 - 开，0 - 关
    @SerializedName("collect_location")
    val collectLocation:Int? = 0,

    // DEV-8666 rate limit on collecting user location
    // 表示采集位置频率限制最快为 多少秒/ 次
    @SerializedName("location_collect_rate")
    val locationCollectRate: Int? = 0,
)

data class Secret(
    @SerializedName("is_encrypted")
    var isEncrypted: Int ?= null,
    @SerializedName("secret_key")
    var secretKey: String ?= null,
    @SerializedName("expired_at")
    var expiredAt: Long ?= null,
)

data class TimeoutExplain(
    val explain_text: String? = "",
    val explain_subtext: String? = "",

    val explain_title: String? = "",
    val button_text: String? = "",
)

data class NativeConfig(
    val map_satellite: String? = "",
    val map_vector: String? = "",
    val map_static: String? = "",
    val url_join_us: String? = "",
)

data class Term(
    val term_text: Tos
)

data class Tos(
    val header: String = "",
    val body: String = "",
)

//data class InitAppGeoip(
//    val continent: String = "",
//    val continent_iso: String = "",
//    val country: String = "",
//    val country_iso: String = "",
//    val lang: String = "",
////    val province: Any,
////    val province_iso: Any
//)

data class Meta(
//    val description: String = "",
//    val header: String = "",
//    val image: String = "",
    val title: String = "",
//    val type: String = "",
    val url_android: String = ""
)

data class Province(
    val id: String = "",
    val location: Location,
    val name: String = "",
//    val proximity: String = ""
)

//data class QrCode(
//    val app_download_android: String = "",
//    val app_download_ios: String = "",
//    val wechat_public: String = ""
//)
//
//data class UaDetection(
//    val browser: String = "",
//    val device_type: String = "",
//    val is_oldWechat: Boolean = false,
//    val is_robot: Boolean = false,
//    val is_wechat: Boolean = false,
//    val platform: String = "",
//    val platform_version: String = ""
//)