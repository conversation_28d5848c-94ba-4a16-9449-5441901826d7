package com.housesigma.android.model

import java.util.ArrayList

data class MapFilter(
    val basement_filter: List<MapFilterIdName>,
    val bathroom_filter: List<MapFilterIdName>,
    val bedroom_filter: List<MapFilterIdName>,
    val days_filter_all: DaysFilterAll,
    val default_filter: DefaultFilter,
    val garage_filter: List<MapFilterIdName>,
    val house_type_filter: List<MapFilterHouseTypeFilter>,
    val listing_status_filter: List<ListingStatusFilter>,
//    val message: String,
    val open_house_filter: List<MapFilterIdName>,
    val listing_type_filter: List<MapFilterIdName>,
    val lot_size_filter: List<LotSizeFilter> ?= null,
    val building_age_filter: List<BuildingAgeFilter> ?= null,
    val filter_tip: String ?= null,
){
    /**
     * days_filter_all.de_listed_v2 为空，不显示 de-listed filter，包括 de-listed filter 的文本
     */
    fun hasDeListed(): Boolean {
        return days_filter_all?.de_listed_v2?.size!=0
    }

    /**
     * days_filter_all.sold_v2 为空，不显示 Sold Filter， 包括 Sold filter 的文本
     */
    fun hasSold(): Boolean {
        return days_filter_all?.sold_v2?.size!=0
    }
}

data class BuildingAgeFilter(
    val value: Int?=null,
    val label: String?=null,
    val text: String?=null,
)

data class LotSizeFilter(
    val value: Int?=null,
    val label: String?=null,
    val text: String?=null,
)

data class DaysFilterAll(
    val listing: List<ListingFilter>,
    val sold_v2: List<ListingFilter>,
    val de_listed_v2: List<ListingFilter>,
)

data class DefaultFilter(
    val list_type: List<String>,
    val rent_list_type: List<String>,
    val listing_days: String,
    val sold_days: String,
    val de_list_days: String,
    var basement: List<String>,
    val open_house_date: String,
    val description: String,
    val max_maintenance_fee: String,
    var bedroom: List<String>,
    val bathroom: String,
    val garage: String,
    val house_type: List<String>,
    val price_sale_min: String,
    val price_sale_max: String,
    val price_rent_min: String,
    val price_rent_max: String,
    val square_footage_min: String,
    val square_footage_max: String,
    val lot_front_feet_min: String,
    val lot_front_feet_max: String,
    val listing_type: List<String>,
    val lot_size_max: String,
    val lot_size_min: String,
    val building_age_max: String,
    val building_age_min: String,
    var houseTypeLocal: ArrayList<String>,
)

data class MapFilterHouseTypeFilter(
    val id: String,
    val name: String,
    var isSelect:Boolean = false
) {
    override fun toString(): String {
        return "id='$id', name='$name')"
    }
}

data class ListingStatusFilter(
    val id: Int,
    val name: String
)

data class MapFilterIdName(
    val id: String,
    val name: String,
    var isSelect:Boolean = false
)


data class ListingFilter(
    val abbr: String,
    val id: String,
    val name: String,
    var checked: Boolean
)
