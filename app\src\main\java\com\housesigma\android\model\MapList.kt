package com.housesigma.android.model

import com.google.gson.annotations.Expose
import org.maplibre.android.annotations.Marker

data class MapList(
    val list: List<MapMarkerInfo> = ArrayList(),
    val alert_message: String,
    val alert_steps: Int,
    var requestZoom: Double = 0.0
)


data class MapMarkerInfo(
    @Expose(serialize = false, deserialize = false)
    @Transient
    var localMarker: Marker? = null,
    val count: Int = 0,
    val ids: List<String> = ArrayList(),
    val label: String = "",
    val location: Location = Location(),
    val marker: String = "",
    val type: String = "",

    //near里特有的
//    var isNear: Boolean = false,

    var nearZoom: Int = 0,//0最小，1小，2中，3大
    var agentMapZoom: Int = 0,//0小圆点，1正常

    //下面俩字段是mapSearchFeature接口才有的
    val ml_num: String = "",
    val type_own_srch: String = ""
) {
    override fun equals(other: Any?): Boolean {
        other as MapMarkerInfo
        //这里不能用ids比较，因为在是聚合点的时候，ids可能会随机返回
        if ((this.marker == other?.marker)
            && this.location.lat == other.location.lat
            && this.location.lon == other.location.lon
            && this.type == other.type
            && this.count == other.count
            && this.nearZoom == other.nearZoom
            //DEV-4310 TRREB Compliance - Sold price shouldn't show on map without login
            //登录以后并未刷新 marker, 且移动地图位置，仍然有部分 marker 显示遮罩，
            //遮罩字段是放在label上的，所以label也要加入对比
            && this.label == other.label
        ) {
            return true
        }
        return false
    }

    fun setAgentPageZoom(zoom:Double){
        if ((zoom > 11)) {
            agentMapZoom = 1
        } else {
            agentMapZoom = 0
        }
    }

    fun setNearZoom(zoom:Double){
        if ((zoom  >= 18.0)) {
            nearZoom = 2
        } else if ((zoom >= 17)) {
            nearZoom = 1
        } else { // < 17
            nearZoom = 0
        }
    }
}
