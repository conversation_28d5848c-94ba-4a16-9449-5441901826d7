package com.housesigma.android.model

import android.util.Log
import org.maplibre.android.annotations.Marker

data class MapSearchSchool(
//    val filter_active: FilterActive,
    val list: List<SchoolInfo> = ArrayList(),
//    val message: String = ""
)


//data class FilterActive(
//    val catholic: Int,
//    val elementary: Int,
//    val lat1: Double,
//    val lat2: Double,
//    val lon1: Double,
//    val lon2: Double,
//    val match_score: Int,
//    val `public`: Int,
//    val secondary: Int
//)

data class SchoolInfo(
    var localMarker: Marker? = null,
//    val address: String = "",
//    val board: String = "",
//    val board_type: String = "",
//    val city: String = "",
    val id: Int = 0,
    val lat: Double = 0.0,
    val lng: Double = 0.0,
//    val name: String = "",
//    val region: String = "",
//    val school_type: String = "",
    val score: Double = 0.0
){
    override fun equals(other: Any?): Boolean {
        other as SchoolInfo
        if ((this.id == other?.id)
        ) {
            return true
        }
        return false
    }
}