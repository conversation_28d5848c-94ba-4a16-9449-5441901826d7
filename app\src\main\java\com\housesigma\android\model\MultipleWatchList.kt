package com.housesigma.android.model

import com.chad.library.adapter.base.entity.MultiItemEntity

class MultipleWatchList : ArrayList<MultipleWatchItem>()

data class MultipleWatchItem(
    val id: String,
    val is_default: Int,
    val name: String? = null,
    val watched_count: Int,
    var is_watched: Int = 0,//1为已关注
    val privacy: Int? = 0,//watchlist privacy status, 0 - private 1- public
    val meta:WatchListMeta ?= null,
    val owner_name:String ?= null,
    val status_chg_date_text:String ?= null,

    var is_load_photo: Boolean = false,//图片已加载过为true
    var photo_url: String? = null, //当用户浏览到该位置自动填充字段
    var is_head: Boolean? = false, //是否头
    override var itemType: Int,
): MultiItemEntity {
    fun isPublic(): Boolean {
        return privacy==1
    }

    fun isPrivate(): Boolean {
        return privacy==0
    }
}

data class WatchListMeta(
    val title: String? = "",
    val url: String? = ""
){
    fun getShareContent(): String {
        return (title?:"") + " " + (url?:"")
    }
}

