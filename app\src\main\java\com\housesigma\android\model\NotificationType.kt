package com.housesigma.android.model

data class NotificationType(
    val contact_email: String?="",
    val notification_v2: NotificationSetting
)

data class NotificationSetting(
    val email: EmailSetting,
    val push_notification: PushNotificationSetting
)

data class EmailSetting(
    val recommend: RecommendSetting,
    val watched_area: WatchedAreaSetting,
    val watched_community: WatchedCommunitySetting,
    val watched_listing: WatchedListingSetting
)

data class PushNotificationSetting(
    val watched_listing: WatchedListingSetting
)

data class RecommendSetting(
    val enabled: Int
)

data class WatchedAreaSetting(
    val enabled: Int
)

data class WatchedCommunitySetting(
    val enabled: Int
)

data class WatchedListingSetting(
    val enabled: Int
)