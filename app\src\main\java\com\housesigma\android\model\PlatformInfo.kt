package com.housesigma.android.model

import android.os.Build
import com.housesigma.android.BuildConfig
import com.housesigma.android.HSApp
import com.housesigma.android.utils.DeviceUtil
import com.housesigma.android.utils.ScreenUtils


//device_type: this.deviceService.deviceType,
//device_version: this.deviceService.deviceVersion,
//os_version: this.deviceService.osVersion,
//width: screen.width,
//height: screen.height,
//ratio: window.devicePixelRatio
data class PlatformInfo(
    val device_type: String = Build.BRAND,//设备牌子
    val device_version: String = Build.MODEL,//设备型号
    val os_version: String = DeviceUtil.oS,//OS版本
    val width: String? = HSApp.appContext?.let { ScreenUtils.getDeviceWidth(it).toString() },
    val height: String? = HSApp.appContext?.let { ScreenUtils.getDeviceHeight(it).toString() },
    val dpi: String? = HSApp.appContext?.let { ScreenUtils.getDeviceDpi(it).toString() },
)

data class SoftInfo(
    val package_id: String?  = BuildConfig.APPLICATION_ID,
    val version: String = BuildConfig.VERSION_NAME,
    val platform: PlatformInfo = PlatformInfo()
) : BaseRequestBody()

data class ABTest(
    val data: String
) : BaseRequestBody()