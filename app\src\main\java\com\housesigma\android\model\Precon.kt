package com.housesigma.android.model

data class Precon(
    val price: String ?= "",
    val project_name: String = "",
    val addr: String = "",
    val property_type_text: String = "",
    val bedroom_text: String = "",
    val sqft_text: String = "",
    val photo_url: String = "",
    val completion_date: String = "",
    val id_project: String = "",
    val province: String = "",
    val municipality: String = "",
    val project_status_text:String = "",


    val marker_label:String?=null,
    val location: Location = Location(),
)