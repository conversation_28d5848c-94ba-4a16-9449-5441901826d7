package com.housesigma.android.model

data class PreconMapFilter(
    val bathrooms_filter: List<MapFilterIdName>,

    val bedrooms_filter: List<MapFilterIdName>,

    val basement_filter: List<MapFilterIdName>,

    val construction_status_filter: List<ConstructionStatusFilter>,

    val default_filter: PreconDefaultFilter,

    val est_completion_year_filter:  List<EstCompletionYearFilter>,

    val project_status_filter: List<ProjectStatusFilter>,

    val property_type_filter: List<PreconPropertyTypeFilter>,
)


data class ConstructionStatusFilter(
    val id: String,
    val name: String,
)

data class PreconPropertyTypeFilter(
    val id: String,
    val name: String,
)

data class EstCompletionYearFilter(
    val id: String,
    val name: String
)

data class ProjectStatusFilter(
    val id: Int,
    val name: String
)

data class PreconDefaultFilter(

    val bathroom: String,

    var bedroom: List<String>,
    var bedroomLocal: List<String>,

    val construction_status: List<String>,
    var preconConstructionStatusLocal: List<String>,

    val description: String,

    val est_completion_year: List<String>,
    var estCompletionYearLocal: List<String>,

    val price_max: String,//price_max
    val price_min: String,//price_min

    val project_status: List<String>,

    val property_type: List<String>,
    var preconPropertyLocal: List<String>,

    val square_footage_min: String,
    val square_footage_max: String,
)

