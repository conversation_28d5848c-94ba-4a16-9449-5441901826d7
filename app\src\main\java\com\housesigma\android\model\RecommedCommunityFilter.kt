package com.housesigma.android.model

data class RecommedCommunityFilter(
    val all_house_type_id: List<String> = ArrayList(),
    val all_investment_id: List<String> = ArrayList(),
    val all_municipality_id: List<Int> = ArrayList(),
    val house_type_filter: List<HouseTypeFilter> = ArrayList(),
    val investment_filter: List<InvestmentFilter> = ArrayList(),
//    val message: String = "",
    val municipality_filter: List<CustomizedMunicipalityFilter> = ArrayList()
)


data class InvestmentFilter(
    val id: String = "",
    val name: String = ""
)
