package com.housesigma.android.model

import com.google.gson.annotations.SerializedName


data class Recommend(
    val filter_is_default: Boolean = false,
    val head: String = "",
    val list: List<HouseDetail> = ArrayList(),
    val pagination: Pagination ?= null,
    val type: Int = 0
)

data class Pagination(
    @SerializedName("current_page")
    val currentPage: Int?=null,

    val rowCount: Int?=null,
)


//data class Analytics(
//    val estimate_price: String,
//    val estimate_price_age: Int,
//    val estimate_price_date: String,
//    val rent_base: String,
//    val rent_yield: String
//)

//data class HouseArea(
//    val area: Any,
//    val area_note: String,
//    val estimate: Int,
//    val unit: String
//)
//
//data class Land(
//    val depth: Int,
//    val front: Int,
//    val unit: String
//)

/**
不止一个 api 有这个字段。
不同的 api，返回的字段不完全一致
 */
data class HouseListStatus(
    val agent_available: Boolean,
//    val archive: Int,
//    val feature: Any,
    val live: Int,
//    val precon: Any,
//    val premium: Int,
    val `public`: Int,
//    val s_r: String,
    val sold: Int,
    val status: String,
    val text: String,
    val watched: Int,
) {
    fun isForSellType(): Boolean {
        return live == 1
    }

    fun isWatched(): Boolean {
        return watched == 1
    }
    
    /**
     * Get display text for status, specifically handle Leased status
     */
    fun getDisplayText(): String {
        return if (status.equals("LSD", ignoreCase = true)) {
            "Leased"
        } else {
            text
        }
    }
    
    /**
     * Get sold for label text, returns "Leased:" for LSD status, "Sold for:" otherwise
     */
    fun getSoldForDisplayText(): String {
        return if (status.equals("LSD", ignoreCase = true)) {
            "Leased:"
        } else {
            "Sold for:"
        }
    }
}

data class Parking(
//    val garage: String,
//    val garage_type: String,
//    val parking: Int,
//    val parking_type: Any,
//    val text: String,
    val total: String?=null
)

//data class Raw(
//    val id_listing: Int,
//    val price: Int,
//    val price_sold: Int
//)

data class Scores(
    val growth: String ?= null,
    val land: String ?= null,
    val rent: String ?= null,
    val school: String ?= null,
)

data class Text(
//    DEV-2302 卡片的新增日期字段 date_preview，把日期字段逻辑统一放到后端
    val date_preview: String = "",
//    val status_chg_date:String,
//    val dom_long: String,
    val highlight: String,

    val highlight_left: HighlightLeft? = null,

    val hs_exclusive_tag: String?=null,
    
    val transaction_via_label: String?=null,
//    val dom_short: String,
//    val dom_short_v2: String,
//    val list_date_long: String,
//    val list_date_short: String,
//    val rooms_long: String,
//    val rooms_short: String
)


data class HighlightLeft(
    val name: String?="",
    val count: Int,
)