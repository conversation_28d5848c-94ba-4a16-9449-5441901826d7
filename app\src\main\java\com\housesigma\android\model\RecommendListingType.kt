package com.housesigma.android.model

import android.text.TextUtils
import com.housesigma.android.R

class RecommendListingType {
    companion object {
        val watchedCommunityUpdates: Int = 6
        val soldBelowBought: Int = 5
        val featuredListings: Int = 7
        val justSold: Int = 1
        val highReurnsType: Int = 8
        val bestForRentalInvestment: Int = 2
        val recommendForYou: Int = 10
        val exclusivePreconAssignment: Int = 11
        val bestForSchool: Int = 3
        val oneYearPriceGrowth: Int = 4
        val newlyListed: Int = 9

        val watchedCommunityNewOrSoldUpdate: Int = 1000

        fun convert(recommendType: Int): String {
            when (recommendType) {
                RecommendListingType.watchedCommunityUpdates -> {
                    return "watched_area_and_community"
                }
                RecommendListingType.soldBelowBought -> {
                    return "sold_below_bought"
                }
                RecommendListingType.featuredListings -> {
                    return "featured_listings"
                }
                RecommendListingType.justSold -> {
                    return "just_sold"
                }
                RecommendListingType.highReurnsType -> {
                    return "high_returns"
                }
                RecommendListingType.bestForRentalInvestment -> {
                    return "best_for_rental_investment"
                }
                RecommendListingType.bestForSchool -> {
                    return "best_for_schools"
                }
                RecommendListingType.oneYearPriceGrowth -> {
                    return "1_year_price_growth"
                }
                RecommendListingType.newlyListed -> {
                    return "newly_listed"
                }
                RecommendListingType.recommendForYou -> {
                    return "recommended_for_you"
                }
                RecommendListingType.exclusivePreconAssignment -> {
                    return "exclusive_precon_assignment"
                }
                else -> {
                    return ""
                }
            }

        }
    }


}