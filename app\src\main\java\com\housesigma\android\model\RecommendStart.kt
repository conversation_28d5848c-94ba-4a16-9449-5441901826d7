package com.housesigma.android.model

data class RecommendStart(
    val community_list: List<RecommendStartItem> = ArrayList(),
    var total_city:Int = 0,
    var total_community:Int = 0,
    var total_listing:Int = 0,

    var current_page:Int = 0,
    var total_page:Int = 0
)

data class RecommendStartItem(
    val community_plus: String = "",
    val municipality_plus: String = "",
    val price_sold_median: String = "",
    val house_type_name: String = "",
    val house_type:String = "",
    val pics:ArrayList<String> = ArrayList(),
    val listing:Int = 0,
    val id_community:String = "",
    val id_municipality:String = ""
)




