package com.housesigma.android.model


class RequestGoogleSignup(
    val client_type: String ?= "android",
    val credential: String = "",
    val pass: String = "",
    val referral_code: String = "",
    val is_agent: String = "",
    val licensed_province: String = "",
    val board_name: String = "",
    val brokerage_name: String = "",
    var agent_cracking: AgentCracking? = null
)


class AgentCracking (
    val is_agent: String = "",
    val licensed_province: String = "",
    val board_name: String = "",
    val brokerage_name: String = ""
)