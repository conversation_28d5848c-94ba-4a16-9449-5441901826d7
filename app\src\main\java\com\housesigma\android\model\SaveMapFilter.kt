package com.housesigma.android.model

import com.google.gson.annotations.SerializedName

data class SaveMapFilter(
    @SerializedName("filter_name")
    var filterName: String = "",
    var category: String = "",
    var filter: MapFilters,
    var id: String ?= null,
)


class MapFilters {
    // Sale\Lease\Precon
    var bathroom: String ?= "0"
    // Sale\Lease\Precon
    var bedroom: List<String> ?= ArrayList()
    // Sale\Lease\Precon
    var description: String ?= ""
    // Sale\Lease\Precon
    var price: List<String> ?= ArrayList()
    // Sale\Lease\Precon
    var square_footage_max: String  ?= ""
    // Sale\Lease\Precon
    var square_footage_min: String  ?= ""
    // 新增：租金回报率筛选
    var rental_yield_min: String? = null
    var rental_yield_max: String? = null
    // 新增：学校评分筛选
    var school_score_min: String? = null
    var school_score_max: String? = null

    // Sale\Lease
    var basement: List<String> ?= ArrayList()
    // Sale\Lease
    var garage: String ?= "0"
    // Sale\Lease
    var house_type: List<String> ?= ArrayList()
    // Sale\Lease
    var max_maintenance_fee: String ?= ""
    // Sale\Lease
    var open_house_date: String ? = "0"
    // Sale\Lease
    var building_age_max: String ? = "0"
    // Sale\Lease
    var building_age_min: String ? = "999"
    // Sale\Lease
    var lot_size_max: String ? = "10000000"
    // Sale\Lease
    var lot_size_min: String ? = "0"
    // Sale\Lease
    var lot_front_feet_max: String ? = ""
    // Sale\Lease
    var lot_front_feet_min: String ? = ""
    // Sale\Lease
    var listing_type: List<String> ? = ArrayList()
    // Sale\Lease
    var de_list_days: String ? = "90"
    // Sale\Lease
    var listing_days: String ? = "0"
    // Sale\Lease
    var sold_days: String ? = "90"


    // Precon
    var property_type: List<String> ? = ArrayList()
    // Precon
    var construction_status: List<String> ? = ArrayList()
    // Precon
    var est_completion_year: List<String> ? = ArrayList()

}