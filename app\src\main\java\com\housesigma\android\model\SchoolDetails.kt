package com.housesigma.android.model

import org.maplibre.android.geometry.LatLng

data class SchoolDetails(
    val address: String,
    val board: String,
    val city: String? = null,
    val name: String,
    val phone: String,
    val board_type: String? = null,
    val region: String? = null,
    val school_type: String? = null,
    val score: String? = null,
//    val score_year: String,
//    val score_year_label: String,
    val web: String,

//    val geo_json: List<List<List<Double>>>,
    val geo_json_v2: GeoJsonV2,
//    val eqao_json: EqaoJson,

//    val score_json: ScoreJson,
    val score_json_v2: List<ScoreJsonV2>? = null,
)

data class GeoJsonV2(
    val coordinates: List<List<List<List<Double>>>>,
)

data class ScoreJsonV2(
    val year: String,
    val rank_name: String,
    val rank_value: String,
    val score_name: String,
    val score_value: String,
)

//data class ScoreJson(
//    val rank_2015: String,
//    val rank_2016: String,
//    val rank_2017: String,
//    val rank_2018: String,
//    val rank_2019: String,
//    val rank_score_2015: String,
//    val rank_score_2016: String,
//    val rank_score_2017: String,
//    val rank_score_2018: String,
//    val rank_score_2019: String,
//    val average_exam_mark_2015: String,
//    val average_exam_mark_2016: String,
//    val average_exam_mark_2017: String,
//    val average_exam_mark_2018: String,
//    val average_exam_mark_2019: String,
//)

//data class EqaoJson(
//    val EQAO3Last5Avg: String,
//    val EQAO3Rank: String,
//    val EQAO3Total: String,
//    val EQAO3YearMark: String,
//    val EQAO6Last5Avg: String,
//    val EQAO6Rank: String,
//    val EQAO6Total: String,
//    val EQAO6YearMark: String,
//    val EQAO9Last5Avg: String,
//    val EQAO9Rank: String,
//    val EQAO9Total: String,
//    val EQAO9YearMark: String,
//)