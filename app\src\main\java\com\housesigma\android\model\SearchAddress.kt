package com.housesigma.android.model

import android.util.Log
import com.housesigma.android.utils.log.Logger

data class SearchAddress(
    val city_list: List<City> = ArrayList(),
    val community_list: List<Community> = ArrayList(),
    val community_suggest: List<Community> = ArrayList(),
    val house_list: List<House> = ArrayList(),
    val precon_list: List<Precon> = ArrayList(),
//    val message: String = "",
    val place_list: List<PlaceList> = ArrayList()
)

data class PlaceList(
    var text: String = "",
    var lat: Double = 0.0,
    var lng: Double = 0.0,
    var id_municipality:String? = ""
)

data class City(
    //0是city，1是Community，2是address
    var location_type: Int = 0,
    var id_community: String = "",
    val address: String = "",
    val location: Location = Location(),
    val id_municipality:String? = ""
)

data class MapHouseText(
    val date_preview: String = "",
    val hs_exclusive_tag: String ?= null
)


data class House(
    val price: String?,//这个是map many接口特有的
    val house_type_name: String,//这个是map many接口特有的
    val text: MapHouseText,//这个是map many接口特有的
    val tos_source: String,//这个是map many接口特有的
    val scores: Scores,//这个是map many接口特有的
    val parking: Parking,//这个是map many接口特有的
//    val list_days: String,//这个是map many接口特有的
    val brokerage_text: String,
    var map: Location = Location(),//这个是map many接口特有的
    var open_house_date:ArrayList<OpenHouse> = ArrayList(),//这个是map many接口特有的
    var address_navigation:String = "",
    val price_sold:String = "",
    val date_preview: String,
    val marker_label:String?=null, //DEV-4998 View in Full Map marker显示字段

//    val addr: String,
    val address: String,
//    val apt_num: String,
//    val bedroom: Any,
//    val bedroom_plus: Any,
    val bedroom_string: String?=null,
    val community_name: String,
//    val data_source: String,
//    val date_start: String,
//    val flg_disp_addr: Int,
//    val flg_idx: Int,
    val garage: String?=null,
//    val history: List<String>,
//    val history_id_listing: List<Int>,
    val id: Int,
    val id_listing: String,
//    val id_municipality: Int,
//    val last_sold: Any,
    val list_status: ListStatus,
    val location: Location = Location(),
//    val ml_count: Int,
    val ml_count_text: String,
//    val ml_num: String,
//    val ml_num_merge: String,
    val municipality_name: String,
    val photo_url: String,
//    val price_abbr: String,
    val price_rent: String,
//    val price_rent_status: Any,
    val price_sale: String,
//    val price_sale_status: String,
//    val province_abbr: String,
//    val rooms_text: String,
    val s_r: String,
    val seo_suffix: String,
//    val status: String,
//    val tags: List<String>,
//    val type: String,
    val type_text: String,

    val washroom: String?=null,


    ) {

    //agent_available
    data class ListStatus(
        val agent_available:Boolean = false,
        val live: Int,
        val `public`: Int,
//    val s_r: String,
        val sold: Int,
        val status: String,
        val text: String
    ) {
        /**
         * Get display text for status, specifically handle Leased status
         */
        fun getDisplayText(): String {
            return if (status.equals("LSD", ignoreCase = true)) {
                "Leased"
            } else {
                text
            }
        }
        
        /**
         * Get sold for label text, returns "Leased:" for LSD status, "Sold for:" otherwise
         */
        fun getSoldForDisplayText(): String {
            return if (status.equals("LSD", ignoreCase = true)) {
                "Leased:"
            } else {
                "Sold for:"
            }
        }
    }
    fun isDFT(): Boolean {
        return list_status.status == "DFT"
    }
    fun isAgreementRequired(): Boolean {
        return list_status.public == -5
    }
    /**
     * DEV-5474 TRREB Compliance for Agent | 根据用户是否为地产经纪显示不同内容
     */
    fun isNotAvailable(): Boolean {
        return list_status.public == -7
    }

    fun isLoginRequired(): Boolean {
        return list_status.public == 0
    }

    //  public = -1 时，trreb不活跃超时
    fun isNeedReLogin(): Boolean {
        return list_status.public == -1
    }

    // DEV-6977 password expire 90 days
    fun isPasswordExpired(): Boolean {
        return list_status.public == -8
    }

    fun statusTypeV2(): listingOnSearchStatusTypeV2 {
        if (list_status.live == 0 && list_status.sold == 0) {// 灰色、划线
            return listingOnSearchStatusTypeV2.offline
        } else if (list_status.live == 1) {// 青色
            return listingOnSearchStatusTypeV2.onSaleOrOnRent
        } else if (list_status.sold == 1) {// 橙色
            return listingOnSearchStatusTypeV2.soldOrRend
        } else {
            // 这种情况按业务逻辑，不应当出现
            Logger.e( "error listStatusV2")
            return listingOnSearchStatusTypeV2.offline
        }
    }

    fun getPriceText(): String {
        if (list_status?.sold == 1){
            return price_sold?:"-"
        } else {
            return price?:"-"
        }
    }

//    fun lineThroughPrice(): Boolean {
//        if (s_r == "Sale" && price_sale_status == "A") {
//            return true
//        } else if (s_r == "Lease" && price_sale_status == "A") {
//            return true
//        }
//        return false
//    }

}

/**
参考岳说的，分为了三种状态
live == 0 && sold == 0: 下线
live == 1: 在售在租
sold == 1: 已售已出租
 */
enum class listingOnSearchStatusTypeV2 {
    /// 下线
    offline,

    /// 在售(在租)
    onSaleOrOnRent,

    /// 已售（已出租）
    soldOrRend
}

data class OpenHouse(
    val text: String = ""
)

//agent_available
data class ListStatus(
    val agent_available:Boolean = false,
    val live: Int,
    val `public`: Int,
//    val s_r: String,
    val sold: Int,
    val status: String,
    val text: String
)
