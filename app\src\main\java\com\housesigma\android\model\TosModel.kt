package com.housesigma.android.model
import com.google.gson.annotations.SerializedName


data class TosModel(
    @SerializedName("data_source")
    val dataSource: List<DataSource?>? = null,
    @SerializedName("hs")
    val hs: Hs? = null
)

data class DataSource(
    @SerializedName("body")
    val body: String? = null,
    @SerializedName("header")
    val header: String? = null,
    @SerializedName("id")
    val id: String? = null
)

data class Hs(
    @SerializedName("body")
    val body: String? = null,
    @SerializedName("header")
    val header: String? = null
)