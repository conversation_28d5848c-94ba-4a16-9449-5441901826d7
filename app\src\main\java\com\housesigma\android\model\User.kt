package com.housesigma.android.model

data class User(
    val create_date: String? = null,
    val email: String? = null,
    var lang: String? = null,
    val login_name: String ? = null,
    val name: String ? = null,
    var phonenumber: String ? = null,
    val premium_active: Int ? = null,
    val premium_expire_date: String ? = null,
    var province: String? = null,
    val user_id: Int ? = null,
    val agent:Agent? = null,
    val referral_code:String? = null,
    val vow_agent:VowAgent ?= null,
    val display_live_chat:Int? = 0
)


data class VowTosTermText(
    val header: String ?= "",
    val body: String ?= "",
    val buttons: VowTosButtons ?= null,
)
data class VowTosButtons(
    val bottom_left: VowTosButtonModel ?= null,
    val bottom_right: VowTosButtonModel ?= null,
    val center_bottom: VowTosButtonModel ?= null,
    val center_up: VowTosButtonModel ?= null
)
data class VowTosButtonModel(
    val text: String ?= "",
    val action: String ?= "",
    val sub_title: String ?= "",
    val url: String ?= "",
)

