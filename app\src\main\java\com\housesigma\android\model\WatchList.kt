package com.housesigma.android.model

import com.chad.library.adapter.base.entity.MultiItemEntity

data class WatchList(
    val houselist: List<HouseDetail> = ArrayList(),
    val watchlist_name: String ?= "",
    val is_default: Int = 0,
    var meta: WatchListMeta ?= null,
    var privacy: Int? = 0,//watchlist privacy status, 0 - private 1- public

    // 以下字段在 api/user/watchlist/subscribe_listings 接口中才返回
    var owner_name :String? = null,
    var is_disabled :Int? = null,
    var updated_on :String? = null,
    var is_subscribed :Int? = null,
    var is_owner :Int ?= null,
) {
    fun isPublic(): <PERSON><PERSON>an {
        return privacy==1
    }

    fun isPrivate(): <PERSON><PERSON>an {
        return privacy==0
    }
}
