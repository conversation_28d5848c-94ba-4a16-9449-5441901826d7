package com.housesigma.android.model

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class WatchPolygon(
    val description: String,
    val filter: WatchPolygonFilter,
    val polygon: List<Polygon>,
    var id:Int,
): Parcelable

//{"house_type":["D.","A.","S.","T.","C.","L."],"list_type":[1,3,5],"listing_price":[0,1750000],
//    "description":"test","basement":["separate","finished"],"bathroom_min":1,"front_feet":[50,75],
//    "garage_min":1,"bedroom_range":[2,3,4,5,1],"square_footage":[2100,3200],"open_house_date":7,"max_maintenance_fee":661}
@Parcelize
data class WatchPolygonFilter(
    var basement: List<String>,
    var bathroom_min: Int,
    var bedroom_range: List<Int>,
    var description: String = "",
    var front_feet: List<Int>,
    var garage_min: Int,
    var house_type: List<String>,
    var list_type: List<Int>,
    var listing_price: List<Int>,
    var max_maintenance_fee: String = "",
    var open_house_date: Int,
    var square_footage: List<Int>,
    var lot_size : List<Int>?=null,
    var building_age : List<Int>?=null,
): Parcelable
