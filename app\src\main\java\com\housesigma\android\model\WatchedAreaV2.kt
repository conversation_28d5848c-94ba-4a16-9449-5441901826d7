package com.housesigma.android.model

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

data class WatchedAreaV2(
    val show_add_email: Int? = 0,
    val list: List<WatchedArea>,
)

data class WatchedArea(
    val description: String,
    val id: Int,
    var photo_url: String?,
    var is_load_photo: Boolean= false,//是否请求过图片api
//    val listing: Listing,
    val notification_text: String,
    val polygon: ArrayList<Polygon> = ArrayList(),
)

//data class Listing(
//    val photo_url: Any
//)
@Parcelize
data class Polygon(
    var lat: Double = 0.0,
    var lon: Double = 0.0
): Parcelable {
    override fun toString(): String {
        return "$lon,$lat"
    }
}