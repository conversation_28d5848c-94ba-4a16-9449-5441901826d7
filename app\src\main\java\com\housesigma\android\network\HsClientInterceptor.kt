package com.housesigma.android.network
import com.housesigma.android.BuildConfig
import com.housesigma.android.HSApp
import com.housesigma.android.R
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.DeviceUtil
import com.housesigma.android.utils.MMKVUtils
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response

class HsClientInterceptor : Interceptor {
    private var hasUserId:Boolean = false

    constructor(hasUserId:<PERSON>olean){
        this.hasUserId = hasUserId
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val appName = HSApp.appContext?.resources?.getString(R.string.app_name) ?: ""
        val appVersionName = BuildConfig.VERSION_NAME
        val packageName = BuildConfig.APPLICATION_ID
        val versionCode = BuildConfig.VERSION_CODE
        val oSVersion = DeviceUtil.oS
        val ua = "$appName/$appVersionName ($packageName; build:$versionCode; $oSVersion) okhttp/3.14.9"


        val requestBuilder = chain.request().newBuilder()

        // Create a request builder and add common headers
        requestBuilder.addHeader("User-Agent", ua)
        requestBuilder.addHeader("Hs-Client-Type", "android_native")
        requestBuilder.addHeader("Hs-Client-Version", appVersionName)

        // Add location headers if has
        val lat = MMKVUtils.getDouble("user_location_latitude")
        val lon = MMKVUtils.getDouble("user_location_longitude")
        val timestamp = MMKVUtils.getLong("user_location_timestamp")
        if (lat!=0.0 && lon!=0.0 && timestamp!=0L) {
            requestBuilder.addHeader("Hs-Client-Latitude", lat.toString())
            requestBuilder.addHeader("Hs-Client-Longitude", lon.toString())
            requestBuilder.addHeader("Hs-Client-Location-Timestamp", timestamp.toString())
        }

        if (hasUserId){
            val userId = MMKVUtils.getStr(LoginFragment.USER_ID)
            if (userId != null) {
                requestBuilder.addHeader("Hs-User-Id", userId)
            }
        }

        // Build the final request

        return chain.proceed(requestBuilder.build())
    }

}