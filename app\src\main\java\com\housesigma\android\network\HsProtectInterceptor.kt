package com.housesigma.android.network

import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.housesigma.android.HSApp
import com.housesigma.android.model.EncryptPayloadModel
import com.housesigma.android.model.EncryptRequestModel
import com.housesigma.android.model.EncryptResponseModel
import com.housesigma.android.model.RefreshSecretKey
import com.housesigma.android.utils.HSDecrypt
import com.housesigma.android.utils.HSEncrypt
import com.housesigma.android.utils.log.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import okhttp3.Interceptor
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okhttp3.ResponseBody
import okhttp3.internal.wait
import okio.Buffer
import org.json.JSONObject
import java.io.ByteArrayOutputStream
import java.net.URLEncoder
import java.util.zip.GZIPInputStream
import kotlin.text.Charsets.UTF_8

class HsProtectInterceptor : Interceptor {
    private val encodeList =
        arrayOf(
            "/bkv2/api/listing/info/detail_v2",
            "/bkv2/api/listing/info/popularity",
            "/bkv2/api/listing/preview/many",
            "/bkv2/api/search/mapsearchv3/list",
            "/bkv2/api/stats/trend/trendHouseList",
            "/bkv2/api/search/homepage/recommendlist_v2"
            )

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val path = originalRequest.url.toUrl().path

        // ================================== 以下为测试代码 ==================================
//        val test = Secret()
//        test.isEncrypted = 1
//        test.secretKey = "testtoken"
//        test.expiredAt = 1733025858407
//        HSApp.initApp?.secret = test
        // ================================== 以上为测试代码 ==================================

        val currentTimestampSec = (System.currentTimeMillis() / 1000).toString()


        // 拿到path，判断是否需要加密，在名单内的才加密解密
        if (!encodeList.contains(path)
            || HSApp.secret?.isEncrypted != 1
        ) {
            return chain.proceed(originalRequest)
        }

        synchronized(this) {
            val expireTime = (HSApp.secret?.expiredAt?:0) - (60 * 1) // 提前1分钟刷新
            val currentTime = System.currentTimeMillis() / 1000
            if (currentTime >= expireTime) { //第1判定条件：临近过期时间
                runBlocking {
                    val resSecretKey = refreshSecretKey()
                    HSApp.secret = resSecretKey?.secret
                }
            }
        }

        // 需要加密，但各参数任意有异常的情况下，上报该异常
        if (HSApp.secret?.secretKey == null || (HSApp.secret?.expiredAt?:0L) == 0L
        ) {
            Logger.e("api need encrypt but secretKey or expiredAt is null")
            FirebaseCrashlytics.getInstance().recordException(Exception("api need encrypt but secretKey or expiredAt is null"))
            return chain.proceed(originalRequest)
        }

        // 获取原始的请求体
        val originalBody = originalRequest.body

        // 读取原始请求体的数据
        val buffer = Buffer()
        originalBody?.writeTo(buffer)

        var originalBodyString = buffer.readUtf8()

        //获取mediaType
        val mediaType = originalBody?.contentType()


        var encryptModel: EncryptPayloadModel? = null
        val type = mediaType?.subtype
        if (type == "json") {
            // 拿到json之后，在json内容里面进行加t时间戳字段
            originalBodyString = addFieldToJson(
                originalBodyString,
                "hs_request_timestamp",
                currentTimestampSec
            )
            encryptModel = HSEncrypt.getEncryptModel(originalBodyString, HSApp.secret?.secretKey?:"")
            val encryptPayload = encryptModel?.et_payload
            val encryptCtr = encryptModel?.ctr
            EncryptRequestModel(encryptPayload, encryptCtr).apply {
                originalBodyString = Gson().toJson(this)
            }
        } else if (type == "x-www-form-urlencoded") {
            // 拿到form之后，在form内容里面进行加t时间戳字段
            originalBodyString = originalBodyString+"&hs_request_timestamp=$currentTimestampSec"
            encryptModel = HSEncrypt.getEncryptModel(originalBodyString, HSApp.secret?.secretKey?:"")
            val encryptPayload = encryptModel?.et_payload
            val encryptCtr = encryptModel?.ctr

            // url编码
            val urlEncryptCtr = URLEncoder.encode(encryptCtr, UTF_8.name())
            val urlEncryptPayload = URLEncoder.encode(encryptPayload, UTF_8.name())

            originalBodyString = "ctr=$urlEncryptCtr&et_payload=$urlEncryptPayload"
        }


        // 创建新的请求体
        val modifiedReqBody = originalBodyString.toRequestBody(mediaType)

        // 构建新的请求
        val modifiedRequest = originalRequest.newBuilder()
            .addHeader("Hs-Request-Timestamp", currentTimestampSec) // header里也加时间戳
            .method(originalRequest.method, modifiedReqBody)
            .build()

        val response = chain.proceed(modifiedRequest)
        val responseBody = response.body
        val responseString = responseBody?.string() ?: ""
        val modifiedResponseString = modifyResponse(responseString, encryptModel)
        val modifiedBody =
            ResponseBody.create(responseBody?.contentType(), modifiedResponseString)
        return response.newBuilder()
            .body(modifiedBody)
            .build()
    }

    private suspend fun refreshSecretKey(): RefreshSecretKey? {
        return withContext(Dispatchers.IO) {
            try {
                val res = NetClient.protectService.refreshSecretKey()
                return@withContext if (res.status) {
                    res.data
                } else {
                    null
                }
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }


    private fun addFieldToJson(jsonString: String, fieldName: String, fieldValue: Any): String {
        // 解析 JSON 字符串为 JSONObject
        val jsonObject = JSONObject(jsonString)

        // 添加新字段
        jsonObject.put(fieldName, fieldValue)

        // 将 JSONObject 转回字符串
        return jsonObject.toString()
    }

    /**
     * 解压gzip ，缓冲区 8KB
     * @param content 压缩的内容
     * @return 解压后的内容
     */
    private fun ungzip(content: ByteArray): String {
        val bufferSize = 8 * 1024 // 8KB buffer
        GZIPInputStream(content.inputStream()).use { gzipInputStream ->
            ByteArrayOutputStream().use { byteArrayOutputStream ->
                val buffer = ByteArray(bufferSize)
                var bytesRead: Int
                while (gzipInputStream.read(buffer).also { bytesRead = it } != -1) {
                    byteArrayOutputStream.write(buffer, 0, bytesRead)
                }
                return byteArrayOutputStream.toString(UTF_8.name())
            }
        }
    }

    /**
     * 解密并修改原response的内容
     * @param response 原response的内容
     * @param encryptPayloadModel 加密信息的模型
     */
    private fun modifyResponse(
        response: String,
        encryptPayloadModel: EncryptPayloadModel?
    ): String {
        try {
            val encryptResponseModel= Gson().fromJson(response, EncryptResponseModel::class.java)
            val encryptResponse = encryptResponseModel.data
            val secretKey = encryptPayloadModel?.secretKey
            val plainTextCounter = encryptPayloadModel?.plainTextCounter

            if (encryptResponse == null || encryptPayloadModel == null || secretKey == null || plainTextCounter == null) {
                FirebaseCrashlytics.getInstance().log("encryptResponseModel == null")
                return response
            }

            val binaryString = HSDecrypt.getDecodedString(
                encryptResponse,
                plainTextCounter,
                secretKey
            )?: byteArrayOf()
            FirebaseCrashlytics.getInstance().log("decrypt success")
            val unzipResponse = ungzip(binaryString)
            FirebaseCrashlytics.getInstance().log("unzip success")

//            # 加密 data 字段，加密后的 data 重新覆盖到 data 字段上，简单明了
//            {
//              "status": true,
//              "data": "<Encrypted_data_string>", // 需要前端去解密覆盖 data
//              "error": {
//                "code": 0,
//                "message": ""
//              }
//            }
            val eStatus = when (encryptResponseModel.status) {
                true -> "true"
                false -> "false"
                else -> "false"
            }
            val eCode = encryptResponseModel.error?.code?:0
            val eMessage = encryptResponseModel.error?.message?:""

            val returnResponse =
                "{\"status\":$eStatus,\"data\":$unzipResponse,\"error\":{\"code\":$eCode,\"message\":\"$eMessage\"}}"
            return returnResponse
        } catch (e: Exception) {
            // 这里有可能存在前端发起的是加密的payload，但是后端返回的是明文的情况
            FirebaseCrashlytics.getInstance().recordException(e)
            e.printStackTrace()
            return response
        }
    }
}