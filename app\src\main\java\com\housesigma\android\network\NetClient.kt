package com.housesigma.android.network

import android.util.Log
import com.housesigma.android.BuildConfig
import com.ihsanbal.logging.Level
import com.ihsanbal.logging.LoggingInterceptor
import okhttp3.OkHttpClient
import okhttp3.Protocol
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

object NetClient {

    private const val BASE_URL = BuildConfig.API_DOMAIN + "/bkv2/"
    private const val COLLECT_URL = BuildConfig.COLLECT_API_DOMAIN+"/v1/"

    val collectApiService: RequestApiService by lazy {
        val retrofit = Retrofit.Builder()
            .baseUrl(COLLECT_URL)
            .client(collectClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()

        return@lazy retrofit.create(RequestApiService::class.java)
    }

    val apiService: RequestApiService by lazy {
        val retrofit = Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(client)
            .addConverterFactory(GsonConverterFactory.create())
            .build()

        return@lazy retrofit.create(RequestApiService::class.java)
    }

    val protectService: RequestApiService by lazy {
        val retrofit = Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(refreshClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()

        return@lazy retrofit.create(RequestApiService::class.java)
    }


    /**
     * 批量删除请求
     *
     * @param tag 标签
     */
    fun cancelCallWithTag(tag: String) {
        // 等待队列
        for (call in client.dispatcher.queuedCalls()) {
            // 注意，不能用 tag()
            if (call.request().tag(String::class.java) == tag) {
                call.cancel()
            }
        }
        // 请求队列
        for (call in client.dispatcher.runningCalls()) {
            // 注意，不能用 tag()
            if (call.request().tag(String::class.java) == tag) {
                call.cancel()
            }
        }
    }

    val collectClient by lazy {
        OkHttpClient.Builder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .addInterceptor(
                TokenInterceptor()
            )
            .addInterceptor(HsClientInterceptor(true))
            .addInterceptor (PublicParamInterceptor())
            .addInterceptor(
                LoggingInterceptor.Builder()
                    .setLevel(Level.NONE)
                    .log(Log.VERBOSE)
                    .build()
            )
            .protocols(listOf(Protocol.HTTP_1_1, Protocol.HTTP_2))
            .build()
    }

    /**
     * 全局 Retrofit 对象
     */
    val client by lazy {
        OkHttpClient.Builder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .addInterceptor(
                TokenInterceptor()
            )
            .addInterceptor(HsClientInterceptor(false))
            .addInterceptor (PublicParamInterceptor())
            .addInterceptor(HsProtectInterceptor())
            .addInterceptor(
                LoggingInterceptor.Builder()
                    .setLevel(Level.BASIC)
                    .log(Log.VERBOSE)
                    .build()
            )
            .protocols(listOf(Protocol.HTTP_1_1, Protocol.HTTP_2))
            .build()
    }

    /**
     * 全局 Retrofit 对象
     */
    val refreshClient by lazy {
        OkHttpClient.Builder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .addInterceptor(
                TokenInterceptor()
            )
            .addInterceptor(HsClientInterceptor(false))
            .addInterceptor (PublicParamInterceptor())
            .addInterceptor(
                LoggingInterceptor.Builder()
                    .setLevel(Level.BASIC)
                    .log(Log.VERBOSE)
                    .build()
            )
            .protocols(listOf(Protocol.HTTP_1_1, Protocol.HTTP_2))
            .build()
    }





}