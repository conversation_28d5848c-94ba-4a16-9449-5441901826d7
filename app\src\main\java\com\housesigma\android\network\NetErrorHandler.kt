package com.housesigma.android.network

import com.housesigma.android.HSApp
import com.housesigma.android.R
import com.housesigma.android.utils.ToastUtils
import okio.IOException
import retrofit2.HttpException
import java.io.EOFException
import java.net.ConnectException
import java.net.SocketException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.net.ssl.SSLException

object NetErrorHandler {

    fun errorHandle(
        error: java.lang.Exception,
    ) {
        when (error) {
            is HttpException -> {
                ToastUtils.showLong(HSApp.appContext?.getString(R.string.network_error_http_exception) + ":${error.code()};${error.message()}")
            }
            is UnknownHostException -> {
                ToastUtils.showLong(HSApp.appContext?.getString(R.string.network_error_unknown_host_exception))
            }
            is SocketTimeoutException -> {
                ToastUtils.showLong(HSApp.appContext?.getString(R.string.network_error_socket_timeout_exception))
            }
            is ConnectException -> {
                ToastUtils.showLong(HSApp.appContext?.getString(R.string.network_error_connect_exception))
            }
            is SocketException -> {
//                ToastUtils.showLong("服务器异常连接关闭")
            }
            is EOFException -> {
//                ToastUtils.showLong("服务器异常连接关闭")
            }
            is IllegalArgumentException -> {
                ToastUtils.showLong(HSApp.appContext?.getString(R.string.network_error_illegal_argument_exception))
            }
            is SSLException -> {
//                ToastUtils.showLong("证书错误")
            }
            is NullPointerException -> {
//                ToastUtils.showLong("数据为空")
            }
            is ServiceErrorException -> {
                ToastUtils.showLong("${error.message}")
            }
            is IOException -> {//取消api会发生这个异常
            }
            else -> {
                ToastUtils.showLong(HSApp.appContext?.getString(R.string.network_error_unknown_exception) + "${error.javaClass.toString()}${error.toString()}")
            }
        }
    }
}