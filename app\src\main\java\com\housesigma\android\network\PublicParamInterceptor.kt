package com.housesigma.android.network

import android.text.TextUtils
import com.housesigma.android.helper.ProvinceHelper
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.LanguageUtils
import com.housesigma.android.utils.MMKVUtils
import okhttp3.FormBody
import okhttp3.Interceptor
import okhttp3.Response

/**
 * 公共参数拦截器
 */
class PublicParamInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val paramMap: MutableMap<String, String> = HashMap()
        val lang: String = LanguageUtils().getLANG()
        val province: String = ProvinceHelper.getAbbreviationFromCache("ON")
        if (!TextUtils.isEmpty(lang)) {
            //  默认语言en_US
//            paramMap["lang"] = lang!!
            paramMap["lang"] = lang
        } else {
            paramMap["lang"] = "en_US"

        }
        paramMap["province"] = province


        // 发现一个bug，结构体内无内容的时候 添加公共字段会失败；
        // 解决方案是：参数传空的以EmptyRequestBody传递

        //拿到原来的request
        val oldRequest = chain.request()
        if (oldRequest.body !is FormBody) {
            //目前app里有两种类型的content type，一种是form，另一种是json/application
//          TODO 后面需要把结构比较复杂的request 参数转成模型处理
            return chain.proceed(oldRequest)
        }

        //拿到请求的url
        val url = oldRequest.url.toString()
        //判断是GET还是POST请求
        if (oldRequest.method.equals("GET", ignoreCase = true)) {
            if (paramMap != null && paramMap.size > 0) {
//                APP里目前没GET请求，先注释掉
//                StringBuilder urlBuilder = new StringBuilder(url);
//                //拼接公共请求参数
//                for (Map.Entry<String, String> entry : paramMap.entrySet()) {
//                    urlBuilder.append("&" + entry.getKey() + "=" + entry.getValue());
//                }
//                url = urlBuilder.toString();
//                //如果之前的url没有？号，我们需要手动给他添加一个？号
//                if (!url.contains("?")) {
//                    url = url.replaceFirst("&", "?");
//                }
                //依据原来的request构造一个新的request,
                val request = oldRequest.newBuilder()
                    .url(url)
                    .build()
                return chain.proceed(request)
            }
        } else {
            if (paramMap != null && paramMap.size > 0) {
                val body = oldRequest.body
                if (body != null && body is FormBody) {
                    val formBody = body
                    //1.把原来的的body里面的参数添加到新的body中
                    val builder = FormBody.Builder()
                    //为了防止重复添加相同的key和value
                    val temMap: MutableMap<String, String> = HashMap()
                    for (i in 0 until formBody.size) {
//                         @FormUrlEncoded 注解已经编码过一次了，还需要做的是 判断这个注解是否存在，如果不存在就需要再编码一次
                        builder.add(formBody.name(i), formBody.value(i))
                        temMap[formBody.name(i)] = formBody.value(i)
                    }
                    //2.把公共请求参数添加到新的body中
                    for ((key, value) in paramMap) {
                        if (!temMap.containsKey(key)) {
                            builder.add(key, value)
                        }
                    }
                    val newFormBody = builder.build()
                    //依据原来的request构造一个新的request,
                    val newRequest = oldRequest.newBuilder()
                        .post(newFormBody)
                        .build()
                    return chain.proceed(newRequest)
                } else {
                    //1.把原来的的body里面的参数添加到新的body中
                    val builder = FormBody.Builder()
                    //为了防止重复添加相同的key和value
                    val temMap: MutableMap<String, String> = HashMap()
                    //2.把公共请求参数添加到新的body中
                    for ((key, value) in paramMap) {
                        if (!temMap.containsKey(key)) {
                            builder.add(key, value)
                        }
                    }
                    val newFormBody = builder.build()
                    //依据原来的request构造一个新的request,
                    val newRequest = oldRequest.newBuilder()
                        .post(newFormBody)
                        .build()
                    return chain.proceed(newRequest)
                }
            }
        }
        return chain.proceed(oldRequest)
    }
}