package com.housesigma.android.network

import com.housesigma.android.model.*
import retrofit2.Call
import retrofit2.Response
import retrofit2.http.*


interface RequestApiService {


    @POST("api/user/watch/removeCommunity")
    @FormUrlEncoded
    suspend fun removeCommunityWatch(
        @Field("house_type") house_type: String,
        @Field("id") id: Int
    ): NetResponse<MsgRes>


    @POST("api/user/watch/communityList")
    suspend fun getWatchCommunityList(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<WatchCommunity>

    @POST("api/user/watchlist/remove_listing")
    @FormUrlEncoded
    suspend fun removeWatched(
        @Field("id_user_watchlist") id_user_watchlist: String,
        @Field("id_listing") id_listing: String,
    ): NetResponse<MsgRes>

    @POST("api/user/watch/addNote")
    @FormUrlEncoded
    suspend fun addWatchNote(
        @Field("id_listing") id_listing: String,
        @Field("note") note: String = ""
    ): NetResponse<MsgRes>

    @POST("api/user/watch_polygon/save")
    suspend fun saveWatchPolygon(
        @Body watchedAreaSave: WatchedAreaSave
    ): NetResponse<SaveWatchPolygon>


    @POST("api/user/watch_polygon/update")
    suspend fun updateWatchPolygon(
        @Body watchedAreaSave: WatchedAreaSave
    ): NetResponse<MsgRes>

    @POST("api/stats/trend/trendhouselist")
    @FormUrlEncoded
    suspend fun getTrendHouseList(
        @Field("community") community: String,
        @Field("house_type") house_type: String,
        @Field("municipality") municipality: String,
        @Field("page") page: Int = 1,
        @Field("type") type: String = "1",
    ): NetResponse<TrendHouseList>

    @POST("api/user/watch_polygon/area")
    @FormUrlEncoded
    suspend fun getWatchPolygonArea(
        @Field("id") id: String,
        @Field("page") page: Int = 1
    ): NetResponse<List<HouseDetail>>


    @POST("api/user/watch_polygon/get")
    @FormUrlEncoded
    suspend fun getWatchPolygon(
        @Field("id") id: String
    ): NetResponse<WatchPolygon>


    @POST("api/user/watch_polygon/photo")
    @FormUrlEncoded
    fun getWatchPolygonPhoto(@Field("id") id: String): Call<NetResponse<PolygonPhoto>>

    @POST("api/community/list/init")
    suspend fun initCommunityFilter(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<CommunityFilter>


    @POST("api/init/app/pushtoken")
    suspend fun pushToken(
        @Body pushTokenRequest: PushTokenRequest
    ): NetResponse<MsgRes>


    @POST("api/user/watch/addCommunity")
    @FormUrlEncoded
    suspend fun addWatchCommunity(
        @Field("house_type") house_type: String = "",
        @Field("id") id: Int
    ): NetResponse<MsgRes>


    @POST("api/user/watch/updateCommunity")
    @FormUrlEncoded
    suspend fun updateWatchCommunity(
        @Field("house_type") house_type: String = "",
        @Field("id") id: Int,
        @Field("watch_type[]") watch_type: List<String>,
    ): NetResponse<MsgRes>

    @POST("api/user/recent/viewed")
    suspend fun recentViewed(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<List<HouseDetail>>

    @FormUrlEncoded
    @POST("api/user/watch_polygon/list_v2")
    suspend fun watchPolygonList(
        @Field("page") page: Int = 1,
    ): NetResponse<WatchedAreaV2>


    @FormUrlEncoded
    @POST("api/user/watch/deletePolygon")
    suspend fun deletePolygon(
        @Field("id") id: Int = 1,
    ): NetResponse<MsgRes>

    @POST("api/user/watchlist/subscribe")
    @FormUrlEncoded
    suspend fun subscribeWatchlist(
        @Field("id_user_watchlist") id_user_watchlist: String,
    ): NetResponse<MsgRes>

    @POST("api/user/watchlist/unsubscribe")
    @FormUrlEncoded
    suspend fun unsubscribeWatchlist(
        @Field("id_user_watchlist") id_user_watchlist: String,
    ): NetResponse<MsgRes>

    @POST("api/user/watchlist/delete")
    @FormUrlEncoded
    suspend fun delWatchlist(
        @Field("id_user_watchlist") id_user_watchlist: String,
    ): NetResponse<MsgRes>

    @POST("api/user/watchlist/add")
    @FormUrlEncoded
    suspend fun addWatchlist(
        @Field("name") name: String,
        @Field("privacy") privacy: Int,
    ): NetResponse<MsgRes>

    @POST("api/user/watchlist/list")
    @FormUrlEncoded
    suspend fun getMultipleWatchList(
        @Field("id_listing") id_listing: String
    ): NetResponse<MultipleWatchList>

    @POST("api/user/watchlist/subscribe_list")
    suspend fun getWatchListSubscribeList(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<MultipleWatchList>

    @POST("api/listing/info/precon_photos")
    @FormUrlEncoded
    suspend fun getPreconPhotos(
        @Field("id_project") id_project: String
    ): NetResponse<HousePhotosInfo>


    @POST("api/user/watchlist/photo")
    @FormUrlEncoded
    fun getWatchListPhoto(@Field("id_user_watchlist") id: String): Call<NetResponse<PolygonPhoto>>

    @FormUrlEncoded
    @POST("api/user/note/list")
    suspend fun getNoteList(
        @Field("page") page: Int = 1,
        @Field("list_type[]") list_type: List<String>,
    ): NetResponse<NoteWatchList>

    @FormUrlEncoded
    @POST("api/user/watchlist/subscribe_listings")
    suspend fun watchlistSubscribeListings(
        @Field("page") page: Int = 1,
        @Field("id_user_watchlist") id_user_watchlist: String,
    ): NetResponse<WatchList>

    @FormUrlEncoded
    @POST("api/user/watchlist/listings")
    suspend fun watchListListings(
        @Field("page") page: Int = 1,
        @Field("list_type[]") list_type: List<String>,
        @Field("id_user_watchlist") id_user_watchlist: String,
    ): NetResponse<WatchList>

    @POST("api/auth/user/settos")
    @FormUrlEncoded
    suspend fun setTos(
        @Field("sources[]") sources: List<String>,
    ): NetResponse<MsgRes>


    @POST("api/auth/user/gettos")
    suspend fun getTos(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<TosModel>

    @POST("api/auth/user/agentboard")
    suspend fun agentboard(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<AgentBoards>

    @POST("api/auth/user/refresh_signin")
    @FormUrlEncoded
    suspend fun refreshSignIn(
        @Field("login_method") login_method: String = "biometric",
        @Field("login_type") login_type: String = "trreb_timeout",
    ): NetResponse<MsgRes>

    @POST("api/auth/user/signin")
    @FormUrlEncoded
    suspend fun signIn(
        @Field("email") email: String = "",
        @Field("pass") pass: String = "",
        @Field("phonenumber") phonenumber: String = "",
        @Field("countrycode") countrycode: String = "",
        @Field("login_type") login_type: String = "normal",
    ): NetResponse<SignIn>

    @FormUrlEncoded
    @POST("api/init/accesstoken/check")
    suspend fun checkAccessToken(
        @Field("access_token") token: String
    ): NetResponse<CheckToken>

    @POST("api/init/accesstoken/new")
    suspend fun getAccessToken(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<Token>

    @FormUrlEncoded
    @POST("api/listing/info/photos")
    suspend fun getListingInfoPhotos(
        @Field("id_listing") id_listing: String
    ): NetResponse<HousePhotosInfo>

    @POST("api/init/app")
    suspend fun initApp(
        @Body softInfo: SoftInfo
    ): NetResponse<Any>

    @POST("api/init/app/abtest_config")
    suspend fun uploadABTestConfig(
        @Body abTest: ABTest
    ): NetResponse<MsgRes>

    @POST("api/user/watchlist/listingids")
    suspend fun watchlistListingIds (
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<WatchlistListingIds>

    @POST("api/init/accesstoken/refresh")
    suspend fun refreshSecretKey (
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<RefreshSecretKey>

    @POST("api/stats/homepage")
    suspend fun getHomePage(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<HomePage>

    @FormUrlEncoded
    @POST("api/search/homepage/recommendlist_v2")
    suspend fun getRecommendList(
        @Field("type") type: String?,
        @Field("page") page: Int?
    ): NetResponse<Recommend>

    @POST("api/search/homepage/customizedFilter")
    suspend fun getCustomizedFilter(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<CustomizedFilter>

    @FormUrlEncoded
    @POST("api/search/homepage/customize")
    suspend fun searchHomePageCustomize(
        @Field("municipality_id[]") municipality_id: List<Int>,
        @Field("house_type[]") house_type: List<String>,
        @Field("price_max") price_max: Int = 6000000,
        @Field("price_min") price_min: Int = 0
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/search/address_v2/suggest")
    suspend fun getSearchAddress(
        @Field("search_term") search_term: String?,
        @Tag tag: String = "suggestv3"
    ): NetResponse<SearchAddress>

    @FormUrlEncoded
    @POST("api/user/profile/update")
    suspend fun updateProfileShowOnBoard(
        @Field("show_onboard") show_onboard: Int?
    ): NetResponse<MsgRes>


    @POST("api/user/profile/update")
    suspend fun updateProfileCoordinate(
        @Body coordinate: DataCoordinate
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/user/profile/update")
    suspend fun updateLang(
        @Field("lang") lang: String
    ): NetResponse<MsgRes>


    @FormUrlEncoded
    @POST("api/listing/info/tos_v2")
    suspend fun getVowTosInfo(
        @Field("id_listing") id_listing: String
    ): NetResponse<VowTosTermText>

    @FormUrlEncoded
    @POST("api/auth/user/tos")
    suspend fun authTos(
        @Field("source") source: String?
    ): NetResponse<MsgRes>

    @POST("api/auth/user/googlesignup")
    suspend fun googleSignUp(
        @Body request: RequestGoogleSignup
    ): NetResponse<SignIn>

    @FormUrlEncoded
    @POST("api/auth/user/googlesignin")
    suspend fun googleSignIn(
        @Field("client_type") client_type: String = "android",
        @Field("credential") credential: String = "",
        @Field("login_type") login_type: String = "normal"
    ): NetResponse<GoogleSignRes>

    @POST("api/auth/user/initcountrycode")
    suspend fun getInitCountryCode(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<CountryCode>

    @POST("api/user/profile/sendtemppassword")
    suspend fun sendTempPassword(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/auth/user/sendcode")
    suspend fun sendCode(
        @Field("email") email: String = "",
        @Field("pass") pass: String = "",
        @Field("phonenumber") phonenumber: String = "",
        @Field("countrycode") countrycode: String = "",
        @Field("name") name: String = ""
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/auth/user/resetcode")
    suspend fun resetCode(
        @Field("email") email: String = "",
        @Field("phonenumber") phonenumber: String = "",
        @Field("countrycode") countrycode: String = ""
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/auth/user/resetpassword")
    suspend fun resetPassword(
        @Field("phonenumber") phonenumber: String = "",
        @Field("countrycode") countrycode: String = "",
        @Field("email") email: String = "",
        @Field("code") code: String = "",
        @Field("pass") pass: String = "",
    ): NetResponse<MsgRes>


    @FormUrlEncoded
    @POST("api/auth/user/signup")
    suspend fun signup(
        @Field("email") email: String = "",
        @Field("code") code: String = "",
        @Field("pass") pass: String = "",
        @Field("phonenumber") phonenumber: String = "",
        @Field("countrycode") countrycode: String = "",
        @Field("name") name: String = "",
        @Field("is_agent") is_agent: String = "",
        @Field("referral_code") referral_code: String = "",
        @Field("licensed_province") licensed_province: String = "",
        @Field("board_name") board_name: String = "",
        @Field("brokerage_name") brokerage_name: String = "",
        @Field("agent_cracking[is_agent]") cracking_is_agent: String ?= null,
        @Field("agent_cracking[licensed_province]") cracking_licensed_province: String?= null,
        @Field("agent_cracking[board_name]") cracking_board_name: String ?= null,
        @Field("agent_cracking[brokerage_name]") cracking_brokerage_name: String ?= null,
    ): NetResponse<SignIn>


    @POST("api/user/profile/stopprompt")
    suspend fun stopPrompt(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<MsgRes>


    @POST("api/auth/user/signout")
    suspend fun signOut(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<MsgRes>

    @POST("api/user/review/check")
    suspend fun reviewCheck(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<ReviewCheck>

    @FormUrlEncoded
    @POST("api/user/review/stop")
    suspend fun stopReview(
        @Field("review_rating") review_rating: String,
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/auth/user/setprovince")
    suspend fun setProvince(
        @Field("province") province: String = "ON",
    ): NetResponse<MsgRes>

    @POST("api/listing/info/disclaimer_v2")
    suspend fun getDisclaimer(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<DisclaimerInfo>


    @POST("api/agent/info/agentlist")
    suspend fun getAgentInfoHomePage(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<AgentInfoHomePage>

    @FormUrlEncoded
    @POST("api/init/app/url_transform")
    suspend fun urlTransform(
        @Field("url") url: String
    ): NetResponse<NativeUrl>

    @POST("api/user/watch/getNotificationType")
    suspend fun getNotificationType(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<NotificationType>

    @FormUrlEncoded
    @POST("api/user/watch/saveNotificationType")
    suspend fun saveNotificationType(
        @Field("type[]") type: List<Int>,
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/user/watch/saveNotificationType")
    suspend fun saveNotificationTypeNew(
        @Field("email_recommend") email_recommend: Int,
        @Field("email_watched_listing") email_watched_listing: Int,
        @Field("email_watched_community") email_watched_community: Int,
        @Field("email_watched_area") email_watched_area: Int,
        @Field("push_watched_listing") push_watched_listing: Int,
    ): NetResponse<MsgRes>


    @FormUrlEncoded
    @POST("api/user/profile/update")
    suspend fun updateProfile(
        @Field("name") name: String = "",
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/user/profile/update")
    suspend fun updateReferralCode(
        @Field("referral_code") referral_code: String = "",
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/user/profile/changepassword")
    suspend fun changePassword(
        @Field("password") password: String,
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/user/contact/feedback")
    suspend fun feedback(
        @Field("message") message: String,
        @Field("contact") contact: String = ""
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/auth/user/changecontactsendcode")
    suspend fun changecontactsendcode(
        @Field("countrycode") countrycode: String,
        @Field("phonenumber") phonenumber: String,
        @Field("email") email: String
    ): NetResponse<MsgRes>


    @FormUrlEncoded
    @POST("api/user/profile/updatephonenumber")
    suspend fun updatephonenumber(
        @Field("code") code: String,
        @Field("countrycode") countrycode: String,
        @Field("phonenumber") phonenumber: String
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/user/profile/updateemail")
    suspend fun updateemail(
        @Field("code") code: String,
        @Field("email") email: String
    ): NetResponse<MsgRes>


    @FormUrlEncoded
    @POST("api/user/profile/changesigninuser")
    suspend fun changesigninuser(
        @Field("code") code: String,
        @Field("countrycode") countrycode: String,
        @Field("phonenumber") phonenumber: String,
        @Field("email") email: String
    ): NetResponse<MsgRes>


    @FormUrlEncoded
    @POST("api/user/profile/delete")
    suspend fun profileDelete(
        @Field("pass") pass: String
    ): NetResponse<MsgRes>


    @POST("api/user/profile/removemail")
    suspend fun removeEmail(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<MsgRes>

    @POST("api/user/profile/removephonenumber")
    suspend fun removePhone(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/listing/preview/many")
    suspend fun getListingPreviewMany(
        @Field("id_listing[]") id_listing: List<String>
    ): NetResponse<ListingPreViewMany>

    @FormUrlEncoded
    @POST("api/user/mapfilter/delete")
    suspend fun deleteMapFilterById(
        @Field("id") id: String
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/user/mapfilter/list")
    suspend fun getMapFilterList(
        @Field("category") category: String
    ): NetResponse<ArrayList<SaveMapFilter>>


    @FormUrlEncoded
    @POST("api/listing/preview/precon_many")
    suspend fun getListingPreviewPreconMany(
        @Field("id_project[]") id_project: List<String>
    ): NetResponse<List<Precon>>



    @FormUrlEncoded
    @POST("api/search/recommendv2/community")
    suspend fun getRecommendv2Community(
        @Field("page") page: Int = 1,
        @Field("price_max") price_max: String = "6000000",
        @Field("price_min") price_min: String = "0",
        @Field("id_community") id_community: String,
        @Field("community_house_type") community_house_type: String,
        @Field("house_type[]") house_type: List<String>,
        @Field("municipality_id[]") municipality_id: List<String>,
        @Field("investment[]") investment: List<String>,
    ): NetResponse<Recommendv2Community>

    @POST("api/search/recommendv2/filter")
    suspend fun getRecommendv2Filter(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<RecommedCommunityFilter>

    @FormUrlEncoded
    @POST("api/search/recommendv2/start")
    suspend fun getRecommendv2Start(
        @Field("page") page: Int = 1,
        @Field("price_max") price_max: String = "6000000",
        @Field("price_min") price_min: String = "0",
        @Field("municipality_id[]") municipality_id: List<String>,
        @Field("investment[]") investment: List<String>,
        @Field("house_type[]") house_type: List<String>,
    ): NetResponse<RecommendStart>

    @FormUrlEncoded
    @POST("api/search/mapsearch/school")
    suspend fun getSearchMapSearchSchool(
        @Field("catholic") catholic: Int,
        @Field("elementary") elementary: Int,
        @Field("lat1") lat1: Double,
        @Field("lat2") lat2: Double,
        @Field("lon1") lon1: Double,
        @Field("lon2") lon2: Double,
        @Field("match_score") match_score: Int,
        @Field("public") public: Int,
        @Field("secondary") secondary: Int,
        @Tag tag: String = "api/search/mapsearch/school"
    ): NetResponse<MapSearchSchool>


    @FormUrlEncoded
    @POST("api/community/detail")
    suspend fun getCommunityDetail(
        @Field("id_community") idCommunity: String
    ): NetResponse<CommunityDetail>

    @FormUrlEncoded
    @POST("api/search/mapsearch/schoolDetails")
    suspend fun getSearchSchoolDetails(
        @Field("id") id: Int,
    ): NetResponse<SchoolDetails>

    @FormUrlEncoded
    @POST("api/search/mapsearchv3/nearbysold")
    suspend fun getSearchV2NearBySold(
        @Field("basement[]") basement: List<String>,
        @Field("bathroom_min") bathroom_min: String,
        @Field("bedroom_range[]") bedroom_range: List<String>,
        @Field("de_list_days") de_list_days: String,
        @Field("description") description: String,
        @Field("max_maintenance_fee") max_maintenance_fee: String,
        @Field("garage_min") garage_min: String,
        @Field("house_type[]") house_type: List<String>,
        @Field("listing_days") listing_days: String,
        @Field("list_type[]") list_type: List<String>,
        @Field("open_house_date") open_house_date: String,
        @Field("price[]") price: List<String>,
        @Field("front_feet[]") front_feet: List<String>,
        @Field("square_footage[]") square_footage: List<String>,
//    open_house_date: 0
//    sold_days: 1
        @Field("lat1") lat1: Double,
        @Field("lat2") lat2: Double,
        @Field("lon1") lon1: Double,
        @Field("lon2") lon2: Double,
        @Field("zoom") zoom: Double,
        @Field("rental_yield_min") rental_yield_min: String? = null,
        @Field("rental_yield_max") rental_yield_max: String? = null,
        @Field("school_score_min") school_score_min: String? = null,
        @Field("school_score_max") school_score_max: String? = null,
        @Tag tag: String = "api/search/mapsearchv3/nearbysold"
    ): NetResponse<MapList>

    @FormUrlEncoded
    @POST("api/stats/trend/citysummary")
    suspend fun getCitySummary(
        @Field("id_municipality") id_municipality: String
    ): NetResponse<CitySummary>


    @FormUrlEncoded
    @POST("api/agent/listing")
    suspend fun getAgentListing(
        @Field("category[]") category: List<String>,
        @Field("id_agent") id_agent: String,
        @Field("slug") slug: String,
    ): NetResponse<MapList>

    @FormUrlEncoded
    @POST("api/search/preconmap/projects")
    suspend fun searchPreconMapProjects(
        @Field("bedroom[]") bedroom: List<String>,
        @Field("bathroom") bathroom: String,
        @Field("project_status[]") project_status: List<String>,
        @Field("est_completion_year[]") est_completion_year: List<String>,
        @Field("property_type[]") property_type: List<String>,
        @Field("construction_status[]") construction_status: List<String>,
        @Field("description") description: String,
        @Field("price_min") price_min: String,
        @Field("price_max") price_max: String,
        @Field("square_footage_max") square_footage_max: String,
        @Field("square_footage_min") square_footage_min: String,
        @Field("lat1") lat1: Double,
        @Field("lat2") lat2: Double,
        @Field("lon1") lon1: Double,
        @Field("lon2") lon2: Double,
        @Field("zoom") zoom: Double,
        @Tag tag: String = "api/search/preconmap/projects"
    ): NetResponse<List<PreconMarker>>

    @FormUrlEncoded
    @POST("api/search/mapsearchv3/listing")
    suspend fun getMapListing22(
        @Field("basement[]") basement: List<String>,
        @Field("bathroom_min") bathroom_min: String,
        @Field("bedroom_range[]") bedroom_range: List<String>,
        @Field("de_list_days") de_list_days: String,
        @Field("sold_days") sold_days: String,
        @Field("description") description: String,
        @Field("max_maintenance_fee") max_maintenance_fee: String,
        @Field("garage_min") garage_min: String,
        @Field("house_type[]") house_type: List<String>,
        @Field("listing_days") listing_days: String,
        @Field("list_type[]") list_type: List<String>,
        @Field("open_house_date") open_house_date: String,
        @Field("price[]") price: List<String>,
        @Field("front_feet[]") front_feet: List<String>,
        @Field("square_footage[]") square_footage: List<String>,
//    open_house_date: 0
        @Field("lat1") lat1: Double,
        @Field("lat2") lat2: Double,
        @Field("lon1") lon1: Double,
        @Field("lon2") lon2: Double,
        @Field("zoom") zoom: Double,
        @Field("listing_type[]") listing_type: List<String>,
        @Field("lot_size[]") lot_size : List<String> ?= null,
        @Field("building_age[]") building_age: List<String> ?= null,
        @Field("rental_yield_min") rental_yield_min: String? = null,
        @Field("rental_yield_max") rental_yield_max: String? = null,
        @Field("school_score_min") school_score_min: String? = null,
        @Field("school_score_max") school_score_max: String? = null,
        @Tag tag: String = "api/search/mapsearchv3/listing"
    ): NetResponse<MapList>

    @POST("api/user/mapfilter/save")
    suspend fun saveMapFilter(
        @Body saveMapFilter: SaveMapFilter
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/search/mapsearchv3/list")
    suspend fun getMapsearchv3List(
        @Field("basement[]") basement: List<String>,
        @Field("bathroom_min") bathroom_min: String,
        @Field("bedroom_range[]") bedroom_range: List<String>,
        @Field("de_list_days") de_list_days: String,
        @Field("sold_days") sold_days: String,
        @Field("description") description: String,
        @Field("max_maintenance_fee") max_maintenance_fee: String,
        @Field("garage_min") garage_min: String,
        @Field("house_type[]") house_type: List<String>,
        @Field("listing_days") listing_days: String,
        @Field("list_type[]") list_type: List<String>,
        @Field("open_house_date") open_house_date: String,
        @Field("price[]") price: List<String>,
        @Field("front_feet[]") front_feet: List<String>,
        @Field("square_footage[]") square_footage: List<String>,
        @Field("lat1") lat1: Double,
        @Field("lat2") lat2: Double,
        @Field("lon1") lon1: Double,
        @Field("lon2") lon2: Double,
        @Field("zoom") zoom: Double,
        @Field("page") page: Int,
        @Field("sort_type") sort_type: String,
        @Field("listing_type[]") listing_type: List<String> ?= null,
        @Field("lot_size[]") lot_size : List<String> ?= null,
        @Field("building_age[]") building_age: List<String> ?= null,
        @Field("rental_yield_min") rental_yield_min: String? = null,
        @Field("rental_yield_max") rental_yield_max: String? = null,
        @Field("school_score_min") school_score_min: String? = null,
        @Field("school_score_max") school_score_max: String? = null,
    ): NetResponse<MapSearchV3List>


    @FormUrlEncoded
    @POST("api/search/mapsearchv3/feature")
    suspend fun getMapSearchv2Feature(
        @Field("basement[]") basement: List<String>,
        @Field("bathroom_min") bathroom_min: String,
        @Field("bedroom_range[]") bedroom_range: List<String>,
//        @Field("de_list_days") de_list_days: Int ,
        @Field("description") description: String,
        @Field("max_maintenance_fee") max_maintenance_fee: String,
        @Field("garage_min") garage_min: String,
        @Field("list_type[]") list_type: List<String>,
        @Field("open_house_date") open_house_date: String,
        @Field("price[]") price: List<String>,
        @Field("front_feet[]") front_feet: List<String>,
        @Field("square_footage[]") square_footage: List<String>,
        @Field("zoom") zoom: Double,
        @Field("rental_yield_min") rental_yield_min: String? = null,
        @Field("rental_yield_max") rental_yield_max: String? = null,
        @Field("school_score_min") school_score_min: String? = null,
        @Field("school_score_max") school_score_max: String? = null,
        @Tag tag: String = "api/search/mapsearchv3/feature"
    ): NetResponse<List<MapMarkerInfo>>

    @POST("api/search/mapsearchv2/mapfilter")
    suspend fun getMapFilter(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<MapFilter>


    @POST("api/search/preconmap/filter")
    suspend fun getPreconMapFilter(
        @Body empty: EmptyRequestBody? = EmptyRequestBody()
    ): NetResponse<PreconMapFilter>



    @FormUrlEncoded
    @POST("api/user/watchlist/update_listings")
    suspend fun updateWatchlistUpdateListings(
        @Field("id_listing") id_listing: String,
        @Field("ml_num") ml_num: String,
        @Field("ids_user_watchlist[]") ids_user_watchlist:  List<String>,
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/user/watchlist/update")
    suspend fun updateWatchlistUpdate(
        @Field("id_user_watchlist") id_user_watchlist: String,
        @Field("name") name:  String,
        @Field("privacy") privacy:  Int,
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/user/watch/add")
    suspend fun addWatch(
        @Field("id_listing") id_listing: String,
        @Field("ml_num") ml_num: String
    ): NetResponse<MsgRes>

    @FormUrlEncoded
    @POST("api/user/watch/remove")
    suspend fun removeWatch(
        @Field("id_listing") id_listing: String,
        @Field("ml_num") ml_num: String
    ): NetResponse<MsgRes>


    @FormUrlEncoded
    @POST("api/listing/info/detail")
    suspend fun listingInfoDetail(
        @Field("id_listing") id_listing: String,
    ): NetResponse<ListingInfoDetail>

    @FormUrlEncoded
    @POST("api/listing/info/not_interested")
    suspend fun flagListingNotInterested(
        @Field("id_listing") id_listing: String,
        @Field("not_interested") not_interested: Int, //1 代表不感兴趣 0 代表取消不感兴趣
    ): NetResponse<ListingInfoDetail>


    @FormUrlEncoded
    @POST("api/user/contact")
    suspend fun userContact(
        @Field("email") email: String? = "",
        @Field("id_listing") id_listing: String? = "",
        @Field("id_project") id_project: String? = "",
        @Field("message") message: String? = "",
        @Field("ml_num") ml_num: String? = "",
        @Field("name") name: String? = "",
        @Field("phone") phone: String? = "",
        @Field("tags[]") tags: List<String>? = arrayListOf("mobile"),
        @Field("tour_via_video_chat") tour_via_video_chat: String,
        @Field("id_agent") id_agent: Int? = null,
    ): NetResponse<ContactAgentMsgRes>

    @FormUrlEncoded
    @POST("api/user/contact/schedule")
    suspend fun userContactSchedule(
        @Field("email") email: String? = "",
        @Field("id_listing") id_listing: String? = "",
        @Field("id_project") id_project: String? = "",
        @Field("message") message: String? = "",
        @Field("ml_num") ml_num: String? = "",
        @Field("name") name: String? = "",
        @Field("phone") phone: String? = "",
        @Field("tags[]") tags: List<String>? = arrayListOf("mobile"),
        @Field("tour_via_video_chat") tour_via_video_chat: String,
        @Field("id_agent") id_agent: Int? = null,
    ): NetResponse<ContactAgentMsgRes>

    @POST("collect")
    suspend fun collect(
        @Body data: PostHSUserData
    ): Response<Void>

}