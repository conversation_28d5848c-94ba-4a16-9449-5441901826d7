package com.housesigma.android.network

import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.MMKVUtils
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response

class TokenInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val token = MMKVUtils.getStr(LoginFragment.LOGIN_TOKEN)
        var build: Request? = null
        if (token == null) {
            build = chain.request().newBuilder().build()
        } else {
            build = chain.request().newBuilder().addHeader("Authorization", "Bearer $token").build()
        }
        return chain.proceed(build)
    }
}