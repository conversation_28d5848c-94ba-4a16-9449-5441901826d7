package com.housesigma.android.network

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.housesigma.android.HSApp
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.NetResponse
import com.housesigma.android.model.Secret
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus

fun <T> ViewModel.launch(
    onProcess: suspend () -> NetResponse<T>,
    onSuccess: (data: T) -> Unit,
    finally: (suspend () -> Unit)? = null,
    onFailure: (suspend () -> Unit)? = null,
) {
    viewModelScope.launch(Dispatchers.IO) {
        try {
            val response = onProcess()
            if (response.status) {
                onSuccess(response.data)
            } else {
                if (onFailure != null) {
                    onFailure()
                }
                if (response.error.code == 900) {
                    // token 错误 重新请求一个token，不需要区分登陆不登陆
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.ReLogin).put(900))
                } else if (response.error.code == 910) {
                    // 显示错误信息, 转到登录/注册页面，只有登陆才会910
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.ReLogin).put(910))
                } else if (response.error.code == 940) {
                    // API需要加密940，请求refresh SecretKey 接口
                    synchronized(this) {
                        runBlocking {
                            val resSecretKey = NetClient.apiService.refreshSecretKey()
                            HSApp.secret = resSecretKey.data.secret
                        }
                    }
                }
                throw ServiceErrorException(response.error.message)
            }
        } catch (exception: Exception) {
            exception.printStackTrace()
            withContext(Dispatchers.Main) {
                NetErrorHandler.errorHandle(exception)
            }
        } finally {
            if (finally != null) {
                finally()
            }
        }
    }
}
