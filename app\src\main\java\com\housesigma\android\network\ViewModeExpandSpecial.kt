package com.housesigma.android.network

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.NetResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus

fun <T> ViewModel.launchSpecial(
    onProcess: suspend () -> NetResponse<T>,
    onSuccess: (data: T) -> Unit,
    finally: (suspend () -> Unit)? = null,
    onFailure: (suspend () -> Unit)? = null,
) {
    viewModelScope.launch(Dispatchers.IO) {
        try {
            val response = onProcess()
            if (response.status) {
                onSuccess(response.data)
            } else {
                // DEV-8316 API - Contact agent events optimize
                // 也是返回成功的情况，但是需要特殊处理
                if (response.data != null) {
                    onSuccess(response.data)
                }
                if (onFailure != null) {
                    onFailure()
                }
                if (response.error.code == 900) {
                    // token 错误 重新请求一个token，不需要区分登陆不登陆
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.ReLogin).put(900))
                } else if (response.error.code == 910) {
                    // 显示错误信息, 转到登录/注册页面，只有登陆才会910
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.ReLogin).put(910))
                }
                throw ServiceErrorException(response.error.message)
            }
        } catch (exception: Exception) {
            exception.printStackTrace()
            withContext(Dispatchers.Main) {
                NetErrorHandler.errorHandle(exception)
            }
        } finally {
            if (finally != null) {
                finally()
            }
        }
    }
}
