package com.housesigma.android.router

import android.content.Context
import android.content.Intent
import com.housesigma.android.AbsSuperApplication
import com.housesigma.android.helper.JumpHelper
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.ui.account.*
import com.housesigma.android.ui.forgot.ForgotPasswordActivity
import com.housesigma.android.ui.main.MainActivity
import com.housesigma.android.ui.main.PersonalizeListingsActivity
import com.housesigma.android.ui.onboard.OnBoardActivity
import com.housesigma.android.ui.recommended.RecommendedCommunityActivity
import com.housesigma.android.ui.recommended.RecommendedCommunityListActivity
import com.housesigma.android.ui.recommended.RecommendedStartActivity
import com.housesigma.android.ui.search.SearchActivity
import com.housesigma.android.ui.signup.SignUpActivity
import com.housesigma.android.ui.watcharea.WatchedAreaActivity
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.log.Logger
import org.greenrobot.eventbus.EventBus

class PublicRouter {

    fun open(context: Context, routeName: String?, map: HashMap<String, Any>?) {
        try {
            Logger.d("routeName is : $routeName, map is $map")
            when (routeName) {
                "homepage" -> {
                    // 首页
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.JUMP_HOME))
                }
                "map" -> {
//                    adb shell am start -a android.intent.action.VIEW -d https://test.housesigma.com/app/en/map/de-listed-5,for-sale/@43.794925,-79.256871,10.9z
                    // 地图
//                "map_type":["for-sale","sold","de-listed-5","for-lease","leased","de-listed-6"],


                    // 地图相关的，有三种不同的类型Json结构：
//                   {
//                        "map_type":["for-sale","sold","de-listed-5","for-lease","leased","de-listed-6"],
//                        "lon": -123.12xxx,
//                        "lat": 49.26xxx,
//                        "zoom": 16,
//                        "view": map/list,
//                        "municipality": 123xxx
//                   }

//                   {
//                        "listing_id": "id_xxx"
//                   }

//                   {
//                        "lon" : "-79.871285",
//                        "with_street" : "43.2557206,-79.8711024",
//                        "zoom" : "12",
//                        "lat" : "43.255967",
//                        "map_type" : [
//                              "for-sale"
//                        ]
//                   }


                    val listingId = map?.get("listing_id")?.toString()
                    listingId?.let {
                        JumpHelper.jumpMapWithParamActivity(context,listingId)
                        return
                    }


                    val withStreet = map?.get("with_street")?.toString()
                    withStreet?.let {
                        val lon = map?.get("lon")?.toString()?.toDouble()
                        val lat = map?.get("lat")?.toString()?.toDouble()
                        val zoom = map?.get("zoom")?.toString()?.toDouble()
                        val mapType = map?.get("map_type") as ArrayList<String>
                        val communityId = (map?.get("community")?:"").toString() //可空

                        JumpHelper.jumpMapActivityFromSearch(
                            context,
                            lat,
                            lon,
                            zoom = zoom,
                            map_type = mapType,
                            municipality = "",
                            communityId
                        )
                        return
                    }

                    val mapType = map?.get("map_type") as ArrayList<String>
                    val lon = map?.get("lon").toString().toDouble()
                    val zoom = map?.get("zoom").toString().toDouble()
                    val lat = map?.get("lat").toString().toDouble()
                    val municipality = (map?.get("municipality")?:"").toString() //可空
                    val view = (map?.get("view")?:"").toString() //可空

                    JumpHelper.jumpMapWithParamActivity(context, lat, lon, zoom, mapType,view,municipality)
                }
                "watched" -> {
                    // 我的关注
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.JUMP_HOME_WATCHED))
                }
                "market" -> {
                    // market
                    val municipality_id = (map?.get("municipality_id")) ?: ""
                    val house_type = (map?.get("house_type")) ?: ""
                    val community = (map?.get("community")) ?: ""

                    WebViewHelper.jumpMarket(
                        context,
                        municipality = municipality_id.toString(),
                        community = community.toString(),
                        house_type = house_type.toString()
                    )
                }
                "account" -> {
                    // 个人中心
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.JUMP_HOME_ACCOUNT))
                }
                "sign-up" -> {
                    // 注册
                    context.startActivity(Intent(context, SignUpActivity::class.java))
                }
                "forgot_password" -> {
                    // 忘记密码
                    context.startActivity(Intent(context, ForgotPasswordActivity::class.java))
                }
                "onboarding" -> {
                    // onboarding
                    context.startActivity(Intent(context, OnBoardActivity::class.java))
                }
                "agent_detail" -> {
                    // agent_detail
                    val slug = map?.get("slug")?.toString()?:""
                    val province = map?.get("province")?.toString()?:""
                    WebViewHelper.jumpAgentDetail(context, slug,province)
                }
                "agents" -> {
                    // agents
                    val province = map?.get("province")?.toString()?:""
                    WebViewHelper.jumpAgents(context, province)
                }

                "agent_experience_listings" -> {
                    // agent experience listings
                    val slug = (map?.get("slug")) ?: ""
                    val category = (map?.get("category")) ?: ""
                    val province = (map?.get("province")) ?: ""
                    WebViewHelper.jumpAgentExperienceListings(context, province.toString(), slug.toString(), category.toString())
                }

                "agent_experience_map" -> {
                    // agent experience map
                    val slug = (map?.get("slug")) ?: ""
                    val province = (map?.get("province")) ?: ""
                    val status = ArrayList((map?.get("status") ?: "").toString().split(","))
                    JumpHelper.jumpAgentMapActivity(context, status, slug.toString(), province.toString())
                }

                "open_url" -> {
//                  open_url
//                  打开 url 有两种方式 | two methods of opening url：
//                  1.内部 WebView | open in our app's WebView；
//                  2.系统浏览器 | open by system browser。
                    val openMethod = map?.get("open_method")?.toString()?.toDouble()?.toInt()
                    val url = map?.get("url")?.toString()
                    url?.let {
                        if (openMethod == 1) {
                            WebViewHelper.jumpInnerWebView(context, url)
                        } else if (openMethod == 2) {
                            WebViewHelper.jumpOuterWebView(context, url)
                        }
                    }
                }
                "listing_detail" -> {
                    val listingId = map?.get("listing_id").toString()
                    val eventSource = map?.get("event_source")?:""
                    // 房源详情页
                    WebViewHelper.jumpHouseDetail(context=context, id_listing = listingId, eventSource = eventSource.toString())
                }
                "precon_photos" ->{
                    val projectId = map?.get("id_project")?.toString()
                    // precon房源的照片
                    if (projectId != null) {
                        WebViewHelper.jumpPreconDetail(context, projectId)
                        JumpHelper.jumpPreconPhotoListActivity(context, projectId)
                    }
                }
                "precon_detail" ->{
                    val projectId = map?.get("id_project")?.toString()
                    // precon房源详情
                    if (projectId != null) {
                        WebViewHelper.jumpPreconDetail(context, projectId)
                    }
                }
                "precon_map" -> {
                    // precon地图
                    val mapType = map?.get("map_type") as ArrayList<String>
                    val lon = map?.get("lon").toString().toDouble()
                    val zoom = map?.get("zoom").toString().toDouble()
                    val lat = map?.get("lat").toString().toDouble()
                    val id_project = (map?.get("id_project")?:"").toString() //可空

                    JumpHelper.jumpPreconMapWithParamActivity(context, lat, lon, zoom, mapType,id_project)
                }
                "listing_photos" -> {
                    val listingId = map?.get("listing_id")?.toString()
                    // 房源的照片
                    if (listingId != null) {
                        WebViewHelper.jumpHouseDetail(context, listingId)
                        JumpHelper.jumpHousePhotoListActivity(context, listingId)
                    }
                }

                "similar_sold" -> {
                    // 附近成交
                    val listingId = map?.get("listing_id")?.toString()
                    if (listingId != null) {
                        WebViewHelper.jumpSimilarSold(context, listingId)
                    }
                }
                "similar_rented" -> {
                    // 附近出租
                    val listingId = map?.get("listing_id")?.toString()
                    if (listingId != null) {
                        WebViewHelper.jumpSimilarRented(context, listingId)
                    }
                }
                "contact_agent" -> {
//                    Contact Agent
                    val listingId = map?.get("listing_id")?.toString()
                    if (listingId != null) {
                        WebViewHelper.jumpHouseContact(context, listingId)
                    }
                }
                "sell_with_housesigma" -> {
//                Sell with HouseSigma

                    WebViewHelper.jumpSell(context)
                }


                "search" -> {
                    // 搜索界面
                    AbsSuperApplication.finishActivity(SearchActivity::class.java)
                    context.startActivity(Intent(context, SearchActivity::class.java))

                }

//                "map" -> {
//                    // 买房/租房地图
//
//
//                }
//
//                "map" -> {
//                    // 在地图上显示某个房源
//
//
//                }

                "estimate" -> {
                    // 估价
                    WebViewHelper.jumpEstimate(context, map)

                }

                "report" -> {
                    // report
//                  adb shell am start -a android.intent.action.VIEW -d https://test.housesigma.com/app/en/reports
                    WebViewHelper.jumpReports(context)

                }

                "listing_list" -> {
                    val type = map?.get("type").toString().toDouble().toInt()
                    // 指定类型的 listing 列表
                    JumpHelper.jumpListingActivity(context, type)

                }

                "recommend_communities" -> {
                    // 智能推荐
                    context.startActivity(Intent(context, RecommendedStartActivity::class.java))


                }

                "recommended_communities" -> {
//                    推荐的社区列表的结果页
                    val houseType = map?.get("property") as ArrayList<String>
                    val investment = map?.get("investment") as ArrayList<String>
                    val city = map?.get("city") as ArrayList<String>
                    val price = map?.get("price") as ArrayList<String>

                    val intent = Intent(context, RecommendedCommunityListActivity::class.java)
                    intent.putStringArrayListExtra("municipality_id", city)
                    intent.putStringArrayListExtra("house_type", houseType)
                    intent.putStringArrayListExtra("investment", investment)
                    intent.putExtra("price_max", price.getOrNull(1)?.toInt() ?: 0)
                    intent.putExtra("price_min", price.getOrNull(0)?.toInt() ?: 0)
                    context.startActivity(intent)

                }

                "community" -> {
//                    (推荐结果中的）某一社区
                    val listings = map?.get("listings")?.toString()?.toDouble()?.toInt()

                    val id_community = map?.get("id_community")?.toString()
                    val communityHouseType = map?.get("community_house_type")?.toString()

                    val houseType = map?.get("property") as ArrayList<String>
                    val investment = map?.get("investment") as ArrayList<String>
                    val city = map?.get("city") as ArrayList<String>
                    val price = map?.get("price") as ArrayList<String>

                    val intent = Intent(context, RecommendedCommunityActivity::class.java)
                    intent.putExtra("municipality_id", city)
                    intent.putExtra("price_max", price.getOrNull(1)?.toInt() ?: 0)
                    intent.putExtra("price_min", price.getOrNull(0)?.toInt() ?: 0)
                    intent.putExtra("house_type",houseType)//house_type是 外层array的那个
                    intent.putExtra("investment", investment)
                    intent.putExtra("community_house_type", communityHouseType)//community_house_type 是string类型
                    intent.putExtra("id_community",id_community)
//                    intent.putExtra("community_plus",item.community_plus)
                    intent.putExtra("listing_size",listings)
                    context.startActivity(intent)
                }

                "customize_preference" -> {
                    //定制过滤条件
                    context.startActivity(Intent(context, PersonalizeListingsActivity::class.java))

                }

                "watched_lists" -> {
                    // 关注的房源组列表
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.JUMP_HOME_WATCHED))
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.JUMP_WATCHED_LISTINGS))
                }

                "watched_notes" -> {
                    // 我的笔记列表
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.JUMP_HOME_WATCHED))
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.JUMP_WATCHED_NOTES))
                }

                "watched_listings" -> {
                    // 关注的房源列表
                    val idUserWatchlist = map?.get("id_user_watchlist").toString()
                    JumpHelper.jumpWatchListActivity(context, idUserWatchlist,isWatchList = true)
                }

                "shared_watchlist" -> {
                    // 分享关注的房源列表
                    val idUserWatchlist = map?.get("id_user_watchlist").toString()
                    JumpHelper.jumpWatchListActivity(context, idUserWatchlist,isWatchList = false)
                }

                "watched_areas" -> {
                    // 关注的区域列表
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.JUMP_HOME_WATCHED))
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.JUMP_WATCHED_AREA))
                }
                "watched_communities" -> {
                    // 关注的社区列表
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.JUMP_HOME_WATCHED))
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.JUMP_WATCHED_COMMUNITIES))
                }
                "recently_viewed" -> {
                    // 最近查看
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.JUMP_HOME_WATCHED))
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.JUMP_WATCHED_RECENTLY_VIEWED))

                }
                "add_watched_area" -> {
                    // 添加关注区域
                    context.startActivity(Intent(context, WatchedAreaActivity::class.java))

                }
                "edit_watched_area" -> {
                    // 编辑关注区域


                }
                "listings_of_watched_area" -> {
                    // 某个关注区域中的房源更新列表


                }
                "add_watched_community" -> {
                    // 添加关注的社区


                }
                "newly_listed_listings_of_community" -> {
                    val community = map?.get("community").toString()
                    val municipality = map?.get("municipality").toString()
                    val house_type = map?.get("house_type").toString()
//                    newly_listed_listings_of_community
                    // 某社区最近上线的房源
                    WebViewHelper.jumpWatchedTrendListing(
                        context,
                        community = community,
                        house_type = house_type,
                        municipality = municipality,
                        "2"
                    )
                }
                "just_sold_listings_of_community" -> {
                    // 某社区最近已售的房源
                    val community = map?.get("community")?.toString()
                    val municipality = map?.get("municipality")?.toString()
                    val house_type = map?.get("house_type")?.toString()
                    WebViewHelper.jumpWatchedTrendListing(
                        context,
                        community = community ?: "",
                        house_type = house_type ?: "",
                        municipality = municipality ?: "",
                        "1"
                    )
                }
                "user_profile" -> {
                    context.startActivity(Intent(context, MyProfileActivity::class.java))

                }
                "about_housesigma" -> {
                    context.startActivity(Intent(context, AboutActivity::class.java))

                }
                "give_us_feedback" -> {
                    context.startActivity(Intent(context, FeedbackActivity::class.java))

                }
                "notification_setting" -> {
                    context.startActivity(Intent(context, NotificationActivity::class.java))

                }
                "change_account" -> {
                    context.startActivity(Intent(context, ChangeAccountActivity::class.java))

                }
                "change_name" -> {
                    context.startActivity(Intent(context, ChangeNameActivity::class.java))
                }
                "change_email" -> {
                    val intent = Intent(context, ChangeContactActivity::class.java)
                    intent.putExtra("is_email", true)
                    context.startActivity(intent)

                }
                "change_contact_phone" -> {
                    val intent = Intent(context, ChangeContactActivity::class.java)
                    intent.putExtra("is_email", false)
                    context.startActivity(intent)
                }
                "change_password" -> {
                    context.startActivity(Intent(context, ChangePasswordActivity::class.java))
                }
                "delete_account" -> {
                    context.startActivity(Intent(context, DelAccountActivity::class.java))
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}