package com.housesigma.android.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;
import com.housesigma.android.R;
import com.housesigma.android.ui.main.MainActivity;
import com.housesigma.android.utils.GALog;

import java.util.Map;

/**
 * copy from old project
 */
public class MessagingService extends FirebaseMessagingService {

    private final String TAG = "hkj";

    private final String CHANNEL_ID = "CHANNEL_ID";

    private int id = 1;

    public MessagingService() {
        super();
    }

    @Override
    public void onCreate() {
        this.createNotificationChannel();
    }

    @Override
    public void onNewToken(@NonNull String token) {
        Log.d(TAG, "onNewToken " + token);

        Intent intent = new Intent("new-fcm-token");
        intent.putExtra("token", token);
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }

    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        if (remoteMessage.getNotification() == null || remoteMessage.getData() == null) {
            return;
        }

        Log.d(TAG, "onMessageReceived " + remoteMessage.getNotification().getTitle());

        Bundle bundle = new Bundle();
        for (Map.Entry<String, String> entry : remoteMessage.getData().entrySet()) {
            bundle.putString(entry.getKey(), entry.getValue());
        }
        bundle.putString("type", "msg");

        Intent intent = new Intent(getApplicationContext(), MainActivity.class);
        intent.putExtra("data", bundle);
        intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);

        // juste add this flag (PendingIntent.FLAG_IMMUTABLE) if you are with API M
        PendingIntent pendingIntent;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            pendingIntent = PendingIntent.getActivity(getApplicationContext(), 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getActivity(getApplicationContext(), 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        }

        try {
            // 显式处理可能的SecurityException，但不需要关注异常，因为在应用开启时，已经弹出所需的权限需要用户允许
            Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
                    .setContentTitle(remoteMessage.getNotification().getTitle())
                    .setContentText(remoteMessage.getNotification().getBody())
                    .setSmallIcon(R.mipmap.ic_launcher)
                    .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                    .setAutoCancel(true)
                    .setContentIntent(pendingIntent)
                    .build();
            NotificationManagerCompat manager = NotificationManagerCompat.from(getApplicationContext());
            manager.notify(++id, notification);
        } catch (SecurityException e) {
            e.printStackTrace();
        }
    }

    private void createNotificationChannel() {
        // Create the NotificationChannel, but only on API 26+ because
        // the NotificationChannel class is new and not in the support library
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            return;
        }

        CharSequence name = "hs_channel";
        int importance = NotificationManager.IMPORTANCE_DEFAULT;
        String description = "hs_channel";

        NotificationChannel channel = new NotificationChannel(CHANNEL_ID, name, importance);
        channel.setDescription(description);

        // Register the channel with the system; you can't change the importance
        // or other notification behaviors after this
        NotificationManager notificationManager = getSystemService(NotificationManager.class);
        notificationManager.createNotificationChannel(channel);
    }
}
