package com.housesigma.android.ui.account

import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityAboutBinding
import com.housesigma.android.utils.AndroidHapticFeedback
import com.housesigma.android.utils.DeviceUtil
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSUtil


class AboutActivity : BaseActivity() {

    private lateinit var aboutBinding: ActivityAboutBinding

    override fun onResume() {
        super.onResume()
        GALog.page("about_housesigma")
    }

    override fun getLayout(): Any {
        aboutBinding = ActivityAboutBinding.inflate(layoutInflater)
        return aboutBinding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        aboutBinding.ivClose.setOnClickListener {
            finish()
        }
    }

    override fun initData() {
    }


}