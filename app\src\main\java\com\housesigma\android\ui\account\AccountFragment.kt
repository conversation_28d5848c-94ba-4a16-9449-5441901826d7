package com.housesigma.android.ui.account

import android.content.Intent
import android.content.Intent.FLAG_ACTIVITY_NEW_TASK
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.BuildConfig
import com.housesigma.android.R
import com.housesigma.android.base.BaseAlertDialogBuilder
import com.housesigma.android.base.BaseHomeFragment
import com.housesigma.android.databinding.FragmentAccountBinding
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.main.MainActivity
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.LanguageUtils
import com.housesigma.android.utils.LiveChatUtil
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.utils.log.Logger
import com.livechatinc.inappchat.ChatWindowErrorType
import com.livechatinc.inappchat.ChatWindowEventsListener
import com.livechatinc.inappchat.models.NewMessageModel


class AccountFragment : BaseHomeFragment(), ChatWindowEventsListener {

    private lateinit var accountBinding: FragmentAccountBinding
    private lateinit var accountViewModel: AccountViewModel
    private var isFirstResume: Boolean = true

    override fun initImmersionBar() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
            titleBar(accountBinding.llView)
        }
//        开启沉浸模式  华为的状态栏会闪烁。https://github.com/gyf-dev/ImmersionBar/issues/194
    }

    override fun createView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        accountViewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        accountBinding = FragmentAccountBinding.inflate(inflater, container, false)


        accountViewModel.updateLangMsgRes.observe(this){
            reStartActivity()
        }

        accountViewModel.checkToken.observe(this){
            Logger.d( "accountFragment accountViewModel.checkToken.observe...." + it.appUser)

            it.run {
                if (this.appUser == null) {
                    if (activity != null) {
                        try {
                            LoginFragment.loginOut(requireActivity())
                        } catch (exception: Exception) {
                            exception.printStackTrace()
                        }
                    }
                } else {
                    if ("zh_CN".equals(LanguageUtils().getLANG())) {
                        accountBinding.tvLanguage.text = "中文"
                    } else if ("en_US".equals(LanguageUtils().getLANG())) {
                        accountBinding.tvLanguage.text = "English"
                    }

                    accountBinding.tvEmail.text = it?.appUser?.email?:""
                    accountBinding.tvName.text = it?.appUser?.name?:""
                    accountBinding.tvPhone.text = it?.appUser?.phonenumber?:""
                    if (it?.appUser?.premium_active==1) {
                        accountBinding.tvPremium.visibility = View.VISIBLE
                    } else {
                        accountBinding.tvPremium.visibility = View.GONE
                    }


                    if (it.agent!=null){
                        accountBinding.llMyAgent.visibility = View.VISIBLE
                    } else {
                        accountBinding.llMyAgent.visibility = View.GONE
                    }

                    accountBinding.llMyAgent.setOnClickListener {
                        if (this.agent==null) return@setOnClickListener
                        GALog.log("account_menu_click","my_agent")
                        val intent = Intent(activity, MyAgentActivity::class.java)
                        intent.putExtra("agent_name",this.agent.name?:"")
                        intent.putExtra("agent_email",this.agent.email?:"")
                        intent.putExtra("agent_phone",this.agent.phone?:"")
                        intent.putExtra("agent_picture",this.agent.picture?:"")
                        intent.putExtra("agent_slug",this.agent.slug?:"")
                        intent.putExtra("agent_province",this.agent.province?:"")
                        startActivity(intent)
                    }

                    if (it.appUser?.display_live_chat==0) {
                        accountBinding.llLivechat.visibility = View.GONE
                    } else if (it.appUser?.display_live_chat==1){
                        accountBinding.llLivechat.setOnClickListener {
                            LiveChatUtil.startActivity(requireContext())
                        }
                        accountBinding.llLivechat.visibility = View.VISIBLE
                    }
            }



            }
        }
        return accountBinding.root
    }

    override fun initView(root: View?) {
    }

    override fun initListener() {
    }

    override fun tag(): String {
        return "account"
    }

    override fun onVisibilityChanged(visible: Boolean) {
        super.onFragmentVisibilityChanged(visible)
        if (visible) {
            GALog.page(tag())
            initUserInfoView()
        }
    }

    override fun refreshLoad() {
        super.refreshLoad()
    }

    override fun lazyLoad() {
        initViews()
    }


    private fun initViews() {


        accountBinding.llAccountAbout.setOnClickListener {
            startActivity(Intent(activity, AboutActivity::class.java))
            GALog.log("account_menu_click","about")
        }
        accountBinding.llAccountFeedback.setOnClickListener {
            startActivity(Intent(activity, FeedbackActivity::class.java))
            GALog.log("account_menu_click","feedback")
        }
        accountBinding.llAccountLanguage.setOnClickListener {
            showChooseLanguageDialog()
            GALog.log("account_menu_click","language")
        }
        accountBinding.llAccountRecommend.setOnClickListener {
            shareRecommend()
        }
        accountBinding.llAccountWatched.setOnClickListener {
            (activity as MainActivity).jumpToWatched()
            GALog.log("account_menu_click","watched")
        }
        accountBinding.llCareer.setOnClickListener {
            activity?.let { that ->
                WebViewHelper.jumpJoinUs(that)
            }
            GALog.log("account_menu_click","careers")
        }

        accountBinding.tvMyProfile.setOnClickListener {
            startActivity(Intent(activity, MyProfileActivity::class.java))
        }

    }

    private fun initUserInfoView() {
        val accessToken = MMKVUtils.getStr(LoginFragment.LOGIN_TOKEN)
        if (!TextUtils.isEmpty(accessToken)) {
            if (accessToken != null) {
                accountViewModel.checkAccessToken(accessToken)
            }
        }
    }

    private fun shareRecommend() {
        try {
            GALog.log("page_share_click", "account")
            val initApp = HSUtil.getInitApp()
            initApp?.let {
                val shareContent = initApp.meta.title + " " + initApp.meta.url_android
                val intent = Intent()
                intent.action = Intent.ACTION_SEND
                intent.putExtra(
                    Intent.EXTRA_TEXT,
                    shareContent
                )
                intent.type = "text/plain"
                startActivity(Intent.createChooser(intent, "choose to share"))
            }
        }catch (e:Exception){
            e.printStackTrace()
        }

    }

    private fun showChooseLanguageDialog() {
        activity?.let {
            val builder = BaseAlertDialogBuilder(it)
            builder.setTitle("Language")
            builder.setPositiveButton(
                "English"
            ) { _, _ ->
                MMKVUtils.saveStr(LoginFragment.LANG, "en_US")
                accountBinding.tvLanguage.text = "English"
                MMKVUtils.saveInt("multilingual", 2)
                accountViewModel.setLang("en_US")
            }
            builder.setNegativeButton(
                "中文"
            ) { _, _ ->
                MMKVUtils.saveStr(LoginFragment.LANG, "zh_CN")
                accountBinding.tvLanguage.text = "中文"
                MMKVUtils.saveInt("multilingual", 1)
                accountViewModel.setLang("zh_CN")
            }
            val dialog: AlertDialog = builder.create()
            dialog.show()
        }
    }

    private fun reStartActivity() {
        val intent = Intent(activity, MainActivity::class.java)
        intent.flags = FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        // 取消其专场动画
        activity?.overridePendingTransition(0, 0)
    }

    override fun onWindowInitialized() {
    }

    override fun onChatWindowVisibilityChanged(visible: Boolean) {
    }

    override fun onNewMessage(message: NewMessageModel?, windowVisible: Boolean) {
    }

    override fun onStartFilePickerActivity(intent: Intent?, requestCode: Int) {
    }

    override fun onRequestAudioPermissions(permissions: Array<out String>?, requestCode: Int) {
    }

    override fun onError(
        errorType: ChatWindowErrorType?,
        errorCode: Int,
        errorDescription: String?
    ): Boolean {
        if (isAdded) {
            activity?.let {
                ToastUtils.showSuccess("Live chat failed to load.")
            }
        }
        return true
    }

    override fun handleUri(uri: Uri?): Boolean {
        return false
    }
}