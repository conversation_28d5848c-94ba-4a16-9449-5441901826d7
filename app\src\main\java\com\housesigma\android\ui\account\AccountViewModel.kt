package com.housesigma.android.ui.account

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.housesigma.android.HSApp
import com.housesigma.android.hybrid.HybridUtils
import com.housesigma.android.model.*
import com.housesigma.android.network.NetClient
import com.housesigma.android.network.launch
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.MMKVUtils

class AccountViewModel : ViewModel() {

    var notificationType: MutableLiveData<NotificationType> = MutableLiveData()
    var saveMsgRes: MutableLiveData<MsgRes> = MutableLiveData()
    var changeSignInUserMsgRes: MutableLiveData<MsgRes> = MutableLiveData()
    var updateMsgRes: MutableLiveData<MsgRes> = MutableLiveData()
    var removeEmailRes: MutableLiveData<MsgRes> = MutableLiveData()
    var removePhoneRes: MutableLiveData<MsgRes> = MutableLiveData()
    var checkToken: MutableLiveData<CheckToken> = MutableLiveData()
    val loadingLiveData = MutableLiveData<Boolean>()
    var updateLangMsgRes: MutableLiveData<MsgRes> = MutableLiveData()
    var searchAddress: MutableLiveData<CountryCode> = MutableLiveData()
    var saveContactSendCodeForDialogRes: MutableLiveData<MsgRes> = MutableLiveData()

    fun getInitCountryCode() {
        launch({
            NetClient.apiService.getInitCountryCode()
        }, {
            searchAddress.postValue(it)
        },{
            loadingLiveData.postValue(true)
        })
    }

    fun getNotificationType() {
        launch({
            NetClient.apiService.getNotificationType()
        }, {
            notificationType.postValue(it)
        })
    }

    fun setLang(lang:String) {
        launch({
            NetClient.apiService.updateLang(lang)
        }, {
            updateLangMsgRes.postValue(it)
        })
    }

    fun setProvince(province:String) {
        launch({
            NetClient.apiService.setProvince(province)
        }, {
            saveMsgRes.postValue(it)
        })
    }

    fun signOut() {
        launch({
            NetClient.apiService.signOut()
        }, {
            saveMsgRes.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun saveNotificationType(emailRecommend: Int,emailWatchedListing: Int,emailWatchedCommunity: Int
                                ,emailWatchedArea: Int,pushWatchedListing: Int) {
        launch({
            NetClient.apiService.saveNotificationTypeNew(emailRecommend,
                emailWatchedListing,emailWatchedCommunity,emailWatchedArea,pushWatchedListing)
        }, {
            saveMsgRes.postValue(it)
        },{
            loadingLiveData.postValue(true)
        })
    }


    fun updateProfile(name: String = "") {
        launch({
            NetClient.apiService.updateProfile(name)
        }, {
            saveMsgRes.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun updateReferralCode(referralCode: String) {
        launch({
            NetClient.apiService.updateReferralCode(referralCode)
        }, {
            saveMsgRes.postValue(it)
        },{
            loadingLiveData.postValue(true)
        })
    }

    fun changePassword(pwd: String) {
        launch({
            NetClient.apiService.changePassword(pwd)
        }, {
            saveMsgRes.postValue(it)
        },{
            loadingLiveData.postValue(true)
        })
    }

    fun feedback(message: String, email: String) {
        launch({
            NetClient.apiService.feedback(message, email)
        }, {
            saveMsgRes.postValue(it)
        },{
            loadingLiveData.postValue(true)
        })
    }

    /**
     * 专门用于弹窗内使用
     */
    fun changeContactSendCodeForDialog(
        email: String = "",
    ) {
        launch({
            NetClient.apiService.changecontactsendcode(
                email = email, countrycode = "", phonenumber = ""
            )
        }, {
            saveContactSendCodeForDialogRes.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }


    fun changecontactsendcode(
        email: String = "",
        phoneNumber: String = "",
        countryCode: String = ""
    ) {
        launch({
            NetClient.apiService.changecontactsendcode(
                email = email,
                phonenumber = phoneNumber, countrycode = countryCode
            )
        }, {
            saveMsgRes.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun updateEmail(
        code: String = "",
        email: String = "",
    ) {
        launch({
            NetClient.apiService.updateemail(
                code = code,
                email = email,
            )
        }, {
            updateMsgRes.postValue(it)
        },{
            loadingLiveData.postValue(true)
        })
    }


    fun updatePhoneNumber(
        code: String = "",
        phoneNumber: String = "",
        countryCode: String = ""
    ) {
        launch({
            NetClient.apiService.updatephonenumber(
                code = code,
                phonenumber = phoneNumber, countrycode = countryCode
            )
        }, {
            updateMsgRes.postValue(it)
        },{
            loadingLiveData.postValue(true)
        })
    }

    fun changeSignInUser(
        code: String = "",
        email: String = "",
        phoneNumber: String = "",
        countryCode: String = ""
    ) {
        launch({
            NetClient.apiService.changesigninuser(
                code = code,
                email = email,
                phonenumber = phoneNumber, countrycode = countryCode
            )
        }, {
            changeSignInUserMsgRes.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }


    fun profileDelete(pass: String) {
        launch({
            NetClient.apiService.profileDelete(pass)
        }, {
            saveMsgRes.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun removeEmail() {
        launch({
            NetClient.apiService.removeEmail()
        }, {
            removeEmailRes.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun removePhone() {
        launch({
            NetClient.apiService.removePhone()
        }, {
            removePhoneRes.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }


    fun checkAccessToken(access_token:String) {
        launch({
            NetClient.apiService.checkAccessToken(access_token)
        }, {
            val user = HybridUtils.saveAndParseUserData(it.hybridUser)
            it.appUser = user
            HSApp.secret = it.secret
            MMKVUtils.saveStr(LoginFragment.PROVINCE, it.province)
            LoginFragment.saveUserInfo(it.appUser)
            checkToken.postValue(it)
        })
    }


}