package com.housesigma.android.ui.account

import android.app.Dialog
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Window
import android.view.WindowManager
import com.housesigma.android.databinding.DialogAddEmailLaterBinding
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MMKVUtils


// DEV-4539 Prompt user to add email | 让缺失contact email的用户填补contact email
class AddEmailLaterDialog(
    context: Context,
    cb: AddEmailLaterDialogCallback,
    autoDismiss: Boolean? = true
) : Dialog(context) {

    interface AddEmailLaterDialogCallback {
        fun onOkay()
        fun onGoBack()
    }

    private var mCallback: AddEmailLaterDialogCallback = cb
    private var mAutoDismiss = autoDismiss ?: true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.window?.requestFeature(Window.FEATURE_NO_TITLE)
        val binding = DialogAddEmailLaterBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initViews(binding)
        this.window?.setBackgroundDrawable(ColorDrawable(0x00000000))
        this.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    private fun initViews(binding: DialogAddEmailLaterBinding) {
        binding.tvOkay.setOnClickListener {
            mCallback.onOkay()
            if (mAutoDismiss) {
                dismiss()
            }
        }
        binding.tvGoBackAndAddEmailNow.setOnClickListener {
            mCallback.onGoBack()
            if (mAutoDismiss) {
                dismiss()
            }
        }

    }


}