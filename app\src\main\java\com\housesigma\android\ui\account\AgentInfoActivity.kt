package com.housesigma.android.ui.account

import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityAgentInfoBinding
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.MMKVUtils


class AgentInfoActivity : BaseActivity() {

    private lateinit var binding: ActivityAgentInfoBinding

    override fun onResume() {
        super.onResume()
//        GALog.page("about_housesigma")
    }

    override fun getLayout(): Any {
        binding = ActivityAgentInfoBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.color_white)
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        binding.ivClose.setOnClickListener {
            finish()
        }
    }

    override fun initData() {
        val isAgent = MMKVUtils.getStr(LoginFragment.IS_AGENT)?:"0"
        val (brokerageName, licensedProvince, boardName) = if (isAgent == "1") {
            Triple(
                MMKVUtils.getStr(LoginFragment.BROKERAGE_NAME)?:"",
                MMKVUtils.getStr(LoginFragment.LICENSED_PROVINCE_TEXT)?:"",
                MMKVUtils.getStr(LoginFragment.BOARD_NAME_TEXT)?:""
            )
        } else {
            Triple("", "", "")
        }


        binding.etBoardName.setText(boardName)
        binding.etProvince.setText(licensedProvince)
        binding.etBrokerageName.setText(brokerageName)
    }


}