package com.housesigma.android.ui.account

import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityChangeAccountBinding
import com.housesigma.android.model.CountrycodeX
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.views.verifycodelib.VerifyCodeCompleteListener
import com.lxj.xpopup.XPopup


class ChangeAccountActivity : BaseActivity() {

    private lateinit var binding: ActivityChangeAccountBinding
    private lateinit var accountViewModel: AccountViewModel
    private var code: String = ""
    private var strList = ArrayList<String>()
    private var countrycodeXList = ArrayList<CountrycodeX>()

    override fun onResume() {
        super.onResume()
        GALog.page("change_account")
    }

    override fun getLayout(): Any {
        binding = ActivityChangeAccountBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }

        val currentLoginName = MMKVUtils.getStr(LoginFragment.LOGIN_NAME)
        currentLoginName?.let {
//            binding.tvCurrent.text = "current: ${currentLoginName}"
            if (currentLoginName.contains("@")) {
                binding.llPhone.visibility = View.GONE
                binding.rlEmail.visibility = View.VISIBLE

                binding.tvChangeSignIn.text = "Change sign-in account to phone number"
            } else {
                binding.llPhone.visibility = View.VISIBLE
                binding.rlEmail.visibility = View.GONE

                binding.tvChangeSignIn.text = "Change sign-in account to email"
            }
        }


        binding.ivClose.setOnClickListener {
            finish()
        }

        binding.ivDel.setOnClickListener {
            binding.etPhone.setText("")
        }

        binding.ivDelEmail.setOnClickListener {
            binding.etEmail.setText("")
        }


        binding.tvChangeSignIn.setOnClickListener {
            if (isPhone()) {
                binding.llPhone.visibility = View.GONE
                binding.rlEmail.visibility = View.VISIBLE
                binding.tvChangeSignIn.text = "Change sign-in account to phone number"
            } else {
                binding.llPhone.visibility = View.VISIBLE
                binding.rlEmail.visibility = View.GONE
                binding.tvChangeSignIn.text = "Change sign-in account to email"
            }
        }

        binding.tvCountryCode.setOnClickListener {
            XPopup.Builder(this)
                .asBottomList(
                    "", strList.toTypedArray()
                ) { position, text ->
                    binding.tvCountryCode.text = countrycodeXList[position].countrycode
                }
                .show()
        }

        binding.tvSubmit.setOnClickListener {
            showLoadingDialog()
            if (isPhone()) {
                // 手机号
                val phone = binding.etPhone.text.toString().trim()
                val countryCode = binding.tvCountryCode.text.toString().trim()
                accountViewModel.changeSignInUser(
                    phoneNumber = phone,
                    countryCode = countryCode,
                    code = code
                )
                GALog.log("user_profile_update","change_account_phone")
            } else {
                // 邮箱
                val email = binding.etEmail.text.toString().trim()
                accountViewModel.changeSignInUser(email = email, code = code)
                GALog.log("user_profile_update","change_account_email")
            }
        }

        binding.verifyCodeView.setCompleteListener(object :
            VerifyCodeCompleteListener {
            override fun verifyCodeComplete() {
                code = binding.verifyCodeView.getText()
            }

        })

        binding.tvSendCode.setOnClickListener {
            showLoadingDialog()
            if (isPhone()) {
                // 手机号
                val phone = binding.etPhone.text.toString().trim()
                val countryCode = binding.tvCountryCode.text.toString().trim()
                accountViewModel.changecontactsendcode(
                    phoneNumber = phone,
                    countryCode = countryCode
                )
                binding.tvSendTo.text = "Sent to " + countryCode + phone
                binding.tvVerifySendTip.text = "We sent you a code to verify your phone"
            } else {
                // 邮箱
                val email = binding.etEmail.text.toString().trim()
                accountViewModel.changecontactsendcode(email = email)
                binding.tvSendTo.text = "Sent to " + email
                binding.tvVerifySendTip.text = "We sent you a code to verify your email"
            }
        }
    }

    override fun initData() {
        accountViewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        showLoadingDialog()
        accountViewModel.getInitCountryCode()

        accountViewModel.loadingLiveData.observe(this) {
            dismissLoadingDialog()
        }

        accountViewModel.saveMsgRes.observe(this) {
            // 切换到输入页
            binding.llEnter.visibility = View.GONE
            binding.llVerify.visibility = View.VISIBLE
        }

        accountViewModel.changeSignInUserMsgRes.observe(this) {
            if (isVerify()) {
                // 关闭验证页面
                finish()
            }
        }


        accountViewModel.searchAddress.observe(this) { list ->
            countrycodeXList.addAll(list.countrycode)
            list.countrycode.forEach {
                strList.add(it.name)
            }
            list.countrycode.getOrNull(0)?.let {
                binding.tvCountryCode.text = it.countrycode
            }
        }
    }


    private fun isPhone(): Boolean {
        return binding.llPhone.visibility == View.VISIBLE
    }

    private fun isVerify(): Boolean {
        return binding.llVerify.visibility == View.VISIBLE
    }

}