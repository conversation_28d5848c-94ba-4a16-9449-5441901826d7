package com.housesigma.android.ui.account

import android.text.TextUtils
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityChangeContactBinding
import com.housesigma.android.model.CountrycodeX
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.views.verifycodelib.VerifyCodeCompleteListener
import com.lxj.xpopup.XPopup

class ChangeContactActivity : BaseActivity() {

    private lateinit var binding: ActivityChangeContactBinding
    private lateinit var accountViewModel: AccountViewModel
    private var code: String = ""
    private var isEmail: Boolean = true
    private var strList = ArrayList<String>()
    private var countrycodeXList = ArrayList<CountrycodeX>()

    override fun getLayout(): Any {
        binding = ActivityChangeContactBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onResume() {
        super.onResume()

        if (isEmail) {
            GALog.page("change_email")
        } else {
            GALog.page("change_contact_phone")
        }

    }

    override fun initView() {
        isEmail = intent.getBooleanExtra("is_email", true)
        val email = intent.getStringExtra("email")?:""
        if (!TextUtils.isEmpty(email)) {
            binding.etEmail.setText(email)
            binding.tvSendTo.text = "Sent to " + email
            jumpVerifyCaptcha()
        }

        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }


        if (isEmail) {
            binding.tvTitle.text = "Change Contact Email"
            binding.tvDelete.text = "Delete Contact Email"
            binding.llPhone.visibility = View.GONE
            binding.rlEmail.visibility = View.VISIBLE
            binding.tvVerifySendTip.text = "We sent you a code to verify your email"
        } else {
            binding.tvTitle.text = "Change Contact Phone"
            binding.tvDelete.text = "Delete Contact Phone"
            binding.llPhone.visibility = View.VISIBLE
            binding.rlEmail.visibility = View.GONE
            binding.tvVerifySendTip.text = "We sent you a code to verify your phone"
        }


        binding.ivClose.setOnClickListener {
            finish()
        }

        binding.ivDel.setOnClickListener {
            binding.etPhone.setText("")
        }

        binding.ivDelEmail.setOnClickListener {
            binding.etEmail.setText("")
        }


        binding.tvSubmit.setOnClickListener {
            showLoadingDialog()
            if (isPhone()) {
                // 手机号
                val phone = binding.etPhone.text.toString().trim()
                val countryCode = binding.tvCountryCode.text.toString().trim()
                accountViewModel.updatePhoneNumber(
                    phoneNumber = phone,
                    countryCode = countryCode,
                    code = code
                )
                GALog.log("user_profile_update","change_phone")
            } else {
                // 邮箱
                val email = binding.etEmail.text.toString().trim()
                accountViewModel.updateEmail(email = email, code = code)
                GALog.log("user_profile_update","change_email")
            }
        }

        binding.verifyCodeView.setCompleteListener(object :
            VerifyCodeCompleteListener {
            override fun verifyCodeComplete() {
                code = binding.verifyCodeView.getText()
            }

        })

        binding.tvSendCode.setOnClickListener {
            if (isPhone()) {
                // 手机号
                val phone = binding.etPhone.text.toString().trim()
                val countryCode = binding.tvCountryCode.text.toString().trim()
                accountViewModel.changecontactsendcode(
                    phoneNumber = phone,
                    countryCode = countryCode
                )
                binding.tvSendTo.text = "Sent to " + countryCode + phone
            } else {
                // 邮箱
                val email = binding.etEmail.text.toString().trim()
                accountViewModel.changecontactsendcode(email = email)
                binding.tvSendTo.text = "Sent to " + email
            }
        }



        binding.tvCountryCode.setOnClickListener {
            XPopup.Builder(this)
                .asBottomList(
                    "", strList.toTypedArray()
                ) { position, text ->
                    binding.tvCountryCode.text = countrycodeXList[position].countrycode
                }
                .show()
        }
    }

    override fun initData() {
        accountViewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        if (!isEmail) {
            showLoadingDialog()
            accountViewModel.getInitCountryCode()
        }

        accountViewModel.loadingLiveData.observe(this){
            dismissLoadingDialog()
        }

        accountViewModel.saveMsgRes.observe(this) {
            ToastUtils.showLong(it.message)
            // 切换到输入页
            binding.llEnter.visibility = View.GONE
            binding.llVerify.visibility = View.VISIBLE
        }

        accountViewModel.updateMsgRes.observe(this) {
            ToastUtils.showLong(it.message)
            setResult(RESULT_OK)
            finish()
        }

        accountViewModel.searchAddress.observe(this) { list ->
            countrycodeXList.addAll(list.countrycode)
            list.countrycode.forEach {
                strList.add(it.name)
            }

            list.countrycode.getOrNull(0)?.let {
                binding.tvCountryCode.text = it.countrycode
            }
        }
    }

    /**
     * 跳转校验验证码页面
     */
    private fun jumpVerifyCaptcha() {
        binding.llEnter.visibility = View.GONE
        binding.llVerify.visibility = View.VISIBLE
    }

    private fun isPhone(): Boolean {
        return !isEmail
    }

    private fun isVerify(): Boolean {
        return binding.llVerify.visibility == View.VISIBLE
    }

}