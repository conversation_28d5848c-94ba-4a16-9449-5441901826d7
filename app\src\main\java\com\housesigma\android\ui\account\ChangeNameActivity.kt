package com.housesigma.android.ui.account

import android.text.TextUtils
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityChangeNameBinding
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.AntiShake
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.ToastUtils


class ChangeNameActivity : BaseActivity() {

    private lateinit var changeNameBinding: ActivityChangeNameBinding
    private lateinit var accountViewModel: AccountViewModel

    override fun onResume() {
        super.onResume()
        GALog.page("change_name")
    }

    override fun getLayout(): Any {
        changeNameBinding = ActivityChangeNameBinding.inflate(layoutInflater)
        return changeNameBinding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }

//        val current = MMKVUtils.getStr(LoginFragment.SIGN_IN_NAME)
//        changeNameBinding.tvCurrent.text = "current: ${current}"
        changeNameBinding.ivClose.setOnClickListener {
            finish()
        }

        changeNameBinding.ivDel.setOnClickListener {
            changeNameBinding.etName.setText("")
        }
        changeNameBinding.tvSave.setOnClickListener {
            if (!TextUtils.isEmpty(changeNameBinding.etName.text.toString().trim())) {
                if (AntiShake.check(
                        "changeNameBinding.tvSave.setOnClickListener",
                        250
                    )
                ) return@setOnClickListener
                showLoadingDialog()
                accountViewModel.updateProfile(changeNameBinding.etName.text.toString().trim())
            }
            GALog.log("user_profile_update","change_name")
        }
    }

    override fun initData() {
        accountViewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        accountViewModel.saveMsgRes.observe(this) {
            ToastUtils.showLong(it.message)
            finish()
        }

        accountViewModel.loadingLiveData.observe(this) {
            dismissLoadingDialog()
        }
    }


}