package com.housesigma.android.ui.account

import android.text.TextUtils
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityChangePasswordBinding
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.utils.AntiShake
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.ToastUtils
import org.greenrobot.eventbus.EventBus


class ChangePasswordActivity : BaseActivity() {

    private lateinit var changePasswordBinding: ActivityChangePasswordBinding
    private lateinit var accountViewModel: AccountViewModel

    override fun onResume() {
        super.onResume()
        GALog.page("change_password")
    }

    override fun getLayout(): Any {
        changePasswordBinding = ActivityChangePasswordBinding.inflate(layoutInflater)
        return changePasswordBinding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        changePasswordBinding.ivClose.setOnClickListener {
            finish()
        }

        changePasswordBinding.tvSave.setOnClickListener {
            if (!TextUtils.isEmpty(changePasswordBinding.etSearchTerm.text.toString().trim())) {
                if (AntiShake.check(
                        "changePasswordBinding.tvSave.setOnClickListener",
                        250
                    )
                ) return@setOnClickListener
                showLoadingDialog()
                accountViewModel.changePassword(changePasswordBinding.etSearchTerm.text.toString().trim())
                GALog.log("user_profile_update","change_password")
            }
        }

        changePasswordBinding.ivDel.setOnClickListener {
            changePasswordBinding.etSearchTerm.setText("")
        }
    }

    override fun initData() {
        accountViewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        accountViewModel.saveMsgRes.observe(this) {
            // DEV-7044 password expire 90 days 修改完密码要通知hybrid刷新页面
            EventBus.getDefault().postSticky(MessageEvent(MessageType.PASSWORD_CHANGE))
            ToastUtils.showLong(it.message)
            finish()
        }
        accountViewModel.loadingLiveData.observe(this){
            dismissLoadingDialog()
        }
    }


}