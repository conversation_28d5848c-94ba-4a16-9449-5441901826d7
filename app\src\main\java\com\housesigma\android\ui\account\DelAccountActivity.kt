package com.housesigma.android.ui.account

import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityDelAccountBinding
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.main.MainActivity
import com.housesigma.android.utils.GALog
import com.housesigma.android.views.LoadingDialog


class DelAccountActivity : BaseActivity() {

    private lateinit var binding: ActivityDelAccountBinding
    private lateinit var accountViewModel: AccountViewModel

    override fun onResume() {
        super.onResume()
        GALog.page("delete_account")
    }

    override fun getLayout(): Any {
        binding = ActivityDelAccountBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        binding.ivClose.setOnClickListener {
            finish()
        }

        binding.tvDelMyAccount.setOnClickListener {
            showLoadingDialog()
            val pass = binding.etPassword.text.toString().trim()
            accountViewModel.profileDelete(pass)
        }
    }

    override fun initData() {
        accountViewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        accountViewModel.loadingLiveData.observe(this) {
            dismissLoadingDialog()
        }
        accountViewModel.saveMsgRes.observe(this) {
            LoginFragment.loginOut(this)
            val intent = Intent(this, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent)
        }
    }


}