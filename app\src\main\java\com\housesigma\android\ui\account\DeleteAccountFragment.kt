package com.housesigma.android.ui.account

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.Html
import android.text.TextUtils
import android.text.TextWatcher
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.ViewModelProvider
import com.google.gson.Gson
import com.gyf.immersionbar.ktx.destroyImmersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseDialogFragment
import com.housesigma.android.databinding.DialogContactUsBinding
import com.housesigma.android.databinding.DialogDeleteAccountBinding
import com.housesigma.android.model.HousePhotosInfo
import com.housesigma.android.model.User
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.main.MainActivity
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.views.LoadingDialog

class DeleteAccountFragment : BaseDialogFragment() {

    private lateinit var accountViewModel: AccountViewModel
    private var isShow: Boolean = false

    private lateinit var binding: DialogDeleteAccountBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreen)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        accountViewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        binding = DialogDeleteAccountBinding.inflate(inflater, container, false)
        initViews()
        initData()
        dialog?.setCanceledOnTouchOutside(true)
        return binding.root
    }

    private fun initData() {
        accountViewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        accountViewModel.loadingLiveData.observe(this) {
            dismissLoadingDialog()
        }
        accountViewModel.saveMsgRes.observe(this) {
            LoginFragment.loginOut(activity)
            val intent = Intent(activity, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
        }
    }

    private fun initViews() {
        val deleteTip = Html.fromHtml("Once you delete the account, there is no going back, please be certain.<br><br>Enter your <strong>password</strong> to confirm")
        binding.tvDelTip.text = deleteTip
        binding.tvSubmit.setOnClickListener {
            val password = binding.etPassword.text.toString().trim()
            if (TextUtils.isEmpty(password)) return@setOnClickListener
            showLoadingDialog()
            accountViewModel.profileDelete(password)
            GALog.log("user_profile_update","delete_account")
        }

        binding.tvSubmit.setBackgroundResource(R.drawable.shape_10radius_gray_fill)
        binding.tvSubmit.setTextColor(resources.getColor(R.color.color_gray))
        isShow = false
        binding.ivShowEye.setOnClickListener {
            if (isShow) {
                binding.etPassword.transformationMethod = PasswordTransformationMethod.getInstance()
                binding.ivShowEye.setImageResource(R.drawable.ic_eye)
            } else {
                binding.etPassword.transformationMethod = HideReturnsTransformationMethod.getInstance()
                binding.ivShowEye.setImageResource(R.drawable.ic_eye_close)
            }
            val trimPassword = binding.etPassword.text.toString()
            binding.etPassword.setSelection(trimPassword.length)
            isShow = !isShow
        }


        binding.etPassword.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                val feedbackStr = p0.toString()
                if (!TextUtils.isEmpty(feedbackStr)) {
                    binding.tvSubmit.setBackgroundResource(R.drawable.shape_10radius_red_color_fill)
                    binding.tvSubmit.setTextColor(resources.getColor(R.color.color_white))
                } else {
                    binding.tvSubmit.setBackgroundResource(R.drawable.shape_10radius_gray_fill)
                    binding.tvSubmit.setTextColor(resources.getColor(R.color.color_gray))
                }
            }

            override fun afterTextChanged(p0: Editable?) {

            }

        })
    }
    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        val windowParams = window?.attributes
        windowParams?.dimAmount = 0.7f // 遮罩效果消失
        window?.setLayout(-1, -2)
        windowParams?.gravity = Gravity.BOTTOM
        window?.attributes = windowParams
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        getDialog()?.let { destroyImmersionBar(it) }
        activity?.let {
            dismissLoadingDialog()
        }
    }

}


