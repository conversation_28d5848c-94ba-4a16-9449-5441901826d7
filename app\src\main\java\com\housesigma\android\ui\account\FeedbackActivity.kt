package com.housesigma.android.ui.account

import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Log
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityFeedbackBinding
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.ToastUtils


class FeedbackActivity : BaseActivity() {

    private lateinit var feedbackBinding: ActivityFeedbackBinding
    private lateinit var accountViewModel: AccountViewModel

    override fun onResume() {
        super.onResume()
        GALog.page("give_us_feedback")
    }

    override fun getLayout(): Any {
        feedbackBinding = ActivityFeedbackBinding.inflate(layoutInflater)
        return feedbackBinding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        feedbackBinding.tvFaq.setOnClickListener {
            WebViewHelper.jumpInnerWebView(this, "https://housesigma.com/blog-en/faq/")
        }
        feedbackBinding.ivClose.setOnClickListener {
            finish()
        }
        feedbackBinding.tvSave.setOnClickListener {
            showLoadingDialog()
            accountViewModel.feedback(
                feedbackBinding.etFeedback.text.toString().trim(),
                feedbackBinding.etEmail.text.toString().trim()
            )
        }

    }

    override fun initData() {
        accountViewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        accountViewModel.saveMsgRes.observe(this) {
            ToastUtils.showLong(it.message)
            finish()
        }
        accountViewModel.loadingLiveData.observe(this) {
            dismissLoadingDialog()
        }
    }


}