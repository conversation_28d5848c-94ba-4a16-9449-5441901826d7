package com.housesigma.android.ui.account

import android.content.Intent
import android.net.Uri
import com.bumptech.glide.Glide
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityMyAgentBinding
import com.housesigma.android.helper.JumpHelper
import com.housesigma.android.ui.listing.ContactDialogType
import com.housesigma.android.ui.listing.ContactUsWithAgentFragment
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.GALog
import java.lang.Exception


class MyAgentActivity : BaseActivity() {

    private lateinit var binding: ActivityMyAgentBinding

    override fun getLayout(): Any {
        binding = ActivityMyAgentBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        val agentName = intent.getStringExtra("agent_name") ?: ""
        val agentEmail = intent.getStringExtra("agent_email") ?: ""
        val agentPhone = intent.getStringExtra("agent_phone") ?: ""
        val agentPicture = intent.getStringExtra("agent_picture") ?: ""
        val agentSlug = intent.getStringExtra("agent_slug") ?: ""
        val agentProvince= intent.getStringExtra("agent_province") ?: ""
        immersionBar {
            navigationBarColor(R.color.color_white)

            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }


        binding.ivClose.setOnClickListener {
            finish()
        }

        binding.tvAgentName.text = agentName
        binding.tvAgentEmail.text = agentEmail
        binding.tvAgentPhone.text = agentPhone
        Glide.with(this)
            .load(agentPicture)
            .circleCrop()
            .into(binding.ivAvatar)

        binding.ivAvatar.setOnClickListener {
            GALog.log("agent_avatar_click")
            WebViewHelper.jumpAgentDetail(this, agentSlug,agentProvince)
        }
        binding.tvAgentName.setOnClickListener {
            GALog.log("agent_avatar_click")
            WebViewHelper.jumpAgentDetail(this, agentSlug,agentProvince)
        }


        binding.llContactAgent.setOnClickListener {
            GALog.log("contact_agent_click", "myagent_form")
            ContactUsWithAgentFragment.newInstance(
                ContactDialogType.ACCOUNT,
                "please contact me", agentName = agentName, agentPicture = agentPicture, gaHsLabel = "myagent_form", agentSlug = agentSlug
            ).show(supportFragmentManager, "myagent_form")
        }

        binding.tvAgentEmail.setOnClickListener {
            try {
                GALog.log("agent_contact_click", "email")
                val data = Intent(Intent.ACTION_SENDTO)
                data.data = Uri.parse("mailto:" + agentEmail)
                data.putExtra(Intent.EXTRA_SUBJECT, "")
                data.putExtra(Intent.EXTRA_TEXT, "")
                startActivity(data)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        binding.tvAgentPhone.setOnClickListener {
            try {
                GALog.log("agent_contact_click", "phone")
                val intent = Intent(Intent.ACTION_DIAL)
                val data = Uri.parse("tel:" + agentPhone)
                intent.data = data
                startActivity(intent)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun initData() {
    }

    override fun onResume() {
        super.onResume()
        GALog.page("my_agent")
    }


}