package com.housesigma.android.ui.account

import android.content.Intent
import android.text.TextUtils
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityMyProfileBinding
import com.housesigma.android.model.CheckToken
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.main.MainActivity
import com.housesigma.android.utils.AntiShake
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.views.HSAlertDialog


class MyProfileActivity : BaseActivity() {

    private lateinit var binding: ActivityMyProfileBinding
    private lateinit var accountViewModel: AccountViewModel
    private var isFirst: Boolean = true

    private val deleteAccountFragment: DeleteAccountFragment? by lazy {
        DeleteAccountFragment()
    }

    override fun getLayout(): Any {
        binding = ActivityMyProfileBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }

        binding.tvSignOut.setOnClickListener {
            if (AntiShake.check("tvSignOut.setOnClickListener", 1000)) return@setOnClickListener
            loadingDialog.show()
            accountViewModel.signOut()
        }

        binding.ivClose.setOnClickListener {
            finish()
        }
        binding.llEmail.setOnClickListener {
            val intent = Intent(this, ChangeContactActivity::class.java)
            intent.putExtra("is_email", true)
            startActivity(intent)
        }
        binding.llName.setOnClickListener {
            startActivity(Intent(this, ChangeNameActivity::class.java))

        }
        binding.llNotification.setOnClickListener {
            startActivity(Intent(this, NotificationActivity::class.java))
        }
        binding.llPassword.setOnClickListener {
            startActivity(Intent(this, ChangePasswordActivity::class.java))
        }
        binding.llPhoneNumber.setOnClickListener {
            val intent = Intent(this, ChangeContactActivity::class.java)
            intent.putExtra("is_email", false)
            startActivity(intent)
        }
        binding.llSignInUser.setOnClickListener {
            startActivity(Intent(this, ChangeAccountActivity::class.java))
        }
        binding.llReferralCode.setOnClickListener {
            startActivity(Intent(this, ReferralCodeActivity::class.java))
        }

        binding.llDelMyAccount.setOnClickListener {
            deleteAccountFragment?.let {
                if (it.isAdded) return@let
                it.show(supportFragmentManager, "")
            }
        }

        binding.llAgentInfo.setOnClickListener {
            val intent = Intent(this, AgentInfoActivity::class.java)
            startActivity(intent)
        }

        binding.ivDelEmail.setOnClickListener {
            val email = binding.tvEmail.text.toString().trim()
            HSAlertDialog(
                this, "Are you sure?", "This will delete the contact email:"+email+".",
                "Cancel", "Sure",
                object : HSAlertDialog.HSAlertCallback {
                    override fun onSuccess() {
                        showLoadingDialog()
                        accountViewModel.removeEmail()
                    }
                }).show()
        }

        binding.ivDelPhone.setOnClickListener {
            val phoneNumber = binding.tvPhoneNumber.text.toString().trim()
            HSAlertDialog(
                this, "Are you sure?", "This will delete the contact phone:"+phoneNumber+".",
                "Cancel", "Sure",
                object : HSAlertDialog.HSAlertCallback {
                    override fun onSuccess() {
                        showLoadingDialog()
                        accountViewModel.removePhone()
                    }
                }).show()
        }

        val isAgent = MMKVUtils.getStr(LoginFragment.IS_AGENT)?:"0"
        if (isAgent == "1") {
            binding.llAgentInfo.visibility = View.VISIBLE
        }else{
            binding.llAgentInfo.visibility = View.GONE
        }

    }

    override fun onResume() {
        super.onResume()
        GALog.page("user_profile")
        if (isFirst) {
            isFirst = !isFirst
        } else {
            getUserInfoShow()
        }
    }

//    private fun handleUserViewByLocal() {
//        val name = MMKVUtils.getStr(LoginFragment.SIGN_IN_NAME) ?: ""
//        val email = MMKVUtils.getStr(LoginFragment.SIGN_IN_EMAIL) ?: ""
////        val phonenumber = MMKVUtils.getStr(LoginFragment.SIGN_IN_PHONE) ?: ""
//        val createDate = MMKVUtils.getStr(LoginFragment.CREATE_DATE) ?: ""
//        val loginName = MMKVUtils.getStr(LoginFragment.LOGIN_NAME) ?: ""
//
//        if (!TextUtils.isEmpty(email)) {
//            binding.ivDelEmail.visibility = View.VISIBLE
//        } else {
//            binding.ivDelEmail.visibility = View.GONE
//        }
//
////        if (!TextUtils.isEmpty(phonenumber)) {
////            binding.ivDelPhone.visibility = View.VISIBLE
////        } else {
////            binding.ivDelPhone.visibility = View.GONE
////        }
//
//        binding.tvName.text = name
//        binding.tvEmail.text = email
////        binding.tvPhoneNumber.text = phonenumber
//        binding.tvRegisterTime.text = createDate
//        binding.tvSignInUser.text = loginName
//    }

    private fun handleUserView(it: CheckToken) {
        if (!TextUtils.isEmpty(it?.appUser?.email)) {
            binding.ivDelEmail.visibility = View.VISIBLE
        } else {
            binding.ivDelEmail.visibility = View.GONE
        }

        if (!TextUtils.isEmpty(it?.appUser?.phonenumber)) {
            binding.ivDelPhone.visibility = View.VISIBLE
        } else {
            binding.ivDelPhone.visibility = View.GONE
        }

        binding.tvName.text = it?.appUser?.name ?: ""
        binding.tvEmail.text = it?.appUser?.email ?: ""
        binding.tvPhoneNumber.text = it?.appUser?.phonenumber ?: ""
        binding.tvRegisterTime.text = it?.appUser?.create_date ?: ""
        binding.tvSignInUser.text = it?.appUser?.login_name ?: ""

        if (it?.appUser?.premium_active==1) {
            binding.llPremiumAccount.visibility = View.VISIBLE
            binding.tvPremiumAccount.text = "Expired on " + it?.appUser?.premium_expire_date?:""
        } else {
            binding.llPremiumAccount.visibility = View.GONE
        }

    }

    override fun initData() {
        accountViewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        accountViewModel.removePhoneRes.observe(this) {
            binding.ivDelPhone.visibility = View.GONE
            getUserInfoShow()
        }
        accountViewModel.loadingLiveData.observe(this) {
            dismissLoadingDialog()
        }
        accountViewModel.removeEmailRes.observe(this) {
            binding.ivDelEmail.visibility = View.GONE
            getUserInfoShow()
        }
        accountViewModel.checkToken.observe(this) {
            handleUserView(it)
        }

        accountViewModel.saveMsgRes.observe(this) {
            LoginFragment.loginOut(this)
            val intent = Intent(this, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent)
        }
        getUserInfoShow()
//        handleUserViewByLocal()
    }

    private fun getUserInfoShow() {
        val accessToken = MMKVUtils.getStr(LoginFragment.LOGIN_TOKEN)
        if (!TextUtils.isEmpty(accessToken)) {
            if (accessToken != null) {
                accountViewModel.checkAccessToken(accessToken)
            }
        }
    }


}