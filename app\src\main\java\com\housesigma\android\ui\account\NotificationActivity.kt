package com.housesigma.android.ui.account

import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.CompoundButton
import androidx.appcompat.view.ContextThemeWrapper
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityNotificationBinding
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.ToastUtils


class NotificationActivity : BaseActivity() {

    private lateinit var binding: ActivityNotificationBinding
    private lateinit var accountViewModel: AccountViewModel

    override fun onResume() {
        super.onResume()
        GALog.page("notification_setting")
    }

    override fun getLayout(): Any {
        binding = ActivityNotificationBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)

            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        binding.ivClose.setOnClickListener {
            finish()
        }
    }

    override fun initData() {
        accountViewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        accountViewModel.saveMsgRes.observe(this) {
            ToastUtils.showLong(it.message)
        }
        accountViewModel.loadingLiveData.observe(this) {
            dismissLoadingDialog()
        }

        accountViewModel.notificationType.observe(this) {
            if (TextUtils.isEmpty(it.contact_email)) {
                binding.tvEmail.text = "no email provided"

                binding.cbRecommend.visibility = View.GONE
                binding.cbWatchedListing.visibility = View.GONE
                binding.cbWatchedCommunity.visibility = View.GONE
                binding.cbWatchedArea.visibility = View.GONE

                binding.cbRecommendDisable.visibility = View.VISIBLE
                binding.cbWatchedListingDisable.visibility = View.VISIBLE
                binding.cbWatchedCommunityDisable.visibility = View.VISIBLE
                binding.cbWatchedAreaDisable.visibility = View.VISIBLE
            } else {
                binding.tvEmail.text = it.contact_email

                binding.cbRecommend.isChecked =
                    it.notification_v2.email.recommend.enabled == 1
                binding.cbWatchedListing.isChecked =
                    it.notification_v2.email.watched_listing.enabled == 1
                binding.cbWatchedCommunity.isChecked =
                    it.notification_v2.email.watched_community.enabled == 1
                binding.cbWatchedArea.isChecked =
                    it.notification_v2.email.watched_area.enabled == 1


                binding.cbRecommend.visibility = View.VISIBLE
                binding.cbWatchedListing.visibility = View.VISIBLE
                binding.cbWatchedCommunity.visibility = View.VISIBLE
                binding.cbWatchedArea.visibility = View.VISIBLE

                binding.cbRecommendDisable.visibility = View.GONE
                binding.cbWatchedListingDisable.visibility = View.GONE
                binding.cbWatchedCommunityDisable.visibility = View.GONE
                binding.cbWatchedAreaDisable.visibility = View.GONE
            }





            binding.cbWatchedListingPush.isChecked =
                it.notification_v2.push_notification.watched_listing.enabled == 1



            binding.cbRecommend.setOnCheckedChangeListener(object :
                CompoundButton.OnCheckedChangeListener {
                override fun onCheckedChanged(compoundButton: CompoundButton?, boolean: Boolean) {
                    if (compoundButton?.isPressed == false) {
                        return
                    }
                    showLoadingDialog()
                    requestSaveNotificationSettings()
                }
            })

            binding.cbWatchedArea.setOnCheckedChangeListener(object :
                CompoundButton.OnCheckedChangeListener {
                override fun onCheckedChanged(compoundButton: CompoundButton?, boolean: Boolean) {
                    if (compoundButton?.isPressed == false) {
                        return
                    }
                    showLoadingDialog()
                    requestSaveNotificationSettings()
                }
            })

            binding.cbWatchedListing.setOnCheckedChangeListener(object :
                CompoundButton.OnCheckedChangeListener {
                override fun onCheckedChanged(compoundButton: CompoundButton?, boolean: Boolean) {
                    if (compoundButton?.isPressed == false) {
                        return
                    }
                    showLoadingDialog()
                    requestSaveNotificationSettings()
                }
            })

            binding.cbWatchedListingPush.setOnCheckedChangeListener(object :
                CompoundButton.OnCheckedChangeListener {
                override fun onCheckedChanged(compoundButton: CompoundButton?, boolean: Boolean) {
                    if (compoundButton?.isPressed == false) {
                        return
                    }
                    showLoadingDialog()
                    requestSaveNotificationSettings()
                }
            })

            binding.cbWatchedCommunity.setOnCheckedChangeListener(object :
                CompoundButton.OnCheckedChangeListener {
                override fun onCheckedChanged(compoundButton: CompoundButton?, boolean: Boolean) {
                    if (compoundButton?.isPressed == false) {
                        return
                    }
                    showLoadingDialog()
                    requestSaveNotificationSettings()
                }
            })

        }
        accountViewModel.getNotificationType()
    }


    fun requestSaveNotificationSettings() {
        val emailRecommend = if (binding.cbRecommend.isChecked) 1 else 0
        val emailWatchedListing = if (binding.cbWatchedListing.isChecked) 1 else 0
        val emailWatchedCommunity = if (binding.cbWatchedCommunity.isChecked) 1 else 0
        val emailWatchedArea = if (binding.cbWatchedArea.isChecked) 1 else 0
        val pushWatchedListing = if (binding.cbWatchedListingPush.isChecked) 1 else 0
        accountViewModel.saveNotificationType(
            emailRecommend,
            emailWatchedListing,
            emailWatchedCommunity,
            emailWatchedArea,
            pushWatchedListing
        )
    }


}