package com.housesigma.android.ui.account

import android.app.Dialog
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Window
import android.view.WindowManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import com.housesigma.android.databinding.DialogPromptUserAddEmailBinding
import com.housesigma.android.utils.GALog


// DEV-4539 Prompt user to add email | 让缺失contact email的用户填补contact email
class PromptUserAddEmailDialog(
    source: String?="",
    viewModelStoreOwner: ViewModelStoreOwner,
    lifecycle: LifecycleOwner,
    context: Context,
    cb: PromptUserAddEmailDialogCallback,
    autoDismiss: Boolean? = true,
) : Dialog(context) {

    interface PromptUserAddEmailDialogCallback {
        fun onSendCode(email:String)
        fun onNotNow()
    }
    private var mSource = source
    private var mContext: ViewModelStoreOwner = viewModelStoreOwner
    private var mLifecycle: LifecycleOwner = lifecycle
    private lateinit var accountViewModel: AccountViewModel

    private var mCallback: PromptUserAddEmailDialogCallback = cb
    private var mAutoDismiss = autoDismiss ?: true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.window?.requestFeature(Window.FEATURE_NO_TITLE)
        accountViewModel = ViewModelProvider(mContext).get(AccountViewModel::class.java)
        val binding = DialogPromptUserAddEmailBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initViews(binding)
        initData(binding)
        this.window?.setBackgroundDrawable(ColorDrawable(0x00000000))
        this.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    private fun initData(binding: DialogPromptUserAddEmailBinding) {
        accountViewModel.saveContactSendCodeForDialogRes.observe(mLifecycle) {
            val email = binding.etEmail.text.toString().trim()
            mCallback.onSendCode(email)
            if (mAutoDismiss) {
                dismiss()
            }
        }

        accountViewModel.loadingLiveData.observe(mLifecycle){
            binding.tvSendCode.isClickable = true
        }
    }

    private fun initViews(binding: DialogPromptUserAddEmailBinding) {
        binding.tvNotNow.setOnClickListener {
            GALog.log("popup_submit","add_email_not_now")
            mCallback.onNotNow()
            if (mAutoDismiss) {
                dismiss()
            }
        }
        binding.tvSendCode.setOnClickListener {
            GALog.log("popup_submit","add_email_send_code")
            binding.tvSendCode.isClickable = false
            val email = binding.etEmail.text.toString().trim()
            accountViewModel.changeContactSendCodeForDialog(email = email)
        }

    }

    override fun show() {
        super.show()
        GALog.log("popup_show","add_email_" + mSource)
    }


}