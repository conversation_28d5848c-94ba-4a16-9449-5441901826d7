package com.housesigma.android.ui.account

import android.text.TextUtils
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityChangePasswordBinding
import com.housesigma.android.databinding.ActivityReferralCodeBinding
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.AntiShake
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.ToastUtils


class ReferralCodeActivity : BaseActivity() {

    private lateinit var binding: ActivityReferralCodeBinding
    private lateinit var accountViewModel: AccountViewModel

    override fun onResume() {
        super.onResume()
        GALog.page("user_profile_referral_code")
    }

    override fun getLayout(): Any {
        binding = ActivityReferralCodeBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        binding.ivClose.setOnClickListener {
            finish()
        }

        binding.tvSave.setOnClickListener {
            if (!TextUtils.isEmpty(binding.etReferralCode.text.toString().trim())) {
                if (AntiShake.check(
                        "binding.tvSave.setOnClickListener",
                        250
                    )
                ) return@setOnClickListener
                showLoadingDialog()
                accountViewModel.updateReferralCode(binding.etReferralCode.text.toString().trim())
            }
        }

        binding.ivDel.setOnClickListener {
            binding.etReferralCode.setText("")
        }

        val referralCode = MMKVUtils.getStr(LoginFragment.REFERRAL_CODE)?:""
        if (!TextUtils.isEmpty(referralCode)) {
            binding.etReferralCode.setTextColor(resources.getColor(R.color.color_gray_dark))
            binding.etReferralCode.setText(referralCode)
            binding.etReferralCode.isFocusable = false
            binding.etReferralCode.isFocusableInTouchMode = false
            binding.ivDel.visibility = View.GONE
            binding.tvSave.visibility = View.GONE
            binding.rlReferralCode.setBackgroundResource(R.drawable.shape_10radius_gray_fill)
        }
    }

    override fun initData() {
        accountViewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        accountViewModel.saveMsgRes.observe(this) {
            GALog.log("user_profile_update","referral_code")
            ToastUtils.showLong(it.message)
            finish()
        }
        accountViewModel.loadingLiveData.observe(this){
            dismissLoadingDialog()
        }
    }


}