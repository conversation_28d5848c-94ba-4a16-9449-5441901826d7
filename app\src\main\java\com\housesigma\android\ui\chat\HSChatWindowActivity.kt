package com.housesigma.android.ui.chat

import android.app.FragmentTransaction
import android.os.Bundle
import android.view.Window
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityHsChatBinding
import com.livechatinc.inappchat.ChatWindowConfiguration
import com.livechatinc.inappchat.ChatWindowFragment


class HSChatWindowActivity : AppCompatActivity() {
    companion object {
        private val DEFINED_KEYS = hashSetOf(
            ChatWindowConfiguration.KEY_LICENCE_NUMBER,
            ChatWindowConfiguration.KEY_GROUP_ID,
            ChatWindowConfiguration.KEY_VISITOR_NAME,
            ChatWindowConfiguration.KEY_VISITOR_EMAIL
        )
    }

    private lateinit var binding: ActivityHsChatBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityHsChatBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.ivClose.setOnClickListener {
            finish()
        }

        var licenceNumber: String? = null
        var groupId: String? = null
        var visitorName: String? = null
        var visitorEmail: String? = null
        val customVariables = HashMap<String, String>()

        val extras = intent.extras
        if (extras != null) {
            licenceNumber = extras.get(ChatWindowConfiguration.KEY_LICENCE_NUMBER)?.toString()
            groupId = extras.get(ChatWindowConfiguration.KEY_GROUP_ID)?.toString()

            if (extras.containsKey(ChatWindowConfiguration.KEY_VISITOR_NAME)) {
                visitorName = extras.get(ChatWindowConfiguration.KEY_VISITOR_NAME)?.toString()
            }

            if (extras.containsKey(ChatWindowConfiguration.KEY_VISITOR_EMAIL)) {
                visitorEmail = extras.get(ChatWindowConfiguration.KEY_VISITOR_EMAIL)?.toString()
            }
            for (key in extras.keySet()) {
                if (!DEFINED_KEYS.contains(key)) {
                    customVariables[key] = extras.get(key)?.toString() ?: ""
                }
            }
        }

        val ft: FragmentTransaction = fragmentManager.beginTransaction()
        ft.replace(R.id.fl_chat, ChatWindowFragment.newInstance(licenceNumber, groupId, visitorName, visitorEmail, customVariables))
        ft.commit()
    }
}