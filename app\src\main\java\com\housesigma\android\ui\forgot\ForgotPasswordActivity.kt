package com.housesigma.android.ui.forgot

import android.content.Intent
import android.text.TextUtils
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityForgotPasswordBinding
import com.housesigma.android.databinding.DialogLoginBinding
import com.housesigma.android.model.CountrycodeX
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.AppManager
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.views.LoadingDialog
import com.lxj.xpopup.XPopup


class ForgotPasswordActivity : BaseActivity() {

    private lateinit var binding: ActivityForgotPasswordBinding
    private lateinit var forgotPasswordModel: ForgotPasswordModel
    private var strList = ArrayList<String>()
    private var countrycodeXList = ArrayList<CountrycodeX>()

    private var phone: String? = null
    private var password: String? = null
    private var name: String? = null
    private var countryCode: String? = null
    private var email: String? = null

    override fun onResume() {
        super.onResume()
        GALog.page("forgot_password")
    }

    override fun getLayout(): Any {
        binding = ActivityForgotPasswordBinding.inflate(layoutInflater)
        forgotPasswordModel = ViewModelProvider(this).get(ForgotPasswordModel::class.java)
        return binding.root
    }

    override fun initView() {
        AppManager.getManager().addActivity(this)
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        initViews()
    }

    override fun onDestroy() {
        super.onDestroy()
        AppManager.getManager().finishActivity(this)
    }

    /**
     * 重置密码界面，应该自动填充用户上次登录的账号信息，逻辑同登录框
     */
    private fun restoreLastLoginUserName(binding: ActivityForgotPasswordBinding) {
        val email = MMKVUtils.getStr(LoginFragment.SIGN_IN_EMAIL)
        var phone = MMKVUtils.getStr(LoginFragment.SIGN_IN_PHONE)
        val via = MMKVUtils.getStr(LoginFragment.SIGN_IN_VIA)

        if (via == "email") {
            if (!TextUtils.isEmpty(email)) {
                binding.etEmail.setText(email)
                binding.llEmail.performClick()
            }
        } else {
            if (!TextUtils.isEmpty(phone)) {
                phone = phone?.replace("+86", "")
                phone = phone?.replace("+852", "")
                phone = phone?.replace("+91", "")
                phone = phone?.replace("+1", "")
                phone = phone?.replace("+44", "")


                binding.etPhone.setText(phone)
                binding.llPhone.performClick()
            }
        }
    }

    override fun initData() {
        forgotPasswordModel.searchAddress.observe(this) { list ->
            countrycodeXList.addAll(list.countrycode)
            list.countrycode.forEach {
                strList.add(it.name)
            }
            list.countrycode.getOrNull(0)?.let {
                binding.tvCountryCode.text = it.countrycode
            }
        }

        forgotPasswordModel.msgRes.observe(this) {
            ToastUtils.showLong(it.message)
            val intent = Intent(this, ForgotPasswordVerifyCodeActivity::class.java)
            if (binding.etEmail.visibility == View.VISIBLE) {
                intent.putExtra("email", email)
            } else {
                intent.putExtra("phone", phone)
                intent.putExtra("countryCode", countryCode)
            }
            startActivity(intent)
        }

        forgotPasswordModel.loadingLiveData.observe(this){
            dismissLoadingDialog()
        }

        showLoadingDialog()
        forgotPasswordModel.getInitCountryCode()
    }


    private fun initViews() {
        binding.ivClose.setOnClickListener {
            finish()
        }

        binding.tvCountryCode.setOnClickListener {
            XPopup.Builder(this)
                .asBottomList(
                    "", strList.toTypedArray()
                ) { position, text ->
                    binding.tvCountryCode.text = countrycodeXList[position].countrycode
                }
                .show()
        }

        binding.llEmail.setOnClickListener {

            binding.vLineEmail.setBackgroundResource(R.color.app_main_color)
            binding.vLinePhone.setBackgroundResource(R.color.color_cccccc)
            binding.tvEmail.setTextColor(resources.getColor(R.color.app_main_color))
            binding.tvPhone.setTextColor(resources.getColor(R.color.color_black))

            binding.etEmail.visibility = View.VISIBLE
            binding.llInputPhone.visibility = View.GONE
        }

        binding.llPhone.setOnClickListener {
            binding.vLinePhone.setBackgroundResource(R.color.app_main_color)
            binding.vLineEmail.setBackgroundResource(R.color.color_cccccc)
            binding.tvEmail.setTextColor(resources.getColor(R.color.color_black))
            binding.tvPhone.setTextColor(resources.getColor(R.color.app_main_color))

            binding.etEmail.visibility = View.GONE
            binding.llInputPhone.visibility = View.VISIBLE
        }


        binding.tvSendCode.setOnClickListener {
            showLoadingDialog()
            if (binding.etEmail.visibility == View.VISIBLE) {
//                email login
                email = binding.etEmail.text.toString().trim()

                phone = null
                countryCode = null

                forgotPasswordModel.resetCode(
                    email = email!!
                )
            } else {
//                phone login
                phone = binding.etPhone.text.toString().trim()
                countryCode = binding.tvCountryCode.text.toString().trim()

                email = null

                forgotPasswordModel.resetCode(
                    phoneNumber = phone!!, countryCode = countryCode!!
                )
            }


        }


        restoreLastLoginUserName(binding)
    }


}