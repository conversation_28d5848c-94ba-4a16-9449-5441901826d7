package com.housesigma.android.ui.forgot

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.housesigma.android.base.BaseViewModel
import com.housesigma.android.model.CountryCode
import com.housesigma.android.model.GoogleSignRes
import com.housesigma.android.model.MsgRes
import com.housesigma.android.model.SignIn
import com.housesigma.android.network.NetClient
import com.housesigma.android.network.launch

class ForgotPasswordModel : BaseViewModel() {

    var searchAddress: MutableLiveData<CountryCode> = MutableLiveData()
    var msgRes: MutableLiveData<MsgRes> = MutableLiveData()


    fun resetCode(
        email: String = "",
        phoneNumber: String = "",
        countryCode: String = "",
    ) {
        launch({
            NetClient.apiService.resetCode(
                email = email,
                phonenumber = phoneNumber, countrycode = countryCode,
            )
        }, {
            msgRes.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun resetPassword(
        email: String = "",
        phoneNumber: String = "",
        countryCode: String = "",
        pass: String = "",
        code:String = ""
    ) {
        launch({
            NetClient.apiService.resetPassword(
                email = email,
                phonenumber = phoneNumber, countrycode = countryCode,
                pass = pass,
                code = code
            )
        }, {
            msgRes.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun getInitCountryCode() {
        launch({
            NetClient.apiService.getInitCountryCode()
        }, {
            searchAddress.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }


}