package com.housesigma.android.ui.forgot

import android.text.TextUtils
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityForgotPasswordVerifyCodeBinding
import com.housesigma.android.utils.AppManager
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.views.verifycodelib.VerifyCodeCompleteListener


class ForgotPasswordVerifyCodeActivity : BaseActivity() {
    private lateinit var forgotPasswordModel: ForgotPasswordModel
    private lateinit var binding: ActivityForgotPasswordVerifyCodeBinding

    private var code: String = ""
    private var phone: String? = null
    private var countryCode: String? = null
    private var email: String? = null

    override fun getLayout(): Any {
        phone = intent.getStringExtra("phone") ?: ""
        countryCode = intent.getStringExtra("countryCode") ?: ""
        email = intent.getStringExtra("email") ?: ""


        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        forgotPasswordModel = ViewModelProvider(this).get(ForgotPasswordModel::class.java)
        binding = ActivityForgotPasswordVerifyCodeBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        initViews()
    }

    override fun initData() {
    }

    private fun initViews() {
        binding.ivClose.setOnClickListener {
            finish()
        }

        if (email.isNullOrEmpty()) {
            binding.tvSendTo.text =
                "A verification code has been sent to $phone, please enter it below"
        } else {
            binding.tvSendTo.text =
                "A verification code has been sent to $email, please enter it below"
        }

        forgotPasswordModel.msgRes.observe(this) {
            ToastUtils.showLong(it.message)
            AppManager.getManager().finishActivity(ForgotPasswordActivity::class.java)
            finish()
        }

        forgotPasswordModel.loadingLiveData.observe(this) {
            dismissLoadingDialog()
        }



        binding.tvSubmit.setOnClickListener {
            GALog.log("forgot_password_submit")
            val password = binding.etPassword.text.trim().toString()
            showLoadingDialog()
            if (TextUtils.isEmpty(email)) {
                // phone register
                forgotPasswordModel.resetPassword(
                    code = code,
                    phoneNumber = phone!!,
                    pass = password!!,
                    countryCode = countryCode!!,
                )
            } else {
                // email register
                forgotPasswordModel.resetPassword(
                    code = code,
                    email = email!!,
                    pass = password!!,
                )
            }
        }
        binding.verifyCodeView.setCompleteListener(object :
            VerifyCodeCompleteListener {
            override fun verifyCodeComplete() {
                code = binding.verifyCodeView.getText()
            }

        })

    }

}