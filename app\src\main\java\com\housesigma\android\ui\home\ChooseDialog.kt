package com.housesigma.android.ui.home

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Window
import android.view.WindowManager
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.housesigma.android.R
import com.housesigma.android.databinding.DialogLocationChooseBinding
import com.housesigma.android.model.InitApp
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.MMKVUtils


class ChooseDialog(context: FragmentActivity, cb: ChooseLocationCallback) : Dialog(context) {

    private var mContext: FragmentActivity = context
    private var mCallback: ChooseLocationCallback = cb

    interface ChooseLocationCallback {
        fun onSuccess(location: String)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        this.window?.setBackgroundDrawableResource(android.R.color.transparent)
        this.window?.requestFeature(Window.FEATURE_NO_TITLE)
        val binding = DialogLocationChooseBinding.inflate(layoutInflater)
        setContentView(binding.root)
        this.window?.setBackgroundDrawable(ColorDrawable(0x00000000))
        this.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT
        )
        val initApp = HSUtil.getInitApp()
        if (initApp == null) return
        val adapter = ChooseProvinceAdapter()
        binding.rv.layoutManager =
            LinearLayoutManager(this.mContext, RecyclerView.VERTICAL, false)
        binding.rv.adapter = adapter
        adapter.addData(initApp.provinces)
        adapter.notifyDataSetChanged()

        adapter.setOnItemChildClickListener { adapter, view, position ->

            when (view.id) {
                R.id.ll -> {
                    val id = initApp.provinces.get(position).id
                    MMKVUtils.saveStr(LoginFragment.PROVINCE,id )
                    mCallback.onSuccess(id)
                    dismiss()
                }
            }
        }
    }


}