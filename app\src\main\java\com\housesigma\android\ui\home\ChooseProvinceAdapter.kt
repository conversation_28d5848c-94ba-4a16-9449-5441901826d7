package com.housesigma.android.ui.home

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.Province

class ChooseProvinceAdapter :
    BaseQuickAdapter<Province, BaseViewHolder>(R.layout.item_choose_province) {

    init {
        addChildClickViewIds(
            R.id.ll
        )
    }
    override fun convert(holder: BaseViewHolder, item: Province) {
        holder.setText(R.id.tv_province_name, item.name)
    }
}