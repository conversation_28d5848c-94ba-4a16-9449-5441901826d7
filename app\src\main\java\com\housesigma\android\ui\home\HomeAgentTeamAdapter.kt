package com.housesigma.android.ui.home

import android.widget.ImageView
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.HomePageAgentModel


class HomeAgentTeamAdapter :
    BaseQuickAdapter<HomePageAgentModel, BaseViewHolder>(R.layout.item_agent_team) {

    init {
        addChildClickViewIds(
            R.id.ll_agent_root,
        )
    }

    override fun convert(holder: BaseViewHolder, item: HomePageAgentModel) {
        holder.setText(R.id.tv_agent_name, item.name ?: "")
        holder.setText(R.id.tv_designation, item.designation ?: "")
        val ivAvatar = holder.getView<ImageView>(R.id.iv_avatar)
        Glide.with(context)
            .load(item.avatar ?: "")
            .circleCrop()
            .into(ivAvatar)
    }


}