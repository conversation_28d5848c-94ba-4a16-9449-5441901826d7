package com.housesigma.android.ui.home

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.widget.TextViewCompat
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.AbsSuperApplication
import com.housesigma.android.BuildConfig
import com.housesigma.android.R
import com.housesigma.android.base.BaseHomeFragment
import com.housesigma.android.base.BaseListingsAdapterHelper
import com.housesigma.android.databinding.FragmentHomeBinding
import com.housesigma.android.databinding.ItemCommuityFooterBinding
import com.housesigma.android.helper.JumpHelper
import com.housesigma.android.helper.ProvinceHelper
import com.housesigma.android.model.AgentInfoHomePage
import com.housesigma.android.model.HomePageAgentModel
import com.housesigma.android.model.HouseDetail
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.Pagination
import com.housesigma.android.model.RecommendListingType
import com.housesigma.android.ui.account.AboutActivity
import com.housesigma.android.ui.account.AccountViewModel
import com.housesigma.android.ui.account.FeedbackActivity
import com.housesigma.android.ui.listing.ListingDotMenuDialog
import com.housesigma.android.ui.listing.ListingModel
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.main.MainActivity
import com.housesigma.android.ui.main.PersonalizeListingsActivity
import com.housesigma.android.ui.map.helper.MapHelper
import com.housesigma.android.ui.notinterested.NotInterestedHelper
import com.housesigma.android.ui.onboard.OnBoardActivity
import com.housesigma.android.ui.search.SearchActivity
import com.housesigma.android.ui.tos.TosDialog
import com.housesigma.android.ui.tos.VowTosDialog
import com.housesigma.android.ui.watched.WatchedViewModel
import com.housesigma.android.ui.webview.WebViewActivity
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.Callback1
import com.housesigma.android.utils.Constants
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.GsonUtils
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.LanguageUtils
import com.housesigma.android.utils.LiveChatUtil
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.MapUtils
import com.housesigma.android.utils.ScreenUtils
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.CombinedChartManager
import com.housesigma.android.views.DisclaimerViewHelper
import com.housesigma.android.views.HSAlertDialog
import com.youth.banner.indicator.CircleIndicator
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.Locale


class HomeFragment : BaseHomeFragment(), LoginFragment.LoginCallback {

    private lateinit var binding: FragmentHomeBinding
    private lateinit var homeViewModel: HomeViewModel
    private lateinit var watchedViewModel: WatchedViewModel
    private lateinit var accountViewModel: AccountViewModel
    private lateinit var listingViewModel: ListingModel

    private lateinit var chooseDialog: ChooseDialog
    private var moreBtnPre: Boolean = false
    private var loginDialog: LoginFragment? = null

    private var isFirstStart: Boolean = false
    private var isFirstResume: Boolean = true
    private var requestAgentInfoHomePageFlag: Boolean = false
    private var recommendForYouPage: Int = 1

    private fun initData() {

        accountViewModel.updateLangMsgRes.observe(this) {
            reStartActivity()
        }

        homeViewModel.agentInfoHomePage.observe(this) {
            handleAgentInfoHomePage(it)
        }

        homeViewModel.mapFilter.observe(this) {
            val mapFilterJson = GsonUtils.parseToStr(it)
            if (mapFilterJson != null) {
                MMKVUtils.saveStr(MapHelper.MAP_FILTER_TAG, mapFilterJson)
            }
        }



        homeViewModel.initApp.observe(this) {
            if (isFirstStart) return@observe
            val that = it
            if (it.update) {
                val leftBtn = if (that.force_update) "" else "Later"
                activity?.let {
                    val hsAlertDialog = HSAlertDialog(
                        requireActivity(),
                        "HouseSigma update",
                        that?.update_message
                            ?: "There is a new version of HouseSigma, please update now.",
                        leftBtn,
                        "Update",
                        autoDismiss = !that.force_update,
                        cb = object : HSAlertDialog.HSAlertCallback {
                            override fun onSuccess() {
                                activity?.let { activity ->
                                    WebViewHelper.jumpOuterWebView(activity,that?.update_url ?: "")
                                }
                            }
                        })
                    hsAlertDialog.setCancelable(false)
                    hsAlertDialog.show()
                }
            }
            isFirstStart = true
        }

        accountViewModel.checkToken.observe(viewLifecycleOwner) {
            Logger.d("accountViewModel.checkToken.observe...." + it.appUser)
            // 用外层的省份字段，user里的省份只是在登录后才存在
//            checkToken api 调用后，省份相关视图都需要刷新
            initTopView()
            adjustToolViewOrder()

            activity?.let { DisclaimerViewHelper.handleDisclaimer(it, binding.tvDisclaimer) }

            homeViewModel.initApp()
            homeViewModel.getHomePage()
            homeViewModel.getHomeRecommendList(1)
            homeViewModel.getHomeRecommendList(2)
            homeViewModel.getHomeRecommendList(3)
            homeViewModel.getHomeRecommendList(4)
            homeViewModel.getHomeRecommendList(5)
            homeViewModel.getHomeRecommendList(6)
            homeViewModel.getHomeRecommendList(7)
            homeViewModel.getHomeRecommendList(8)
            homeViewModel.getHomeRecommendList(9)
            homeViewModel.getHomeRecommendList(10)
            homeViewModel.getHomeRecommendList(11)
            homeViewModel.getMapFilter()


            if (it.appUser == null) {
                if (activity != null) {
                    try {
                        LoginFragment.loginOut(requireActivity())
                    } catch (exception: Exception) {
                        exception.printStackTrace()
                    }

                    val layoutParams = binding.ivSearch.layoutParams as LinearLayout.LayoutParams
                    layoutParams.rightMargin = ScreenUtils.dpToPx(16f).toInt()
                    binding.ivSearch.layoutParams = layoutParams
                    binding.ivLiveChat.visibility = View.GONE
                }
            }else{
                watchedViewModel.watchlistListingIds()
                if (it.appUser?.display_live_chat==1) {
                    val layoutParams = binding.ivSearch.layoutParams as LinearLayout.LayoutParams
                    layoutParams.rightMargin = 0
                    binding.ivSearch.layoutParams = layoutParams
                    binding.ivLiveChat.visibility = View.VISIBLE

                    binding.ivLiveChat.setOnClickListener {
                        LiveChatUtil.startActivity(requireContext())
                    }
                } else {
                    val layoutParams = binding.ivSearch.layoutParams as LinearLayout.LayoutParams
                    layoutParams.rightMargin = ScreenUtils.dpToPx(16f).toInt()
                    binding.ivSearch.layoutParams = layoutParams
                    binding.ivLiveChat.visibility = View.GONE
                }
            }
        }

        accountViewModel.saveMsgRes.observe(viewLifecycleOwner) {
            Logger.d("chooseDialog saveMsgRes observe....")
            hiddenAgentTeamView()
            initTopView()
            adjustToolViewOrder()
            loadData()
            refreshAgentInfoViewLazyLoad()
            EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_MARKET))
        }

        homeViewModel.homePage.observe(viewLifecycleOwner) {
            binding.tvTip.text = it.bottom_slogan ?: ""
            binding.tvEchartTitle.text = it.chart_title ?: ""
            binding.tvChartSource.text = it.chart_tips ?: ""
            binding.tvChartSource.text = it.chart_tips ?: ""
            binding.tvChartSubtitle.text = it.chart_subtitle ?: ""

            binding.banner.adapter =
                activity?.let { it1 -> MessageBannerAdapter(it.notification.native, it1) }


            //x轴数据
            val xData: MutableList<String> = ArrayList()
            for (i in 0..6) {
                xData.add(i.toString())
            }
            //y轴数据集合
            val yBarDatas: MutableList<Float> = ArrayList()
            val yLineDatas: MutableList<Float> = ArrayList()
            val timeDatas: MutableList<String> = ArrayList()

            it.chart?.let { chartData->
                for (chart in it.chart) {
                    yBarDatas.add(chart.sold_count?.toFloatOrNull() ?: 0.0f)//Media Price
                    yLineDatas.add(chart.price_sold?.toFloatOrNull() ?: 0.0f)//Total sold
                    timeDatas.add(chart.period)//date
                }
                val combinedChartManager = CombinedChartManager(binding.chart, activity)
                combinedChartManager.setData(timeDatas)
                combinedChartManager.showHomeCombinedChart(xData, yBarDatas, yLineDatas)
                combinedChartManager.setCallback(object : Callback1 {
                    override fun onData(any: Any) {
                        val index = any as Int
                        if (index > it.chart.size || index == -1) {
                            binding.cardView.visibility = View.GONE
                            return
                        }
                        val chart = it.chart[index]
                        binding.tvChartDate.text = chart.period
                        binding.tvChartMediaPrice.text =
                            "$" + HSUtil.formatNumberThousandsSeparators(
                                chart.price_sold?.toIntOrNull() ?: 0
                            )
                        binding.tvChartTotalSold.text =
                            HSUtil.formatNumberThousandsSeparators(chart.sold_count?.toIntOrNull() ?: 0)
                        binding.cardView.visibility = View.VISIBLE
                    }
                })
            }

            // DEV-6015 chart 数据为 null或数组为空 时，显示 "Data Not Available"，并缩减占用高度。
            if (it.chart == null || it.chart.size==0) {
                binding.tvChartNoData.visibility = View.VISIBLE
                binding.chart.visibility = View.GONE
            }else{
                binding.tvChartNoData.visibility = View.GONE
                binding.chart.visibility = View.VISIBLE
            }

            Logger.d("show onboard : " + it.show_onboard)
            // 1为显示onboard页面，直接跳转过去就行
            if (it.show_onboard == 1) {
//                if (AntiShake.check("show_onboard", 1500)) {
//                    return@observe
//                }
                startActivity(Intent(activity, OnBoardActivity::class.java))
            }

        }
    }

    private fun hiddenAgentTeamView() {
        binding.rvAgentTeam.visibility = View.GONE
        binding.rlAgentTeamHeader.visibility = View.GONE
    }

    private fun showAgentTeamView() {
        binding.rvAgentTeam.visibility = View.VISIBLE
        binding.rlAgentTeamHeader.visibility = View.VISIBLE
    }

    private fun handleAgentInfoHomePage(agentInfoHomePage: AgentInfoHomePage) {
        if (agentInfoHomePage.agents == null || agentInfoHomePage.agents.size==0) {
            hiddenAgentTeamView()
            return
        }
        showAgentTeamView()
        binding.tvAgentTeamTitle.text = agentInfoHomePage.title?:""
        binding.tvAgentTeamSeeMore.setOnClickListener {
            GALog.log("agent_team_see_more_click")
            activity?.let { it1 -> WebViewHelper.jumpAgents(it1) }
        }
        binding.rvAgentTeam.layoutManager =
            GridLayoutManager(activity, 2)
        val homeAgentTeamAdapter = HomeAgentTeamAdapter()
        binding.rvAgentTeam.adapter = homeAgentTeamAdapter
        homeAgentTeamAdapter.setOnItemChildClickListener { adapter, view, position ->
            when (view.id) {
                R.id.ll_agent_root -> {
                    val item = adapter.getItem(position) as HomePageAgentModel
                    GALog.log("agent_avatar_click")
                    item.slug?.let { slug ->
                        activity?.let { cxt -> WebViewHelper.jumpAgentDetail(cxt, slug) }
                    }
                }
            }
        }
        homeAgentTeamAdapter.data.clear()
        homeAgentTeamAdapter.addData(agentInfoHomePage.agents)
        homeAgentTeamAdapter.notifyDataSetChanged()
    }

    override fun initImmersionBar() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
            titleBar(binding.vTop)
        }
    }

    override fun createView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        homeViewModel = ViewModelProvider(this).get(HomeViewModel::class.java)
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        accountViewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        listingViewModel = ViewModelProvider(this).get(ListingModel::class.java)

        binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView(root: View?) {
    }

    override fun initListener() {
    }

    override fun tag(): String {
        return "homepage"
    }

    override fun lazyLoad() {
        initViews()
        initData()
        loadData()
    }

    override fun refreshLoad() {
        super.refreshLoad()
        Logger.d("refreshLoad....")
        loadData()
    }


    override fun onVisibilityChanged(visible: Boolean) {
        super.onFragmentVisibilityChanged(visible)
        if (visible) {
            refreshAgentInfoViewLazyLoad()
            GALog.page("homepage")
        }
    }

    /**
     * 重置AgentInfoView的状态并使用懒加载的方式加载控件
     */
    private fun refreshAgentInfoViewLazyLoad() {
        requestAgentInfoHomePageFlag = false
        binding.llBottom.post {
            lazyLoadAgentInfoView()
        }
    }

    private fun lazyLoadAgentInfoView() {
        val llBottomVisibilityPercents = ScreenUtils.getVisibilityPercents(binding.llBottom)
        val rvAgentTeamVisibilityPercent = ScreenUtils.getVisibilityPercents(binding.rvAgentTeam)
        if (llBottomVisibilityPercents != 0 || rvAgentTeamVisibilityPercent!=0) {
            requestAgentInfoHomePageFlag = true
            homeViewModel.getAgentInfoHomePage()
        }
    }

    override fun onStart() {
        super.onStart()
        if (isFirstResume) {
            isFirstResume = !isFirstResume
        } else {
            Logger.d("onStart....")
            loadData()
        }
    }

    private fun loadData() {
        recommendForYouPage = 1
        val accessToken = MMKVUtils.getStr(LoginFragment.LOGIN_TOKEN)
        if (!TextUtils.isEmpty(accessToken)) {
            accountViewModel.checkAccessToken(accessToken!!)
        }
    }

    private fun initViews() {

        setHSScrollViewChangeListener()

        //处理最下面布局
        initBottomView()

        //处理最上面布局 location部分
        initTopView()

        //处理消息条
        initMessageBanner()

        //处理更多按钮
        initMoreBtn()

        // 搜索栏下面的工具按钮Estimate啥的。。
        initToolBtn()


        //初始化logo，中文英文
        initLogo()

        //调整推荐列表顺序
        initRecommendOrder()

        // 如果是BC省，需要调整卡片高度
        adjustCardViewHeight()


        //适配字体大小
        TextViewCompat.setAutoSizeTextTypeWithDefaults(
            binding.tvSaleSold,
            TextViewCompat.AUTO_SIZE_TEXT_TYPE_UNIFORM
        )
        TextViewCompat.setAutoSizeTextTypeWithDefaults(
            binding.tvRecommend,
            TextViewCompat.AUTO_SIZE_TEXT_TYPE_UNIFORM
        )

        binding.tvEnglish.setOnClickListener {
            changeLang()
        }

        binding.tvViewMoreStats.setOnClickListener {
            GALog.log("more_stats_click")
            EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_MARKET))
            (activity as MainActivity).jumpToMarket()
        }


        binding.refreshLayout.setOnRefreshListener { refreshLayout ->
            refreshLayout.finishRefresh(2000)
            loadData()
        }

        val adapter1 = HomeListingsAdapter(1)
        binding.rvHomeJustSold.adapter = adapter1
        val adapter2 = HomeListingsAdapter(2)
        binding.rvBestForRentalInvestment.adapter = adapter2
        val adapter3 = HomeListingsAdapter(3)
        binding.rvHomeBestForSchool.adapter = adapter3
        val adapter4 = HomeListingsAdapter(4)
        binding.rvHome1yearPriceGrowth.adapter = adapter4
        val adapter5 = HomeListingsAdapter(5)
        binding.rvHomeSoldBelowBought.adapter = adapter5
        val adapter6 = WatchedAreaHomeAdapter()
        binding.rvHomeWatchedCommunityUpdates.adapter = adapter6
        val adapter7 = HomeListingsAdapter(7)
        binding.rvHomeFeaturedListings.adapter = adapter7
        val adapter8 = HomeListingsAdapter(8)
        binding.rvHomeHighReturnsType.adapter = adapter8
        val adapter9 = HomeListingsAdapter(9)
        binding.rvHomeNewlyListed.adapter = adapter9
        val adapter10 = WatchedAreaHomeAdapter()
        binding.rvHomeRecommendForYou.adapter = adapter10
        val adapter11 = WatchedAreaHomeAdapter()
        binding.rvHomeExclusivePreconAssignment.adapter = adapter11

        homeViewModel.recommend1.observe(viewLifecycleOwner) {
            setFilterIsDefault(it.filter_is_default)
            bindViews(
                adapter1,
                it.list,
                binding.llHomeJustSold,
                binding.tvSeeMoreJustSold,
                it.type,
                it.pagination)
        }
        homeViewModel.recommend2.observe(viewLifecycleOwner) {
            bindViews(
                adapter2,
                it.list,
                binding.llBestForRentalInvestment,
                binding.tvSeeMoreBestForRentalInvestment,
                it.type,
                it.pagination
            )
        }
        homeViewModel.recommend3.observe(viewLifecycleOwner) {
            bindViews(
                adapter3,
                it.list,
                binding.llHomeBestForSchool,
                binding.tvSeeMoreBestForSchool,
                it.type,
                it.pagination
            )
        }
        homeViewModel.recommend4.observe(viewLifecycleOwner) {
            bindViews(
                adapter4,
                it.list,
                binding.llHome1yearPriceGrowth,
                binding.tvSeeMore1yearPriceGrowth,
                it.type,
                it.pagination
            )
        }
        homeViewModel.recommend5.observe(viewLifecycleOwner) {
            bindViews(
                adapter5,
                it.list,
                binding.llHomeSoldBelowBought,
                binding.tvSeeMoreSoldBelowBought,
                it.type,
                it.pagination
            )
        }
        homeViewModel.recommend6.observe(viewLifecycleOwner) {
            bindViews(
                adapter6,
                it.list,
                binding.llWatchedCommunityUpdates,
                binding.tvSeeMoreWatchedCommunityUpdates,
                it.type,
                it.pagination
            )
        }
        homeViewModel.recommend7.observe(viewLifecycleOwner) {
            bindViews(
                adapter7,
                it.list,
                binding.llHomeFeaturedListings,
                binding.tvSeeMoreFeaturedListings,
                it.type,
                it.pagination
            )
        }
        homeViewModel.recommend8.observe(viewLifecycleOwner) {
            bindViews(
                adapter8,
                it.list,
                binding.llHomeHighReturnsType,
                binding.tvSeeMoreHighReturnsType,
                it.type,
                it.pagination
            )
        }
        homeViewModel.recommend9.observe(viewLifecycleOwner) {
            bindViews(
                adapter9,
                it.list,
                binding.llHomeNewlyListed,
                binding.tvSeeMoreNewlyListed,
                it.type,
                it.pagination
            )
        }
        homeViewModel.recommend10.observe(viewLifecycleOwner) {
            adapter10.setRecommendForYou(true)
            bindViews(
                adapter10,
                it.list,
                binding.llRecommendForYou,
                binding.tvSeeMoreRecommendForYou,
                it.type,
                it.pagination
            )
        }

        homeViewModel.recommend11.observe(viewLifecycleOwner) {
            bindViews(
                adapter11,
                it.list,
                binding.llHomeExclusivePreconAssignment,
                binding.tvSeeMoreExclusivePreconAssignment,
                it.type,
                it.pagination
            )
        }


        binding.rvHomeExclusivePreconAssignment.layoutManager =
            LinearLayoutManager(activity, RecyclerView.HORIZONTAL, false)

        binding.rvHomeWatchedCommunityUpdates.layoutManager =
            LinearLayoutManager(activity, RecyclerView.HORIZONTAL, false)

        binding.rvBestForRentalInvestment.layoutManager =
            LinearLayoutManager(activity, RecyclerView.HORIZONTAL, false)

        binding.rvHome1yearPriceGrowth.layoutManager =
            LinearLayoutManager(activity, RecyclerView.HORIZONTAL, false)

        binding.rvHomeBestForSchool.layoutManager =
            LinearLayoutManager(activity, RecyclerView.HORIZONTAL, false)

        binding.rvHomeFeaturedListings.layoutManager =
            LinearLayoutManager(activity, RecyclerView.HORIZONTAL, false)

        binding.rvHomeJustSold.layoutManager =
            LinearLayoutManager(activity, RecyclerView.HORIZONTAL, false)

        binding.rvHomeSoldBelowBought.layoutManager =
            LinearLayoutManager(activity, RecyclerView.HORIZONTAL, false)

        binding.rvHomeHighReturnsType.layoutManager =
            LinearLayoutManager(activity, RecyclerView.HORIZONTAL, false)

        binding.rvHomeNewlyListed.layoutManager =
            LinearLayoutManager(activity, RecyclerView.HORIZONTAL, false)

        binding.rvHomeRecommendForYou.layoutManager =
            LinearLayoutManager(activity, RecyclerView.HORIZONTAL, false)

        binding.llLocationChoose.setOnClickListener {
            GALog.log("province_select_click")
            showChooseDialog()
        }

        binding.rlCustomize.setOnClickListener {
            GALog.log("preferences_start")
            if (LoginFragment.isLogin()) {
                startActivity(Intent(activity, PersonalizeListingsActivity::class.java))
            } else {
                showLoginDialog()
            }

        }

        binding.ivSearch.setOnClickListener {
            GALog.log("search_start")
            AbsSuperApplication.finishActivity(SearchActivity::class.java)
            startActivity(Intent(activity, SearchActivity::class.java))
        }


    }

    private fun setHSScrollViewChangeListener() {
        binding.hsScrollView.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            if (requestAgentInfoHomePageFlag) return@setOnScrollChangeListener
            lazyLoadAgentInfoView()
        }
    }

    private fun adjustCardViewHeight() {
        binding.llRecommendRoot.postDelayed({
            binding.llRecommendRoot.forceLayout()
            binding.llRecommendRoot.requestLayout()

            binding.llWatchedCommunityUpdates.forceLayout()
            binding.llWatchedCommunityUpdates.requestLayout()
        },200)
    }

    //        对于未登录的用户，把这些放在上方：
//        Best For Rental Investment
//        Best For School
//        1 Year Price Growth
    private fun initRecommendOrder() {

        binding.llRecommendRoot.removeAllViews()
        if (LoginFragment.isLogin()) {
            binding.llRecommendRoot.addView(binding.llHomeExclusivePreconAssignment)
            binding.llRecommendRoot.addView(binding.rvHomeExclusivePreconAssignment)

            binding.llRecommendRoot.addView(binding.llHomeSoldBelowBought)
            binding.llRecommendRoot.addView(binding.rvHomeSoldBelowBought)

            binding.llRecommendRoot.addView(binding.llHomeHighReturnsType)
            binding.llRecommendRoot.addView(binding.rvHomeHighReturnsType)

            binding.llRecommendRoot.addView(binding.llHomeFeaturedListings)
            binding.llRecommendRoot.addView(binding.rvHomeFeaturedListings)

            binding.llRecommendRoot.addView(binding.llHomeJustSold)
            binding.llRecommendRoot.addView(binding.rvHomeJustSold)

            binding.llRecommendRoot.addView(binding.llHomeNewlyListed)
            binding.llRecommendRoot.addView(binding.rvHomeNewlyListed)

            binding.llRecommendRoot.addView(binding.llBestForRentalInvestment)
            binding.llRecommendRoot.addView(binding.rvBestForRentalInvestment)

            binding.llRecommendRoot.addView(binding.llHomeBestForSchool)
            binding.llRecommendRoot.addView(binding.rvHomeBestForSchool)

            binding.llRecommendRoot.addView(binding.llHome1yearPriceGrowth)
            binding.llRecommendRoot.addView(binding.rvHome1yearPriceGrowth)


        } else {
            binding.llRecommendRoot.addView(binding.llHomeExclusivePreconAssignment)
            binding.llRecommendRoot.addView(binding.rvHomeExclusivePreconAssignment)

            binding.llRecommendRoot.addView(binding.llBestForRentalInvestment)
            binding.llRecommendRoot.addView(binding.rvBestForRentalInvestment)

            binding.llRecommendRoot.addView(binding.llHomeBestForSchool)
            binding.llRecommendRoot.addView(binding.rvHomeBestForSchool)

            binding.llRecommendRoot.addView(binding.llHome1yearPriceGrowth)
            binding.llRecommendRoot.addView(binding.rvHome1yearPriceGrowth)

            binding.llRecommendRoot.addView(binding.llHomeSoldBelowBought)
            binding.llRecommendRoot.addView(binding.rvHomeSoldBelowBought)

            binding.llRecommendRoot.addView(binding.llHomeHighReturnsType)
            binding.llRecommendRoot.addView(binding.rvHomeHighReturnsType)

            binding.llRecommendRoot.addView(binding.llHomeFeaturedListings)
            binding.llRecommendRoot.addView(binding.rvHomeFeaturedListings)

            binding.llRecommendRoot.addView(binding.llHomeJustSold)
            binding.llRecommendRoot.addView(binding.rvHomeJustSold)

            binding.llRecommendRoot.addView(binding.llHomeNewlyListed)
            binding.llRecommendRoot.addView(binding.rvHomeNewlyListed)
        }
    }

    private fun changeLang() {
        val tvEnglishStr = binding.tvEnglish.text.toString().trim()
        if ("English".equals(tvEnglishStr)) {
            MMKVUtils.saveStr(LoginFragment.LANG, "en_US")
            MMKVUtils.saveInt("multilingual", 2)
            if (LoginFragment.isLogin()) {
                accountViewModel.setLang("en_US")
            } else {
                reStartActivity()
            }
        } else if ("中文".equals(tvEnglishStr)) {
            MMKVUtils.saveStr(LoginFragment.LANG, "zh_CN")
            MMKVUtils.saveInt("multilingual", 1)

            if (LoginFragment.isLogin()) {
                accountViewModel.setLang("zh_CN")
            } else {
                reStartActivity()
            }
        }
    }

    private fun initLogo() {
        if (LanguageUtils().getAppLocale() == Locale.ENGLISH) {
            binding.ivLogo.setBackgroundResource(R.drawable.ic_home_logo_2)
        } else {
            binding.ivLogo.setBackgroundResource(R.drawable.ic_logo_cn)
        }
    }

    /**
     * 显示定制红点
     */
    private fun setFilterIsDefault(filterIsDefault: Boolean) {
//        val labelFilterCity = MMKVUtils.getStr("label_filter_city")
//        val isPersonalizePrice = MMKVUtils.getBoolean("personalize_price", false)
        if (filterIsDefault) {
            binding.ivPersonalizeChanged.setBackgroundResource(R.drawable.ic_customize)
            binding.rlCustomize.setBackgroundResource(R.drawable.shape_10radius_main_color)
            binding.tvPersonalizeListing.setTextColor(resources.getColor(R.color.app_main_color))
        } else {
            binding.ivPersonalizeChanged.setBackgroundResource(R.drawable.ic_customize_changed)
            binding.rlCustomize.setBackgroundResource(R.drawable.shape_10radius_main_color_fill)
            binding.tvPersonalizeListing.setTextColor(resources.getColor(R.color.color_white))
        }
    }

    private fun adjustToolViewOrder(){
        if (ProvinceHelper.getAbbreviationFromCache("ON")=="ON"){
            binding.llBtnLine1.removeAllViews()
            binding.llBtnLine2.removeAllViews()

            binding.llBtnLine1.addView(binding.llSale)
            binding.llBtnLine1.addView(binding.llPrecon)
            binding.llBtnLine1.addView(binding.llRental)
            binding.llBtnLine1.addView(binding.llBtnMore)

            binding.llBtnLine2.addView(binding.llEstimate)
            binding.llBtnLine2.addView(binding.llRecommend)
            binding.llBtnLine2.addView(binding.llReports)
            binding.llBtnLine2.addView(binding.llAgents)
            binding.viewPlaceholder.layoutParams = LinearLayout.LayoutParams(
                0,
                LinearLayout.LayoutParams.MATCH_PARENT, 0f
            )
            binding.llBtnLine2.addView(binding.viewPlaceholder)
        }else {
            binding.llBtnLine1.removeAllViews()
            binding.llBtnLine2.removeAllViews()

            binding.llBtnLine1.addView(binding.llSale)
//            binding.llBtnLine1.addView(binding.llPrecon)
            binding.llBtnLine1.addView(binding.llRental)
            binding.llBtnLine1.addView(binding.llEstimate)
            binding.llBtnLine1.addView(binding.llBtnMore)

            binding.llBtnLine2.addView(binding.llRecommend)
            binding.llBtnLine2.addView(binding.llReports)
            binding.llBtnLine2.addView(binding.llAgents)
            binding.viewPlaceholder.layoutParams = LinearLayout.LayoutParams(
                0,
                LinearLayout.LayoutParams.MATCH_PARENT, 1f
            )
            binding.llBtnLine2.addView(binding.viewPlaceholder)
        }


    }

    private fun initToolBtn() {
        adjustToolViewOrder()

        binding.llBtnMore.setOnClickListener {
            GALog.log("menu_click", "more")
            moreBtnPre = !moreBtnPre
            if (moreBtnPre) {
                binding.tvMoreLess.text = resources.getString(R.string.home_btn_less)
                binding.ivMore.setBackgroundResource(R.drawable.ic_home_more_pre)
                binding.llBtnLine2.visibility = View.VISIBLE
            } else {
                binding.tvMoreLess.text = resources.getString(R.string.home_btn_more)
                binding.ivMore.setBackgroundResource(R.drawable.ic_home_more)
                binding.llBtnLine2.visibility = View.GONE
            }

        }

        binding.llEstimate.setOnClickListener {
            GALog.log("menu_click", "valuation")
            activity?.let {
                WebViewHelper.jumpEstimate(it, null)
            }
        }

        binding.llReports.setOnClickListener {
            GALog.log("menu_click", "blog")
            activity?.let {
                WebViewHelper.jumpReports(it)
            }
        }

        binding.llAgents.setOnClickListener {
            GALog.log("menu_click", "agents")
            activity?.let {
                WebViewHelper.jumpAgents(it)
            }
        }

        binding.llRecommend.setOnClickListener {
            GALog.log("menu_click", "recommend")
            activity?.let {
                JumpHelper.jumpRecommendStartActivity(it)
            }
        }

        binding.llSale.setOnClickListener {
            GALog.log("menu_click", "sale_sold")
            activity?.let {
                val firstShowWatchHint = MMKVUtils.getBoolean("first_show_watch_hint", true)
                MMKVUtils.saveBoolean("first_show_watch_hint", false)
                JumpHelper.jumpMapForSaleActivity(it, isShowWatchHint = firstShowWatchHint)
            }
        }

        binding.llPrecon.setOnClickListener {
            GALog.log("menu_click", "precon")
            activity?.let {
                JumpHelper.jumpPreconMapForSaleActivity(it)
            }
        }

        binding.llRental.setOnClickListener {
            GALog.log("menu_click", "rental")
            activity?.let {
                JumpHelper.jumpMapForRentalActivity(it)
            }
        }
    }

    // 不足 10 个房源不显示 see more
    private fun initMoreBtn() {
        binding.tvSeeMore1yearPriceGrowth.setOnClickListener {
            GALog.log(
                "preview_more_click",
                RecommendListingType.convert(RecommendListingType.oneYearPriceGrowth)
            )
            activity?.let { it1 ->
                JumpHelper.jumpListingActivity(
                    it1,
                    RecommendListingType.oneYearPriceGrowth
                )
            }
        }
        binding.tvSeeMoreBestForRentalInvestment.setOnClickListener {
            GALog.log(
                "preview_more_click",
                RecommendListingType.convert(RecommendListingType.bestForRentalInvestment)
            )
            activity?.let { it1 ->
                JumpHelper.jumpListingActivity(
                    it1,
                    RecommendListingType.bestForRentalInvestment
                )
            }
        }
        binding.tvSeeMoreBestForSchool.setOnClickListener {
            GALog.log(
                "preview_more_click",
                RecommendListingType.convert(RecommendListingType.bestForSchool)
            )
            activity?.let { it1 ->
                JumpHelper.jumpListingActivity(
                    it1,
                    RecommendListingType.bestForSchool
                )
            }
        }
        binding.tvSeeMoreFeaturedListings.setOnClickListener {
            GALog.log(
                "preview_more_click",
                RecommendListingType.convert(RecommendListingType.featuredListings)
            )
            activity?.let { it1 ->
                JumpHelper.jumpListingActivity(
                    it1,
                    RecommendListingType.featuredListings
                )
            }
        }
        binding.tvSeeMoreHighReturnsType.setOnClickListener {
            GALog.log(
                "preview_more_click",
                RecommendListingType.convert(RecommendListingType.highReurnsType)
            )
            activity?.let { it1 ->
                JumpHelper.jumpListingActivity(
                    it1,
                    RecommendListingType.highReurnsType
                )
            }
        }
        binding.tvSeeMoreJustSold.setOnClickListener {
            GALog.log(
                "preview_more_click",
                RecommendListingType.convert(RecommendListingType.justSold)
            )
            activity?.let { it1 ->
                JumpHelper.jumpListingActivity(
                    it1,
                    RecommendListingType.justSold
                )
            }
        }
        binding.tvSeeMoreSoldBelowBought.setOnClickListener {
            GALog.log(
                "preview_more_click",
                RecommendListingType.convert(RecommendListingType.soldBelowBought)
            )
            activity?.let { it1 ->
                JumpHelper.jumpListingActivity(
                    it1,
                    RecommendListingType.soldBelowBought
                )
            }
        }
        binding.tvSeeMoreWatchedCommunityUpdates.setOnClickListener {
            GALog.log(
                "preview_more_click",
                RecommendListingType.convert(RecommendListingType.watchedCommunityUpdates)
            )
            activity?.let { it1 ->
                JumpHelper.jumpListingActivity(
                    it1,
                    RecommendListingType.watchedCommunityUpdates
                )
            }
        }

        binding.tvSeeMoreNewlyListed.setOnClickListener {
            GALog.log(
                "preview_more_click",
                RecommendListingType.convert(RecommendListingType.newlyListed)
            )
            activity?.let { it1 ->
                JumpHelper.jumpListingActivity(
                    it1,
                    RecommendListingType.newlyListed
                )
            }
        }

        binding.tvSeeMoreRecommendForYou.setOnClickListener {
            GALog.log(
                "preview_more_click",
                RecommendListingType.convert(RecommendListingType.recommendForYou)
            )
            activity?.let { it1 ->
                JumpHelper.jumpListingActivity(
                    it1,
                    RecommendListingType.recommendForYou
                )
            }
        }

        binding.tvSeeMoreExclusivePreconAssignment.setOnClickListener {
            GALog.log(
                "preview_more_click",
                RecommendListingType.convert(RecommendListingType.exclusivePreconAssignment)
            )
            activity?.let { it1 ->
                JumpHelper.jumpListingActivity(
                    it1,
                    RecommendListingType.exclusivePreconAssignment
                )
            }
        }
    }


    private fun initMessageBanner() {
        binding.banner.scrollTime = 400
        binding.banner.indicator = CircleIndicator(activity)
    }

    private fun initBottomView() {
        binding.tvFeedBack.setOnClickListener {
            startActivity(Intent(activity, FeedbackActivity::class.java))
        }

        binding.tvAbout.setOnClickListener {
            startActivity(Intent(activity, AboutActivity::class.java))
        }

        binding.tvVersion.text =
            "Version " + BuildConfig.VERSION_NAME + " Code " + BuildConfig.VERSION_CODE

        binding.tvPrivacy.setOnClickListener {
            val intent = Intent(activity, WebViewActivity::class.java)
            intent.putExtra(
                WebViewHelper.WEB_VIEW_URL,
                "https://housesigma.com/blog-en/privacy-policy/"
            )
            //          intent.putExtra(WebViewActivity.WEB_VIEW_TITLE, data.text)
            startActivity(intent)
        }
        binding.tvTerms.setOnClickListener {
            val intent = Intent(activity, WebViewActivity::class.java)
            intent.putExtra(
                WebViewHelper.WEB_VIEW_URL,
                "https://housesigma.com/blog-en/terms-of-use/"
            )
            //          intent.putExtra(WebViewActivity.WEB_VIEW_TITLE, data.text)
            startActivity(intent)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        when (event.type) {
            MessageType.RELOAD_HOME -> {
                loadData()
                initRecommendOrder()
                adjustCardViewHeight()
            }

            MessageType.SHOW_AB_TEST_CONFIG -> {
                //DEV-4995 在APP端应该显示所有的remote_config，显示自己是a组还是b组
                try {
                    val abTestRemoteConfigStr = event.getString()
                    abTestRemoteConfigStr?.let {
                        if (TextUtils.isEmpty(it)) {
                            binding.tvAbTest.visibility = View.GONE
                        } else {
                            binding.tvAbTest.text = abTestRemoteConfigStr
                            binding.tvAbTest.visibility = View.VISIBLE
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            MessageType.RELOAD_DISCLAIMERS -> {
                activity?.let { DisclaimerViewHelper.handleDisclaimer(it, binding.tvDisclaimer) }
            }

            else -> {}
        }
    }


    private fun showLoginDialog(reLogin: String = "") {
        GALog.log("login_button_click")
        if (loginDialog == null) {
            loginDialog = LoginFragment()
        }
        if (loginDialog?.isAdded == true) return
        val bundle = Bundle()
        bundle.putString("reLogin", reLogin)
        loginDialog?.arguments = bundle
        loginDialog?.show(childFragmentManager, "")
    }

    private fun bindViews(
        adapter: BaseQuickAdapter<HouseDetail, BaseViewHolder>,
        list: List<HouseDetail>,
        rootLayout: LinearLayout,
        seeMoreLayout: View,
        type: Int,
        pagination: Pagination? = null
    ) {

        var vlist = list
        if (type == RecommendListingType.recommendForYou) {
            if (recommendForYouPage == 1) {
                adapter.data.clear()
            }

            vlist = list.filter {
                !NotInterestedHelper.findNotInterestedByListingId(it.id_listing)
            }
            adapter.addData(vlist)

            // 如果不足3个房源且房源数不为0个，就继续翻页请求
            if (adapter.data.size<=3 && list.size!=0) {
                recommendForYouPage++
                homeViewModel.getHomeRecommendList(RecommendListingType.recommendForYou, page = recommendForYouPage)
            }
        } else {
            adapter.setList(vlist)
        }
        val data = adapter.data
        if (data.size == 0) {
            adapter.recyclerView.visibility = View.GONE
            rootLayout.visibility = View.GONE
        } else {
            adapter.recyclerView.visibility = View.VISIBLE
            rootLayout.visibility = View.VISIBLE

            adapter.setOnItemChildClickListener { adapter, view, position ->
                val listing = data.getOrNull(position) ?: return@setOnItemChildClickListener
                when (view.id) {
                    R.id.rl -> {
                        if (BaseListingsAdapterHelper.canJumpListingDetail(listing)){
                            return@setOnItemChildClickListener
                        }

                        if (type == RecommendListingType.watchedCommunityUpdates) {
//                            DEV-4496 如果一个watched section里的房源，恰巧是楼花，那么：
//                            hs_label：watched_and_exclusive_precon_assignment
                            if (TextUtils.isEmpty(listing.text.hs_exclusive_tag)) {
                                GALog.log("preview_click", RecommendListingType.convert(type))
                            } else {
                                GALog.log(
                                    "preview_click",
                                    "watched_and_exclusive_precon_assignment"
                                )
                            }
                        } else {
                            GALog.log("preview_click", RecommendListingType.convert(type))
                        }

                        activity?.let {
                            WebViewHelper.jumpHouseDetail(
                                it,
                                listing.id_listing,
                                listing.seo_suffix,
                                type = type
                            )
                        }
                    }


                    R.id.tv_login_required -> {
                        val tvLoginRequiredStr = (view as TextView).text.toString()
                        if (Constants.TEXT_VIEW_TRREB_TIMEOUT.equals(tvLoginRequiredStr)){
                            showLoginDialog(reLogin = LoginFragment.RE_LOGIN_TRREB_TIMEOUT)
                        } else if ("Password Expired".equals(tvLoginRequiredStr)) {
                            showLoginDialog(reLogin = LoginFragment.RE_LOGIN_VALIDATE)
                        }else{
                            showLoginDialog()
                        }
                    }

                    R.id.tv_agreement_required -> {
                        GALog.log("agreement_button_click")
                        activity?.let {
                            TosDialog(HomeFragment@ this,
                                viewLifecycleOwner,
                                it,
                                listing.tos_source,
                                object : TosDialog.TosCallback {
                                    override fun onSuccess() {
                                        loadData()
                                    }
                                }).show()
                        }

                    }

                    R.id.tv_not_available -> {
                        activity?.let {
                            VowTosDialog(listing.id_listing,this,viewLifecycleOwner,it).show()
                        }
                    }

                    R.id.iv_dot_menu -> {
                        GALog.log("more_vert_button_click","listing_card")
                        val listingDotMenuDialog = ListingDotMenuDialog.newInstance(listing.id_listing)
                        listingDotMenuDialog.setListingDotMenuCallback(object :ListingDotMenuDialog.ListingDotMenuCallback{
                            override fun notInterested(isNotInterested: Boolean) {
                                listingDotMenuDialog.dismiss()
                                if (isNotInterested) {
                                    GALog.log("mask_as_not_interested","marked_not_interested")
                                    NotInterestedHelper.saveNotInterested(listing.id_listing)
                                    listingViewModel.flagListingNotInterested(listing.id_listing, true)
                                    adapter.removeAt(position)
                                    adapter.notifyDataSetChanged()

                                    if (adapter.data.size <= 3) {
                                        recommendForYouPage++
                                        homeViewModel.getHomeRecommendList(RecommendListingType.recommendForYou, page = recommendForYouPage)
                                    }

                                } else {
                                    GALog.log("mask_as_not_interested","unmarked_not_interested")
                                    NotInterestedHelper.delNotInterested(listing.id_listing)
                                    listingViewModel.flagListingNotInterested(listing.id_listing, false)
                                }
                            }
                        })
                        listingDotMenuDialog.show(childFragmentManager, "")
                    }

                }
            }

            if ((pagination?.currentPage?:0) !=1) {
                return
            }
            if (adapter.hasFooterLayout()) {
                adapter.removeAllFooterView()
            }
//            不足 10 个房源不显示 see more
            if ((pagination?.rowCount?:0) >= 10) {
                val binding = ItemCommuityFooterBinding.inflate(layoutInflater)
                adapter.addFooterView(
                    binding.root,
                    orientation = LinearLayout.HORIZONTAL
                )

                binding.rlMoreList.setOnClickListener {
                    GALog.log("preview_see_more_click", RecommendListingType.convert(type))
                    Logger.d("jump to more list activity type = " + type)
                    activity?.let {
                        JumpHelper.jumpListingActivity(it, type)
                    }
                }

                seeMoreLayout.visibility = View.VISIBLE
            } else {
                seeMoreLayout.visibility = View.GONE
            }

        }
    }


    private fun showChooseDialog() {
        activity?.let {
            chooseDialog = ChooseDialog(it,
                object : ChooseDialog.ChooseLocationCallback {
                    override fun onSuccess(location: String) {
                        // 这是要清掉上次记忆的移动经纬度位置和zoom，设置默认省份中心点位置
                        MMKVUtils.removeData("last_lat")
                        MMKVUtils.removeData("last_lon")
                        MapUtils.setCurrentMapZoom(7.0)
                        // 清除掉上次省份的mapfilter缓存
                        MMKVUtils.removeData(MapHelper.MAP_FILTER_TAG)

                        accountViewModel.setProvince(location)
                    }
                })

            chooseDialog.setOnDismissListener {


            }

            chooseDialog.show()
        }
    }

    private fun initTopView() {
        val fullStringProvince = ProvinceHelper.getFullStringFromCache("Ontario")
        binding.tvLocation.text = fullStringProvince
        binding.cardView.visibility = View.GONE
    }

    private fun reStartActivity() {
        val intent = Intent(activity, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        // 取消其专场动画
        activity?.overridePendingTransition(0, 0)
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
    }

}