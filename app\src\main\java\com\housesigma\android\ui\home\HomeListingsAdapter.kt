package com.housesigma.android.ui.home

import android.graphics.Paint
import android.text.Html
import android.text.TextUtils
import android.util.TypedValue
import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.base.BaseListingsAdapterHelper
import com.housesigma.android.helper.ProvinceHelper
import com.housesigma.android.model.HouseDetail
import com.housesigma.android.model.RecommendListingType
import com.housesigma.android.utils.HSConstant
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.ViewUtils
import me.jessyan.autosize.utils.AutoSizeUtils


class HomeListingsAdapter(type: Int) :
    BaseQuickAdapter<HouseDetail, BaseViewHolder>(R.layout.item_home_listing) {

    private var recommendType = type

    init {
        addChildClickViewIds(
            R.id.tv_login_required,
            R.id.tv_agreement_required,
            R.id.tv_not_available,
            R.id.rl
        )
    }

    override fun convert(holder: BaseViewHolder, item: HouseDetail) {
        adjustCardViewHeight(holder.getView(R.id.rl))
        adjustCardPhotoViewHeight(holder.getView(R.id.iv_house_pic))

        // 副标题区，主要为位置信息
        var address =
            if (!TextUtils.isEmpty(item.municipality_name))
                "${item.address},  ${item.municipality_name}"
            else item.address
        address = address.plus(
            if (!TextUtils.isEmpty(item.community_name))
                " - ${item.community_name}"
            else ""
        )

        holder.setText(R.id.tv_address, address)

//        // 底部房源信息，包含：房屋类型，卧、卫、车库数量
        holder.setText(R.id.tv_house_type_name, item.house_type_name)
        if (item.bedroom_string==null) {
            holder.setGone(R.id.tv_bedroom_string, true)
        } else {
            holder.setVisible(R.id.tv_bedroom_string, true)
            holder.setText(R.id.tv_bedroom_string, item.bedroom_string)
        }

        if (item.washroom==null) {
            holder.setGone(R.id.tv_washroom, true)
        } else {
            holder.setVisible(R.id.tv_washroom, true)
            holder.setText(R.id.tv_washroom, item.washroom)
        }

        if (item.parking.total==null) {
            holder.setGone(R.id.tv_garage, true)
        } else {
            holder.setVisible(R.id.tv_garage, true)
            holder.setText(R.id.tv_garage, item.parking.total)
        }
        holder.setText(R.id.tv_days_ago, item.text.date_preview)
        if (!TextUtils.isEmpty(item?.list_status?.text)) {
            holder.setVisible(R.id.tv_status, true)
            holder.setText(R.id.tv_status, item?.list_status?.text?:"")
        } else {
            holder.setGone(R.id.tv_status, true)
        }


        // 主标题区
        handleTitleAreaText(holder, item)
        // tag view
        handleTagViewDisplay(holder, item)

        // brokerage_text
        handleBrokerageText(holder, item)

        // 楼花tag
        handleAssignmentTag(holder, item)

        // with hs tag
        handleWithHsTag(holder, item)

        // 房源图片以及遮挡逻辑
        BaseListingsAdapterHelper.handleImageMaskView(context,item, holder)
    }

    private fun adjustCardPhotoViewHeight(view: View) {
        if (!HSUtil.isPad(context)){
            ViewUtils.setRelativeLayoutWidthAndMargin(context,view, 0.8767442f,0)
        }
        ViewUtils.setViewHeightParams(view,
            AutoSizeUtils.dp2px(context, HSConstant.CARD_HEIGHT).toInt())
    }

    private fun adjustCardViewHeight(view: RelativeLayout) {
        if (!HSUtil.isPad(context)) {
            ViewUtils.setRelativeLayoutWidthAndMargin(context,view,0.8767442f,16)
        }
        ViewUtils.setViewHeightParams(view,
            AutoSizeUtils.dp2px(context,HSConstant.CARD_HEIGHT).toInt())
    }

    private fun handleAssignmentTag(holder: BaseViewHolder, item: HouseDetail) {
        if (TextUtils.isEmpty(item.text.hs_exclusive_tag)) {
            holder.setGone(R.id.tv_assignment, true)
        } else {
            holder.setText(R.id.tv_assignment,item.text.hs_exclusive_tag)
            holder.setVisible(R.id.tv_assignment, true)
        }
    }

    private fun handleWithHsTag(holder: BaseViewHolder, item: HouseDetail) {
        if (!TextUtils.isEmpty(item.text.transaction_via_label)) {
            holder.setText(R.id.tv_with_hs, item.text.transaction_via_label)
            holder.setVisible(R.id.tv_with_hs, true)
        } else {
            holder.setGone(R.id.tv_with_hs, true)
        }
    }

    private fun handleBrokerageText(holder: BaseViewHolder, item: HouseDetail) {
        if (TextUtils.isEmpty(item.brokerage_text)) {
            holder.setGone(R.id.tv_bc_brokerage_text, true)
            holder.setGone(R.id.line2, true)
            holder.setVisible(R.id.line, true)
        } else {
            holder.setVisible(R.id.tv_bc_brokerage_text, true)
            holder.setText(R.id.tv_bc_brokerage_text, item.brokerage_text)
            holder.setVisible(R.id.line2, true)
            holder.setGone(R.id.line, true)
        }
    }

    private fun handleTagViewDisplay(holder: BaseViewHolder, item: HouseDetail) {
        when (recommendType) {
            RecommendListingType.watchedCommunityUpdates,
            RecommendListingType.soldBelowBought,
            RecommendListingType.justSold,
            RecommendListingType.watchedCommunityNewOrSoldUpdate,
            RecommendListingType.highReurnsType,
            RecommendListingType.newlyListed,
            -> {
                holder.setVisible(R.id.tv_tag, false)
            }

            else -> {
                holder.setVisible(R.id.tv_tag, true)
                when (recommendType) {
                    RecommendListingType.featuredListings,
                    -> {
                        holder.setBackgroundResource(
                            R.id.tv_tag,
                            R.drawable.label_house_cyan_strong_bg
                        )
                        val featureHtml = Html.fromHtml("<strong>Featured</strong>")
                        val tvTag = holder.getView<TextView>(R.id.tv_tag)
                        tvTag.text = featureHtml
                    }
                    else -> {
                        if (!TextUtils.isEmpty(item.text.highlight)) {
                            if (item.text.highlight.contains(":")) {
                                holder.setBackgroundResource(
                                    R.id.tv_tag,
                                    R.drawable.label_house_feature_orange_bg
                                )
                                val split = item.text.highlight.split(":")
                                val htmlStr =
                                    Html.fromHtml(split[0] + ":<strong>" + split[1] + "</strong>")
                                val tvTag = holder.getView<TextView>(R.id.tv_tag)
                                tvTag.text = htmlStr
                            }
                        }
                    }
                }


            }
        }

    }

    private fun handleTitleAreaText(holder: BaseViewHolder, item: HouseDetail) {

        when (recommendType) {
            RecommendListingType.soldBelowBought,
            RecommendListingType.highReurnsType -> {
                holder.setGone(R.id.tv_what_for, true)
                holder.setText(R.id.tv_price, item.price_change_yearly_text)
            }
//            RecommendListingType.justSold -> {
//                holder.setVisible(R.id.tv_what_for, true)
//                if (!TextUtils.isEmpty(item.price_sold)) {
//                    holder.setText(R.id.tv_what_for, "Sold for:")
//                    holder.setText(R.id.tv_price, " $" + item.price_sold)
//                } else {
//                    holder.setText(R.id.tv_price, "-")
//                }
//            }
            else -> {
                holder.setVisible(R.id.tv_what_for, true)
                holder.setText(R.id.tv_what_for, "Listed:")
//                if (item.raw.price != 0) {
//                    holder.setText(R.id.tv_price, "$".plus(item.raw.price))
//                } else {
                holder.setText(R.id.tv_price, " $".plus(item.price))
//                }


            }
        }


        val tvPrice = holder.getView<TextView>(R.id.tv_price)
        if (recommendType == RecommendListingType.justSold) {
            if (TextUtils.isEmpty(item.price_sold)) {
                holder.setGone(R.id.ll_watched_sold, true)
                tvPrice.paint.flags = 0
                tvPrice.paint.isAntiAlias = true

                tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP,18.0f)
                tvPrice.setTextAppearance(context, R.style.H1Header)
            } else {
                holder.setVisible(R.id.ll_watched_sold, true)
                
                // Set the sold for label text based on listing status
                holder.setText(R.id.tv_sold_for_label, item.list_status.getSoldForDisplayText())
                
                holder.setText(R.id.tv_watched_sold_price, " $" + item.price_sold)
                tvPrice.paint.flags = (Paint.STRIKE_THRU_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG)//中划线
                holder.setText(R.id.tv_watched_days_ago, item.text.date_preview)
                holder.setText(R.id.tv_days_ago, "")

                tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP,14.0f)
                tvPrice.setTextAppearance(context, R.style.Subtitles2)
            }
        } else {
            holder.setGone(R.id.ll_watched_sold, true)
            tvPrice.paint.flags = 0
            tvPrice.paint.isAntiAlias = true

            tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP,18.0f)
            tvPrice.setTextAppearance(context, R.style.H1Header)
        }


        if (recommendType == RecommendListingType.watchedCommunityUpdates) {
            if (item.list_status.live==1) {
                tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP,18.0f)
                tvPrice.setTextAppearance(context, R.style.H1Header)

                holder.setText(R.id.tv_days_ago, item.text.date_preview)
                holder.setText(R.id.tv_watched_days_ago, "")
            }

            if (item.list_status.sold==1) {
                tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP,14.0f)
                tvPrice.setTextAppearance(context, R.style.Subtitles2)

                holder.setText(R.id.tv_days_ago, "")
                holder.setText(R.id.tv_watched_days_ago, item.text.date_preview)
            }

            if (item.list_status.live == 0 && item.list_status.sold == 0) {
                tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP,18.0f)
                tvPrice.setTextAppearance(context, R.style.H1Header)

                holder.setText(R.id.tv_days_ago, item.text.date_preview)
                holder.setText(R.id.tv_watched_days_ago, "")

                tvPrice.paint.flags = (Paint.STRIKE_THRU_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG)//中划线
            }
        }

    }

}