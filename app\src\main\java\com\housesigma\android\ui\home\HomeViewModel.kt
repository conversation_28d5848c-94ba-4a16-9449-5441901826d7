package com.housesigma.android.ui.home

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.google.gson.Gson
import com.housesigma.android.BuildConfig
import com.housesigma.android.HSApp
import com.housesigma.android.hybrid.HybridUtils
import com.housesigma.android.model.*
import com.housesigma.android.network.NetClient
import com.housesigma.android.network.launch
import com.housesigma.android.utils.DeviceUtil
import com.housesigma.android.utils.GsonUtils
import com.housesigma.android.utils.LanguageUtils
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.log.Logger
import org.greenrobot.eventbus.EventBus

class HomeViewModel : ViewModel() {

    var token: MutableLiveData<Token> = MutableLiveData()
    var housePhotos: MutableLiveData<HousePhotosInfo> = MutableLiveData()
    var homePage: MutableLiveData<HomePage> = MutableLiveData()
    var recommend1: MutableLiveData<Recommend> = MutableLiveData()
    var recommend2: MutableLiveData<Recommend> = MutableLiveData()
    var recommend3: MutableLiveData<Recommend> = MutableLiveData()
    var recommend4: MutableLiveData<Recommend> = MutableLiveData()
    var recommend5: MutableLiveData<Recommend> = MutableLiveData()
    var recommend6: MutableLiveData<Recommend> = MutableLiveData()
    var recommend7: MutableLiveData<Recommend> = MutableLiveData()
    var recommend8: MutableLiveData<Recommend> = MutableLiveData()
    var recommend9: MutableLiveData<Recommend> = MutableLiveData()
    var recommend10: MutableLiveData<Recommend> = MutableLiveData()
    var recommend11: MutableLiveData<Recommend> = MutableLiveData()
    var initApp: MutableLiveData<InitApp> = MutableLiveData()
    var nativeRouter: MutableLiveData<NativeUrl> = MutableLiveData()
    var agentInfoHomePage: MutableLiveData<AgentInfoHomePage> = MutableLiveData()
    var mapFilter: MutableLiveData<MapFilter> = MutableLiveData()
    var disclaimerInfo: MutableLiveData<DisclaimerInfo> = MutableLiveData()


    fun getDisclaimer() {
        launch({
            NetClient.apiService.getDisclaimer()
        }, {
            disclaimerInfo.postValue(it)
        })
    }
    fun getAgentInfoHomePage() {
        launch({
            NetClient.apiService.getAgentInfoHomePage()
        }, {
            agentInfoHomePage.postValue(it)
        })
    }

    fun urlTransform(url: String) {
        launch({
            NetClient.apiService.urlTransform(url)
        }, {
            nativeRouter.postValue(it)
        })
    }

    fun initApp() {
        launch({
            NetClient.apiService.initApp(
                SoftInfo()
            )
        }, {
            val initAppModel = HybridUtils.saveAndParseInitAppData(it)
            initAppModel?.let { model->
                initApp.postValue(model)

                val mapSatellite = model.native_config?.map_satellite ?: ""
                val mapStatic = model.native_config?.map_static ?: ""
                val mapVector = model.native_config?.map_vector ?: ""
                val urlJoinUs = model.native_config?.url_join_us ?: ""

                MMKVUtils.saveStr("map_satellite", mapSatellite)
                MMKVUtils.saveStr("map_static", mapStatic)
                MMKVUtils.saveStr("map_vector", mapVector)
                MMKVUtils.saveStr("url_join_us", urlJoinUs)
            }
        })
    }

    fun uploadABTestConfig(jsonStr:String) {
        launch({
            NetClient.apiService.uploadABTestConfig(
                ABTest(jsonStr)
            )
        }, {
        })
    }

    fun getHomePage() {
        launch({
            NetClient.apiService.getHomePage()
        }, {
            homePage.postValue(it)
        })
    }

    fun getListingInfoPhotos(id_listing: String) {
        launch({
            NetClient.apiService.getListingInfoPhotos(id_listing)
        }, {
            housePhotos.postValue(it)
        })
    }


    fun getPreconPhotos(id_project: String) {
        launch({
            NetClient.apiService.getPreconPhotos(id_project)
        }, {
            housePhotos.postValue(it)
        })
    }

    fun pushToken(fcmToken: String) {
        launch({
            val pushTokenRequest = PushTokenRequest(push_info = PushInfo(token = fcmToken))
            NetClient.apiService.pushToken(pushTokenRequest)
        }, {
            //忽略返回值
        })
    }

    fun getAccessToken() {
        launch({
            NetClient.apiService.getAccessToken()
        }, {
            HSApp.secret = it.secret
            token.postValue(it)
        })
    }

    fun getMapFilter() {
        launch({
            NetClient.apiService.getMapFilter()
        }, {
            mapFilter.postValue(it)
        })
    }

    fun getHomeRecommendList(recommendType: Int, page: Int = 1) {
        launch({
            NetClient.apiService.getRecommendList(
                recommendType.toString(),
                page = page
            )
        }, {
            when (recommendType) {
                1 -> {
                    recommend1.postValue(it)
                }
                2 -> {
                    recommend2.postValue(it)
                }
                3 -> {
                    recommend3.postValue(it)
                }
                4 -> {
                    recommend4.postValue(it)
                }
                5 -> {
                    recommend5.postValue(it)
                }
                6 -> {
                    recommend6.postValue(it)
                }
                7 -> {
                    recommend7.postValue(it)
                }
                8 -> {
                    recommend8.postValue(it)
                }
                9 -> {
                    recommend9.postValue(it)
                }
                10 -> {
                    recommend10.postValue(it)
                }
                11 -> {
                    recommend11.postValue(it)
                }
            }

        })
    }
}