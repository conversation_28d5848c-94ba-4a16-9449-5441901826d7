package com.housesigma.android.ui.home

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnLoadMoreListener
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.base.BaseListingsAdapterHelper
import com.housesigma.android.databinding.ActivityListingBinding
import com.housesigma.android.model.HouseDetail
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.RecommendListingType
import com.housesigma.android.ui.account.NotificationActivity
import com.housesigma.android.ui.listing.ListingDotMenuDialog
import com.housesigma.android.ui.listing.ListingModel
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.notinterested.NotInterestedHelper
import com.housesigma.android.ui.tos.TosDialog
import com.housesigma.android.ui.tos.VowTosDialog
import com.housesigma.android.ui.watched.NormalListingCollectionAdapter
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.Constants
import com.housesigma.android.utils.GALog
import com.housesigma.android.views.DisclaimerViewHelper
import com.housesigma.android.views.HSLoadMoreView
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshListener
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


class ListingActivity : BaseActivity(), LoginFragment.LoginCallback {

    private lateinit var adapter: ListingsAdapter
    private lateinit var normalAdapter: NormalListingCollectionAdapter
    private lateinit var binding: ActivityListingBinding
    private lateinit var homeViewModel: HomeViewModel
    private lateinit var listingViewModel: ListingModel
    private var recommendType: Int = 0
    private var page: Int = 1
    private var mList: MutableList<HouseDetail> = ArrayList()
    private var loginDialog: LoginFragment? = null

    override fun onResume() {
        super.onResume()
        GALog.page("listing_list")
    }
    override fun getLayout(): Any {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        binding = ActivityListingBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        recommendType = intent.getIntExtra("type", 0)
        binding.refreshLayout.setOnRefreshListener(object : OnRefreshListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                page = 1
                loadData()
            }
        })
        binding.ivClose.setOnClickListener {
            finish()
        }
        binding.rv.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        if (recommendType== RecommendListingType.watchedCommunityUpdates ||
            recommendType== RecommendListingType.exclusivePreconAssignment ||
            recommendType== RecommendListingType.recommendForYou){
            normalAdapter = NormalListingCollectionAdapter()
            binding.rv.adapter = normalAdapter
            normalAdapter.loadMoreModule.loadMoreView = HSLoadMoreView()
            normalAdapter.loadMoreModule.setOnLoadMoreListener(object : OnLoadMoreListener{
                override fun onLoadMore() {
                    page++
                    loadData()
                }
            })
        }else{
            adapter = ListingsAdapter(recommendType)
            adapter.loadMoreModule.loadMoreView = HSLoadMoreView()
            adapter.loadMoreModule.setOnLoadMoreListener(object : OnLoadMoreListener{
                override fun onLoadMore() {
                    page++
                    loadData()
                }
            })
            binding.rv.adapter = adapter
        }


        // DEV-6934 只有 Recommended for You 这个二级页面展示 通知设置的入口。
        if (recommendType== RecommendListingType.recommendForYou){
            binding.ivNotification.visibility = View.VISIBLE
            normalAdapter.setRecommendForYou(true)
        } else {
            binding.ivNotification.visibility = View.GONE
        }

        binding.ivNotification.setOnClickListener {
            startActivity(Intent(this,NotificationActivity::class.java))
        }

    }

    override fun initData() {
        listingViewModel = ViewModelProvider(this).get(ListingModel::class.java)
        homeViewModel = ViewModelProvider(this).get(HomeViewModel::class.java)
        homeViewModel.recommend1.observe(this) {
            binding.tvTitle.text = it.head
            bindViews(adapter, it.list)
        }
        homeViewModel.recommend2.observe(this) {
            binding.tvTitle.text = it.head
            bindViews(adapter, it.list)
        }
        homeViewModel.recommend3.observe(this) {
            binding.tvTitle.text = it.head
            bindViews(adapter, it.list)
        }
        homeViewModel.recommend4.observe(this) {
            binding.tvTitle.text = it.head
            bindViews(adapter, it.list)
        }
        homeViewModel.recommend5.observe(this) {
            binding.tvTitle.text = it.head
            bindViews(adapter, it.list)
        }
        homeViewModel.recommend6.observe(this) {
            binding.tvTitle.text = it.head
            bindViews(normalAdapter, it.list)
        }
        homeViewModel.recommend7.observe(this) {
            binding.tvTitle.text = it.head
            bindViews(adapter, it.list)
        }
        homeViewModel.recommend8.observe(this) {
            binding.tvTitle.text = it.head
            bindViews(adapter, it.list)
        }
        homeViewModel.recommend9.observe(this) {
            binding.tvTitle.text = it.head
            bindViews(adapter, it.list)
        }
        homeViewModel.recommend10.observe(this) {
            binding.tvTitle.text = it.head
            bindViews(normalAdapter, it.list)
        }
        homeViewModel.recommend11.observe(this) {
            binding.tvTitle.text = it.head
            bindViews(normalAdapter, it.list)
        }
        loadData()
    }


    private fun loadData() {
        homeViewModel.getHomeRecommendList(recommendType, page = page)
    }

    private fun bindViews(
        adapter: BaseQuickAdapter<HouseDetail, BaseViewHolder>,
        originalList: List<HouseDetail>,
    ) {
        adapter.loadMoreModule.preLoadNumber = 2
        adapter.loadMoreModule.loadMoreComplete()

        if (originalList.size<10) {
            adapter.loadMoreModule.loadMoreEnd(true)
            adapter.loadMoreModule.isAutoLoadMore = false
            adapter.loadMoreModule.isEnableLoadMore = false
            adapter.loadMoreModule.isEnableLoadMoreIfNotFullPage = false
        }else{
            adapter.loadMoreModule.isEnableLoadMore = true
            adapter.loadMoreModule.isAutoLoadMore = true
        }

        binding.refreshLayout.finishRefresh(true)
        
        if (page == 1) {
            adapter.data.clear()
        }

        var filteredList = originalList
        if (recommendType == RecommendListingType.recommendForYou) {
            filteredList = originalList.filter {
                !NotInterestedHelper.findNotInterestedByListingId(it.id_listing)
            }
            adapter.addData(filteredList)
            if (adapter.data.size <= 3 && originalList.isNotEmpty()) {
                page++
                homeViewModel.getHomeRecommendList(RecommendListingType.recommendForYou, page = page)
            }
        } else {
            adapter.addData(filteredList)
        }



        DisclaimerViewHelper.handleDisclaimer(adapter,this)
        val data = adapter.data
        if (data.size == 0) {
            if (recommendType != RecommendListingType.recommendForYou){
                adapter.recyclerView.visibility = View.GONE
            }
        } else {
            adapter.recyclerView.visibility = View.VISIBLE
            adapter.setOnItemChildClickListener { adapter, view, position ->
                val listing = data.getOrNull(position) ?: return@setOnItemChildClickListener
                when (view.id) {
                    R.id.rl -> {

                        if (BaseListingsAdapterHelper.canJumpListingDetail(listing)){
                            return@setOnItemChildClickListener
                        }

                        if (recommendType==RecommendListingType.watchedCommunityUpdates) {
//                            DEV-4496 如果一个watched section里的房源，恰巧是楼花，那么：
//                            hs_label：watched_and_exclusive_precon_assignment
                            if (TextUtils.isEmpty(listing.text.hs_exclusive_tag)) {
                                GALog.log("preview_click", RecommendListingType.convert(recommendType))
                            } else {
                                GALog.log("preview_click", "watched_and_exclusive_precon_assignment")
                            }
                        } else {
                            GALog.log("preview_click", RecommendListingType.convert(recommendType))
                        }

                        WebViewHelper.jumpHouseDetail(
                            this,
                            listing.id_listing,
                            listing.seo_suffix,
                            type = recommendType
                        )

                    }


                    R.id.tv_login_required -> {
                        val tvLoginRequiredStr = (view as TextView).text.toString()
                        if (Constants.TEXT_VIEW_TRREB_TIMEOUT.equals(tvLoginRequiredStr)){
                            showLoginDialog(reLogin = LoginFragment.RE_LOGIN_TRREB_TIMEOUT)
                        } else if ("Password Expired".equals(tvLoginRequiredStr)) {
                            showLoginDialog(reLogin = LoginFragment.RE_LOGIN_VALIDATE)
                        }else{
                            showLoginDialog()
                        }
                    }

                    R.id.tv_agreement_required -> {
                        showSignTos(listing)

                    }

                    R.id.tv_not_available -> {
                        this?.let {
                            VowTosDialog(listing.id_listing,it,it,this).show()
                        }
                    }

                    R.id.iv_dot_menu -> {
                        GALog.log("more_vert_button_click","listing_card")
                        val listingDotMenuDialog = ListingDotMenuDialog.newInstance(listing.id_listing)
                        listingDotMenuDialog.setListingDotMenuCallback(object :
                            ListingDotMenuDialog.ListingDotMenuCallback{
                            override fun notInterested(isNotInterested: Boolean) {
                                listingDotMenuDialog.dismiss()
                                if (isNotInterested) {
                                    GALog.log("mask_as_not_interested","marked_not_interested")
                                    NotInterestedHelper.saveNotInterested(listing.id_listing)
                                    listingViewModel.flagListingNotInterested(listing.id_listing, true)
                                    adapter.removeAt(position)
                                    adapter.notifyDataSetChanged()

                                    if (adapter.data.size <= 3) {
                                        page++
                                        homeViewModel.getHomeRecommendList(RecommendListingType.recommendForYou, page = page)
                                    }

                                } else {
                                    GALog.log("mask_as_not_interested","unmarked_not_interested")
                                    NotInterestedHelper.delNotInterested(listing.id_listing)
                                    listingViewModel.flagListingNotInterested(listing.id_listing, false)
                                }
                            }
                        })
                        listingDotMenuDialog.show(supportFragmentManager, "")
                    }

                }
            }
        }

    }

    private fun showSignTos(houseDetail: HouseDetail) {
        this?.let {
            TosDialog(
                this, it, it, houseDetail.tos_source,
                object : TosDialog.TosCallback {
                    override fun onSuccess() {
                        page = 1
                        loadData()
                    }
                }).show()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        when (event.type) {
            MessageType.PASSWORD_CHANGE -> {
                page = 1
                loadData()
            }
            else -> {}
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }


    private fun showLoginDialog(reLogin: String = "") {
        GALog.log("login_button_click")
        supportFragmentManager?.let {
            if (loginDialog == null) {
                loginDialog = LoginFragment()
            }
            if (loginDialog?.isAdded == true) return
            val bundle = Bundle()
            bundle.putString("reLogin", reLogin)
            loginDialog?.arguments = bundle
            loginDialog?.show(it, "")
        }
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
        page = 1
        loadData()
    }

}