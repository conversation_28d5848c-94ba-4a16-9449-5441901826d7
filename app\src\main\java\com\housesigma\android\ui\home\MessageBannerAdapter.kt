package com.housesigma.android.ui.home

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.RecyclerView
import com.housesigma.android.R
import com.housesigma.android.model.NativeRouter
import com.housesigma.android.router.PublicRouter
import com.housesigma.android.ui.home.MessageBannerAdapter.BannerViewHolder
import com.housesigma.android.utils.GALog
import com.youth.banner.adapter.BannerAdapter
import com.youth.banner.util.BannerUtils


class MessageBannerAdapter(mDatas: List<NativeRouter?>?, activity: FragmentActivity) :
    BannerAdapter<NativeRouter?, BannerViewHolder>(mDatas) {
    private val mContext: Context
    override fun onCreateHolder(parent: ViewGroup, viewType: Int): BannerViewHolder {
        val view = BannerUtils.getView(parent, R.layout.banner_home)
        return BannerViewHolder(view)
    }

    class BannerViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        var tvMsg: TextView
        var ivCanClick: ImageView

        init {
            tvMsg = view.findViewById(R.id.tv_msg)
            ivCanClick = view.findViewById(R.id.iv_can_click)
        }
    }

    init {
        mContext = activity
    }

    override fun onBindView(
        holder: BannerViewHolder?,
        data: NativeRouter?,
        position: Int,
        size: Int
    ) {
        holder?.tvMsg?.text = data?.text
        holder?.itemView?.setOnClickListener {
            GALog.log("home_notification_click",data?.ga_event)
            // 解析app内跳转页面的协议
            PublicRouter().open(mContext, data?.url?.action, data?.url?.params)
        }

        if (data?.url?.action == null) {
            holder?.ivCanClick?.visibility = View.INVISIBLE
        } else {
            holder?.ivCanClick?.visibility = View.VISIBLE
        }
    }

}