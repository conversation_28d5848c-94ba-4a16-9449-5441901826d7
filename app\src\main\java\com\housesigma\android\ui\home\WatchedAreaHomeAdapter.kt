package com.housesigma.android.ui.home

import android.graphics.Paint
import android.text.TextUtils
import android.util.TypedValue
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.base.BaseListingsAdapterHelper
import com.housesigma.android.model.HouseDetail

class WatchedAreaHomeAdapter :
    BaseQuickAdapter<HouseDetail, BaseViewHolder>(R.layout.item_watched_area_home) {

    private var mIsRecommendForYou = false

    init {
        addChildClickViewIds(
            R.id.tv_login_required,
            R.id.tv_agreement_required,
            R.id.tv_not_available,
            R.id.rl,
            R.id.iv_dot_menu
        )
    }

    fun setRecommendForYou(isRecommendForYou: Boolean) {
        this.mIsRecommendForYou = isRecommendForYou
    }

    override fun convert(holder: BaseViewHolder, item: HouseDetail) {

        if (mIsRecommendForYou&&!item.isNeedReLogin()) {
            holder.setVisible(R.id.iv_dot_menu, true)
        } else {
            holder.setGone(R.id.iv_dot_menu, true)
        }

        if (!TextUtils.isEmpty(item?.list_status?.text)) {
            holder.setVisible(R.id.tv_status, true)
            holder.setText(R.id.tv_status, item?.list_status?.text?:"")
        } else {
            holder.setGone(R.id.tv_status, true)
        }

        // 副标题区，主要为位置信息
        var address =
            if (!TextUtils.isEmpty(item.municipality_name))
                "${item.address},  ${item.municipality_name}"
            else item.address
        address = address.plus(
            if (!TextUtils.isEmpty(item.community_name))
                " - ${item.community_name}"
            else ""
        )

        holder.setText(R.id.tv_address, address)

//        // 底部房源信息，包含：房屋类型，卧、卫、车库数量
        holder.setText(R.id.tv_house_type_name, item.house_type_name)
        if (item.bedroom_string==null) {
            holder.setGone(R.id.tv_bedroom_string, true)
        } else {
            holder.setVisible(R.id.tv_bedroom_string, true)
            holder.setText(R.id.tv_bedroom_string, item.bedroom_string)
        }

        if (item.washroom==null) {
            holder.setGone(R.id.tv_washroom, true)
        } else {
            holder.setVisible(R.id.tv_washroom, true)
            holder.setText(R.id.tv_washroom, item.washroom)
        }

        if (item.parking.total==null) {
            holder.setGone(R.id.tv_garage, true)
        } else {
            holder.setVisible(R.id.tv_garage, true)
            holder.setText(R.id.tv_garage, item.parking.total)
        }

        // 主标题区
        handleTitleAreaText(holder, item)

        // brokerage_text
        handleBrokerageText(holder, item)


        //left label
        handleLeftLabel(holder,item)

        // 楼花tag
        handleAssignmentTag(holder, item)

        // with hs tag
        handleWithHsTag(holder, item)

        // 房源图片以及遮挡逻辑
        BaseListingsAdapterHelper.handleImageMaskView(context,item, holder)
    }

    private fun handleAssignmentTag(holder: BaseViewHolder, item: HouseDetail) {
        if (TextUtils.isEmpty(item.text.hs_exclusive_tag)) {
            holder.setGone(R.id.tv_assignment, true)
        } else {
            holder.setText(R.id.tv_assignment,item.text.hs_exclusive_tag)
            holder.setVisible(R.id.tv_assignment, true)
        }
    }

    private fun handleWithHsTag(holder: BaseViewHolder, item: HouseDetail) {
        if (!TextUtils.isEmpty(item.text.transaction_via_label)) {
            holder.setText(R.id.tv_with_hs, item.text.transaction_via_label)
            holder.setVisible(R.id.tv_with_hs, true)
        } else {
            holder.setGone(R.id.tv_with_hs, true)
        }
    }

    private fun handleLeftLabel(holder: BaseViewHolder, item: HouseDetail) {
        item.text.highlight_left?.let {
            holder.setVisible(R.id.ll_left_label, true)
            holder.setText(R.id.tv_label_name, it.name)
            if (it.count<=1) {
                holder.setGone(R.id.tv_left_count, true)
            }else{
                holder.setVisible(R.id.tv_left_count, true)
                holder.setText(R.id.tv_left_count, " +"+(it.count-1))
            }
        }

        if (item.text.highlight_left==null){
            holder.setGone(R.id.ll_left_label, true)
        }
    }

    private fun handleBrokerageText(holder: BaseViewHolder, item: HouseDetail) {
        if (TextUtils.isEmpty(item.brokerage_text)) {
            holder.setGone(R.id.tv_bc_brokerage_text, true)
            holder.setGone(R.id.line2, true)
            holder.setVisible(R.id.line, true)
        } else {
            holder.setVisible(R.id.tv_bc_brokerage_text, true)
            holder.setText(R.id.tv_bc_brokerage_text, item.brokerage_text)
            holder.setVisible(R.id.line2, true)
            holder.setGone(R.id.line, true)
        }
    }


    private fun handleTitleAreaText(holder: BaseViewHolder, item: HouseDetail) {
        holder.setVisible(R.id.tv_what_for, true)

        val tvPrice = holder.getView<TextView>(R.id.tv_price)

        if (!TextUtils.isEmpty(item.price)) {
            holder.setVisible(R.id.ll_sell,true)
            holder.setText(R.id.tv_price, " $" + item.price)
        } else {
            holder.setGone(R.id.ll_sell,true)
        }

//        list_status.live === 1 (for sale, for lease)
//        list_status.sold === 1 (Sold, Leased)
//        list_status.live === 0, list_status.sold === 0 (Terminated, Suspeneded, Expired ....)
        if (item.list_status.live==1) {
            tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP,18.0f)
            tvPrice.setTextAppearance(context, R.style.H1Header)

            holder.setText(R.id.tv_days_ago, item.text.date_preview)
            holder.setText(R.id.tv_watched_days_ago, "")
        }

        if (!TextUtils.isEmpty(item.price_sold)&&item.list_status.sold==1) {
            tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP,14.0f)
            tvPrice.setTextAppearance(context, R.style.Subtitles2)

            holder.setText(R.id.tv_days_ago, "")
            holder.setText(R.id.tv_watched_days_ago, item.text.date_preview)

            holder.setVisible(R.id.ll_sold,true)
            
            // Set the sold for label text based on listing status
            holder.setText(R.id.tv_sold_for_label_watched, item.list_status.getSoldForDisplayText())
            
            holder.setText(R.id.tv_sold_price, " $" + item.price_sold)
            tvPrice.paint.flags = (Paint.STRIKE_THRU_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG)//中划线
        } else {
            tvPrice.paint.flags = 0
            tvPrice.paint.isAntiAlias = true
            holder.setGone(R.id.ll_sold,true)
        }

        if (item.list_status.live == 0 && item.list_status.sold == 0) {
            tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP,18.0f)
            tvPrice.setTextAppearance(context, R.style.H1Header)

            holder.setText(R.id.tv_days_ago, item.text.date_preview)
            holder.setText(R.id.tv_watched_days_ago, "")

            tvPrice.paint.flags = (Paint.STRIKE_THRU_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG)//中划线
        }
    }

}