package com.housesigma.android.ui.listing

import android.content.DialogInterface
import android.content.MutableContextWrapper
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.core.view.isEmpty
import androidx.lifecycle.ViewModelProvider
import com.bumptech.glide.Glide
import com.google.gson.Gson
import com.gyf.immersionbar.ktx.destroyImmersionBar
import com.housesigma.android.HSApp
import com.housesigma.android.R
import com.housesigma.android.base.BaseDialogFragment
import com.housesigma.android.databinding.DialogContactUsWithAgentBinding
import com.housesigma.android.hybrid.HybridUtils
import com.housesigma.android.model.User
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.ScreenUtils
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.HSNoScrollWebView
import me.jessyan.autosize.AutoSizeConfig

class ContactUsWithAgentFragment() :
    BaseDialogFragment() {

    private lateinit var contactUsModel: ListingModel
    private var idListing :String ?= null
    private var idProject :String ?= null
    private var contactType: ContactDialogType? = null
    private var message :String ?= null
    private var mlNum :String ?= null
    private var agentName :String ?= null
    private var agentPicture :String ?= null
    private var agentSlug :String ?= null
    private var showTrustPilot :Boolean = false
    private var gaHsLabel :String = ""
    private var _tag :String ?= null
    private var idAgent :Int ?= null

    private lateinit var binding: DialogContactUsWithAgentBinding

    companion object {
        private var webView: HSNoScrollWebView? = null

        fun newInstance(contactType :ContactDialogType,
                        message:String,
                        mlNum:String? = "",
                        idListing: String? = "",
                        agentName:String? = "",
                        agentPicture:String?="",
                        idProject:String?="",
                        showTrustPilot:Boolean=false,
                        agentSlug:String? = null,
                        gaHsLabel:String,
                        tag:String ? = null,
                        idAgent:Int? = null): ContactUsWithAgentFragment {
            val args = Bundle()
            args.putSerializable("contactType", contactType.name)
            args.putString("message", message)
            args.putString("mlNum", mlNum)
            args.putString("idListing", idListing)
            args.putString("agentName", agentName)
            args.putString("agentPicture", agentPicture)
            args.putString("idProject", idProject)
            args.putString("agentSlug", agentSlug)
            args.putString("gaHsLabel", gaHsLabel)
            args.putBoolean("showTrustPilot", showTrustPilot)
            args.putString("tag", tag)
            if (idAgent != null) {
                args.putInt("id_agent", idAgent)
            }
            val fragment = ContactUsWithAgentFragment()
            fragment.arguments = args
            return fragment
        }

        fun destroyTrustPilotView(){
            if (webView == null) {
                return
            }
            try {
                webView?.destroy()
                webView = null
            } catch (e:Exception) {
                e.printStackTrace()
            }
        }

        fun loadTrustPilotView(){
            try {
                webView = HSApp.appContext?.let { HSNoScrollWebView(MutableContextWrapper(it)) }
            } catch (e:Exception) {
                ToastUtils.showLong("Chromium WebView package does not exist")
                e.printStackTrace()
            }

            val bootstrap =
                "<!-- TrustBox script --> <script type=\"text/javascript\" src=\"https://static.housesigma.com/library/v5.tp.widget.bootstrap.min.js\" async></script> <!-- End Trustbox script -->"
            val trustBox =
                "<!-- TrustBox widget - Mini Carousel --> <div class=\"trustpilot-widget\" data-locale=\"en-US\" data-template-id=\"5419b6ffb0d04a076446a9af\" data-businessunit-id=\"63697f57dd230a9e14928418\" data-style-height=\"350px\" data-style-width=\"100%\" data-theme=\"light\" data-stars=\"1,2,3,4,5\"> <a href=\"https://www.trustpilot.com/review/revelsystems.com\" target=\"_blank\">Trustpilot</a> </div> <!-- End TrustBox widget -->"
            webView?.let {
                it.setBackgroundColor(Color.WHITE)
                it.setBackgroundResource(android.R.color.transparent)
                it.settings.javaScriptEnabled = true
                it.settings.blockNetworkImage = true
                it.settings.displayZoomControls = false
                it.settings.javaScriptCanOpenWindowsAutomatically = true
                it.settings.domStorageEnabled = true
                it.settings.cacheMode = WebSettings.LOAD_DEFAULT
                it.settings.databaseEnabled = true
                it.loadDataWithBaseURL(
                    "https://widget.trustpilot.com",
                    bootstrap + trustBox,
                    "text/html",
                    null,
                    null
                )
            }
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreen)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        contactUsModel = ViewModelProvider(this).get(ListingModel::class.java)
        binding = DialogContactUsWithAgentBinding.inflate(inflater, container, false)

        contactType = ContactDialogType.valueOf(arguments?.getString("contactType")?:"")
        message = arguments?.getString("message")
        mlNum = arguments?.getString("mlNum")
        idListing = arguments?.getString("idListing")
        agentName = arguments?.getString("agentName")
        agentPicture = arguments?.getString("agentPicture")
        idProject = arguments?.getString("idProject")
        showTrustPilot = arguments?.getBoolean("showTrustPilot")?:false
        agentSlug = arguments?.getString("agentSlug")
        gaHsLabel = arguments?.getString("gaHsLabel")?:""
        _tag = arguments?.getString("tag")?:""
        idAgent = arguments?.getInt("id_agent").takeIf { it != 0 }

        initViews()
        initData()
        dialog?.setCanceledOnTouchOutside(true)
        return binding.root
    }

    private fun initData() {
        contactUsModel.userContactMsg.observe(this) {
            ToastUtils.showSuccess(it.message)
            if (it.gaEvent != null && it.gaHsLabel!=null) {
                GALog.log(it.gaEvent, it.gaHsLabel)
            } else {
                GALog.log("contact_agent_submit", gaHsLabel)
                dismiss()
            }
        }

        contactUsModel.loadingLiveData.observe(this){
            dismissLoadingDialog()
        }
    }

    private fun initViews() {
        val userJsonStr = HybridUtils.getUserForHybrid()
        val user = Gson().fromJson(userJsonStr, User::class.java)

        if (TextUtils.isEmpty(agentName)) {
            // 无agent
            binding.llAgentInfo.visibility = View.GONE
            binding.tvContactUsTitle.visibility = View.VISIBLE
            if (contactType==ContactDialogType.SCHEDULE) {
                binding.tvContactUsTitle.text = getString(R.string.schedule_viewing)
                binding.tvContactUsSubtitle.visibility = View.VISIBLE
                binding.tvContactUsSubtitle.text = "Tour with HouseSigma Agent"
            } else if (contactType==ContactDialogType.CONTACT||contactType==ContactDialogType.ACCOUNT||contactType==ContactDialogType.PRECON) {
                binding.tvContactUsTitle.setText("Contact HouseSigma Agent")
                binding.tvContactUsSubtitle.visibility = View.GONE
            }
        } else {
            binding.tvContactUsTitle.visibility = View.GONE
            binding.tvContactUsSubtitle.visibility = View.GONE
            binding.llAgentInfo.visibility = View.VISIBLE
            binding.tvContactUsAgentName.text = agentName
            binding.tvContactUsAgentName.maxWidth = ScreenUtils.dpToPx(350f).toInt()
            binding.llAgentInfo.setOnClickListener {
                agentSlug?.let { slug ->
                    GALog.log("agent_avatar_click",gaHsLabel)
                    WebViewHelper.jumpAgentDetail(requireContext(), slug = slug)
                }
            }
        }


        binding.etMessage.setText(message)

        Glide.with(this)
            .load(agentPicture)
            .circleCrop()
            .into(binding.ivAvatar)

        binding.ivDelEmail.setOnClickListener {
            binding.etContactEmail.setText("")
        }

        binding.ivDelMessage.setOnClickListener {
            binding.etMessage.setText("")
        }

        binding.ivDelName.setOnClickListener {
            binding.etName.setText("")
        }

        binding.ivDelPhone.setOnClickListener {
            binding.etContactPhone.setText("")
        }

        addEditTextShowDelObserver(binding.etContactPhone,binding.ivDelPhone)
        addEditTextShowDelObserver(binding.etName,binding.ivDelName)
        addEditTextShowDelObserver(binding.etContactEmail,binding.ivDelEmail)
        addEditTextShowDelObserver(binding.etMessage,binding.ivDelMessage)







        user?.phonenumber?.let {
            binding.etContactPhone.setText(it)
            binding.ivDelPhone.visibility = View.VISIBLE
        }
        user?.email?.let {
            binding.etContactEmail.setText(it)
            binding.ivDelEmail.visibility = View.VISIBLE
        }
        user?.name?.let {
            binding.etName.setText(it)
            binding.ivDelName.visibility = View.VISIBLE
        }

        when (contactType) {
            ContactDialogType.SCHEDULE -> {
                binding.tvSubmit.text = getString(R.string.schedule_viewing)
            }
            ContactDialogType.CONTACT,ContactDialogType.ACCOUNT,ContactDialogType.PRECON -> {
                binding.tvSubmit.text = getString(R.string.contact_agent)
            }
            else -> {}
        }


        binding.tvSubmit.setOnClickListener {
            submitUserContact()
        }

        if (showTrustPilot) {
            addTrustPilotView()
            binding.webviewContainer.visibility = View.VISIBLE
        } else {
            binding.webviewContainer.visibility = View.GONE
        }
    }

    private fun addTrustPilotView() {
        // 适配 trustpilot网站 @media screen and (min-width: 420px)
        HSApp.appContext?.let {
            val deviceWidth = ScreenUtils.getDeviceWidth(it)
            val deviceDensity = ScreenUtils.getDeviceDensity(it)
            if (((deviceWidth/deviceDensity) < 420)) {
                // 两行
                val lp = binding.webviewContainer.layoutParams as LinearLayout.LayoutParams
                lp.height = ScreenUtils.dpToPx(55f).toInt()
                binding.webviewContainer.layoutParams = lp
            } else {
                // 一行
                val lp = binding.webviewContainer.layoutParams as LinearLayout.LayoutParams
                lp.height = ScreenUtils.dpToPx(28f).toInt()
                binding.webviewContainer.layoutParams = lp
            }
        }

        webView?.context?.let {
            val contextWrapper = it as MutableContextWrapper
            contextWrapper.baseContext = context
            if (binding.webviewContainer.isEmpty()) {
                webView?.let {
                    it.parent?.let {viewParent ->
                        (viewParent as ViewGroup).removeView(webView)
                    }

                    binding.webviewContainer.addView(
                        webView,
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )

                    it.webViewClient = object : WebViewClient() {
                        override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                            <EMAIL>?.let { mContext ->
                                WebViewHelper.jumpInnerWebView(mContext,url)
                            }
                            return true
                        }
                    }
                }

            }
        }
    }

    private fun addEditTextShowDelObserver(etContactPhone: EditText, ivDelPhone: ImageView) {
        etContactPhone.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }
            override fun onTextChanged(
                text: CharSequence?,
                start: Int,
                lengthBefore: Int,
                lengthAfter: Int
            ) {
                if (text.toString().length == 0) {
                    ivDelPhone.visibility = View.GONE
                } else {
                    ivDelPhone.visibility = View.VISIBLE
                }
            }
            override fun afterTextChanged(p0: Editable?) {
            }
        })

    }
    private fun submitUserContact() {
        val email = binding.etContactEmail.text.toString().trim()
        val name = binding.etName.text.toString().trim()
        val message = binding.etMessage.text.toString().trim()
        val phone = binding.etContactPhone.text.toString().trim()
        val tour_via_video_chat = if (binding.cbTourViaVideoChat.isChecked) "1" else "0"

        showLoadingDialog()
        if (contactType==ContactDialogType.SCHEDULE) {
            contactUsModel.userContactSchedule(
                email = email,
                id_listing = idListing,
                message = message,
                ml_num = mlNum,
                name = name,
                phone = phone,
                tour_via_video_chat = tour_via_video_chat,
                id_agent = idAgent,
            )
        } else if (contactType==ContactDialogType.CONTACT) {
            contactUsModel.userContact(
                email = email,
                id_listing = idListing,
                message = message,
                ml_num = mlNum,
                name = name,
                phone = phone,
                tag = _tag,
                tour_via_video_chat = tour_via_video_chat,
                id_agent = idAgent,
            )
        } else if (contactType==ContactDialogType.ACCOUNT) {
            contactUsModel.userContact(
                email = email,
                id_listing = "",
                message = message,
                ml_num = "",
                name = name,
                phone = phone,
                tour_via_video_chat = tour_via_video_chat,
            )
        } else if (contactType==ContactDialogType.PRECON) {
            contactUsModel.userContact(
                email = email,
                message = message,
                ml_num = mlNum,
                name = name,
                phone = phone,
                tour_via_video_chat = tour_via_video_chat,
                id_project = idProject
            )
        }

    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        val windowParams = window?.attributes
        windowParams?.dimAmount = 0.7f // 遮罩效果消失
        window?.setLayout(-1, -2)
        windowParams?.gravity = Gravity.BOTTOM
        window?.attributes = windowParams
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        getDialog()?.let { destroyImmersionBar(it) }
        activity?.let {
            dismissLoadingDialog()
        }
    }

}


