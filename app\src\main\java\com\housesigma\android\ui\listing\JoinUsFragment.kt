package com.housesigma.android.ui.listing

import android.content.DialogInterface
import android.content.MutableContextWrapper
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.core.view.isEmpty
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.destroyImmersionBar
import com.housesigma.android.HSApp
import com.housesigma.android.R
import com.housesigma.android.base.BaseDialogFragment
import com.housesigma.android.databinding.DialogJoinUsBinding
import com.housesigma.android.ui.watched.WatchedViewModel
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.ScreenUtils
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.views.HSNoScrollWebView
import com.housesigma.android.views.LoadingDialog

class JoinUsFragment : BaseDialogFragment() {

    private lateinit var watchedViewModel: WatchedViewModel
    private var loadingDialog: LoadingDialog? = null
    private lateinit var binding: DialogJoinUsBinding
    private var showTrustPilot :Boolean = false

    companion object {
        private var webView: HSNoScrollWebView? = null
        fun newInstance(isForSellType: Boolean,
                        municipalityName:String,
                        showTrustPilot:Boolean=false): JoinUsFragment {
            val args = Bundle()
            args.putBoolean("is_for_sell_type", isForSellType)
            args.putString("municipality_name", municipalityName)
            args.putBoolean("showTrustPilot", showTrustPilot)
            val fragment = JoinUsFragment()
            fragment.arguments = args
            return fragment
        }

        fun destroyTrustPilotView(){
            if (webView == null) {
                return
            }
            try {
                webView?.destroy()
                webView = null
            } catch (e:Exception) {
                e.printStackTrace()
            }
        }

        fun loadTrustPilotView(){
            try {
                webView = HSApp.appContext?.let { HSNoScrollWebView(
                    MutableContextWrapper(it)
                ) }
            } catch (e:Exception) {
                ToastUtils.showLong("Chromium WebView package does not exist")
                e.printStackTrace()
            }

            val bootstrap =
                "<!-- TrustBox script --> <script type=\"text/javascript\" src=\"https://static.housesigma.com/library/v5.tp.widget.bootstrap.min.js\" async></script> <!-- End Trustbox script -->"
            val trustBox =
                "<!-- TrustBox widget - Mini Carousel --> <div class=\"trustpilot-widget\" data-locale=\"en-US\" data-template-id=\"5419b6ffb0d04a076446a9af\" data-businessunit-id=\"63697f57dd230a9e14928418\" data-style-height=\"350px\" data-style-width=\"100%\" data-theme=\"light\" data-stars=\"1,2,3,4,5\"> <a href=\"https://www.trustpilot.com/review/revelsystems.com\" target=\"_blank\">Trustpilot</a> </div> <!-- End TrustBox widget -->"

            webView?.let {
                it.setBackgroundColor(Color.WHITE)
                it.setBackgroundResource(android.R.color.transparent)
                it.settings.javaScriptEnabled = true
                it.settings.blockNetworkImage = true
                it.settings.displayZoomControls = false
                it.settings.javaScriptCanOpenWindowsAutomatically = true
                it.settings.domStorageEnabled = true
                it.settings.cacheMode = WebSettings.LOAD_DEFAULT
                it.settings.databaseEnabled = true
                it.loadDataWithBaseURL(
                    "https://widget.trustpilot.com",
                    bootstrap + trustBox,
                    "text/html",
                    null,
                    null
                )
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreen)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        binding = DialogJoinUsBinding.inflate(inflater, container, false)
        initViews()
//        initData()
//        dialog?.setCanceledOnTouchOutside(false)
        return binding.root
    }


    private fun initViews() {
        val isForSellType = arguments?.getBoolean("is_for_sell_type")?:false
        val municipalityName = arguments?.getString("municipality_name")?:""
        showTrustPilot = arguments?.getBoolean("showTrustPilot")?:false

        if (showTrustPilot) {
            addTrustPilotView()
            binding.webviewContainer.visibility = View.VISIBLE

            // showTrustPilot时，需要动态调整布局中tvJoinUsIntro的topMargin
            val lp = binding.tvJoinUsIntro.layoutParams as LinearLayout.LayoutParams
            lp.topMargin = 10
            binding.tvJoinUsIntro.layoutParams = lp
        } else {
            binding.webviewContainer.visibility = View.GONE
        }

        binding.tvJoinUs.setOnClickListener {
            GALog.log("join_housesigma_click", "no_agent")
            activity?.let { that ->
                WebViewHelper.jumpJoinUs(that)
            }
        }



        if (isForSellType) {
            binding.tvContactUsTitle.text = getString(R.string.schedule_viewing)
            binding.tvTourWith.visibility = View.VISIBLE
        } else {
            binding.tvContactUsTitle.text = "Contact HouseSigma Agent"
            binding.tvTourWith.visibility = View.GONE
        }

        binding.tvJoinUsIntro.text =
            "Sorry we don't have a community agent working in this area. Are you REALTOR® working actively in this community? Join us to become a HouseSigma Agent in " + municipalityName + "."

    }

    private fun addTrustPilotView() {
        // 适配 trustpilot网站 @media screen and (min-width: 420px)
        HSApp.appContext?.let {
            val deviceWidth = ScreenUtils.getDeviceWidth(it)
            val deviceDensity = ScreenUtils.getDeviceDensity(it)
            if (((deviceWidth/deviceDensity) < 420)) {
                // 两行
                val lp = binding.webviewContainer.layoutParams as LinearLayout.LayoutParams
                lp.height = ScreenUtils.dpToPx(55f).toInt()
                binding.webviewContainer.layoutParams = lp
            } else {
                // 一行
                val lp = binding.webviewContainer.layoutParams as LinearLayout.LayoutParams
                lp.height = ScreenUtils.dpToPx(28f).toInt()
                binding.webviewContainer.layoutParams = lp
            }
        }

        val contextWrapper = (webView?.context as MutableContextWrapper)
        contextWrapper.baseContext = context
        if (binding.webviewContainer.isEmpty()) {

            webView?.let {
                it.parent?.let {viewParent ->
                    (viewParent as ViewGroup).removeView(webView)
                }

                binding.webviewContainer.addView(
                    webView,
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )

                it.webViewClient = object : WebViewClient() {
                    override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                        <EMAIL>?.let { mContext ->
                            WebViewHelper.jumpInnerWebView(mContext,url)
                        }
                        return true
                    }
                }
            }

        }
    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        val windowParams = window?.attributes
        windowParams?.dimAmount = 0.7f // 遮罩效果消失
        window?.setLayout(-1, -2)
        windowParams?.gravity = Gravity.BOTTOM
        window?.attributes = windowParams
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        getDialog()?.let { destroyImmersionBar(it) }
        activity?.let {
            loadingDialog?.dismiss()
        }
    }

}


