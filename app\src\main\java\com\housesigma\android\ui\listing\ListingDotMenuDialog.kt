package com.housesigma.android.ui.listing

import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.gyf.immersionbar.ktx.destroyImmersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseDialogFragment
import com.housesigma.android.databinding.DialogListingDotMenuBinding
import com.housesigma.android.ui.map.propertype.MapSettingDialog.MapSettingsCallback
import com.housesigma.android.ui.notinterested.NotInterestedHelper
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.views.SwitchButton


class ListingDotMenuDialog : BaseDialogFragment() {

    private lateinit var binding: DialogListingDotMenuBinding
    private var mCallback:ListingDotMenuCallback?=null
    private var mListingId :String = ""

    interface ListingDotMenuCallback {
        fun notInterested(isNotInterested: Boolean)
    }

    companion object {
        fun newInstance(listingId:String): ListingDotMenuDialog {
            val args = Bundle()
            args.putString("listingId", listingId)
            val fragment = ListingDotMenuDialog()
            fragment.arguments = args
            return fragment
        }
    }

    fun setListingDotMenuCallback(cb: ListingDotMenuCallback){
        mCallback = cb
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreen)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogListingDotMenuBinding.inflate(inflater, container, false)
        mListingId = arguments?.getString("listingId")?:""
        initViews()
        return binding.root
    }

    private fun initViews() {
        val notInterested = NotInterestedHelper.findNotInterestedByListingId(mListingId)
        binding.sbNotInterested.setChecked(notInterested)
        binding.sbNotInterested.setOnCheckedChangeListener(object : SwitchButton.OnCheckedChangeListener {
            override fun onCheckedChanged(button: SwitchButton, checked: Boolean) {
                mCallback?.notInterested(checked)
            }
        })
    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        val windowParams = window?.attributes
        windowParams?.dimAmount = 0.7f // 遮罩效果消失
        window?.setLayout(-1, -2)
        windowParams?.gravity = Gravity.BOTTOM
        window?.attributes = windowParams
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        getDialog()?.let { destroyImmersionBar(it) }
        arguments
    }

}


