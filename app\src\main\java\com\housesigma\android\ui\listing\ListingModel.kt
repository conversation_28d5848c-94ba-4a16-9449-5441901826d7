package com.housesigma.android.ui.listing

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.housesigma.android.model.*
import com.housesigma.android.network.NetClient
import com.housesigma.android.network.launch
import com.housesigma.android.network.launchSpecial
import com.housesigma.android.utils.GALog

class ListingModel : ViewModel() {

    val loadingLiveData = MutableLiveData<Boolean>()
    var userContactMsg: MutableLiveData<ContactAgentMsgRes> = MutableLiveData()

    /**
     * 标记listing为不感兴趣
     * @param idListing
     * @param notInterested
     */
    fun flagListingNotInterested(idListing: String, notInterested: Boolean) {
        launch({
            NetClient.apiService.flagListingNotInterested(idListing,if (notInterested) 1 else 0)
        },{})
    }

    fun userContact(email: String?="",
                id_listing: String?="",
                id_project: String?="",
                message: String?="",
                ml_num: String?="",
                name: String?="",
                phone: String?="",
                tag: String?="",
                tour_via_video_chat: String,
                id_agent: Int? = null) {
        launchSpecial({
            val tags = arrayListOf("mobile")
            if (tag!=null) {
                tags.add(tag)
            }
            NetClient.apiService.userContact(
                email = email,
                id_listing = id_listing,
                id_project = id_project,
                message = message,
                ml_num = ml_num,
                name = name,
                phone = phone,
                tags = tags,
                tour_via_video_chat = tour_via_video_chat,
                id_agent = id_agent
            )
        }, {
            userContactMsg.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun userContactSchedule(email: String?="",
                    id_listing: String?="",
                    id_project: String?="",
                    message: String?="",
                    ml_num: String?="",
                    name: String?="",
                    phone: String?="",
                    tour_via_video_chat: String,
                    id_agent: Int? = null) {
        launchSpecial({
            NetClient.apiService.userContactSchedule(
                email = email,
                id_listing = id_listing,
                id_project = id_project,
                message = message,
                ml_num = ml_num,
                name = name,
                phone = phone,
                tour_via_video_chat = tour_via_video_chat,
                id_agent = id_agent
            )
        }, {
            userContactMsg.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

}