package com.housesigma.android.ui.login

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.Html
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.text.style.URLSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.RequiresApi
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.google.android.gms.auth.api.identity.Identity
import com.google.android.gms.auth.api.identity.SignInClient
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.android.gms.tasks.Task
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.gyf.immersionbar.ktx.destroyImmersionBar
import com.housesigma.android.BuildConfig
import com.housesigma.android.HSApp
import com.housesigma.android.R
import com.housesigma.android.base.BaseDialogFragment
import com.housesigma.android.databinding.DialogLoginBinding
import com.housesigma.android.hybrid.HybridUtils
import com.housesigma.android.model.CountrycodeX
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.User
import com.housesigma.android.ui.account.ChangePasswordActivity
import com.housesigma.android.ui.forgot.ForgotPasswordActivity
import com.housesigma.android.ui.onetap.OneTapActivity
import com.housesigma.android.ui.signup.SignUpActivity
import com.housesigma.android.ui.signup.SignUpModel
import com.housesigma.android.ui.watched.WatchedHelper
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.GsonUtils
import com.housesigma.android.utils.HSURLSpan
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.SoftKeyBoardListener
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.LoadingDialog
import com.housesigma.android.views.Sheet
import com.lxj.xpopup.XPopup
import org.greenrobot.eventbus.EventBus


class LoginFragment : BaseDialogFragment() {

    private var showOneTapUI: Boolean = true
    private lateinit var signupModel: SignUpModel
    private var strList = ArrayList<String>()
    private var countrycodeXList = ArrayList<CountrycodeX>()
    private var loadingDialog: LoadingDialog? = null
    private lateinit var oneTapClient: SignInClient
    private lateinit var binding: DialogLoginBinding
    private val REQ_ONE_TAP = 2  // Can be any integer unique to the Activity
    private val RC_SIGN_IN = 1
    private var idToken: String? = ""
    private var reLoginType: String = ""

    private val mCallback: LoginCallback? by lazy {
        when {
            // 如果该 Fragment 作为子 Fragment 附在某个父 Fragment 上，且父 Fragment 实现了 Callback 接口
            parentFragment != null && parentFragment is LoginCallback -> parentFragment as LoginCallback
            // 如果该 Fragment 附在某个父 Fragment 上，但其未实现 Callback 接口
            // 但该 Fragment（直接或间接）附在了某个 Activity 上，且 Activity 实现了 Callback 接口
            activity != null && activity is LoginCallback -> activity as LoginCallback
            // 该 Fragment 没有附到任何 Activity 或父 Fragment 上，或它们都没有实现 Callback 接口
            else -> null
        }
    }

    interface LoginCallback {
        fun onLoginSuccess()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreen)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        GALog.log("login_start")
        signupModel = ViewModelProvider(this).get(SignUpModel::class.java)
        binding = DialogLoginBinding.inflate(inflater, container, false)
        loadingDialog = context?.let { LoadingDialog(it) }
        initViews()
        loadData()
        handleBiometricLogic()
        return binding.root
    }

    private fun handleBiometricLogic() {
        try {
            context?.let {
                if (RE_LOGIN_TRREB_TIMEOUT.equals(reLoginType) && Build.VERSION.SDK_INT > Build.VERSION_CODES.P) {
                    val biometricManager = BiometricManager.from(it)
                    val canAuthenticate = biometricManager.canAuthenticate()
                    if (canAuthenticate == BiometricManager.BIOMETRIC_SUCCESS) {
                        // 设备支持生物识别
                        setupBiometricAuthentication()
                    }
                }
            }
        } catch (securityException: Exception) {
            securityException.printStackTrace()
        }
    }

    @RequiresApi(Build.VERSION_CODES.P)
    private fun setupBiometricAuthentication() {
        binding.llRoot.visibility = View.GONE
        val biometricPrompt = BiometricPrompt(this,
            ContextCompat.getMainExecutor(requireActivity()),
            object : BiometricPrompt.AuthenticationCallback() {
                override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                    GALog.log("confirm_login_button_click","biometric_auth")
                    showLoadingDialog()
                    // 生物识别设置成功
                    signupModel.refreshSignIn()
                }

                override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                    // 处理错误
                    GALog.log("confirm_login_button_click","other_auth")
                    binding.llRoot.visibility = View.VISIBLE
                }

                override fun onAuthenticationFailed() {
//                    ToastUtils.showLong("onAuthenticationFailed")
//                    // 认证失败
//                    binding.llRoot.visibility = View.VISIBLE
                }
            })

        val promptInfo = BiometricPrompt.PromptInfo.Builder()
            .setTitle("HouseSigma")
            .setDescription("Stay signed-in with Biometrics")
            .setNegativeButtonText("Cancel")
            .build()

        biometricPrompt.authenticate(promptInfo)
    }

    private fun loadData() {
        signupModel.refreshSignIn.observe(this) {
            ToastUtils.showLong(it.message)
            loginSuccess()
        }

        signupModel.loadingLiveData.observe(this) {
            dismissLoadingDialog()
        }

        signupModel.searchAddress.observe(this) { list ->
            countrycodeXList.clear()
            strList.clear()
            countrycodeXList.addAll(list.countrycode)
            list.countrycode.forEach {
                strList.add(it.name)
            }

            list.countrycode.getOrNull(0)?.let {
                binding.tvCountryCode.text = it.countrycode
            }
        }

        signupModel.tempPasswordMsgRes.observe(this) {
            ToastUtils.showLong(it.message)
        }

        signupModel.googleSignRes.observe(this) {
            val user = HybridUtils.saveAndParseUserData(it.hybridUser)
            it.appUser = user?:User()
            if (it.registered) {
                it.appUser?.let {
                    loginSuccess(it, "onetap")
                }
            } else {
                // 这里不用user对象传递是存在一个问题：
                // User对象Parcelize化之后，有些字段一旦为空的时候
                // Parcelize化的时候，就会报空指针异常，需要检查全局代码有没有类似的问题(已检查过)
                val intent = Intent(activity, OneTapActivity::class.java)
                intent.putExtra("email", it.appUser?.email?:"")
                intent.putExtra("id_token", idToken)
                startActivity(intent)
            }

        }

        signupModel.getInitCountryCode()
    }

    /**
     * 登录成功后持久化操作
     */
    private fun loginSuccess(it: User, from: String) {
        GALog.log("login_success", from)

        saveUserInfo(it)

        notifyLoginSuccess()
    }

    private fun notifyLoginSuccess() {
        EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_MAP))
        EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_MARKET))
        EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_HOME))
        EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_FCM_TOKEN))
        EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_WEB_VIEW))
        mCallback?.onLoginSuccess()
    }

    /**
     * 生物登录成功（生物登录不需要持久化，本身就已经登录了，只是验证是机主自己）
     */
    private fun loginSuccess() {
        notifyLoginSuccess()
    }

    private fun initViews() {
        val decorView = dialog?.window?.decorView
        Sheet().setTouch(binding.llRoot, decorView, this)

        reLoginType = arguments?.getString("reLogin", "")?:""
        handleTitle(reLoginType)
        handleExplainWhyText(reLoginType)
        handleSendTemporaryPassword(reLoginType)
        hiddenSignUp(reLoginType)

        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestIdToken(BuildConfig.ONE_TAP_WEB_CLIENT_ID)
            .requestEmail()
            .build()
        // Build a GoogleSignInClient with the options specified by gso.
        var mGoogleSignInClient = GoogleSignIn.getClient(requireActivity(), gso)


        signupModel.loadingLiveData.observe(viewLifecycleOwner) {

            loadingDialog?.let {
                if (it.isShowing) {
                    it.dismiss()
                }
            }
        }

        signupModel.signInFailure.observe(viewLifecycleOwner) {
            var from = "phone"
            try {
                if ((binding.etEmail.visibility == View.VISIBLE)) {
                    from = "email"
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

            if (from == "phone") {
                GALog.log("login_failed",from)
            } else {
                GALog.log("login_failed",from)
            }
        }

        signupModel.signIn.observe(viewLifecycleOwner) {
            var from = "phone"
            try {
                if ((binding.etEmail.visibility == View.VISIBLE)) {
                    from = "email"
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

            if (from == "phone") {
                MMKVUtils.saveStr(SIGN_IN_VIA, "phone")
                MMKVUtils.saveStr(SIGN_IN_PHONE, binding.etPhone.text.toString().trim())
            } else {
                MMKVUtils.saveStr(SIGN_IN_VIA, "email")
                MMKVUtils.saveStr(SIGN_IN_EMAIL, binding.etEmail.text.toString().trim())
            }

            loginSuccess(it.appUser, from)
        }

        binding.rlSignInGoogle.setOnClickListener {
            GALog.log("third_party_auth_start","onetap")
            activity?.let {
                val signInIntent = mGoogleSignInClient.signInIntent
                startActivityForResult(signInIntent, RC_SIGN_IN)
            }
        }

        binding.tvCountryCode.setOnClickListener {
            XPopup.Builder(activity)
                .asBottomList(
                    "", strList.toTypedArray()
                ) { position, text ->
                    binding.tvCountryCode.text = countrycodeXList[position].countrycode
                }
                .show()
        }

        binding.tvForgotPassword.setOnClickListener {
            GALog.log("forgot_password_start")
            if (reLoginType== RE_LOGIN_TRREB_TIMEOUT) {
                signupModel.sendTempPassword()
            } else if (reLoginType == RE_LOGIN_VALIDATE){
                activity?.let {
                    startActivity(Intent(activity, ChangePasswordActivity::class.java))
                    dismiss()
                }
            } else {
                activity?.let {
                    startActivity(Intent(activity, ForgotPasswordActivity::class.java))
                    dismiss()
                }
            }
        }

        binding.tvSignIn.setOnClickListener {
            activity?.let {
                loadingDialog?.let {
                    it.show()
                }
            }

            if (binding.etEmail.visibility == View.VISIBLE) {
                GALog.log("login_submit", "email")
//                email login
                val email = binding.etEmail.text.toString().trim()
                val password = binding.etPassword.text.toString().trim()

//                if (!TextUtils.isEmpty(email)) {
//                    MMKVUtils.saveStr(SIGN_IN_EMAIL, email)
//                }

                signupModel.signIn(email = email, pass = password, reLoginType = reLoginType)
            } else {
                GALog.log("login_submit", "phone")
//                phone login
                val phone = binding.etPhone.text.toString().trim()
                val password = binding.etPassword.text.toString().trim()
                val countryCode = binding.tvCountryCode.text.toString().trim()

//                if (!TextUtils.isEmpty(countryCode)) {
//                    MMKVUtils.saveStr(SIGN_IN_COUNTRY_CODE, countryCode)
//                }


                signupModel.signIn(
                    phoneNumber = phone,
                    pass = password,
                    countryCode = countryCode,
                    reLoginType = reLoginType
                )
            }

        }

        binding.llSignUp.setOnClickListener {
            GALog.log("registration_start")
            startActivity(Intent(activity, SignUpActivity::class.java))
            dismiss()
        }

        binding.llEmail.setOnClickListener {

            binding.vLineEmail.setBackgroundResource(R.color.app_main_color)
            binding.vLinePhone.setBackgroundResource(R.color.color_cccccc)
            binding.tvEmail.setTextColor(resources.getColor(R.color.app_main_color))
            binding.tvPhone.setTextColor(resources.getColor(R.color.color_black))

            binding.etEmail.visibility = View.VISIBLE
            binding.llInputPhone.visibility = View.GONE
        }

        binding.llPhone.setOnClickListener {
            binding.vLinePhone.setBackgroundResource(R.color.app_main_color)
            binding.vLineEmail.setBackgroundResource(R.color.color_cccccc)
            binding.tvEmail.setTextColor(resources.getColor(R.color.color_black))
            binding.tvPhone.setTextColor(resources.getColor(R.color.app_main_color))

            binding.etEmail.visibility = View.GONE
            binding.llInputPhone.visibility = View.VISIBLE
        }


        restoreLastLoginUserName(binding)

        activity?.let {
//            DEV-5110 当软键盘弹起时，在最底部加了一定的高度，可以让用户滑动，展示出被软键盘挡住的内容
            SoftKeyBoardListener.setListener(it, object : SoftKeyBoardListener.OnSoftKeyBoardChangeListener {
                override fun keyBoardShow(height: Int) {
                    binding.vBottom.visibility = View.VISIBLE
                }

                override fun keyBoardHide(height: Int) {
                    binding.vBottom.visibility = View.GONE
                }
            })
        }
    }

    private fun hiddenSignUp(reLoginType: String) {
        if (reLoginType.equals(RE_LOGIN_TRREB_TIMEOUT)){
            binding.llSignUp.visibility = View.GONE
        } else {
            binding.llSignUp.visibility = View.VISIBLE
        }
    }

    private fun handleTitle(reLoginType: String) {
        if (reLoginType.equals(RE_LOGIN_VALIDATE)){
            binding.tvLoginTitle.text = "Re-Validate Password"
        }else if (reLoginType.equals(RE_LOGIN_TRREB_TIMEOUT)){
            binding.tvLoginTitle.text = "Confirm Login"
        } else {
            binding.tvLoginTitle.text = "Login"
        }
    }

    private fun handleSendTemporaryPassword(reLoginType: String) {
        if (reLoginType.equals(RE_LOGIN_VALIDATE)){
            binding.tvForgotPassword.text = "Change Password"
        }else if (reLoginType.equals(RE_LOGIN_TRREB_TIMEOUT)){
            binding.tvForgotPassword.text = "Get temporary password"
        } else {
            binding.tvForgotPassword.text = "Forgot Password?"
        }
    }

    private fun handleExplainWhyText(reLoginType: String) {
        if (!TextUtils.isEmpty(reLoginType)) {
            binding.tvExplainText.visibility = View.VISIBLE
            binding.tvExplainSubtext.visibility = View.VISIBLE
        } else {
            binding.tvExplainText.visibility = View.GONE
            binding.tvExplainSubtext.visibility = View.GONE
            return
        }
        val initApp = HSUtil.getInitApp()

        try {
            initApp?.let {
                val explainText :String
                val explainSubtext :String
                if (reLoginType.equals(RE_LOGIN_VALIDATE)){
                    explainText = initApp.revalidate_explain?.explain_text?:""
                    explainSubtext = initApp.revalidate_explain?.explain_subtext?:""
                }else if (reLoginType.equals(RE_LOGIN_TRREB_TIMEOUT)){
                    explainText = initApp.timeout_explain?.explain_text?:""
                    explainSubtext = initApp.timeout_explain?.explain_subtext?:""
                } else {
                    explainText = ""
                    explainSubtext = ""
                }

                val spanned = Html.fromHtml(explainText)
                val spannableStringBuilder = SpannableStringBuilder(spanned)
                val urls = spannableStringBuilder.getSpans(0, spanned.length, URLSpan::class.java)
                for (url in urls) {
                    val hsUrlSpan = HSURLSpan(activity, url.url)
                    val start = spannableStringBuilder.getSpanStart(url)
                    val end = spannableStringBuilder.getSpanEnd(url)
                    val flags = spannableStringBuilder.getSpanFlags(url)
                    spannableStringBuilder.setSpan(hsUrlSpan, start, end, flags)
                    //一定要加上这一句,看过很多网上的方法，都没加这一句，导致ClickableSpan的onClick方法没有回调，直接用浏览器打开了
                    spannableStringBuilder.removeSpan(url)
                }
                binding.tvExplainText.text = spannableStringBuilder
                binding.tvExplainText.movementMethod = LinkMovementMethod.getInstance()
                binding.tvExplainSubtext.text = explainSubtext
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 恢复上次登陆的账号
     */
    private fun restoreLastLoginUserName(binding: DialogLoginBinding) {
        val email = MMKVUtils.getStr(SIGN_IN_EMAIL)
        var phone = MMKVUtils.getStr(SIGN_IN_PHONE)
        val via = MMKVUtils.getStr(SIGN_IN_VIA)

        if (via == "email") {
            if (!TextUtils.isEmpty(email)) {
                binding.etEmail.setText(email)
                binding.llEmail.performClick()
            }
        } else {
            if (!TextUtils.isEmpty(phone)) {
                phone = phone?.replace("+86", "")
                phone = phone?.replace("+852", "")
                phone = phone?.replace("+91", "")
                phone = phone?.replace("+1", "")
                phone = phone?.replace("+44", "")


                binding.etPhone.setText(phone)
                binding.llPhone.performClick()
            }
        }
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            RC_SIGN_IN -> {
                val task = GoogleSignIn.getSignedInAccountFromIntent(data);
                handleSignInResult(task);
            }
            REQ_ONE_TAP -> {
                try {
                    val credential = oneTapClient.getSignInCredentialFromIntent(data)
                    idToken = credential.googleIdToken
                    when {
                        idToken != null -> {
                            // Got an ID token from Google. Use it to authenticate
                            // with your backend.
                            Logger.d( "Got ID token.")
                            signupModel.googleSignIn(credential = idToken!!)
                        }
                        else -> {
                            binding.llRoot.visibility = View.VISIBLE
                            // Shouldn't happen.
                            Logger.d( "No ID token!")
                        }
                    }
                } catch (e: ApiException) {
                    binding.llRoot.visibility = View.VISIBLE
                    when (e.statusCode) {
                        CommonStatusCodes.CANCELED -> {
                            Logger.d( "One-tap dialog was closed.")
                            // Don't re-prompt the user.
                            showOneTapUI = false
//                            setOneTapUI()
                        }
                        CommonStatusCodes.NETWORK_ERROR -> {
                            ToastUtils.showLong("One-tap encountered a network error.")
                            Logger.d( "One-tap encountered a network error.")
                            // Try again or just ignore.
                        }
                        else -> {
                            ToastUtils.showLong("Couldn't get credential from result.")
                            Logger.d( "Couldn't get credential from result." +
                                        " (${e.localizedMessage})"
                            )
                        }
                    }
                }
            }
        }
    }

    private fun handleSignInResult(completedTask: Task<GoogleSignInAccount>) {
        try {
            val account: GoogleSignInAccount = completedTask.getResult(ApiException::class.java)
            idToken = account.idToken
            signupModel.googleSignIn(credential = account.idToken!!, reLoginType = reLoginType)
            // Signed in successfully, show authenticated UI.
        } catch (e: ApiException) {
            // The ApiException status code indicates the detailed failure reason.
            // Please refer to the GoogleSignInStatusCodes class reference for more information.
            Logger.e( "signInResult:failed code=", e)
        }
    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        val windowParams = window?.attributes
//        windowParams?.dimAmount = 0.0f // 遮罩效果消失
        window?.setLayout(-1, -2)
        windowParams?.gravity = Gravity.BOTTOM
        window?.attributes = windowParams
//      // TODO 发现一个bug：设置BottomSheetDialogFragment 后，布局会被积压
//        view?.let {
//            val parent = it.parent as View
//            val behavior = BottomSheetBehavior.from(parent)
//            it.measure(0,0)
//            behavior.peekHeight = it.measuredHeight
//            val params = parent.layoutParams as CoordinatorLayout.LayoutParams
//            params.gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
//            parent.layoutParams = params
//        }

    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        getDialog()?.let { destroyImmersionBar(it) }
        arguments
    }

    companion object {
        const val USER_JSON = "user_json"

        const val LOGIN_TOKEN = "access_token"

        //        const val LOCATION = "location"
        const val Agreement = "agreement"

        const val USER_ID = "user_id"
        const val LOGIN_NAME = "login_name"
        const val SIGN_IN_NAME = "sign_in_name"

        const val CREATE_DATE = "create_date"
        const val LANG = "lang"
        const val PROVINCE = "province"

        const val SIGN_IN_VIA = "sign_in_via"
        const val SIGN_IN_EMAIL = "email"
        const val SIGN_IN_PHONE = "phone"

        // agent DEV-5477 [2023 Q3] TRREB Compliance for Agent | 根据用户是否为地产经纪显示不同内容
        const val IS_AGENT = "is_agent"
        const val LICENSED_PROVINCE_TEXT = "licensed_province_text"
        const val BOARD_NAME_TEXT = "board_name_text"
        const val BROKERAGE_NAME = "brokerage_name"

        const val CRACK_IS_AGENT = "crack_is_agent"
        const val CRACK_BROKERAGE_NAME = "cracking_brokerage_name"
        const val CRACK_LICENSED_PROVINCE = "crack_licensed_province"
        const val CRACK_BOARD_NAME = "crack_board_name"
        const val REFERRAL_CODE = "referral_code"

        const val RE_LOGIN_TRREB_TIMEOUT = "trreb_timeout"
        const val RE_LOGIN_VALIDATE = "re-validate"

        fun saveUserInfo(user: User?) {
            user?.let {
                MMKVUtils.saveStr(SIGN_IN_NAME, it.name)
                MMKVUtils.saveStr(LOGIN_NAME, it.login_name)
                MMKVUtils.saveStr(CREATE_DATE, it.create_date)
                MMKVUtils.saveStr(LANG, it.lang)
                MMKVUtils.saveStr(REFERRAL_CODE, it.referral_code?:"")
                MMKVUtils.saveStr(PROVINCE, it.province)

                if (it.agent != null) {
                    MMKVUtils.saveStr(IS_AGENT, "1")
                    MMKVUtils.saveStr(LICENSED_PROVINCE_TEXT, it.agent.licensed_province_text?:"")
                    MMKVUtils.saveStr(BOARD_NAME_TEXT, it.agent.board_name_text?:"")
                    MMKVUtils.saveStr(BROKERAGE_NAME, it.agent.brokerage_name?:"")
                } else {
                    MMKVUtils.saveStr(IS_AGENT, "0")
                }

                if (it.user_id != 0) {
                    MMKVUtils.saveStr(USER_ID, it.user_id.toString())
                }

                MMKVUtils.getStr(LoginFragment.USER_ID)?.let { userId ->
                    Firebase.analytics.setUserId(userId)
                }
            }
        }


        fun isLogin(): Boolean {
            return !TextUtils.isEmpty(MMKVUtils.getStr(LOGIN_NAME))
        }

        fun isAgreement(): Boolean {
            return MMKVUtils.getBoolean(Agreement)
        }

        fun loginOut(context: Context?) {
            if (context != null) {
                Identity.getSignInClient(context).signOut()
            }
            WatchedHelper.clearAllData()
            MMKVUtils.removeData(LOGIN_NAME)
            MMKVUtils.removeData("first_show_watch_hint")
            MMKVUtils.removeData(USER_JSON)
            //退出账号也保留省份选择
//            MMKVUtils.removeData(LOCATION)
//            MMKVUtils.removeData(SIGN_IN_EMAIL)
//            MMKVUtils.removeData(SIGN_IN_PHONE)
            //下面这俩控制首页的定制过滤的小红点
            MMKVUtils.removeData("label_filter_city")
            MMKVUtils.removeData("personalize_price")
            //退出账号移除控制评价
            MMKVUtils.removeData("stop_review")
//            MMKVUtils.removeData(LOGIN_TOKEN)
        }
    }
}


