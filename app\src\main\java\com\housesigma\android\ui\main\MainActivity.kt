package com.housesigma.android.ui.main

import android.content.Context
import android.content.Intent
import android.content.IntentSender
import android.os.Bundle
import android.text.TextUtils
import android.util.AttributeSet
import android.util.Log
import android.view.KeyEvent
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.google.android.gms.auth.api.identity.BeginSignInRequest
import com.google.android.gms.auth.api.identity.Identity
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.android.gms.tasks.OnCompleteListener
import com.google.android.gms.tasks.Task
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.installations.FirebaseInstallations
import com.google.firebase.ktx.Firebase
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.FirebaseRemoteConfigInfo
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfigSettings
import com.gyf.immersionbar.ImmersionBar
import com.housesigma.android.BuildConfig
import com.housesigma.android.HSApp
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.helper.JumpHelper
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.User
import com.housesigma.android.router.PublicRouter
import com.housesigma.android.ui.account.AccountViewModel
import com.housesigma.android.ui.home.HomeViewModel
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.onetap.OneTapActivity
import com.housesigma.android.ui.signup.SignUpModel
import com.housesigma.android.ui.watched.WatchedViewModel
import com.housesigma.android.utils.*
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.NoScrollViewPager
import me.jessyan.autosize.AutoSize
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


class MainActivity : BaseActivity() , LoginFragment.LoginCallback{

    private lateinit var signupModel: SignUpModel
    private lateinit var homeViewModel: HomeViewModel
    private lateinit var accountViewModel: AccountViewModel
    private lateinit var watchedViewModel: WatchedViewModel
    private lateinit var mAdapter: MainFragmentPagerAdapter
    private lateinit var vp: NoScrollViewPager
    private var reLogin: Boolean = true

    private lateinit var bottomNavigationView: BottomNavigationView

    private var selectedIndex: Int = -1
    private var loginDialog: LoginFragment? = null

    private val oneTapClient by lazy { Identity.getSignInClient(this) }
    private val REQ_ONE_TAP = 2  // Can be any integer unique to the Activity
    private var idToken: String? = ""

    private var firstPressBackTime: Long = 0

    override fun getLayout(): Any {
        return R.layout.activity_main
    }

    override fun initView() {
        PermissionUtils.requestNotificationPermission(this)
        mAdapter = MainFragmentPagerAdapter(supportFragmentManager)

        vp = findViewById<NoScrollViewPager>(R.id.vp)!!
        bottomNavigationView = findViewById(R.id.nav_view)!!

        vp.offscreenPageLimit = 5
        vp.adapter = mAdapter
        vp.setCurrentItem(0, false)

        mAdapter = MainFragmentPagerAdapter(supportFragmentManager)

        setIndexSelected(0)
        bottomNavigationView.setOnNavigationItemSelectedListener(mOnNavigationItemSelectedListener)
        bottomNavigationView.itemIconTintList = null
    }

    private fun sendGAUserId() {
        MMKVUtils.getStr(LoginFragment.USER_ID)?.let { userId ->
            Firebase.analytics.setUserId(userId)
        }
    }

    private fun loginSuccess(it: User) {
        LoginFragment.saveUserInfo(it)
    }

    private fun showGoogleOneTap() {
        if (HSApp.isShowedOneTap) return
        HSApp.isShowedOneTap = true
        this?.let {

            val googleApiAvailability = GoogleApiAvailability.getInstance()
            val available =
                googleApiAvailability.isGooglePlayServicesAvailable(it) == ConnectionResult.SUCCESS
            if (!available) {
                return
            }


            val signInRequest = BeginSignInRequest.builder()
//                .setPasswordRequestOptions(
//                    BeginSignInRequest.PasswordRequestOptions.builder()
//                        .setSupported(true)
//                        .build()
//                )
                .setGoogleIdTokenRequestOptions(
                    BeginSignInRequest.GoogleIdTokenRequestOptions.builder()
                        .setSupported(true)
                        // Your server's client ID, not your Android client ID.
                        .setServerClientId(BuildConfig.ONE_TAP_WEB_CLIENT_ID)
                        // Only show accounts previously used to sign in.
                        .setFilterByAuthorizedAccounts(false)
                        .build()
                )
                // Automatically sign in when exactly one credential is retrieved.
                .setAutoSelectEnabled(false)
                .build()

            oneTapClient.beginSignIn(signInRequest)
                .addOnSuccessListener(it) { result ->
                    try {
                        startIntentSenderForResult(
                            result.pendingIntent.intentSender, REQ_ONE_TAP,
                            null, 0, 0, 0, null
                        )
                    } catch (e: IntentSender.SendIntentException) {
                        Logger.d( "Couldn't start One Tap UI: ${e.localizedMessage}")
                    }
                }
                .addOnFailureListener(it) { e ->
                    // No saved credentials found. Launch the One Tap sign-up flow, or
                    // do nothing and continue presenting the signed-out UI.
//                        ToastUtils.showLong(e.localizedMessage)
                    e.printStackTrace()
                    Logger.d( "addOnFailureListener" + e.localizedMessage)
                }
        }

    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            REQ_ONE_TAP -> {
                try {
                    val credential = oneTapClient.getSignInCredentialFromIntent(data)
                    idToken = credential.googleIdToken
                    when {
                        idToken != null -> {
                            // Got an ID token from Google. Use it to authenticate
                            // with your backend.
                            GALog.log("third_party_auth_start","onetap")
                            Logger.d( "Got ID token.")
                            signupModel.googleSignIn(credential = idToken!!)
                        }
                        else -> {
                            // Shouldn't happen.
                            Logger.d( "No ID token!")
                        }
                    }
                } catch (e: ApiException) {
                    when (e.statusCode) {
                        CommonStatusCodes.CANCELED -> {
                            Logger.d( "One-tap dialog was closed.")
                            // Don't re-prompt the user.
                        }
                        CommonStatusCodes.NETWORK_ERROR -> {
                            ToastUtils.showLong("One-tap encountered a network error.")
                            Logger.d( "One-tap encountered a network error.")
                            // Try again or just ignore.
                        }
                        else -> {
                            ToastUtils.showLong("Couldn't get credential from result.")
                            Log.d(
                                "hkj", "Couldn't get credential from result." +
                                        " (${e.localizedMessage})"
                            )
                        }
                    }
                }
            }
        }
    }

    override fun onCreateView(name: String, context: Context, attrs: AttributeSet): View? {
        // 解决预加载WebView时，HSWebView中适配PAD失效问题 https://github.com/JessYanCoding/AndroidAutoSize/issues/5
        if (!HSUtil.isPad(context)) {
            AutoSize.autoConvertDensityOfGlobal(this)
        }
        return super.onCreateView(name, context, attrs)
    }

    override fun initData() {
        initRemoteConfig()


        signupModel = ViewModelProvider(this).get(SignUpModel::class.java)
        homeViewModel = ViewModelProvider(this).get(HomeViewModel::class.java)
        accountViewModel = ViewModelProvider(this).get(AccountViewModel::class.java)
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)

        homeViewModel.disclaimerInfo.observe(this) {
            val disclaimerJsonStr = GsonUtils.parseToStr(it)
            MMKVUtils.saveStr("disclaimers_v2",disclaimerJsonStr)
            EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_DISCLAIMERS))
        }


        homeViewModel.nativeRouter.observe(this) {
            Logger.d( "native router $it")
            PublicRouter().open(this, it.action, it.params)
        }

        signupModel.googleSignRes.observe(this) {
            if (it.registered) {
                loginSuccess(it.appUser)
                EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_MARKET))
                EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_HOME))
                EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_FCM_TOKEN))
                EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_WEB_VIEW))
            } else {
                val intent = Intent(this, OneTapActivity::class.java)
                intent.putExtra("email", it.appUser.email)
                intent.putExtra("id_token", idToken)
                startActivity(intent)
            }
        }

        checkToken()
        pushFCMToken()

        val extras = intent.extras
        var isJumped = handleURI(intent.extras)
        if (extras != null && extras.getBundle("data") != null) {
            isJumped = handleURI()
        }

        if (!isJumped && !LoginFragment.isLogin()) {
            showGoogleOneTap()
        }

        if (LoginFragment.isLogin()) {
            sendGAUserId()
        }

        homeViewModel.getDisclaimer()
    }



    private fun logInstallationAuthToken() {
        // [START get_installation_token]
        FirebaseInstallations.getInstance().getToken(/* forceRefresh */ true)
            .addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    Logger.e("Installation auth token: " + task.result?.token)
                } else {
                    Logger.e("Unable to get Installation auth token")
                }
            }
        // [END get_installation_token]
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.clear()
    }

    private fun initRemoteConfig() {
        val remoteConfig: FirebaseRemoteConfig = Firebase.remoteConfig
        remoteConfig.ensureInitialized().addOnCompleteListener(object :
            OnCompleteListener<FirebaseRemoteConfigInfo> {
            override fun onComplete(p0: Task<FirebaseRemoteConfigInfo>) {
//              [START enable_dev_mode] 获取remote config同步时间，非prod渠道用
                if (!"prod".equals(BuildConfig.FLAVOR)) {
                    val configSettings = remoteConfigSettings {
                        minimumFetchIntervalInSeconds = 60
                    }
                    remoteConfig.setConfigSettingsAsync(configSettings)
                }
//              [END enable_dev_mode]
                remoteConfig.fetchAndActivate()
                    .addOnCompleteListener(this@MainActivity) { task ->
                        if (task.isSuccessful) {
                            Logger.e("Fetch and activate succeeded")
                            /**
                             * DEV-4136 测试ab test是否可用 该实验结束后，要移除该逻辑
                             */
                            val versionSuffix = remoteConfig.getString("version_suffix")
                            if (!TextUtils.isEmpty(versionSuffix)) {
                                val label =  versionSuffix+"_load_4136"
                                GALog.log("common_ab_test",label)
                            }


                            val abTestArray = ArrayList<String>()
                            val showAbTestConfigs = ArrayList<String>()
                            /**
                             * DEV-4349 获取A/B Test下发的所有remoteConfig参数，用于产生GA报表分析用
                             */
                            for (config in remoteConfig.all) {
                                // key为issue number 如 DEV-4136
                                val key = config.key?:""
                                // value为A、B组
                                var value = (config.value.asString()?:"").toString()
                                if (TextUtils.isEmpty(value)) {
                                    value = "default" //表示不在实验组内或实验未开始
                                }
                                val label = value + "_load_" + key
                                abTestArray.add(label)

                                val showLabel = key + " " + value
                                showAbTestConfigs.add(showLabel)
                            }
                            settingUserScope(abTestArray)

                            /**
                             * DEV-4387 api - 接口添加参数，接收 A/B 参数
                             * 后续可能根据 A/B group, 各个 API 返回不同的结果，实现 API 的 A/B 测试逻辑
                             *
                             * 如果传空字符串的话，不会保存，传 {} 的话会覆盖掉之前的配置,在这里一定会在remote_config中获取正确配置
                             */
                            val json = GsonUtils.parseToStr(remoteConfig.all) ?: "{}"
                            homeViewModel.uploadABTestConfig(json)
                            MMKVUtils.saveStr("ab_test_remote_config",abTestArray.joinToString("-"))
                            EventBus.getDefault().postSticky(MessageEvent(MessageType.SHOW_AB_TEST_CONFIG).put(showAbTestConfigs.joinToString( "\n")))
                        } else {
                            Logger.e("Fetch failed")
                        }
                    }
            }
        })



    }

    /**
     * DEV-5911 Native 发布 GA user scope setting
     */
    private fun settingUserScope(abTestArray: ArrayList<String>) {
        val hashMap = HashMap<String, String>()
        abTestArray?.forEachIndexed { index, string ->
            try {
                val split = string?.split("_load_")
                // 殿轩：custom dimension的数量是有上限的，一次只能发一个，ab test实验停止后再发下一个
                val analytics = Firebase.analytics
                analytics.setUserProperty("ab_test_dimension", (split?.get(1)) + (split?.get(0)))
                hashMap.put("ab_test_dimension", (split?.get(1)) + (split?.get(0)))
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        HSLog.setUserProperty(hashMap)
    }

    /**
     * 处理从外部跳转过来的
     */
    private fun handleURI(newIntentBundle: Bundle? = null): Boolean {
        // 从推送过来的
//        Logger.d( "intent has extras : " + intent.extras)
        val bundle = newIntentBundle?:intent.getBundleExtra("data")
        Logger.e("intent has extras : " + bundle)
        val type = bundle?.getString("type")
        if (type == "msg") {
            GALog.log("push_notification_click")

            val pushType = bundle.getString("push_type")
            if (pushType == "push_watched_property") {
                GALog.log("hs_push_click","push_watched_property")
            }

            val idListing = bundle.getString("id_listing")
            idListing?.let {
                val map = HashMap<String, Any>()
                map["listing_id"] = idListing
                PublicRouter().open(this, "listing_detail", map)
                return true
            }
        } else if (type == "applinks"){
            val uri = bundle.getString("uri")
            Logger.d( "applinks uri is : " + uri)
            homeViewModel.urlTransform(uri.toString())
            return true
        }


        //从url scheme跳进来的
        val action = intent.action
        if (Intent.ACTION_VIEW.equals(action)) {
            val uri = intent.data
            if (uri != null) {
                Logger.d( "uri : $uri")
                homeViewModel.urlTransform(uri.toString())
                return true
            }
        }
        return false
    }

    private fun checkToken() {
        homeViewModel.token.observe(this) {
            Logger.d( "token : " + it.access_token)
            MMKVUtils.saveStr(LoginFragment.LOGIN_TOKEN, it.access_token)
            LoginFragment.loginOut(this)
            val intent = Intent(MainActivity@ this, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
        }
    }

    /**
     * 监听收到的fcm token，发送给服务端 /api/init/app/pushtoken，服务端拿到token 就可以推送了
     *
     */
    private fun pushFCMToken() {
// 切换用户，切换后的这个用户的fcm token和上个用户的相同就要先deleteToken再获取 岳：这里不需要这个逻辑
//        FirebaseMessaging.getInstance().deleteToken()
        FirebaseMessaging.getInstance().token.addOnCompleteListener(OnCompleteListener { task ->
            if (!task.isSuccessful) {
                Logger.d( "Fetching FCM registration token failed", task.exception)
                return@OnCompleteListener
            }

            // Get new FCM registration token
            val token = task.result
            Logger.d( "Get new FCM registration token : $token")
            homeViewModel.pushToken(token)
        })
    }

    override fun onStart() {
        super.onStart()
        // Main页面没有销毁的情况，不需要unregister
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        // launchMode="singleTask" 的时候 从外部跳转是不走onCreate函数的，走onNewIntent
        setIntent(intent)
        val bundle = intent?.getBundleExtra("data")?:getIntent().extras
        handleURI(bundle)
    }

    override fun onResume() {
        super.onResume()
        sendGAUserId()
    }

    override fun onStop() {
        super.onStop()
//        EventBus.getDefault().unregister(this)//解绑
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        Logger.d( "onMessageEvent " + event.type)
        when (event.type) {
            MessageType.ReLogin -> {
                if (reLogin) {
                    reLogin = !reLogin
                    Logger.d( "MessageType.ReLogin.....")
                    homeViewModel.getAccessToken()
                }
            }

            MessageType.JUMP_HOME -> {
                jumpToHome()
            }

            MessageType.JUMP_HOME_WATCHED -> {
                jumpToWatched()
            }

            MessageType.JUMP_HOME_ACCOUNT -> {
                jumpToAccount()
            }

            MessageType.RELOAD_FCM_TOKEN -> {
                pushFCMToken()
                accountViewModel.setLang(LanguageUtils().getLANG())
            }

            MessageType.WATCHED_MULTIPLE_WATCHLIST_CHANGED,MessageType.NEED_REQUEST_WATCHLISTLISTINGIDS -> {
                watchedViewModel.watchlistListingIds()
            }
            else -> {}
        }
    }

    private val mOnNavigationItemSelectedListener =
        BottomNavigationView.OnNavigationItemSelectedListener { item ->

            when (item.itemId) {

                // home, market 和 map 都无需登陆就能看
                R.id.navigation_home -> {
                    GALog.log("tabbar_click", "home")
                    setIndexSelected(0)
                    item.isChecked = true
                    return@OnNavigationItemSelectedListener true
                }
                R.id.navigation_map -> {
                    GALog.log("tabbar_click", "map")
                    val firstShowWatchHint = MMKVUtils.getBoolean("first_show_watch_hint", true)
                    MMKVUtils.saveBoolean("first_show_watch_hint", false)
                    JumpHelper.jumpMapForSaleActivity(this, isShowWatchHint = firstShowWatchHint)
                    return@OnNavigationItemSelectedListener false
                }
                R.id.navigation_watched -> {
                    GALog.log("tabbar_click", "watched")
                    if (LoginFragment.isLogin()) {
                        setIndexSelected(2)
                        item.isChecked = true
                        return@OnNavigationItemSelectedListener true
                    } else {
                        showLoginDialog()
                        return@OnNavigationItemSelectedListener false
                    }
                }

                R.id.navigation_market -> {
                    GALog.log("tabbar_click", "market")
//                    WebViewHelper.jumpMarket(this)
                    setIndexSelected(3)
                    item.isChecked = true
                    return@OnNavigationItemSelectedListener true
                }

                R.id.navigation_account -> {
                    GALog.log("tabbar_click", "account")
                    if (LoginFragment.isLogin()) {
                        setIndexSelected(4)
                        item.isChecked = true
                        return@OnNavigationItemSelectedListener true
                    } else {
                        showLoginDialog()
                        return@OnNavigationItemSelectedListener false
                    }
                }
            }
            false
    }

    fun jumpToMarket() {
        bottomNavigationView.selectedItemId = R.id.navigation_market
    }

    fun jumpToWatched() {
        bottomNavigationView.selectedItemId = R.id.navigation_watched
    }

    fun jumpToHome() {
        bottomNavigationView.selectedItemId = R.id.navigation_home
    }

    fun jumpToAccount() {
        bottomNavigationView.selectedItemId = R.id.navigation_account
    }

    private fun setIndexSelected(index: Int) {
        if (selectedIndex == index) {
            return
        }
        vp.setCurrentItem(index, false)
        selectedIndex = index
    }

    private fun showLoginDialog() {
        GALog.log("login_button_click")
        supportFragmentManager.let {
            if (loginDialog == null) {
                loginDialog = LoginFragment()
            }
            if (loginDialog?.isAdded == true) return
            loginDialog?.show(it, "")
        }
    }

    override fun onBackPressed() {
        val intent = Intent()
        intent.action = Intent.ACTION_MAIN
        intent.addCategory(Intent.CATEGORY_HOME)
        intent.addCategory(Intent.CATEGORY_DEFAULT)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        startActivity(intent)
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        val secondPressBackTime = System.currentTimeMillis()
        if (keyCode==KeyEvent.KEYCODE_BACK){
            if (secondPressBackTime - firstPressBackTime < 2000) {
                return super.onKeyDown(keyCode, event)
            } else {
                ToastUtils.showLong(resources.getString(R.string.back_tip))
                firstPressBackTime = System.currentTimeMillis()
            }
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

}