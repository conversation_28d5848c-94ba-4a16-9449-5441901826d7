package com.housesigma.android.ui.main;

import android.util.Log;
import android.view.ViewGroup;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.housesigma.android.ui.account.AccountFragment;
import com.housesigma.android.ui.home.HomeFragment;
import com.housesigma.android.ui.map.MapFragment;
import com.housesigma.android.ui.market.MarketFragment;
import com.housesigma.android.ui.watched.WatchedFragment;


public class MainFragmentPagerAdapter extends FragmentPagerAdapter {
    private final int PAGER_COUNT = 5;
    private HomeFragment homeFragment = null;
    private MapFragment mapFragment = null;
    private WatchedFragment watchedFragment = null;
    private MarketFragment marketFragment = null;
    private AccountFragment accountFragment = null;

    public MainFragmentPagerAdapter(FragmentManager fm) {
        super(fm);
        homeFragment = new HomeFragment();
        mapFragment = new MapFragment();
        watchedFragment = new WatchedFragment();
        marketFragment = new MarketFragment();
        accountFragment = new AccountFragment();
    }


    @Override
    public int getCount() {
        return PAGER_COUNT;
    }

    @Override
    public Object instantiateItem(ViewGroup vg, int position) {
        return super.instantiateItem(vg, position);
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        super.destroyItem(container, position, object);
    }

    @Override
    public Fragment getItem(int position) {
        Fragment fragment = null;
        switch (position) {
            case 0:

                if (homeFragment==null){
                    homeFragment = new HomeFragment();

                }
                fragment = homeFragment;
                break;
            case 1:
                if (mapFragment==null){
                    mapFragment = new MapFragment();
                }
                fragment= mapFragment;
                break;
            case 2:
                if (watchedFragment==null){
                    watchedFragment = new WatchedFragment();

                }

                fragment= watchedFragment;
                break;
            case 3:
                if (marketFragment==null){
                    marketFragment = new MarketFragment();
                }

                accountFragment = new AccountFragment();
                fragment= marketFragment;
                break;
            case 4:
                if (accountFragment==null){
                    accountFragment = new AccountFragment();
                }
                fragment= accountFragment;
                break;
        }
        return fragment;
    }
}
