package com.housesigma.android.ui.main

import android.util.Log
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.donkingliang.labels.LabelsView
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityPersonalizeListingsBinding
import com.housesigma.android.model.CustomizedFilter
import com.housesigma.android.model.CustomizedMunicipalityFilter
import com.housesigma.android.model.HouseTypeFilter
import com.housesigma.android.ui.recommended.RecommendModel
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.SaleSeekBar
import com.jaygoo.widget.OnRangeChangedListener
import com.jaygoo.widget.RangeSeekBar


class PersonalizeListingsActivity : BaseActivity() {

    private lateinit var binding: ActivityPersonalizeListingsBinding
    private lateinit var recommendModel: RecommendModel
    private var adapter = PersonalizeListingsAdapter()
//    private var priceLeft: Int = 0
//    private var priceRight: Int = 6000000


    override fun onResume() {
        super.onResume()
        GALog.page("customize_preference")
    }
    override fun getLayout(): Any {
        binding = ActivityPersonalizeListingsBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        initViews()
    }

    override fun initData() {
        recommendModel = ViewModelProvider(this).get(RecommendModel::class.java)
        recommendModel.msgRes.observe(this) {
            ToastUtils.showLong(it.message)
            finish()
        }
        recommendModel.customizedFilter.observe(this) {
            handleCacheData(it)
        }

        recommendModel.getCustomizedFilter()
    }

    /**
     * 数据穿插对比，把上次记忆的数据，塞回到label的状态中
     */
    private fun handleCacheData(it: CustomizedFilter) {
        val activeFilter = it.active_filter
        val municipalityId = activeFilter.municipality_id
        val municipalityFilter = it.municipality_filter
        // 穿插比对，对municipality_filter中的isSelect字段赋值,从接口中拿lable是否选中
        for (itemMunicipalityId in municipalityId) {
            for (itemMunicipalityFilter in municipalityFilter) {
                var judgeAllSelectCount = 0
                val innerItemMunicipalityFilter = itemMunicipalityFilter.list
                for (municipalityItemFilter in innerItemMunicipalityFilter) {
                    if (itemMunicipalityId == municipalityItemFilter.id) {
                        municipalityItemFilter.isSelect = true
                    }

                    if (!municipalityItemFilter.isSelect) {
                        judgeAllSelectCount++
                    }
                }
                itemMunicipalityFilter.isSelect = (judgeAllSelectCount==0)
            }
        }

        val houseTypeFilter = it.house_type_filter
        val activeHouseType = activeFilter.house_type
        for (itemActiveHouseType in activeHouseType) {
            for (itemHouseTypeFilter in houseTypeFilter) {
                if (itemHouseTypeFilter.id == itemActiveHouseType) {
                    itemHouseTypeFilter.isSelect = true
                }
            }
        }

        adapter.addData(municipalityFilter)


        binding.labels.setLabels(houseTypeFilter) { _, _, data -> data.name }
        val selectIntArray: MutableList<Int> = ArrayList()
        var i = 0
        for (itemHouseTypeFilter in houseTypeFilter) {
            if (itemHouseTypeFilter.isSelect == true) {
                selectIntArray.add(i)
            }
            i++
        }
        binding.labels.setSelects(selectIntArray)

        val priceMax = if (activeFilter.price_max == 0) 6000000.0f else activeFilter.price_max
        val priceMin = activeFilter.price_min


        binding.saleSb.setProgress(HSUtil.calRevertSquareFootageCurve(priceMin.toInt()).toFloat(),
            HSUtil.calRevertSquareFootageCurve(priceMax.toInt()).toFloat()
        )
    }

    private fun initViews() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        binding.labels.selectType = LabelsView.SelectType.MULTI
        binding.ivClose.setOnClickListener { finish() }
        binding.rv.adapter = adapter
        binding.rv.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)


        adapter.addChildClickViewIds(
            R.id.tv_select_all
        )
        adapter.setOnItemChildClickListener { adapter, view, position ->
            when (view.id) {
                R.id.tv_select_all -> {
                    val item = adapter.getItem(position) as CustomizedMunicipalityFilter
                    item.isSelect = !item.isSelect
                    for (municipalityItemFilter in item.list) {
                        municipalityItemFilter.isSelect = item.isSelect
                    }
                    adapter.notifyItemChanged(position)
                }
            }
        }
        binding.saleSb.setOnChangeListener(object :SaleSeekBar.OnChangeListener{
            override fun onChange(showPrice: String) {
                binding.tvPrice.text = showPrice
            }

            override fun onStopTrackingTouch() {
            }
        })

        binding.tvClear.setOnClickListener {
            binding.labels.clearAllSelect()
            binding.saleSb.setDefaultValue()

            val data = adapter.data
            if (data == null) return@setOnClickListener
            val list = data as List<CustomizedMunicipalityFilter>
            for (customizedMunicipalityFilter in list) {
                for (itemHouseTypeFilter in customizedMunicipalityFilter.list) {
                    itemHouseTypeFilter.isSelect = false
                }
            }
            adapter.notifyDataSetChanged()
        }

        binding.tvSave.setOnClickListener {
            GALog.log("preferences_submit")
            val selectLabelDatas = binding.labels.getSelectLabelDatas<HouseTypeFilter>()
            Logger.d( "house_type >>>>$selectLabelDatas")

            val selectHouseTypeFilterID = ArrayList<String>()
            for (item in selectLabelDatas) {
                selectHouseTypeFilterID.add(item.id)
            }


            val municipalityIds = ArrayList<Int>()
            for (datum in adapter.data) {
                for (municipalityItemFilter in datum.list) {
                    if (municipalityItemFilter.isSelect) {
                        municipalityIds.add(municipalityItemFilter.id)
                    }
                }
            }

            Logger.d( "municipality_id >>>>$municipalityIds")

            val cacheProperty = municipalityIds.toString()
                .replace("[", "")
                .replace("]", "")
                .replace(" ", "")
            // 持久化 写入cache
            MMKVUtils.saveStr("label_filter_city", cacheProperty)

            // 价格更改过
            MMKVUtils.saveBoolean("personalize_price", binding.saleSb.isDefaultValue())


            recommendModel.searchHomePageCustomize(
                municipalityIds,
                selectHouseTypeFilterID,
                price_max = binding.saleSb.priceRight,
                price_min = binding.saleSb.priceLeft
            )
        }

    }


}