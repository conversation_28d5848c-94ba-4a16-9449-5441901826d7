package com.housesigma.android.ui.map

import android.location.Location
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.housesigma.android.HSApp
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.utils.FusedLocationUtils
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.log.Logger
import org.greenrobot.eventbus.EventBus

open class BaseMapActivity :  AppCompatActivity() {

    private var mFusedLocationHelper: FusedLocationUtils? = null
    private var mLastLocationTimestamp: Long = 0

    override fun onResume() {
        super.onResume()
        collectUserLocation()
    }

    fun collectUserLocation() {
        HSApp.initApp?.let {
            val locationCollectRate = it.locationCollectRate ?: 0
            // 上次时间和这次时间进行对比，如果时间间隔小于采集频率，不进行采集
            val currentTime = System.currentTimeMillis()
            if (currentTime - mLastLocationTimestamp < locationCollectRate * 1000) {
                Logger.i("collectUserLocation locationCollectRate is too fast")
                return
            }
            mLastLocationTimestamp = currentTime
        }

        // 如果没有google play服务，不进行定位
        if (GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(this) != ConnectionResult.SUCCESS) {
            Logger.i("collectUserLocation GoogleApiAvailability is not available")
            return
        }
        Logger.i("collectUserLocation GoogleApiAvailability is available")

        // 如果没有定位权限，不进行定位
        if (mFusedLocationHelper == null) {
            mFusedLocationHelper = FusedLocationUtils(this)
        }

        // 获取用户的位置信息
        mFusedLocationHelper?.getCurrentLocation(object : FusedLocationUtils.LocationResultListener {
            override fun onLocationResult(location: Location?) {
                Logger.d("collectUserLocation onLocationResult $location")
                if (location != null) {
                    val latitude = location.latitude
                    val longitude = location.longitude
                    Logger.d("collectUserLocation onLocationResult $latitude $longitude")
                    // 保存用户的位置信息
                    MMKVUtils.saveDouble("user_location_latitude", latitude)
                    MMKVUtils.saveDouble("user_location_longitude", longitude)
                    MMKVUtils.saveLong("user_location_timestamp", System.currentTimeMillis())
                    EventBus.getDefault().postSticky(MessageEvent(MessageType.UPDATE_LAST_POSITION))
                } else {
                    // 处理位置为空的情况
                    Logger.d("collectUserLocation onLocationResult location is null")
                }
            }

            override fun onLocationError(error: String) {
                // 处理位置获取错误的情况
                Logger.d("requestLocationTest onLocationError $error")
            }
        })
    }
}