package com.housesigma.android.ui.map

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Color
import android.graphics.RectF
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Bundle
import android.os.Handler
import android.provider.Settings
import android.text.Html
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.text.style.URLSpan
import android.view.*
import android.widget.CompoundButton
import android.widget.LinearLayout
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.gson.Gson
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.AbsSuperApplication
import com.housesigma.android.BuildConfig
import com.housesigma.android.R
import com.housesigma.android.databinding.ActivityMapBinding
import com.housesigma.android.databinding.PopMapFsSDlFilterBinding
import com.housesigma.android.databinding.PopwindowMapSchoolBinding
import com.housesigma.android.helper.ProvinceHelper
import com.housesigma.android.model.*
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.main.MainActivity
import com.housesigma.android.ui.map.helper.MapHelper
import com.housesigma.android.ui.map.propertype.MapFiltersView
import com.housesigma.android.ui.map.propertype.MapListViewSortTypeView
import com.housesigma.android.ui.map.propertype.MapPropertyView
import com.housesigma.android.ui.map.propertype.MapSettingDialog
import com.housesigma.android.ui.search.SearchActivity
import com.housesigma.android.ui.tos.TosDialog
import com.housesigma.android.ui.tos.VowTosDialog
import com.housesigma.android.ui.watcharea.WatchedAreaActivity
import com.housesigma.android.ui.watched.WatchedHelper
import com.housesigma.android.ui.watched.WatchedViewModel
import com.housesigma.android.ui.watcharea.WatchedAreaSuccessFragment
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.*
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.DisclaimerViewHelper
import com.housesigma.android.views.HSAlertDialog
import com.housesigma.android.views.HSDecoration
import com.housesigma.android.views.HSPopWindow
import com.housesigma.android.views.SwitchButton
import com.housesigma.android.views.tip.TipGAEvent
import com.housesigma.android.views.tip.TipHelper
import com.housesigma.android.views.tip.TipModel
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import org.maplibre.android.annotations.*
import org.maplibre.android.annotations.Polygon
import org.maplibre.android.camera.CameraPosition
import org.maplibre.android.camera.CameraUpdateFactory
import org.maplibre.android.geometry.LatLng
import org.maplibre.android.geometry.LatLngBounds
import org.maplibre.android.maps.MapView
import org.maplibre.android.maps.Style
import kotlinx.coroutines.*
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import permissions.dispatcher.*
import java.math.BigDecimal
import org.maplibre.android.MapLibre
import org.maplibre.android.maps.MapLibreMap
import java.util.concurrent.ConcurrentHashMap


@RuntimePermissions
class MapActivity : BaseMapActivity(), LoginFragment.LoginCallback {

    companion object {
        const val REQUEST_CODE_WATCHED_AREA = 1001
    }


    //====================Map相关的参数start=========================

    private var lastListingInfo: MapMarkerInfo? = null
    private var lastSchoolInfo: SchoolInfo? = null
    private var lastNearByInfo: MapMarkerInfo? = null
    private var lastFeatrueInfo: MapMarkerInfo? = null
    private var lastClickMarker: Marker? = null

    private var lastMarkerInfoIds: List<String> = ArrayList()
    private lateinit var mapView: MapView
    private lateinit var mapLibreMap: MapLibreMap
    private var isMapViewActive: Boolean = false // 用于标记地图是否激活状态
    private var isMapStyleLoaded: Boolean = false // 用于标记地图样式是否加载成功


    private var currentClickMarkerId = 0L

    // 各个api 同等类型的marker单独储存，去重逻辑单独处理 优势 更清晰好维护
    private var listingMarkerInfoMap = ConcurrentHashMap<Long, MapMarkerInfo>() //listing2
    private var nearbyMarkerInfoMap = ConcurrentHashMap<Long, MapMarkerInfo>()
    private var featureMarkerInfoMap = ConcurrentHashMap<Long, MapMarkerInfo>()
    private var viewInMapInfoMap = ConcurrentHashMap<Long, String>()

    private var edmontonCenterMarker : Marker?=null

    private var schoolMarkerInfoMap = ConcurrentHashMap<Long, SchoolInfo>()

    private var mapSettingsDialog: MapSettingDialog? = null

    private var lon1: Double = 0.0
    private var lon2: Double = 0.0
    private var lat1: Double = 0.0
    private var lat2: Double = 0.0

    /**
     * 真实的经纬度参数
     */
    private var realLon1: Double = 0.0
    private var realLon2: Double = 0.0
    private var realLat1: Double = 0.0
    private var realLat2: Double = 0.0


    //====================Map相关的参数end===========================

    private var schoolCatholic: Int = 1
    private var schoolElementary: Int = 1
    private var schoolMatch_score: Int = 1
    private var schoolPublic: Int = 1
    private var schoolSecondary: Int = 1


    private lateinit var binding: ActivityMapBinding
    private lateinit var mapHelper: MapHelper
    private lateinit var mapViewModel: MapViewModel

    // 1. 3. 5是卖， 2 4 6是租房
    private var listType = ArrayList<String>()

    private var mListViewPage = 1

    // sale/sold/de listed筛选数据，其中"sale"包含了delisted和sold两个条件了
    private var mapFilter: MapFilter? = null

    private var mapPropertyView: MapPropertyView? = null
    private var mapFiltersView: MapFiltersView? = null
    private var mapListViewSortTypeView: MapListViewSortTypeView? = null

    // 中间三个按钮，其中第一个sale是默认不可选择的，另外两个是可以选择的
    // ? 带参数进来的，又可以选择第一个按钮
    private var isSelectOne: Boolean = false
    private var isSelectSold: Boolean = false
    private var isSelectDelisted: Boolean = false

    // 过滤器popwindow
    private lateinit var popWindowFilter: HSPopWindow
    private lateinit var xPopWindow: BasePopupView

    private lateinit var bottomSheetBehavior : BottomSheetBehavior<LinearLayout>



    private var ready: Boolean = false //请求filter接口回调的标记位

    private var isSale: Boolean = false //是否是卖，有两种，一种租，一种买卖
    private var mapType: ArrayList<String> = ArrayList()


    private var communityPolygon: Polygon? = null
    private var communityPolyline: Polyline? = null

    private var schoolPolygons: ArrayList<Polygon> = ArrayList()
    private var schoolPolylines: ArrayList<Polyline> = ArrayList()

    private var loginDialog: LoginFragment? = null

    private var idMunicipality:String = ""
    private var idCommunity:String = ""
    private var viewType:String = "map" //这个页面有2种形式，map和list

    private var watchAreaPolygon: ArrayList<com.housesigma.android.model.Polygon>? = null
    private val mMapScoreAdapter by lazy { MapScoreAdapter() }
    private val mListingPreviewAdapter by lazy { initListingPreviewMany() }
    private val mListingViewAdapter by lazy { initListingViewAdapter() }
    private val mMunicipalitiesAdapter by lazy { initMunicipalitiesAdapter() }

    override fun onCreate(savedInstanceState: Bundle?) {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        isSale = intent.getBooleanExtra("is_sale", true)
        mapType = intent.getStringArrayListExtra("map_type")?:ArrayList<String>()
        watchAreaPolygon = intent.getParcelableArrayListExtra("watch_area_polygon")
        idMunicipality = intent.getStringExtra("municipality_id")?:""
        idCommunity = intent.getStringExtra("id_community")?:""
        viewType =  intent.getStringExtra("view_type")?:"map"

        Logger.e("mapType $mapType")

        MapLibre.getInstance(this)
        super.onCreate(savedInstanceState)
        mapViewModel = ViewModelProvider(this).get(MapViewModel::class.java)
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }

        binding = ActivityMapBinding.inflate(layoutInflater)
        setContentView(binding.root)



        initViews(savedInstanceState)
        initLoginDialog()
    }


    private fun initLoginDialog() {
        if (loginDialog != null) return
        loginDialog = LoginFragment()
    }

    private fun initData() {
        mapViewModel.communityDetail.observe(this) {
            showCommunityBoundaries(it)
        }
        mapViewModel.mapNearByList.observe(this) { that ->

            Logger.e("mapNearByList observer " + that)
            CoroutineScope(Dispatchers.Main).launch {
                handleNearByList(that)
            }
        }

        mapViewModel.schoolDeatils.observe(this) {
            delSchoolPolygon()
            Logger.e("school details $it")
            showSchoolArea(it)
            binding.tvMapSchoolName.text = it.name
            binding.tvScore.text = it.score ?:"-"

            val addressDisplay = listOf(it.address, it.city, it.region)
                .filterNot { it.isNullOrBlank() }
                .joinToString(", ")

            val typeDisplay = listOf(it.school_type, it.board_type)
                .filterNot { it.isNullOrBlank() }
                .takeIf { it.size == 2 }
                ?.let { "${it[0]} (${it[1]})" } ?: ""

            binding.tvAddress.text = "Address: " + addressDisplay
            binding.tvPhoneNumber.text = "Phone Number: " + it.phone
            binding.tvSchoolType.text = "School Type: " + typeDisplay
            binding.tvSchoolBoard.text = "School Board: " + it.board
            try {
                val text = "Web Site: " + "<a href=\"${it.web}\">${it.web}</a>"
                val spanned = Html.fromHtml(text)
                val spannableStringBuilder = SpannableStringBuilder(spanned)
                val urls = spannableStringBuilder.getSpans(0, spanned.length,URLSpan::class.java)
                for (url in urls) {
                    val hsUrlSpan = HSURLSpan(this,url.url)
                    val start = spannableStringBuilder.getSpanStart(url)
                    val end = spannableStringBuilder.getSpanEnd(url)
                    val flags = spannableStringBuilder.getSpanFlags(url)
                    spannableStringBuilder.setSpan(hsUrlSpan, start, end, flags)
                    //一定要加上这一句,看过很多网上的方法，都没加这一句，导致ClickableSpan的onClick方法没有回调，直接用浏览器打开了
                    spannableStringBuilder.removeSpan(url)
                }
                binding.tvWebsite.text = spannableStringBuilder
                binding.tvWebsite.movementMethod = LinkMovementMethod.getInstance()
            } catch (e:Exception) {
                e.printStackTrace()
            }




            if (it.score_json_v2 == null) {
                binding.rvScore.adapter = mMapScoreAdapter
                mMapScoreAdapter.data.clear()
                binding.tvScoreHead.visibility = View.GONE
            } else {
                it.score_json_v2?.let {
                    binding.rvScore.adapter = mMapScoreAdapter
                    mMapScoreAdapter.data = it.toMutableList()
                }
                binding.tvScoreHead.visibility = View.VISIBLE
            }
            mMapScoreAdapter.notifyDataSetChanged()
            binding.llListings.visibility = View.GONE
            binding.llSchoolDetail.visibility = View.VISIBLE
        }


        mapViewModel.mapSearchSchool.observe(this) { it ->
            CoroutineScope(Dispatchers.Main).launch {
                handleSchoolMarks(it)
            }
        }


        mapViewModel.mapFilter.observe(this) {
            loadMapFilter(it)

        }

        mapViewModel.listingPreViewManyWithMarker.observe(this) {
            try {
                if (it.houseList.size == 1) {
                    val house = it.houseList.getOrNull(0)
                    house?.let {
                        house.map?.let {
                            if (house.map.lat != 0.0 || house.map.lon != 0.0) {
                                moveCameraToPoint(Location(house.map.lat, house.map.lon))

                                val marker = mapHelper.addViewInFullMapMarker(this,mapLibreMap,house)
                                marker?.let { thatMarker ->
                                    // 这里要单独存放，不然区别不出来是哪种类型的marker
                                    viewInMapInfoMap.put(
                                        thatMarker.id, house.id_listing
                                    )


                                    // 剔除掉同View in Full map类型相同经纬度，相同id_listing的marker
                                    mapHelper.removeDuplicateMarkers(listingMarkerInfoMap,house.id_listing)
                                    mapHelper.removeDuplicateMarkers(nearbyMarkerInfoMap,house.id_listing)
                                    mapHelper.removeDuplicateMarkers(featureMarkerInfoMap,house.id_listing)
                                }

                            }
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

            handleListingPreview(it)
        }

        mapViewModel.listingPreViewMany.observe(this) {
            handleListingPreview(it)
        }

        mapViewModel.mapSearchV3ListNoData.observe(this) {
//            list view 切换条件/切换 page 体验优化
//            loading 完成后再清空之前的数据, 不然会闪一下再显示当前的列表
            binding.tvListViewTotal.text = "0 Listing"
            mListingViewAdapter.data.clear()
            mListingViewAdapter.notifyDataSetChanged()
        }

        mapViewModel.citySummary.observe(this) { that ->
            that.filterActive?.let {
                binding.tvListViewMunicipalityName.text = it.municipalityName + " - Market Stats"
                // 面包屑
//              if id_municipality = null => current_province(final)
//              if id_municipality != null => current_province(first) / municipality_name(final)  (web mobile v7 逻辑: 要去掉-read-estate)
                if (TextUtils.isEmpty(idMunicipality)) {
                    binding.tvBreadcrumbNavigationFirst.text = ""
                    binding.tvBreadcrumbNavigationFinal.text = it.currentProvince + " Real Estate"
                    binding.tvBreadcrumbNavigationFirst.setOnClickListener {  }
                } else {
                    binding.tvBreadcrumbNavigationFirst.text = it.currentProvince
                    binding.tvBreadcrumbNavigationFinal.text = " / "+it.municipalityName?.replace("-read-estate","") + " Real Estate"
                    binding.tvBreadcrumbNavigationFirst.setOnClickListener {
                        // 移动到省份正确的中心点，获取扩大后的区域，清空选择的二级面包屑
                        goToFirstPageIfViewTypeIsList()
                        moveRightRegion(null)
                        getMapRequestRegion()
                        idMunicipality = ""
                        changeListView()
                    }
                }
            }

            that.current?.let {
                binding.tvListNew.text = it.listNew?:""
                binding.tvPeriodName.text = it.periodName?:""
                binding.tvPeriodNameNew.text = it.periodName?:""
                binding.tvPriceSold.text = if(it.priceSold==null) "" else "$ "+it.priceSold

                binding.tvPriceSoldChange1Years.text = if(it.priceSoldChangeYear==null) "-" else it.priceSoldChangeYear+ "%"
                binding.tvPriceSoldChange5Years.text =  if(it.priceSoldChange5Years==null) "-" else it.priceSoldChange5Years+ "%"
                binding.tvPriceSoldChange10Years.text = if(it.priceSoldChange10Years==null) "-" else it.priceSoldChange10Years+ "%"


                if (it.priceSoldChangeYear.toString().toDoubleOrNull() == null) {
                    binding.tvPriceSoldChange1Years.setTextColor(resources.getColor(R.color.color_dark))
                } else {
                    it.priceSoldChangeYear.toString().toDoubleOrNull()?.let {
                        if (it<0) {
                            binding.tvPriceSoldChange1Years.setTextColor(resources.getColor(R.color.color_red_red))
                        } else {
                            binding.tvPriceSoldChange1Years.setTextColor(resources.getColor(R.color.color_green_dark))
                            binding.tvPriceSoldChange1Years.text = "+" + binding.tvPriceSoldChange1Years.text
                        }
                    }
                }

                if (it.priceSoldChange5Years.toString().toDoubleOrNull() == null) {
                    binding.tvPriceSoldChange5Years.setTextColor(resources.getColor(R.color.color_dark))
                } else {
                    it.priceSoldChange5Years.toString().toDoubleOrNull()?.let {
                        if (it<0) {
                            binding.tvPriceSoldChange5Years.setTextColor(resources.getColor(R.color.color_red_red))
                        } else {
                            binding.tvPriceSoldChange5Years.setTextColor(resources.getColor(R.color.color_green_dark))
                            binding.tvPriceSoldChange5Years.text = "+" + binding.tvPriceSoldChange5Years.text
                        }
                    }
                }

                if (it.priceSoldChange10Years.toString().toDoubleOrNull() == null) {
                    binding.tvPriceSoldChange10Years.setTextColor(resources.getColor(R.color.color_dark))
                } else {
                    it.priceSoldChange10Years.toString().toDoubleOrNull()?.let {
                        if (it<0) {
                            binding.tvPriceSoldChange10Years.setTextColor(resources.getColor(R.color.color_red_red))
                        } else {
                            binding.tvPriceSoldChange10Years.setTextColor(resources.getColor(R.color.color_green_dark))
                            binding.tvPriceSoldChange10Years.text = "+" + binding.tvPriceSoldChange10Years.text
                        }
                    }
                }


            }

            that.municipalities?.let {
                val allMunicipalityList = it.toMutableList()
                if (allMunicipalityList.size>5) {
                    binding.tvListViewShowMoreMunicipalities.text = "Show More"
                    binding.tvListViewShowMoreMunicipalities.setCompoundDrawablesWithIntrinsicBounds(resources.getDrawable(R.drawable.ic_map_list_view_show_more_arrow_down),null, null, null)

                    mMunicipalitiesAdapter.data = allMunicipalityList.subList(0,5)
                    binding.llListViewShowMoreMunicipalities.visibility = View.VISIBLE
                    binding.llListViewShowMoreMunicipalities.setOnClickListener {
                        val showMoreStatusStr = binding.tvListViewShowMoreMunicipalities.text
                        if ("Show More".equals(showMoreStatusStr)){
                            mMunicipalitiesAdapter.data = allMunicipalityList
                            binding.tvListViewShowMoreMunicipalities.text = "Show Less"
                            binding.tvListViewShowMoreMunicipalities.setCompoundDrawablesWithIntrinsicBounds(resources.getDrawable(R.drawable.ic_map_list_view_show_more_arrow_up),null, null, null)
                        } else {
                            mMunicipalitiesAdapter.data = allMunicipalityList.subList(0,5)
                            binding.tvListViewShowMoreMunicipalities.text = "Show More"
                            binding.tvListViewShowMoreMunicipalities.setCompoundDrawablesWithIntrinsicBounds(resources.getDrawable(R.drawable.ic_map_list_view_show_more_arrow_down),null, null, null)
                        }
                        mMunicipalitiesAdapter.notifyDataSetChanged()
                    }
                } else {
                    mMunicipalitiesAdapter.data = allMunicipalityList
                    binding.llListViewShowMoreMunicipalities.visibility = View.GONE
                }
                mMunicipalitiesAdapter.notifyDataSetChanged()



            }
        }

        mapViewModel.loadingLiveData.observe(this) {
            hideProgress()
            binding.llSchoolDetail.visibility = View.GONE
            hiddenListingPreview()
            Handler().postDelayed({
                setChangeMapVisibility()
            },10)
        }

        mapViewModel.mapSearchV3List.observe(this) {
            if (it.total > 1){
                binding.tvListViewTotal.text = it.total.toString() + " Listings"
            }else{
                binding.tvListViewTotal.text = it.total.toString() + " Listing"
            }
            val totalPage = Math.ceil(((it.total / 20).toDouble())).toInt()

            if (totalPage > 1) {
                binding.llPageControl.visibility = View.VISIBLE
            } else {
                binding.llPageControl.visibility = View.GONE
            }

            binding.pageControlView.setTotalPage(totalPage)
            mListingViewAdapter.setOnItemChildClickListener { adapter, view, position ->
                handleListViewClick(adapter, position, view)
            }
            it.houseList?.let {
                mListingViewAdapter.data = it.toMutableList()
                mListingViewAdapter.notifyDataSetChanged()
            }
        }

        mapViewModel.mapList.observe(this) { that ->
            CoroutineScope(Dispatchers.Main).launch {
                Logger.e("收到listing网络请求handleMapListing....."+System.currentTimeMillis())
                handleMapListing(that)
            }
        }

        mapViewModel.mapSearchFeature.observe(this) { that ->
            CoroutineScope(Dispatchers.Main).launch {
                handleFeatureMarkers(that)
            }

        }

        val mapFilterJson = MMKVUtils.getStr(MapHelper.MAP_FILTER_TAG)
        if (TextUtils.isEmpty(mapFilterJson)) {
            mapViewModel.getMapFilter()
        } else {
            val mapFilter = Gson().fromJson(mapFilterJson, MapFilter::class.java)
            loadMapFilter(mapFilter)
        }

//        val listingId = intent.getStringExtra("id_listing") ?: ""
//        if (!TextUtils.isEmpty(listingId)) {
//            getListingPreviewMany(listingId)
//        }

        val listingId = intent.getStringExtra("id_listing") ?: ""
        if (!TextUtils.isEmpty(listingId)) {
            lastMarkerInfoIds = arrayListOf(listingId)
            getListingPreviewMany(true)
        }

        mapViewModel.saveMapFilterMsg.observe(this) {
            ToastUtils.showSuccess(it.message)
            getMapFilterList()
        }

        mapViewModel.mapFilterList.observe(this) {
            mapFiltersView?.removeFilterIfRemoteDeleted(it)
            mapFiltersView?.bindSaveFilterSelectList(this@MapActivity,it,object : Callback1 {
                override fun onData(any: Any) {
                    val saveMapFilter = any as MapFilters
                    val listingDays = saveMapFilter.listing_days?:"0"
                    val deListDays = saveMapFilter.sold_days?: "90"
                    val houseType = saveMapFilter.house_type?:ArrayList()

                    MMKVUtils.saveStr(isSale.toString() + "map_listing_days",listingDays)
                    MMKVUtils.saveStr(isSale.toString() + "map_s_ld",deListDays)

                    val tempHouseTypeLocal = ArrayList<String>()
                    mapFilter?.let {
                        it.house_type_filter.forEachIndexed { index, houseTypeFilter ->
                            for (defaultHouseType in houseType) {
                                if (defaultHouseType == houseTypeFilter.id) {
                                    tempHouseTypeLocal.add(index.toString())
                                }
                            }
                        }

                        it.days_filter_all.listing.forEachIndexed { index, item ->
                            if (item.id == listingDays) {
                                MMKVUtils.saveStr(isSale.toString() + "map_fs_ld_abbr",item.abbr)
                                binding.tvFilterSale.text = item.abbr
                            }
                        }

                        it.days_filter_all.sold_v2.forEachIndexed { index, item ->
                            if (item.id == deListDays) {
                                MMKVUtils.saveStr(isSale.toString() + "map_s_ld_abbr", item.abbr)
                                binding.tvFilterSoldDelisted.text = item.abbr
                            }
                        }
                    }
                    val cacheProperty = tempHouseTypeLocal.toString()
                        .replace("[", "")
                        .replace("]", "")
                        .replace(" ", "")
                    MMKVUtils.saveStr(isSale.toString() +"propertyLabels", cacheProperty)


                    mapPropertyView?.setLabelValueFromCache()



                    reloadMapData()
                }
            })
        }

        mapViewModel.deleteMapFilterMsg.observe(this) {
            ToastUtils.showDel(it.message)
            getMapFilterList()
        }

        getMapFilterList()
    }

    private fun deleteMapFilterById(id: String) {
        mapViewModel.deleteMapFilterById(id)
    }

    /**
     * 未登录，不显示MapFilterSave模块，save filter的视图状态取决于api返回的数据
     */
    private fun getMapFilterList(){
        if (!LoginFragment.isLogin()) {
            mapFiltersView?.goneSaveFilterViews()
            return
        }
        if (isSale) {
            mapViewModel.getMapFilterList(MapType.FOR_SALE.value)
        } else {
            mapViewModel.getMapFilterList(MapType.FOR_LEASE.value)
        }
    }

    /**
     * DEV-9021 目前只是跳转到房源详情页才会取消社区边界
     */
    private fun removeCommunityBoundaries() {
        communityPolygon?.let {
            mapLibreMap.removePolygon(it)
        }
        communityPolyline?.let {
            mapLibreMap.removePolyline(it)
        }
    }

    private fun showCommunityBoundaries(communityDetail: CommunityDetail) {
        try {
            communityDetail.boundary?.let {
                val polygon = ArrayList<LatLng>()
                val polyline = ArrayList<LatLng>()
                for (point in it.coordinates[0]) {
                    val latLng = LatLng(point[1], point[0])
                    polyline.add(latLng)
                    polygon.add(latLng)
                }
                communityPolygon = mapLibreMap.addPolygon(
                    PolygonOptions()
                        .addAll(polygon)
                        .fillColor(Color.parseColor("#28a3b3"))
                        .alpha(0.2f)
                )
                communityPolyline = mapLibreMap.addPolyline(
                    PolylineOptions()
                        .addAll(polyline)
                        .color(Color.parseColor("#28a3b3"))
                        .width(3.0f)
                        .alpha(1f)
                )

                // 移动到能显示下communityPolygon的zoom
                val bounds = LatLngBounds.Builder()
                communityPolygon?.points?.forEach {
                    bounds.include(it)
                }
                if (communityPolygon != null) {
                    val latLngBounds = bounds.build()
                    val cameraUpdate = CameraUpdateFactory.newLatLngBounds(latLngBounds, 50)
                    mapLibreMap.moveCamera(cameraUpdate)
                }
            }
        } catch (e:Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 显示学校区域，支持多个区域，区域支持多个外围和多个内洞组成。
     */
    private fun showSchoolArea(schoolDetails: SchoolDetails) {
        try {
            if (schoolDetails.geo_json_v2.coordinates != null && schoolDetails.geo_json_v2.coordinates.size != 0) {
                for (outerPolygons in schoolDetails.geo_json_v2.coordinates) {
                    // 一共有几个外圈区域
                    var outerPolygonIndex = 0
                    val polygonOuter = ArrayList<LatLng>()
                    val polygonHoles = ArrayList<ArrayList<LatLng>>()
                    val polyline = ArrayList<LatLng>()

                    for (outerPolygon in outerPolygons) {
                        val polygonHole = ArrayList<LatLng>()
                        outerPolygonIndex++
                        // polygon 分外层和洞
                        polygonHole.clear()
                        for (point in outerPolygon) {
                            val latLng = LatLng(point[1], point[0])
                            polyline.add(latLng)
                            if (outerPolygonIndex == 1) { // 最外层
                                polygonOuter.add(latLng)
                            } else { //洞
                                polygonHole.add(latLng)
                            }
                        }
                        if (polygonHole.size != 0) {
                            polygonHoles.add(polygonHole)
                        }

                        val schoolPolyline = mapLibreMap.addPolyline(
                            PolylineOptions()
                                .addAll(polyline)
                                .color(Color.parseColor("#BC0000"))
                                .width(2.0f)
                                .alpha(1f)
                        )
                        polyline.clear()
                        schoolPolylines.add(schoolPolyline)

                    }
                    val schoolPolygon = mapLibreMap.addPolygon(
                        PolygonOptions()
                            .addAll(polygonOuter)
                            .addAllHoles(polygonHoles)
                            .fillColor(Color.parseColor("#FF0000"))
                            .strokeColor(Color.parseColor("#BC0000"))
                            .alpha(0.1f)
                    )
                    schoolPolygons.add(schoolPolygon)

                    polygonOuter.clear()
                    polygonHoles.clear()
                }
            }
        }catch (e:Exception){
            e.printStackTrace()
        }
    }

    private fun handleListingPreview(it: ListingPreViewMany) {
        showListingPreview()
        binding.llSchoolDetail.visibility = View.GONE
        mListingPreviewAdapter.setOnItemChildClickListener { adapter, view, position ->
            handleListViewClick(adapter, position, view)
        }

        mListingPreviewAdapter.data = it.houseList.toMutableList()
        mListingPreviewAdapter.notifyDataSetChanged()

        (binding.rvListings.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(0, 0)
        (binding.rvListings.layoutManager as LinearLayoutManager).setStackFromEnd(true)

        if (it.houseList.size <= 1) {
            binding.tvListingsSize.text = "${it.houseList.size} Listing in total"
        } else {
            binding.tvListingsSize.text = "${it.houseList.size} Listings in total"
        }
    }

    private fun handleListViewClick(
        adapter: BaseQuickAdapter<*, *>,
        position: Int,
        view: View
    ) {
        val item = adapter.getItem(position) as House
        when (view.id) {
            R.id.tv_contact_agent -> {
                if (item.isAgreementRequired()||item.isNeedReLogin() || item.isLoginRequired() || item.isNotAvailable() || item.isPasswordExpired()) {
                    return
                }
                if (isListView()) {
                    GALog.log("contact_agent_click","listview_inactive")
                } else {
                    GALog.log("contact_agent_click","mapview_inactive")
                }

                this@MapActivity?.let {
                    WebViewHelper.jumpHouseContact(
                        it,
                        item.id_listing
                    )
                }
            }

            R.id.ll -> {
                if (item.isAgreementRequired()||item.isNeedReLogin() || item.isLoginRequired() || item.isNotAvailable() || item.isPasswordExpired()) {
                    return
                }

//              DEV-4546 map页面的listing card 的点击事件：preview_click 区分出map/list mode
                if (isListView()) {
                    GALog.log("preview_click", "list_mode")
                } else {
                    GALog.log("preview_click", "map_mode")
                }
                this@MapActivity?.let {
                    if (MapUtils.isCollectLocation()) {
                        collectUserLocation()
                    }
                    WebViewHelper.jumpHouseDetail(
                        it,
                        item.id_listing,
                        item.seo_suffix,
                        eventSource = "map"
                    )

                }
            }

            R.id.tv_agreement_required -> {
                this@MapActivity?.let {
                    TosDialog(it, it, it, item.tos_source,
                        object : TosDialog.TosCallback {
                            override fun onSuccess() {
                                if (viewType=="map") {
                                    getListingPreviewMany()
                                } else {
                                    changeListView()
                                }
                            }
                        }).show()
                }

            }

            R.id.tv_not_available -> {
                this@MapActivity?.let {
                    VowTosDialog(item.id_listing,it,it,it).show()
                }
            }

            R.id.tv_login_required -> {
                val tvLoginRequiredStr = (view as TextView).text.toString()
                if (Constants.TEXT_VIEW_TRREB_TIMEOUT.equals(tvLoginRequiredStr)){
                    showLoginDialog(reLogin = LoginFragment.RE_LOGIN_TRREB_TIMEOUT)
                } else if ("Password Expired".equals(tvLoginRequiredStr)) {
                    showLoginDialog(reLogin = LoginFragment.RE_LOGIN_VALIDATE)
                }else{
                    showLoginDialog()
                }
            }

        }
    }

    private suspend fun handleSchoolMarks(mapSearchSchool: MapSearchSchool) {
        val notSameMapList = ArrayList<Marker>()
        // api返回的marker，判断现有marker列表中有无，
        // 如果marker列表中没有，需要添加上
        // 如果marker列表中有，则不需要添加
        // 只需要保留比较本地和网络重叠的部分，去掉不同的。
        for (entry in schoolMarkerInfoMap) {
            var find = false
            mapSearchSchool.list.forEach {
                if (entry.value.equals(it)) {
                    find = true
                }
            }
            if (!find) {
                entry.value.localMarker?.let {
                    notSameMapList.add(it)
                }
            }
        }
        notSameMapList.forEach {
            schoolMarkerInfoMap.remove(it.id)
        }
        mapLibreMap.removeAnnotations(notSameMapList)
        mapSearchSchool.list.forEach {
            var find = false
            for (mutableEntry in schoolMarkerInfoMap) {
                if (mutableEntry.value.equals(it)) {
                    find = true
//                    Logger.e( "find same element in hashMapFeatureInfo")
                    break
                }
            }

            if (!find) {
                val marker = mapHelper.addSchoolMarker(it)
                marker?.let { schoolMarker ->
                    it.localMarker = schoolMarker
                    // 这里要单独存放，不然区别不出来是哪种类型的marker
                    schoolMarkerInfoMap.put(
                        schoolMarker.id, it
                    )
                }
            }
        }
    }

    private suspend fun handleFeatureMarkers(that: List<MapMarkerInfo>) {
        val notSameMapList = ArrayList<Marker>()
        // api返回的marker，判断现有marker列表中有无，
        // 如果marker列表中没有，需要添加上
        // 如果marker列表中有，则不需要添加
        // 只需要保留比较本地和网络重叠的部分，去掉不同的。
        for (entry in featureMarkerInfoMap) {
            var find = false
            that.forEach {
                if (entry.value.equals(it)) {
                    find = true
                }
            }
            if (!find) {
                entry.value.localMarker?.let {
                    notSameMapList.add(it)
                }
            }
        }
        notSameMapList.forEach {
            featureMarkerInfoMap.remove(it.id)
        }
        mapLibreMap.removeAnnotations(notSameMapList)
        that.forEach {
            var find = false
            for (mutableEntry in featureMarkerInfoMap) {
                if (mutableEntry.value.equals(it)) {
                    find = true
//                    Logger.e( "find same element in hashMapFeatureInfo")
                    break
                }
            }

            if (!find) find = mapHelper.isSameLocationAndIds(viewInMapInfoMap, it)

            if (!find) {
                val marker = mapHelper.addFeatureMarker(it)
                marker?.let { thatMarker ->
                    it.localMarker = thatMarker
                    // 这里要单独存放，不然区别不出来是哪种类型的marker
                    featureMarkerInfoMap.put(
                        thatMarker.id, it
                    )
                }
            }
        }


    }

    private suspend fun handleNearByList(that: MapList) {
        val notSameMapList = ArrayList<Marker>()
        // api返回的marker，判断现有marker列表中有无，
        // 如果marker列表中没有，需要添加上
        // 如果marker列表中有，则不需要添加
        // 只需要保留比较本地和网络重叠的部分，去掉不同的。
        val cameraZoom = mapHelper.getCameraPositionZoom(mapLibreMap)

        for (entry in nearbyMarkerInfoMap) {
            var find = false
            that.list.forEach {
//                it.isNear = true

                it.setNearZoom(cameraZoom)
                if (entry.value.equals(it)) {
                    find = true
                }
            }
            if (!find) {
                entry.value.localMarker?.let {
                    notSameMapList.add(it)
                }
            }
        }
        notSameMapList.forEach {
            nearbyMarkerInfoMap.remove(it.id)
        }
        mapLibreMap.removeAnnotations(notSameMapList)
        that.list.forEach {
            it.setNearZoom(cameraZoom)

            var find = false
            for (mutableEntry in nearbyMarkerInfoMap) {
                if (mutableEntry.value.equals(it)) {
                    find = true
//                    Logger.e( "find same element in hashMapNearByInfo")
                    break
                }
            }
            if (!find) find = mapHelper.isSameLocationAndIds(viewInMapInfoMap, it)

            if (!find) {
                var marker: Marker? = null
                if ("sold".equals(it.marker)) {
                    marker = mapHelper.addNearBySoldMarker(it)
                }
                marker?.let { thatMarker ->
                    it.localMarker = thatMarker
                    // 这里要单独存放，不然区别不出来是哪种类型的marker
                    nearbyMarkerInfoMap.put(
                        thatMarker.id, it
                    )
                }
            }
        }

        binding.progress.visibility = View.GONE
    }

    private fun initListingPreviewMany(): MapListingAdapter {
        val divider = HSDecoration(this, DividerItemDecoration.VERTICAL, false)
        binding.rvListings.addItemDecoration(divider)
        binding.rvListings.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        val adapter = MapListingAdapter()
        binding.rvListings.adapter = adapter
        return adapter
    }

    private fun initMunicipalitiesAdapter(): MapMuniciplitiesAdapter {
        binding.rvMunicipalities.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        val adapter = MapMuniciplitiesAdapter()
        adapter.setType(isSale)
        binding.rvMunicipalities.adapter = adapter
        return adapter
    }

    private fun initListingViewAdapter(): MapListingAdapter {
        val divider = HSDecoration(this, DividerItemDecoration.VERTICAL, false)
        binding.rvListingView.addItemDecoration(divider)
        binding.rvListingView.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        val adapter = MapListingAdapter()
        binding.rvListingView.adapter = adapter
        return adapter
    }

    private suspend fun handleMapListing(that: MapList) {
        if (TextUtils.isEmpty(that.alert_message)) {
            binding.llNoResultTip.visibility = View.GONE
        }else{
            binding.tvNoResultTip.text = that.alert_message
            if (that.alert_steps==1) {//为1显示clearFilters按钮
                GALog.log("hint_displayed","map_filters_no_listing")
                binding.tvTipClearFilters.visibility = View.VISIBLE
                binding.tvTipClearFilters.setOnClickListener {
                    GALog.log("map_clear_filters_click")
                    binding.llNoResultTip.visibility = View.GONE
                    mapPropertyView?.clearFilters()
                    mapFiltersView?.restoreSaveFilterSettings()
                    mapFiltersView?.restoreFromSelectMapFilters()
                    mapFilter?.let {
//                        MMKVUtils.saveStr(isSale.toString() +"propertyLabels",  it.default_filter.houseTypeLocal.joinToString(","))

                        var itemListingDay : ListingFilter ?= null
                        for (listingFilter in it.days_filter_all.listing) {
                            if (it.default_filter.listing_days.equals(listingFilter.id)) {
                                itemListingDay = listingFilter
                                break
                            }
                        }
                        itemListingDay?.let {
                            MMKVUtils.saveStr(isSale.toString() + "map_listing_days",itemListingDay.id)//listing_days
                            MMKVUtils.saveStr(isSale.toString() + "map_fs_ld_abbr", itemListingDay.abbr)
                        }

                        var itemSoldDay : ListingFilter ?= null
                        for (listingFilter in it.days_filter_all.sold_v2) {
                            if (it.default_filter.de_list_days.equals(listingFilter.id)) {
                                itemSoldDay = listingFilter
                                break
                            }
                        }
                        itemSoldDay?.let {
                            MMKVUtils.saveStr(isSale.toString() + "map_s_ld",itemSoldDay.id)//listing_days
                            MMKVUtils.saveStr(isSale.toString() + "map_s_ld_abbr", itemSoldDay.abbr)
                        }
                    }
                    binding.tvFilterSale.text = MMKVUtils.getStr(isSale.toString() + "map_fs_ld_abbr")?:""
                    binding.tvFilterSoldDelisted.text = MMKVUtils.getStr(isSale.toString() + "map_s_ld_abbr")?:""
                    setDefaultListType()
                    refreshListType()
                    reloadMapData()
                }
            }else{
                GALog.log("hint_displayed","map_no_listing")
                binding.tvTipClearFilters.visibility = View.GONE
            }
            binding.llNoResultTip.visibility = View.VISIBLE
        }





        val notSameMapList = ArrayList<Marker>()
        // api返回的marker，判断现有marker列表中有无，
        // 如果marker列表中没有，需要添加上
        // 如果marker列表中有，则不需要添加
        // 只需要保留比较本地和网络重叠的部分，去掉不同的。
        for (entry in listingMarkerInfoMap) {
            var find = false
            for (mapMarkerInfo in that.list) {
                if (entry.value.equals(mapMarkerInfo)) {
                    find = true
                    break
                }
            }
            if (!find) {
                entry.value.localMarker?.let {
                    notSameMapList.add(it)
                }
            }
        }

        notSameMapList.forEach {
            listingMarkerInfoMap.remove(it.id)
        }


        mapLibreMap.removeAnnotations(notSameMapList)


        val start = System.currentTimeMillis()
        val currentZoom = mapHelper.getCameraPositionZoom(mapLibreMap)
        that.list.forEach {
            var find = false
            for (mutableEntry in listingMarkerInfoMap) {
                if (mutableEntry.value.equals(it)) {
                    find = true
//                    Logger.e( "find same element in hashMapMapInfo")
                    break
                }
            }
            if (!find) find = mapHelper.isSameLocationAndIds(viewInMapInfoMap, it)

            if (!find) {
                var marker: Marker? = null
                if ("sold".equals(it.marker)) {
                    marker = mapHelper.addSoldMarker(it)
                } else if ("de-listed".equals(it.marker)) {
                    marker = mapHelper.addDeListedMarker(it)
                } else if ("active".equals(it.marker)) {
                    marker = mapHelper.addSaleMarker(it)
                } else if ("normal".equals(it.marker)) {
                    // DEV-9336 发生zoom level 改变时，中断掉zoom level被改变之前加载marker的逻辑
                    if (mapHelper.areZoomLevelsInSameRange(currentZoom, that.requestZoom)) {
                        marker = mapHelper.addGroupMarker(it)
                    }
                }

                marker?.let { markerThat ->
                    it.localMarker = markerThat
                    listingMarkerInfoMap.put(
                        markerThat.id, it
                    )
                }
            }
        }
        val end = System.currentTimeMillis()
        val duration = (end - start)
        Logger.e("function cast time is " +duration)
        if (!"prod".equals(BuildConfig.FLAVOR)) {
            binding.tvTestMarkerDuration.text = "Marker loading speed: " + duration + "ms"
        }
        binding.progress.visibility = View.GONE
    }

    private fun loadMapFilter(it: MapFilter?) {
        mapFilter = it

        showMapTip(it?.filter_tip)

        mapPropertyView?.setData(mapFilter!!)
        mapFiltersView?.setup(mapFilter!!)

//        mapPropertyLayout!!.readCache()
        mapPropertyView!!.setLabelValueFromCache()

//        if (propertyCache is String) {
//            binding.tvPropertyTypes.text = propertyCache
//        } else if (propertyCache is List<*>) {
//            if ((propertyCache as List<MapFilterHouseTypeFilter>).size > 1) {
//                binding.tvPropertyMore.visibility = View.VISIBLE
//                binding.tvPropertyTypes.text =
//                    (propertyCache as List<MapFilterHouseTypeFilter>)[0].name
//                binding.tvPropertyMore.text =
//                    ((propertyCache as List<MapFilterHouseTypeFilter>).size - 1).toString() + "+"
//            } else if ((propertyCache as List<MapFilterHouseTypeFilter>).size == 1) {
//                binding.tvPropertyMore.visibility = View.GONE
//                binding.tvPropertyTypes.text =
//                    (propertyCache as List<MapFilterHouseTypeFilter>)[0].name
//            } else {
//                binding.tvPropertyMore.visibility = View.GONE
//                binding.tvPropertyMore.text = ""
//            }
//        }



        var map_fs_ld_abbr = MMKVUtils.getStr(isSale.toString() + "map_fs_ld_abbr")
        if (!TextUtils.isEmpty(map_fs_ld_abbr)) {
            binding.tvFilterSale.text = map_fs_ld_abbr
        }
        var map_s_ld_abbr = MMKVUtils.getStr(isSale.toString() + "map_s_ld_abbr")
        if (!TextUtils.isEmpty(map_s_ld_abbr)) {
            // 需要在回填的时候判断一下，当前的  days_filter_all.sold_v2 和 days_filter_all.de_list_v2 存不存在当前的 active filter, 有才回填，没有就恢复成默认
            var findActiveSoldDelistedFilter = false
            mapFilter?.days_filter_all?.sold_v2?.forEach { item ->
                if (map_s_ld_abbr==item.abbr) {
                    findActiveSoldDelistedFilter = true
                }
            }
            if (findActiveSoldDelistedFilter) {
                binding.tvFilterSoldDelisted.text = map_s_ld_abbr
            } else {
                MMKVUtils.removeData(isSale.toString() + "map_s_ld")
                MMKVUtils.removeData(isSale.toString() + "map_s_ld_abbr")
            }
        }

        if (mapFilter?.hasDeListed()!=true) {
            binding.tvDelisted.visibility = View.GONE
        }

        if (mapType.size == 0) {
            setDefaultListType()
        }

        refreshListType()


        ready = true
        reloadMapData()
    }

    private fun setDefaultListType() {
        mapFilter?.let {
            listType?.clear()
            mapType?.clear()
            if (it.default_filter.list_type.contains("1")) {
                mapType?.add("for-sale")
            } else if (it.default_filter.rent_list_type.contains("2")) {
                mapType?.add("for-lease")
            } else if (it.default_filter.list_type.contains("3")) {
                mapType?.add("sold")
            } else if (it.default_filter.rent_list_type.contains("4")) {
                mapType?.add("leased")
            } else if (it.default_filter.list_type.contains("5")) {
                mapType?.add("de-listed-5")
            } else if (it.default_filter.rent_list_type.contains("6")) {
                mapType?.add("de-listed-6")
            } else {
            }
        }
    }

    private fun initListViewViews(){
        mapListViewSortTypeView = MapListViewSortTypeView(this)
        mapListViewSortTypeView?.setSelectClickListener(object :Callback1{
            override fun onData(any: Any) {
                xPopWindow.dismiss()
                binding.tvMapListViewSortType.text = mapListViewSortTypeView!!.sortStr
                changeListView()
            }
        })
        binding.tvMapListViewSortType.setOnClickListener {
            binding.tvMapListViewSortType.post {
                xPopWindow =  XPopup.Builder(MapActivity@this)
                    .atView(binding.tvMapListViewSortType)
                    .animationDuration(0)
                    .asCustom(mapListViewSortTypeView)
                    .show()
            }
        }

        mMunicipalitiesAdapter.addChildClickViewIds(R.id.ll_root)
        mMunicipalitiesAdapter.setOnItemChildClickListener { adapter, view, position ->
            val item = adapter.getItem(position) as CitySummaryMunicipality
            when (view.id) {
                R.id.ll_root -> {
                    goToFirstPageIgnoreViewType()
                    idMunicipality = item.municipalityId?:""
                    item.location?.let { moveCameraToPoint(it,12.0) }
                    getMapRequestRegion()
                    changeListView()
                }
            }
        }
    }

    @SuppressLint("WrongConstant")
    private fun initViews(savedInstanceState: Bundle?) {
        bottomSheetBehavior = BottomSheetBehavior.from(binding.llListings)
        initListViewViews()
        DisclaimerViewHelper.handleDisclaimer(this,binding.tvDisclaimer)

        if (isSale) {
            binding.tvForSale.text = "For Sale"
            binding.tvSold.text = "Sold"
            binding.tvDelisted.text = "De-listed"
        } else {
            binding.llWatch.visibility = View.GONE
            binding.tvForSale.text = "For Lease"
            binding.tvSold.text = "Leased"
            binding.tvDelisted.text = "De-listed"
        }


        binding.llWatch.setOnClickListener {
            GALog.log("watch_area_start")
            if (LoginFragment.isLogin()) {
                startActivityForResult(Intent(this, WatchedAreaActivity::class.java), REQUEST_CODE_WATCHED_AREA)
            } else {
                showLoginDialog()
            }
        }

        binding.ivCloseMapSchool.setOnClickListener {
            binding.llSchoolDetail.visibility = View.GONE
        }

        binding.llSearch.setOnClickListener {
            GALog.log("search_start")
            try {
                AbsSuperApplication.finishActivity(SearchActivity::class.java)
            } catch (exception: Exception) {
                exception.printStackTrace()
            }
            val intent = Intent(this, SearchActivity::class.java)
            intent.putExtra("is_sale", isSale)
            startActivity(intent)
        }
        refreshListType()


        mapPropertyView = MapPropertyView().createView(layoutInflater, isSale)
        mapPropertyView!!.setLabelTextView(binding.tvPropertyTypes,binding.tvPropertyMore)
        mapFiltersView = MapFiltersView().createView(layoutInflater, isSale,cb = object : Callback0 {
            override fun onData() {
                goToFirstPageIfViewTypeIsList()
                reloadMapData()
            }
        })


        binding.llFiltersMore.setOnClickListener {

            mapFiltersView!!.setDeleteFiltersClickListener(this@MapActivity,cb = object : Callback1 {
                override fun onData(any: Any) {
                    val id = any as String
                    deleteMapFilterById(id)
                }
            })

            mapFiltersView!!.setSaveFiltersClickListener(
                this@MapActivity,
                cb = object : Callback1 {
                override fun onData(filtersName: Any) {
                    val mapFilters = MapFilters()
                    mapFilters.basement = mapFiltersView!!.getBasement()
                    mapFilters.bathroom = mapFiltersView!!.getBathroomMin()
                    mapFilters.bedroom =  mapFiltersView!!.getBedroomRange()
                    mapFilters.description =  mapFiltersView!!.getDescription()

                    mapFilters.garage = mapFiltersView!!.getGarageMin()
                    mapFilters.max_maintenance_fee =  mapFiltersView!!.getFee()
                    mapFilters.open_house_date =  mapFiltersView!!.getOpenHouseDate()

                    mapFilters.lot_size_max =  mapFiltersView!!.getLotSize()?.getOrNull(1)
                    mapFilters.lot_size_min =  mapFiltersView!!.getLotSize()?.getOrNull(0)
                    mapFilters.building_age_max =  mapFiltersView!!.getBuildingAge()?.getOrNull(1)
                    mapFilters.building_age_min =  mapFiltersView!!.getBuildingAge()?.getOrNull(0)

                    mapFilters.square_footage_max =  mapFiltersView!!.getSquareFootage()?.getOrNull(1)?:"0"
                    mapFilters.square_footage_min =  mapFiltersView!!.getSquareFootage()?.getOrNull(0)?:"0"
                    mapFilters.lot_front_feet_max =  mapFiltersView!!.getFeet()?.getOrNull(1)?:"0"
                    mapFilters.lot_front_feet_min =  mapFiltersView!!.getFeet()?.getOrNull(0)?:"0"
                    mapFilters.price =  mapFiltersView!!.getPrice()
                    mapFilters.listing_type =  mapFiltersView!!.getListingType()

                    mapFilters.house_type = mapPropertyView!!.getPropertyTypes()

                    val listingDays = MMKVUtils.getStr(isSale.toString() + "map_listing_days") ?: "0"
                    val deListDays = MMKVUtils.getStr(isSale.toString() + "map_s_ld") ?: "90"


                    mapFilters.de_list_days = deListDays
                    mapFilters.listing_days = listingDays
                    mapFilters.sold_days = deListDays

                    mapFilters.property_type = null
                    mapFilters.construction_status = null
                    mapFilters.est_completion_year = null

                    filtersName as String
                    val category = if (isSale) MapType.FOR_SALE else MapType.FOR_LEASE
                    val saveMapFilter = SaveMapFilter(filterName = filtersName, category = category.toString(), filter = mapFilters)
                    mapViewModel.saveMapFilter(saveMapFilter)
                }
            })




            // 筛选 最右侧 更多筛选条件 布局
            mapFiltersView!!.setApplyClickListener(cb = object : Callback1 {
                override fun onData(any: Any) {
                    popWindowFilter.dissmiss()
                }
            })

            mapFiltersView!!.setClearClickListener(cb = object : Callback1 {
                override fun onData(any: Any) {
                    popWindowFilter.dissmiss()
                    goToFirstPageIfViewTypeIsList()
                    reloadMapData()
                }
            })

            GALog.log("filter_select", "filters")
            val screenHeight = ScreenUtils.getDeviceHeight(this)
            binding.vLine.post {
                val popWindowYPosition =  binding.vLine.bottom
                val popHeight = screenHeight - popWindowYPosition
//                Logger.e("popHeight: " + popHeight + " screenHeight: " + screenHeight + " vLine.bottom: " + binding.vLine.bottom)
                popWindowFilter = HSPopWindow.PopupWindowBuilder(this)
                    .setInputMethodMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
                    .setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
                    .setView(mapFiltersView!!.getLayout().root)
                    .size(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        popHeight
                    )
                    .enableBackgroundDark(false) //弹出popWindow时，背景是否变暗
                    .setBgDarkAlpha(0.5f)
                    .create()
                    .showAsDropDown(binding.vLine, 0, 0)
            }
        }

        binding.llPropertyTypes.setOnClickListener {
            mapPropertyView!!.setOnLabelClickListener(cb = object : Callback1 {
                override fun onData(name: Any) {

                    val list = name as List<MapFilterHouseTypeFilter>
                    binding.tvPropertyTypes.text = list[0].name
                    if (list.size > 1) {
                        binding.tvPropertyMore.visibility = View.VISIBLE
                        binding.tvPropertyMore.text = (list.size - 1).toString().plus("+")
                    } else {
                        binding.tvPropertyMore.visibility = View.GONE
                        binding.tvPropertyMore.text = ""
                    }

                    goToFirstPageIfViewTypeIsList()
                    reloadMapData()
                }
            })



            mapPropertyView!!.setClearClickListener(cb = object : Callback1 {
                override fun onData(name: Any) {
                    goToFirstPageIfViewTypeIsList()
                    popWindowFilter.dissmiss()
//                    binding.tvPropertyTypes.text = "All property types"
//                    binding.tvPropertyMore.visibility = View.GONE
                    reloadMapData()
                }
            })


            mapPropertyView!!.setApplyClickListener(cb = object : Callback1 {
                override fun onData(name: Any) {
                    goToFirstPageIfViewTypeIsList()
                    popWindowFilter.dissmiss()
                    reloadMapData()
                }
            })
            GALog.log("filter_select", "property_type")
            popWindowFilter = HSPopWindow.PopupWindowBuilder(this)
                .setView(mapPropertyView!!.getLayout().root)

                .size(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                .enableBackgroundDark(false) //弹出popWindow时，背景是否变暗
                .setBgDarkAlpha(0.5f)
                .create()
                .showAsDropDown(binding.vLine, 0, 0)
        }

        binding.llForSaleDelised.setOnClickListener {

            val inflate = PopMapFsSDlFilterBinding.inflate(layoutInflater)
            var adapterLeft = MapFilterSaleSoldAdapter(MainActivity@ this, true, isSale)
            inflate.rvLeft.adapter = adapterLeft
            inflate.rvLeft.layoutManager =
                LinearLayoutManager(AddWatchCommunityActivity@ this, RecyclerView.VERTICAL, false)
            mapFilter?.days_filter_all?.listing?.let { it1 -> adapterLeft.addData(it1) }

            // 有三种情况：
            // sold de-listed都有、只有sold、只有de-listed
            if (mapFilter?.hasDeListed() == true && mapFilter?.hasSold()==true){
                inflate.tvSoldDelistTitle.text = "Sold & De-listed"
            } else if (mapFilter?.hasDeListed() != true && mapFilter?.hasSold() == true){
                inflate.tvSoldDelistTitle.text = "Sold"
            } else if (mapFilter?.hasDeListed() == true && mapFilter?.hasSold() != true){
                inflate.tvSoldDelistTitle.text = "De-listed"
            }

            adapterLeft.addChildClickViewIds(R.id.rl)
            adapterLeft.setOnItemChildClickListener { adapter, view, position ->
                val item = adapter.getItem(position) as ListingFilter
                when (view.id) {
                    R.id.rl -> {
                        popWindowFilter.dissmiss()
                        binding.tvFilterSale.text = item.abbr
                        item.checked = true

                        Logger.e("listing_days " + item.id)
                        MMKVUtils.saveStr(
                            isSale.toString() + "map_listing_days",
                            item.id
                        )//listing_days
                        MMKVUtils.saveStr(isSale.toString() + "map_fs_ld_abbr", item.abbr)
                        adapterLeft.notifyDataSetChanged()
                        goToFirstPageIfViewTypeIsList()
                        reloadMapData()
                    }
                }
            }
            var adapterRight = MapFilterSaleSoldAdapter(MainActivity@ this, false, isSale)
            adapterRight.addChildClickViewIds(R.id.rl)
            adapterRight.setOnItemChildClickListener { adapter, view, position ->
                val item = adapter.getItem(position) as ListingFilter
                when (view.id) {
                    R.id.rl -> {
                        popWindowFilter.dissmiss()
                        binding.tvFilterSoldDelisted.text = item.abbr
                        item.checked = true
                        MMKVUtils.saveStr(isSale.toString() + "map_s_ld", item.id)
                        MMKVUtils.saveStr(isSale.toString() + "map_s_ld_abbr", item.abbr)
                        adapterRight.notifyDataSetChanged()
                        goToFirstPageIfViewTypeIsList()
                        reloadMapData()
                    }
                }
            }

            inflate.rvRight.adapter = adapterRight
            inflate.rvRight.layoutManager =
                LinearLayoutManager(AddWatchCommunityActivity@ this, RecyclerView.VERTICAL, false)
            mapFilter?.days_filter_all?.sold_v2?.let { it1 -> adapterRight.addData(it1) }

            GALog.log("filter_select", "date")
            popWindowFilter = HSPopWindow.PopupWindowBuilder(this)
                .setView(inflate.root)
                .size(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                .enableBackgroundDark(false) //弹出popWindow时，背景是否变暗
                .setBgDarkAlpha(0.5f)
                .create()
                .showAsDropDown(binding.vLine, 0, 0)
        }

        binding.tvForSale.setOnClickListener {
            if (isSale) {
                GALog.log("filter_select", "sale")
            } else {
                GALog.log("filter_select", "lease")
            }

            if (!isSelectOne) {
                binding.tvForSale.setBackgroundResource(R.drawable.shape_map_left_selected)
                binding.tvForSale.setTextColor(resources.getColor(R.color.color_white))
                if (isSale) {
                    if (!listType.contains("1")) {
                        listType.add("1")
                    }
                } else {
                    if (!listType.contains("2")) {
                        listType.add("2")
                    }
                }
            } else {
                if (listType.size == 1) {
                    return@setOnClickListener
                }

                binding.tvForSale.setBackgroundResource(R.drawable.shape_map_left)
                binding.tvForSale.setTextColor(resources.getColor(R.color.color_gray_dark))

                if (isSale) {
                    if (listType.contains("1")) {
                        listType.remove("1")
                    }
                } else {
                    if (listType.contains("2")) {
                        listType.remove("2")
                    }
                }
            }
            isSelectOne = !isSelectOne
//            MMKVUtils.saveBoolean("map_select_one", isSelectSold)
            reloadMapData()
        }

        binding.tvSold.setOnClickListener {
            if (isSale) {
                GALog.log("filter_select", "sold")
            } else {
                GALog.log("filter_select", "leased")
            }

            if (!isSelectSold) {

                if (mapFilter?.hasDeListed()==true) {
                    binding.tvSold.setBackgroundResource(R.drawable.shape_map_center_selected)
                } else {
                    binding.tvSold.setBackgroundResource(R.drawable.shape_map_filter_only_sold_selected)
                }


                binding.tvSold.setTextColor(resources.getColor(R.color.color_white))


                if (isSale) {
                    if (!listType.contains("3")) {
                        listType.add("3")
                    }
                } else {
                    if (!listType.contains("4")) {
                        listType.add("4")
                    }
                }

            } else {
                if (listType.size == 1) {
                    return@setOnClickListener
                }
                if (mapFilter?.hasDeListed()==true){
                    binding.tvSold.setBackgroundResource(R.drawable.shape_map_center)
                }else{
                    binding.tvSold.setBackgroundResource(R.drawable.shape_map_filter_only_sold)
                }
                binding.tvSold.setTextColor(resources.getColor(R.color.color_gray_dark))
                if (isSale) {
                    if (listType.contains("3")) {
                        listType.remove("3")
                    }
                } else {
                    if (listType.contains("4")) {
                        listType.remove("4")
                    }
                }
            }
            isSelectSold = !isSelectSold
//            MMKVUtils.saveBoolean("map_select_sold", isSelectSold)
            goToFirstPageIfViewTypeIsList()
            reloadMapData()
        }


        binding.tvDelisted.setOnClickListener {
            if (isSale) {
                GALog.log("filter_select", "delisted")
            } else {
                GALog.log("filter_select", "lease_delisted")
            }
            if (!isSelectDelisted) {
                binding.tvDelisted.setBackgroundResource(R.drawable.shape_map_right_selected)
                binding.tvDelisted.setTextColor(resources.getColor(R.color.color_white))

                if (isSale) {
                    if (!listType.contains("5")) {
                        listType.add("5")
                    }
                } else {
                    if (!listType.contains("6")) {
                        listType.add("6")
                    }
                }

            } else {
                if (listType.size == 1) {
                    return@setOnClickListener
                }
                binding.tvDelisted.setBackgroundResource(R.drawable.shape_map_right)
                binding.tvDelisted.setTextColor(resources.getColor(R.color.color_gray_dark))
                if (isSale) {
                    if (listType.contains("5")) {
                        listType.remove("5")
                    }
                } else {
                    if (listType.contains("6")) {
                        listType.remove("6")
                    }
                }
            }

//            MMKVUtils.saveBoolean("map_select_de_listed", isSelectDelisted)
            isSelectDelisted = !isSelectDelisted
            goToFirstPageIfViewTypeIsList()
            reloadMapData()
        }


        binding.ivCloseMapListings.setOnClickListener {
            hiddenListingPreview()
        }

        binding.ivClose.setOnClickListener { finish() }
        mapView = binding.mapView
        mapView.onCreate(savedInstanceState)
        mapView.getMapAsync { map ->
            mapLibreMap = map
            mapLibreMap.uiSettings.isLogoEnabled = false
            mapLibreMap.uiSettings.isAttributionEnabled = false
            mapLibreMap.uiSettings.isRotateGesturesEnabled = false
            mapLibreMap.setMaxPitchPreference(0.0)//关闭仰角
            mapLibreMap.setMinPitchPreference(0.0)
            mapHelper = MapHelper(MapActivity@ this, layoutInflater, mapLibreMap)
            if (MapUtils.isShowRaeMapTips()) {
                edmontonCenterMarker = mapHelper.addEdmontonCenterMarker()
            }
            setUpMapboxMap(ConstantsHelper.getMapVector())
            initData()

            // TODO 获取mapbox是异步的，这块要延迟一段时间才可以获取到地图上的经纬度
            //  ，需要考虑有更好的办法替代以下逻辑，目前delay 1000ms的方案不靠谱
            Handler().postDelayed({
                if ("list".equals(viewType)) {
                    changeListView()
                }
            },1000)
        }


        readSchoolCache()


        binding.pageControlView.setPageChangeListener { HSPageControlView, numPerPage ->
            Logger.e(".....")
            mListViewPage = numPerPage
            changeListView()
        }

        binding.tvChangeMap.setOnClickListener {
            GALog.log("map_mode_click")
            changeMapView()
            reloadMapData()
        }

        binding.tvChangeMapOuter.setOnClickListener {
            GALog.log("map_mode_click")
            changeMapView()
            reloadMapData()
        }

        binding.svListView.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            setChangeMapVisibility()
        }


        binding.ivToolListView.setOnClickListener {
            GALog.log("list_mode_click")

            goToFirstPageIgnoreViewType()
            changeListView()
        }

        binding.ivToolSchool.setOnClickListener {

            GALog.log("school_click")
            // TODO review code 需要封装组件
            var mapSchoolLayout = PopwindowMapSchoolBinding.inflate(layoutInflater)
            mapSchoolLayout.root.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)


            mapSchoolLayout.cbCatholic
                .setChecked(MMKVUtils.getBoolean("map_pop_school_school_catholic", true))
            mapSchoolLayout.cbCatholic.setOnCheckedChangeListener(object :
                CompoundButton.OnCheckedChangeListener {
                override fun onCheckedChanged(compoundButton: CompoundButton?, boolean: Boolean) {
//                    if (compoundButton?.isPressed == false) {
//                        return
//                    }
                    MMKVUtils.saveBoolean("map_pop_school_school_catholic", boolean)
                    schoolCatholic = if (boolean) 1 else 0
                    reloadMapData()
                }
            })

            mapSchoolLayout.cbMatchScore
                .setChecked(MMKVUtils.getBoolean("map_pop_school_school_matchscore", false))
            mapSchoolLayout.cbMatchScore.setOnCheckedChangeListener(object :
                CompoundButton.OnCheckedChangeListener {
                override fun onCheckedChanged(compoundButton: CompoundButton?, boolean: Boolean) {
//                    if (compoundButton?.isPressed == false) {
//                        return
//                    }
                    MMKVUtils.saveBoolean("map_pop_school_school_matchscore", boolean)
                    schoolMatch_score = if (boolean) 1 else 0
                    reloadMapData()
                }
            })

            mapSchoolLayout.cbSecondary
                .setChecked(MMKVUtils.getBoolean("map_pop_school_school_secondary", true))
            mapSchoolLayout.cbSecondary.setOnCheckedChangeListener(object :
                CompoundButton.OnCheckedChangeListener {
                override fun onCheckedChanged(compoundButton: CompoundButton?, boolean: Boolean) {
//                    if (compoundButton?.isPressed == false) {
//                        return
//                    }
                    MMKVUtils.saveBoolean("map_pop_school_school_secondary", boolean)
                    schoolSecondary = if (boolean) 1 else 0
                    reloadMapData()
                }
            })

            mapSchoolLayout.cbPublic
                .setChecked(MMKVUtils.getBoolean("map_pop_school_school_public", true))
            mapSchoolLayout.cbPublic.setOnCheckedChangeListener(object :
                CompoundButton.OnCheckedChangeListener {
                override fun onCheckedChanged(compoundButton: CompoundButton?, boolean: Boolean) {
//                    if (compoundButton?.isPressed == false) {
//                        return
//                    }
                    MMKVUtils.saveBoolean("map_pop_school_school_public", boolean)
                    schoolPublic = if (boolean) 1 else 0
                    reloadMapData()
                }
            })
            mapSchoolLayout.cbElementary
                .setChecked(MMKVUtils.getBoolean("map_pop_school_school_elementary", true))
            mapSchoolLayout.cbElementary.setOnCheckedChangeListener(object :
                CompoundButton.OnCheckedChangeListener {
                override fun onCheckedChanged(compoundButton: CompoundButton?, boolean: Boolean) {
//                    if (compoundButton?.isPressed == false) {
//                        return
//                    }
                    MMKVUtils.saveBoolean("map_pop_school_school_elementary", boolean)
                    schoolElementary = if (boolean) 1 else 0
                    reloadMapData()
                }
            })

            // 默认是不显示学校的
            val showSchool = MMKVUtils.getBoolean("map_pop_school_show", false)
            Logger.e("showSchool: " + showSchool)
            mapSchoolLayout.sbShowSchools.setChecked(showSchool)
            mapSchoolLayout.ivCloseSchoolSettings.setOnClickListener {
                popWindowFilter.dissmiss()
            }

            mapSchoolLayout.sbShowSchools.setOnCheckedChangeListener(object :
                SwitchButton.OnCheckedChangeListener {
                override fun onCheckedChanged(button: SwitchButton, checked: Boolean) {
                    //这里不能加isPressed 否则会对checkbox点击事件无响应
//                    if (button?.isPressed == false) {
//                        return
//                    }
                    Logger.e("map_pop_school_show: " + checked)
                    MMKVUtils.saveBoolean("map_pop_school_show", checked)
                    if (checked) {
                        reloadMapData()
                    } else {
                        delSchoolMarkers()
                        delSchoolPolygon()
                    }
                }
            })
            
            popWindowFilter = HSPopWindow.PopupWindowBuilder(this)
                .setView(mapSchoolLayout.root)
                .size(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                .enableBackgroundDark(true)
                .setBgDarkAlpha(0.5f)
                .create()

            val screenHeight = windowManager.defaultDisplay.height

            popWindowFilter.showAtLocation(
                binding.mapView,
                Gravity.TOP, 0, screenHeight
            )
        }

        binding.ivToolLocation.setOnClickListener {
            GALog.log("current_position_click")
            // 申请定位
            requestLocationWithPermissionCheck()

        }

        binding.ivToolSettings.setOnClickListener {
            GALog.log("map_type_layer_click")
            if (mapSettingsDialog == null) {
                mapLibreMap.style?.uri?.let { it1 -> Logger.e(it1) }
                mapSettingsDialog = MapSettingDialog.newInstance(isSale)
                mapSettingsDialog?.setMapSettingsCallback(object :
                    MapSettingDialog.MapSettingsCallback {
                    override fun changeStyle(isSatellite:Boolean) {
                        isMapStyleLoaded = false
                        if (isSatellite) {
                            GALog.log("satellite_click", "satellite")
                            mapLibreMap.setStyle(ConstantsHelper.getMapSatellite()) {
                                isMapStyleLoaded = true
                            }
                        } else {
                            GALog.log("satellite_click", "street")
                            mapLibreMap.setStyle(ConstantsHelper.getMapVector()) {
                                isMapStyleLoaded = true
                            }
                        }
                    }

                    override fun changeSoldListings() {
                        reloadMapData()
                    }
                })
            }
            mapLibreMap.style?.uri?.let { it1 ->  mapSettingsDialog?.setStyle(it1==ConstantsHelper.getMapSatellite())}
            mapSettingsDialog?.show(supportFragmentManager, "")
        }
    }

    /**
     * 判断 viewType 是否是 ViewType.LIST，如果是，则将 currentPage 设置为切换到第一页。
     */
    private fun goToFirstPageIfViewTypeIsList() {
        if (viewType == "list") {
            goToFirstPageIgnoreViewType()
        }
    }

    /**
     * 将 currentPage 设置为切换到第一页无论ViewType是哪种模式
     */
    private fun goToFirstPageIgnoreViewType() {
        mListViewPage = 1
        binding.pageControlView.setCurrentPage(mListViewPage)
    }

    /**
     * 如果内部changeMap按钮可见，就隐藏外部的changeMap按钮，反之显示
     */
    private fun setChangeMapVisibility() {
        val visibilityPercents = ScreenUtils.getVisibilityPercents(binding.tvChangeMap)
        val municipalitiesVisible = ScreenUtils.getLocalVisibleRect(this, binding.rvMunicipalities, 0)//false
        if (visibilityPercents==100) {
            binding.tvChangeMapOuter.visibility = View.GONE
        } else {
            if (municipalitiesVisible){
                binding.tvChangeMapOuter.visibility = View.GONE
            }else{
                binding.tvChangeMapOuter.visibility = View.VISIBLE
            }
        }
    }

    private fun changeListView() {
        viewType = "list"
        binding.tvAttribution.visibility = View.GONE
        // 给house type views挪位置，节约代码，共用同一套view
        binding.llFilterType.removeAllViews()
        binding.llTypeListViewRoot.removeAllViews()
        binding.llTypeListViewRoot.addView(binding.tvForSale)
        binding.llTypeListViewRoot.addView(binding.tvTypeDivider1)
        binding.llTypeListViewRoot.addView(binding.tvSold)
        binding.llTypeListViewRoot.addView(binding.tvTypeDivider2)
        binding.llTypeListViewRoot.addView(binding.tvDelisted)


        // 切换到listview模式，初始化view
        binding.svListView.smoothScrollTo(0, 0,0)

        binding.llSchoolDetail.visibility = View.GONE
        hiddenListingPreview()

        binding.ivToolListView.visibility = View.GONE
        binding.ivToolLocation.visibility = View.GONE
        binding.ivToolSchool.visibility = View.GONE
        binding.ivToolSettings.visibility = View.GONE
        binding.mapView.visibility = View.GONE
        binding.llFilterType.visibility = View.GONE

        binding.svListView.visibility = View.VISIBLE
        binding.rvMunicipalities.visibility = View.VISIBLE
        binding.rvListingView.visibility = View.VISIBLE

        showProgress()





        // clean data
        mListingViewAdapter.notifyDataSetChanged()

        mMunicipalitiesAdapter.data.clear()
        mMunicipalitiesAdapter.notifyDataSetChanged()


        // request api
        mapViewModel.getCitySummary(idMunicipality)
        val cameraZoom = mapHelper.getCameraPositionZoom(mapLibreMap)
        val listingDays = MMKVUtils.getStr(isSale.toString() + "map_listing_days") ?: "0"
        var deListDays = MMKVUtils.getStr(isSale.toString() + "map_s_ld") ?: "90"
        mapViewModel.getMapSearchV3List(
            houseType = mapPropertyView!!.getPropertyTypes(),
            lon1 = realLon1,
            lon2 = realLon2,
            lat1 = realLat1,
            lat2 = realLat2,
            zoom = cameraZoom,
            listing_days = listingDays,
            de_list_days = deListDays,
            list_type = listType,
            basement = mapFiltersView!!.getBasement(),
            bedroom_range = mapFiltersView!!.getBedroomRange(),
            open_house_date = mapFiltersView!!.getOpenHouseDate(),
            bathroom_min = mapFiltersView!!.getBathroomMin(),
            garage_min = mapFiltersView!!.getGarageMin(),
            description = mapFiltersView!!.getDescription(),
            max_maintenance_fee = mapFiltersView!!.getFee(),
            price = mapFiltersView!!.getPrice(),
            front_feet = mapFiltersView!!.getFeet(),
            square_footage = mapFiltersView!!.getSquareFootage(),
            page = mListViewPage,
            sortType = mapListViewSortTypeView!!.sortValue,
            listing_type = mapFiltersView!!.getListingType(),
            lot_size = mapFiltersView!!.getLotSize(),
            building_age = mapFiltersView!!.getBuildingAge(),
            rental_yield_range = mapFiltersView!!.getRentalYieldRange(),
            school_score_range = mapFiltersView!!.getSchoolScoreRange(),
        )
    }

    private fun changeMapView() {
        viewType = "map"
        binding.tvAttribution.visibility = View.VISIBLE
        binding.llTypeListViewRoot.removeAllViews()
        binding.llFilterType.removeAllViews()
        binding.llFilterType.addView(binding.tvForSale)
        binding.llFilterType.addView(binding.tvTypeDivider1)
        binding.llFilterType.addView(binding.tvSold)
        binding.llFilterType.addView(binding.tvTypeDivider2)
        binding.llFilterType.addView(binding.tvDelisted)

        binding.ivToolListView.visibility = View.VISIBLE
        binding.ivToolLocation.visibility = View.VISIBLE
        binding.ivToolSchool.visibility = View.VISIBLE
        binding.ivToolSettings.visibility = View.VISIBLE
        binding.mapView.visibility = View.VISIBLE


        binding.llFilterType.visibility = View.VISIBLE

        binding.tvChangeMapOuter.visibility = View.GONE
        binding.svListView.visibility = View.GONE
        binding.rvMunicipalities.visibility = View.GONE
        binding.rvListingView.visibility = View.GONE
    }

    private fun showMapTip(showBuildingAgeTip:String?) {
        TipHelper.clearTip()

        if (LoginFragment.isLogin() && isSale) {
            val isShowWatchHint = intent.getBooleanExtra("is_show_watch_hint", false)
            if (isShowWatchHint) {
                TipHelper.addTip(TipModel(
                    showAtView = binding.llWatch,
                    tipContent = "Tap here to set up a Watched Area and get alerted when there are listing updates.",
                    ga = TipGAEvent(eventName = "hint_displayed",hsLabel = "watch_area")
                ))
            }
        }

        val firstShowBuildingAgeTip = MMKVUtils.getBoolean(TipHelper.FIRST_SHOW_BUILDING_AGE_FILTER_TIP, true)
        showBuildingAgeTip?.let {
            if (firstShowBuildingAgeTip) {
                TipHelper.addTip(TipModel(
                    showAtView = binding.llFiltersMore,
                    tipContent = showBuildingAgeTip,
                    ga = TipGAEvent(eventName = "hint_displayed",hsLabel = "building_age_filter")
                ))
                MMKVUtils.saveBoolean(TipHelper.FIRST_SHOW_BUILDING_AGE_FILTER_TIP, false)
            }
        }

        binding.llWatch.post {
            TipHelper.showTip(this, layoutInflater)
        }
    }

    private fun refreshListType() {
        binding.tvForSale.setBackgroundResource(R.drawable.shape_map_left)
        binding.tvForSale.setTextColor(resources.getColor(R.color.color_gray_dark))


        if (mapFilter?.hasDeListed()==true) {
            binding.tvSold.setBackgroundResource(R.drawable.shape_map_center)
        }else{
            binding.tvSold.setBackgroundResource(R.drawable.shape_map_filter_only_sold)
        }

        binding.tvSold.setTextColor(resources.getColor(R.color.color_gray_dark))
        binding.tvDelisted.setBackgroundResource(R.drawable.shape_map_right)
        binding.tvDelisted.setTextColor(resources.getColor(R.color.color_gray_dark))
        if (mapType?.contains("for-sale") == true || mapType?.contains("for-lease") == true) {
            isSelectOne = true
        } else {
            isSelectOne = false
        }
        if (mapType?.contains("for-sale") == true || mapType?.contains("for-lease") == true) {
            if (isSelectOne) {
                binding.tvForSale.setBackgroundResource(R.drawable.shape_map_left_selected)
                binding.tvForSale.setTextColor(resources.getColor(R.color.color_white))

                if (isSale) {
                    if (!listType.contains("1")) {
                        listType.add("1")
                    }
                } else {
                    if (!listType.contains("2")) {
                        listType.add("2")
                    }
                }


            }
        }
        if (mapType?.contains("sold") == true || mapType?.contains("leased") == true) {
            isSelectSold = true
        }else{
            isSelectSold = false
        }
        //        isSelectSold = MMKVUtils.getBoolean("map_select_sold", false)
        if (isSelectSold) {

            if (mapFilter?.hasDeListed()==true){
                binding.tvSold.setBackgroundResource(R.drawable.shape_map_center_selected)
            }else{
                binding.tvSold.setBackgroundResource(R.drawable.shape_map_filter_only_sold_selected)
            }
            binding.tvSold.setTextColor(resources.getColor(R.color.color_white))

            if (isSale) {
                if (!listType.contains("3")) {
                    listType.add("3")
                }
            } else {
                if (!listType.contains("4")) {
                    listType.add("4")
                }
            }


        }

        //        if (map_type.contains("for-sale")|| map_type.contains("sold")|| map_type.contains("de-listed-5")) {
        //            intent.putExtra("is_sale", true)
        //        }
        //        if (map_type.contains("for-lease")|| map_type.contains("leased")|| map_type.contains("de-listed-6")) {
        //            intent.putExtra("is_sale", false)
        //        }
        if (mapType?.contains("de-listed-5") == true || mapType?.contains("de-listed-6") == true) {
            isSelectDelisted = true
        }else{
            isSelectDelisted = false
        }

        //        isSelectDelisted = MMKVUtils.getBoolean("map_select_de_listed", false)
        if (isSelectDelisted) {
            binding.tvDelisted.setBackgroundResource(R.drawable.shape_map_right_selected)
            binding.tvDelisted.setTextColor(resources.getColor(R.color.color_white))

            if (isSale) {
                if (!listType.contains("5")) {
                    listType.add("5")
                }
            } else {
                if (!listType.contains("6")) {
                    listType.add("6")
                }
            }
        }
        //
        //        if (isSale) {
        //            listType.add("1")
        //        } else {
        //            listType.add("2")
        //        }
    }

    private fun updateMapFilterRedPoint() {
        mapFiltersView?.let {
            if (it.isModified()) {
                binding.ivFilterMorePoint.visibility = View.VISIBLE
            } else {
                binding.ivFilterMorePoint.visibility = View.GONE
            }
        }
    }

    private fun readSchoolCache() {
        schoolCatholic = if (MMKVUtils.getBoolean("map_pop_school_school_catholic", true)) 1 else 0
        schoolElementary =
            if (MMKVUtils.getBoolean("map_pop_school_school_elementary", true)) 1 else 0
        schoolMatch_score =
            if (MMKVUtils.getBoolean("map_pop_school_school_matchscore", false)) 1 else 0
        schoolPublic = if (MMKVUtils.getBoolean("map_pop_school_school_public", true)) 1 else 0
        schoolSecondary =
            if (MMKVUtils.getBoolean("map_pop_school_school_secondary", true)) 1 else 0
    }

    private fun showLoginDialog(reLogin: String = "") {
        GALog.log("login_button_click")
        initLoginDialog()
        if (loginDialog?.isAdded == true) return
        val bundle = Bundle()
        bundle.putString("reLogin", reLogin)
        loginDialog?.arguments = bundle
        loginDialog?.show(supportFragmentManager, "")
    }

    @SuppressLint("MissingPermission")
    @NeedsPermission(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    fun requestLocation() {
        try {
            if (!LocationUtils.isLocServiceEnable(this)) {
                HSAlertDialog(
                    this,
                    "Location Permission Required",
                    "Please enable location permissions in settings.",
                    "Cancel",
                    "Settings",
                    object : HSAlertDialog.HSAlertCallback {
                        override fun onSuccess() {
                            val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                            startActivityForResult(intent, 0)
                        }
                    }).show()
                return
            }
            getHSLocation()
            collectUserLocation()
        } catch (e: Exception) {
//         #fixbug  有设备没有network provider，可能是模拟器用户
//         Fatal Exception: java.lang.IllegalArgumentException
//                    provider doesn't exist: network
            e.printStackTrace()
            moveRightRegion(null)
        }
    }

    /**
     * 先使用android自带定位api简单判断经纬度是否在指定范围内
     * 如果在范围内，则开启mapbox我的位置功能，mapbox我的位置功能使用的是play框架的混合定位
     * 反复测试过，以上这种效果最好
     */
    private fun getHSLocation() {
        val netWorkLocation = LocationUtils.getNetWorkLocation(this)
        if (netWorkLocation!=null) {
            moveRightRegion(netWorkLocation)
        }
        LocationUtils.addLocationListener(
            this,
            LocationManager.NETWORK_PROVIDER,
            object : LocationListener,
                LocationUtils.ILocationListener {
                override fun onSuccess(location: Location?) {
                    LocationUtils.unRegisterListener(this@MapActivity)
                    moveRightRegion(location)
                }

                override fun onFail() {
                    moveRightRegion(null)
                }

                override fun onLocationChanged(location: Location) {
                }
        })
    }


    /**
     * 移动到正确位置
     * @param resultLocation 位置信息
     */
    private fun moveRightRegion(resultLocation: Location?) {
        // DEV-2285 如果用户点了定位，记住定位地点, 点击定位后，把用户真实的定位地址发给后端
        resultLocation?.let {
            if (LoginFragment.isLogin()) { //未登录时，不请求 api 保存 coordinate
                mapViewModel.updateProfileCoordinate(it.latitude.toString(),it.longitude.toString())
            }
        }

        // 跳转到我的位置 但是有限制，不会跳出省
        // 超出界限时，将定位到默认地址 ，目前是多伦多
        val neLatLng = LatLng(65.0, -50.0)
        val swLatLng = LatLng(40.0, -137.0)
        //           BC @49.246292,-123.116226
        //           ON @43.955259, -79.346008
        var FAILBACK_LATLON = LatLng(43.955259, -79.346008)

        val abbreviationFromCache = ProvinceHelper.getAbbreviationFromCache("ON")
        if ("ON".equals(abbreviationFromCache)) {
            FAILBACK_LATLON = LatLng(43.955259, -79.346008)
        } else if ("BC".equals(abbreviationFromCache)) {
            FAILBACK_LATLON = LatLng(49.246292, -123.116226)
        } else if ("AB".equals(abbreviationFromCache)) {
            FAILBACK_LATLON = LatLng(51.045005, -114.072129)
        }

        val FAILBACK_ZOOM = 7.0

        if (resultLocation != null) {
            var latitude = resultLocation.latitude
            var longitude = resultLocation.longitude
            var zoom = MapHelper.zoom - 1
            if (!MapUtils.inBounds(
                    LatLng(
                        latitude,
                        longitude
                    ), neLatLng, swLatLng
                )
            ) {
                latitude = FAILBACK_LATLON.latitude
                longitude = FAILBACK_LATLON.longitude
                zoom = FAILBACK_ZOOM
                mapLibreMap.cameraPosition = CameraPosition.Builder()
                    .target(
                        LatLng(
                            latitude,
                            longitude
                        )
                    )
                    .zoom(zoom)
                    .build()
            } else {
                if (isMapViewActive&&isMapStyleLoaded) {
                    mapLibreMap.cameraPosition = CameraPosition.Builder()
                        .zoom(14.0)
                        .build()
                    mapHelper.showMapboxLocationComponent(mapLibreMap)
                }
            }
        }
    }

    @OnShowRationale(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    fun showRationaleForRequestLocation(request: PermissionRequest) {
        HSAlertDialog(
            this,
            "Location Permission Required",
            "Please enable location permissions in settings.",
            "Cancel",
            "Settings",
            object : HSAlertDialog.HSAlertCallback {
                override fun onSuccess() {
                    request.proceed()
                }
            }).show()
    }

    @OnPermissionDenied(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    fun requestLocationDenied() {
        ToastUtils.showLong("Please enable location permissions in settings.")
    }

    @OnNeverAskAgain(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    fun requestLocationNeverAskAgain() {
        ToastUtils.showLong("Please enable location permissions in settings.")
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        onRequestPermissionsResult(requestCode, grantResults)
        if (intArrayOf(0, 0).contentEquals(grantResults)) {
            GALog.log("location_permission_request", "true")
        } else {
            GALog.log("location_permission_request", "false")
        }
    }

    private fun setUpMapboxMap(styleUrl: String) {
        mapLibreMap.addOnMapClickListener(object : MapLibreMap.OnMapClickListener {
            override fun onMapClick(point: LatLng): Boolean {
                return false
            }
        })


        mapLibreMap.setMaxZoomPreference(20.0)
        mapLibreMap.setMinZoomPreference(4.0)

        mapLibreMap.setOnMarkerClickListener(object : MapLibreMap.OnMarkerClickListener {
            override fun onMarkerClick(marker: Marker): Boolean {
                var result = CoroutineScope(Dispatchers.Main).launch {
                    handleMarkerOnclick(marker)
                }
//                return handleMarkerOnclick(marker)
                return true
            }
        })
        isMapStyleLoaded = false
        mapLibreMap.setStyle(styleUrl, object : Style.OnStyleLoaded {
            override fun onStyleLoaded(style: Style) {
                isMapStyleLoaded = true
                if (watchAreaPolygon == null) {
                    moveCameraToCenter()
                } else {
                    createEditArea()
                }

                addOnCameraMoveListener()

                val markerCenterLat = intent.getDoubleExtra("marker_center_lat", 0.0)
                val markerCenterLon = intent.getDoubleExtra("marker_center_lon", 0.0)
                if (markerCenterLat != 0.0 || markerCenterLon != 0.0) {
                    val iconFactory = IconFactory.getInstance(this@MapActivity)
                    val options = MarkerOptions()
                        .position(LatLng(markerCenterLat, markerCenterLon))
                        .icon(iconFactory.fromResource(R.drawable.ic_map_location))

                    mapLibreMap.addMarker(options)
                }

                // 请求api/community/detail接口，然后根据boundary字段绘制一个多边形
                if (idCommunity != "") {
                    mapViewModel.getCommunityDetail(idCommunity)
                }
            }
        })
    }


    /**
     * 检测两个点的标记是否相交
     * @param marker1 第一个标记
     * @param marker2 第二个标记
     * @return 如果两个标记相交，则返回 true，否则返回 false
     */
    private fun areMarkersIntersecting(marker1: Marker, marker2: Marker): Boolean {
        // 获取标记的图标并计算其宽度和高度
        val icon1 = marker1.icon?.bitmap
        val icon2 = marker2.icon?.bitmap

        val markerWidth1 = icon1?.width ?: 0
        val markerHeight1 = icon1?.height ?: 0

        val markerWidth2 = icon2?.width ?: 0
        val markerHeight2 = icon2?.height ?: 0

        // 获取标记的位置
        val point1 = mapLibreMap.projection.toScreenLocation(marker1.position)
        val point2 = mapLibreMap.projection.toScreenLocation(marker2.position)

        // 计算第一个标记的边界框
        val boundingBox1 = RectF(
            point1.x - markerWidth1 / 2,
            point1.y - markerHeight1 / 2,
            point1.x + markerWidth1 / 2,
            point1.y + markerHeight1 / 2
        )

        // 计算第二个标记的边界框
        val boundingBox2 = RectF(
            point2.x - markerWidth2 / 2,
            point2.y - markerHeight2 / 2,
            point2.x + markerWidth2 / 2,
            point2.y + markerHeight2 / 2
        )

        // 检查两个边界框是否相交
        return RectF.intersects(boundingBox1, boundingBox2)
    }

    /**
     * DEV-8141 Use marker rotation to manage click events on overlapping markers
     */
    private fun pollMarkerOnclick(marker: Marker): Pair<Marker?, Marker?> {
        var targetMarker = marker
        var targetSideMarker: Marker? = null
        // 获取点击位置的屏幕坐标
        val screenPoint = mapLibreMap.projection.toScreenLocation(targetMarker.position)

        // 清空并合并两个HashMap
        val combineHashMap = HashMap<Long, MapMarkerInfo>().apply {
            putAll(listingMarkerInfoMap)
            putAll(featureMarkerInfoMap)
        }

        // 创建一个空列表来存储挨得很近的 marker
        val closeMarkersList = mutableListOf<Marker>()

        // 寻找有几个marker挨着很近？
        combineHashMap.forEach { entry ->
            val otherMarker = entry.value.localMarker
            if (otherMarker != null) {
                if (areMarkersIntersecting(targetMarker, otherMarker)) {
                    closeMarkersList.add(otherMarker)
                }
            }
        }


        var detectedFeatureMarker = false
        // 输出挨得很近的 marker 的信息
        for (closeMarker in closeMarkersList) {
            if (featureMarkerInfoMap.get(targetMarker.id)!=null){
                Logger.d("featureMarkerInfoMap found . ID: ${closeMarker.id}")
                detectedFeatureMarker = true
            }
        }

        Logger.d("寻找有几个marker挨着很近？ " + closeMarkersList.count() + ", detectedFeatureMarker: " + detectedFeatureMarker)
        if (detectedFeatureMarker) {
            var zoom = mapHelper.getCameraPositionZoom(mapLibreMap)
            if (closeMarkersList.count() > 2) {
                val currentZoom =
                    BigDecimal(zoom + 1).setScale(1, BigDecimal.ROUND_HALF_UP)
                        .toDouble()
                mapLibreMap.animateCamera(
                    CameraUpdateFactory.newCameraPosition(
                        CameraPosition.Builder()
                            .target(
                                LatLng(targetMarker.position)
                            )
                            .zoom(currentZoom)
                            .build()
                    )
                )
                return Pair(null,null)
            }
        } else {
            return Pair(marker,null)
        }

        var findTargetMarker = false
        // 检查点击位置是否有重叠的marker，如果有重叠的marker，则轮询响应，在这里主要处理listing和feature两种类型的
        for (markerEntry in combineHashMap) {
            val localMarker = markerEntry.value.localMarker
            if (localMarker != null) {
                if (areMarkersIntersecting(targetMarker, localMarker)) {
                    if (currentClickMarkerId != localMarker.id) {
                        if (findTargetMarker) {
                            targetSideMarker = localMarker
                        } else {
                            findTargetMarker = true
                            targetMarker = localMarker
                            currentClickMarkerId = targetMarker.id
                        }
                    }else{
                        targetSideMarker = localMarker
                    }
                    Logger.d("Marker overlap detected. Switching to marker with ID: ${targetMarker.id}," +
                            " Position: ${targetMarker.position}"+", targetSideMarker: ${targetSideMarker?.id}")
                }
            }
        }
        return Pair(targetMarker,targetSideMarker)
    }


    private suspend fun handleMarkerOnclick(marker: Marker): Boolean {
        val pair = pollMarkerOnclick(marker)
        val targetMarker = pair.first
        val targetSideMarker = pair.second
        if (targetMarker==null) return false

        edmontonCenterMarker?.let {
            if (it.id == targetMarker.id) {
                GALog.log("rae_unavailable_marker_click")
                showRaeUnavailableTip()
                return true
            }
        }

        Logger.d("targetMarker: " + targetMarker.id + ", targetSideMarker: " + targetSideMarker?.id +
                ", lastClickMarker: " + lastClickMarker?.id)

        GALog.log("marker_click")
        // 有很多种marker ，需要挨个遍历改样式
        val listingMarkerInfo = listingMarkerInfoMap.get(targetMarker.id)
        val schoolInfo = schoolMarkerInfoMap.get(targetMarker.id)
        val nearByInfo = nearbyMarkerInfoMap.get(targetMarker.id)
        val featureInfo = featureMarkerInfoMap.get(targetMarker.id)
        val viewInMapInfo = viewInMapInfoMap.get(targetMarker.id)

        viewInMapInfo?.let {
            lastMarkerInfoIds = arrayListOf(it.toString())
            getListingPreviewMany()
        }

        schoolInfo?.let {
            schoolInfo?.id?.let {
                mapHelper.updateSchoolMarker(schoolInfo, targetMarker)
                mapViewModel.getSearchSchoolDetails(it)
            }
        }

        nearByInfo?.let {
            mapHelper.updateNearBySoldMarker(it, targetMarker)
            lastMarkerInfoIds = it.ids
            getListingPreviewMany()
        }

        featureInfo?.let {
            if ("feature".equals(featureInfo.marker)) {
                mapHelper.updateFeatureMarker(it, targetMarker)
                lastMarkerInfoIds = it.ids
                getListingPreviewMany()
            }
        }

        listingMarkerInfo?.let { markerInfo ->
            val cameraZoom = mapHelper.getCameraPositionZoom(mapLibreMap)
            markerInfo.count?.let {
                // 对于group marker，数字大于等于25的时候，点击marker，应该放大地图。小于25的时候显示房源列表
                if (markerInfo.count <= 25 || cameraZoom >= 17) {
                    Logger.e("markerInfo: " + markerInfo)

                    if ("sold".equals(markerInfo.marker)) {
                        mapHelper.updateSoldMarker(markerInfo, targetMarker)
                    } else if ("de-listed".equals(markerInfo.marker)) {
                        mapHelper.updateDeListedMarker(markerInfo, targetMarker)
                    } else if ("active".equals(markerInfo.marker)) {
                        mapHelper.updateSaleMarker(markerInfo, targetMarker)
                    } else if ("normal".equals(markerInfo.marker)) {
                        mapHelper.updateGroupMarker(markerInfo, targetMarker)
                    } else if ("feature".equals(markerInfo.marker)) {
                        mapHelper.updateFeatureMarker(markerInfo, targetMarker)
                    }

                    lastMarkerInfoIds = markerInfo.ids
                    getListingPreviewMany()
                } else {

                    var addzoom = 1.0
                    if (cameraZoom < 10) {
                        addzoom = 3.0
                    } else if (cameraZoom <= 14) {
                        addzoom = 2.0
                    }

                    val finalMoveZoom =
                        BigDecimal(cameraZoom + addzoom).setScale(1, BigDecimal.ROUND_HALF_UP)
                            .toDouble()
                    mapLibreMap.animateCamera(
                        CameraUpdateFactory.newCameraPosition(
                            CameraPosition.Builder()
                                .target(
                                    LatLng(markerInfo.location.lat, markerInfo.location.lon)
                                )
                                .zoom(finalMoveZoom)
                                .build()
                        )
                    ,100)
                    return false
                }
            }
        }

        targetSideMarker?.let {
            updateMarker(
                targetSideMarker,
                listingMarkerInfoMap.get(targetSideMarker?.id),
                schoolMarkerInfoMap.get(targetSideMarker?.id),
                nearbyMarkerInfoMap.get(targetSideMarker?.id),
                featureMarkerInfoMap.get(targetSideMarker?.id)
            )
        }

        if (targetSideMarker?.id != lastClickMarker?.id && targetMarker.id != lastClickMarker?.id) {
            updateMarker(
                lastClickMarker,
                lastListingInfo,
                lastSchoolInfo,
                lastNearByInfo,
                lastFeatrueInfo
            )
        }
        lastClickMarker = targetMarker
        lastListingInfo = listingMarkerInfo
        lastSchoolInfo = schoolInfo
        lastNearByInfo = nearByInfo
        lastFeatrueInfo = featureInfo
        return true
    }

    private suspend fun updateMarker(
        lastClickMarker: Marker?,
        lastListingInfo: MapMarkerInfo?,
        lastSchoolInfo: SchoolInfo?,
        lastNearByInfo: MapMarkerInfo?,
        lastFeatrueInfo: MapMarkerInfo?
    ) {
        if (lastClickMarker != null) {

            if (lastSchoolInfo != null) {
                mapHelper.updateSchoolMarker(
                    lastSchoolInfo!!,
                    lastClickMarker!!,
                    true
                )
            }

            if (lastNearByInfo != null) {
                mapHelper.updateNearBySoldMarker(
                    lastNearByInfo!!,
                    lastClickMarker!!,
                    true
                )
            }

            lastFeatrueInfo?.let {
                if ("feature".equals(it.marker)) {
                    mapHelper.updateFeatureMarker(
                        lastFeatrueInfo!!,
                        lastClickMarker!!,
                        true
                    )
                }
            }


            lastListingInfo?.let {
                if ("sold".equals(it.marker)) {
                    mapHelper.updateSoldMarker(
                        lastListingInfo!!,
                        lastClickMarker!!,
                        true
                    )
                } else if ("de-listed".equals(it.marker)) {
                    mapHelper.updateDeListedMarker(
                        lastListingInfo!!,
                        lastClickMarker!!,
                        true
                    )
                } else if ("active".equals(it.marker)) {
                    mapHelper.updateSaleMarker(
                        lastListingInfo!!,
                        lastClickMarker!!,
                        true
                    )
                } else if ("normal".equals(it.marker)) {
                    mapHelper.updateGroupMarker(
                        lastListingInfo!!,
                        lastClickMarker!!,
                        true
                    )
                } else {
                    // do nothing
                }
            }
        }
    }

    private fun showRaeUnavailableTip() {
        GALog.log("popup_show", "rae_unavailable_tip")
        val hsDialog = HSAlertDialog(
            MapActivity@ this,
            "",
            "Edmonton sold data available on desktop/mobile website \n" +
                    "https://housesigma.com",
            "Cancel",
            "Open Browser",
            autoDismiss = true,
            cb = object : HSAlertDialog.HSAlertCallback {
                override fun onSuccess() {
                    WebViewHelper.jumpOuterWebView(
                        this@MapActivity,
                        "https://housesigma.com"
                    )
                }
            })

        hsDialog.setOnCancelListener {
            GALog.log("popup_submit", "rae_unavailable_tip_cancel")
        }
        hsDialog.setHSLeftBtnPressCallback(object : HSAlertDialog.HSBtnPressCallback {
            override fun pressLeftBtn() {
                GALog.log("popup_submit", "rae_unavailable_tip_cancel")
            }

            override fun pressRightBtn() {
                GALog.log("popup_submit", "rae_unavailable_tip_open_browser")
            }
        })
        hsDialog.setCancelable(true)
        hsDialog.show()
    }


    private fun getListingPreviewMany(withMarker: Boolean = false) {
        lastMarkerInfoIds?.let { mapViewModel.getListingPreviewMany(it, withMarker) }
    }

    /**
     * 移动camera 到某个位置
     */
    private fun moveCameraToPoint(point: com.housesigma.android.model.Location,zoom:Double = 14.0) {
        var currentMapCenter = LatLng(point.lat, point.lon)
        var currentMapZoom = zoom
        mapLibreMap.cameraPosition = CameraPosition.Builder()
            .target(
                currentMapCenter
            )
            .zoom(currentMapZoom)
            .build()
    }

    private fun createEditArea() {
        if (watchAreaPolygon == null) return
        val polygon = watchAreaPolygon!!
        if (polygon.size < 2) return
        // 在地图上放置一个watch area的多边形
        val polygonLatLngList = ArrayList<LatLng>()
        val polylineLatLngList = ArrayList<LatLng>()
        for (polygonItem in polygon) {
            polygonLatLngList.add(LatLng(polygonItem.lat, polygonItem.lon))
            polylineLatLngList.add(LatLng(polygonItem.lat, polygonItem.lon))
        }
        polylineLatLngList.add(LatLng(polygon[0].lat, polygon[0].lon))

        mapLibreMap.addPolygon(
            PolygonOptions()
                .addAll(polygonLatLngList)
                .fillColor(Color.parseColor("#1092F0"))
                .alpha(0.3f)
        )
        mapLibreMap.addPolyline(
            PolylineOptions()
                .addAll(polylineLatLngList)
                .color(Color.parseColor("#1E91FB"))
                .width(3.0f)
                .alpha(1f)
        )



        if (polygon != null) {
            val latLngBounds = LatLngBounds.Builder()
            polygon.forEach {
                latLngBounds.include(MapUtils.polygon2LatLng(it))
            }
            mapLibreMap.animateCamera(
                CameraUpdateFactory.newLatLngBounds(
                    latLngBounds.build(), 100
                )
            )
        }
    }

    /**
     * 移动camera 到中心点位置，在这儿的位置是固定的几个点
     */
    private fun moveCameraToCenter() {
        // 这里的位置和选择的location相关  数据来源于 /api/init/app的 provinces中

        var currentMapCenter = MapUtils.getCurrentMapCenter()
        var currentMapZoom = MapUtils.getCurrentMapZoom()

        mapLibreMap.cameraPosition = CameraPosition.Builder()
            .target(
                currentMapCenter
            )
            .zoom(currentMapZoom)
            .build()
    }

    /**
     * 监听camera move，如在60ms内有变化，就可以获取屏幕可见经纬度范围，请求api接口
     */
    private fun addOnCameraMoveListener() {
        getMapRequestRegion()

//        https://stackoverflow.com/questions/38727517/oncamerachangelistener-is-deprecated
//        用addOnCameraIdleListener监听代替OnCameraChangeListener
        mapLibreMap.addOnCameraIdleListener {
            if (!ready) return@addOnCameraIdleListener
            Logger.d("move camera idle.........")

            // 可见区域
            getMapRequestRegion()
            Logger.e("准备请求reloadMapData....."+System.currentTimeMillis())
            reloadMapData()

        }

    }

    private fun getMapRequestRegion() {
        goToFirstPageIgnoreViewType()

        val visibleRegion = mapLibreMap.projection.visibleRegion
//        val farLeft = visibleRegion.farLeft //可视区域的左上角。
//        val nearRight = visibleRegion.nearRight //可视区域的右下角。
        val bounds = visibleRegion.latLngBounds
        val center = visibleRegion.latLngBounds.center
        val cameraZoom = mapHelper.getCameraPositionZoom(mapLibreMap)

        if (!"prod".equals(BuildConfig.FLAVOR)) {
            binding.tvTestZoom.text = "zoom:" + cameraZoom
        }

        bounds.let {
            val west = it.longitudeWest
            val south = it.latitudeSouth
            val east = it.longitudeEast
            val north = it.latitudeNorth

            val extraLat = Math.abs(south - north) / 2
            val extraLon = Math.abs(west - east) / 2

            //真实的经纬度，listview模式下list列表接口需要
            realLat1 = it.latitudeNorth
            realLon1 = it.longitudeEast
            realLat2 = it.latitudeSouth
            realLon2 = it.longitudeWest

            //扩大后的经纬度，linting接口需要
            lat1 = it.latitudeNorth + (if (north > south) extraLat else -extraLat)
            lon1 = it.longitudeEast + (if (east > west) extraLon else -extraLon)
            lat2 = it.latitudeSouth + (if (south > north) extraLat else -extraLat)
            lon2 = it.longitudeWest + (if (west > east) extraLon else -extraLon)
        }

        MapUtils.setCurrentMapCenter(LatLng(center.latitude, center.longitude))
        MapUtils.setCurrentMapZoom(cameraZoom)
    }

    private fun isListView(): Boolean {
        return viewType == "list"
    }

    val reloadMapData = debounce(250L) {
        Logger.e("收到listing网络请求debounce 开始处理....."+System.currentTimeMillis())
        if (isListView()) {//list-view
            updateMapFilterRedPoint() // 在list模式下更新小红点
            changeListView()
        } else {//map-view
            requestDataWithinVisibleMapArea()
            hideProgress()
        }
//        Logger.e("收到listing网络请求debounce 结束....."+System.currentTimeMillis())
    }

    // DEV-5106 快速滑动地图的时候，当地图还在飞动，没有停止时，恰巧又点了切换listview模式按钮，就会触发请求两次listview模式的api的问题
    val reloadMapDataOnlyOnMapType = debounce(250L) {
        if (!isListView()) {//map-view
            requestDataWithinVisibleMapArea()
            hideProgress()
        }
    }

    private fun showProgress() {
        binding.progress.visibility = View.VISIBLE
    }


    private fun hideProgress() {
        binding.progress.visibility = View.GONE
    }

    private fun requestDataWithinVisibleMapArea() {
//        Logger.e( "debounceReloadMapData2: "+System.currentTimeMillis())
        if (lat1 == 0.0) return
//        Logger.e( "list type " + listType)
        showProgress()
        updateMapFilterRedPoint()
        binding.progress.visibility = View.VISIBLE
        val listingDays = MMKVUtils.getStr(isSale.toString() + "map_listing_days") ?: "0"
        var deListDays = MMKVUtils.getStr(isSale.toString() + "map_s_ld") ?: "90"
        val cameraZoom = mapHelper.getCameraPositionZoom(mapLibreMap)

        Logger.e("发起listing网络请求....."+System.currentTimeMillis())
        mapViewModel.getMapListing2(
            houseType = mapPropertyView!!.getPropertyTypes(),
            lon1 = lon1,
            lon2 = lon2,
            lat1 = lat1,
            lat2 = lat2,
            zoom = cameraZoom,
            listing_days = listingDays,
            de_list_days = deListDays,
            list_type = listType,
            basement = mapFiltersView!!.getBasement(),
            bedroom_range = mapFiltersView!!.getBedroomRange(),
            open_house_date = mapFiltersView!!.getOpenHouseDate(),
            bathroom_min = mapFiltersView!!.getBathroomMin(),
            garage_min = mapFiltersView!!.getGarageMin(),
            description = mapFiltersView!!.getDescription(),
            max_maintenance_fee = mapFiltersView!!.getFee(),
            price = mapFiltersView!!.getPrice(),
            front_feet = mapFiltersView!!.getFeet(),
            square_footage = mapFiltersView!!.getSquareFootage(),
            listing_type = mapFiltersView!!.getListingType(),
            lot_size = mapFiltersView!!.getLotSize(),
            building_age = mapFiltersView!!.getBuildingAge(),
            rental_yield_range = mapFiltersView!!.getRentalYieldRange(),
            school_score_range = mapFiltersView!!.getSchoolScoreRange(),
        )


        // feature的marker要zoom大于9才显示
        if (cameraZoom >= 9) {
            mapViewModel.getMapSearchv2Feature(
                zoom = cameraZoom,
                list_type = listType,
                basement = mapFiltersView!!.getBasement(),
                bedroom_range = mapFiltersView!!.getBedroomRange(),
                open_house_date = mapFiltersView!!.getOpenHouseDate(),
                bathroom_min = mapFiltersView!!.getBathroomMin(),
                garage_min = mapFiltersView!!.getGarageMin(),
                description = mapFiltersView!!.getDescription(),
                max_maintenance_fee = mapFiltersView!!.getFee(),
                price = mapFiltersView!!.getPrice(),
                front_feet = mapFiltersView!!.getFeet(),
                square_footage = mapFiltersView!!.getSquareFootage(),
                rental_yield_range = mapFiltersView!!.getRentalYieldRange(),
                school_score_range = mapFiltersView!!.getSchoolScoreRange()
            )
        } else {
            val mapFeatureList: ArrayList<Marker> = ArrayList()
            featureMarkerInfoMap.forEach {
                it.value.localMarker?.let { it1 -> mapFeatureList.add(it1) }
            }
            mapLibreMap.removeAnnotations(mapFeatureList)
            featureMarkerInfoMap.clear()
        }


        // nearbysold还有一种小蓝点的情况大于16小于18的时候显示
        // NearBySold的非原点的marker要zoom大于18才显示
        val nearbySold = MMKVUtils.getBoolean(isSale.toString() + "nearbySold", true)
        if (cameraZoom >= 16.0 && nearbySold) {
            mapViewModel.getSearchV2NearBySold(
                houseType = mapPropertyView!!.getPropertyTypes(),
                lon1 = lon1,
                lon2 = lon2,
                lat1 = lat1,
                lat2 = lat2,
                zoom = cameraZoom,
                listing_days = listingDays,
                de_list_days = deListDays,
                list_type = listType,
                basement = mapFiltersView!!.getBasement(),
                bedroom_range = mapFiltersView!!.getBedroomRange(),
                open_house_date = mapFiltersView!!.getOpenHouseDate(),
                bathroom_min = mapFiltersView!!.getBathroomMin(),
                garage_min = mapFiltersView!!.getGarageMin(),
                description = mapFiltersView!!.getDescription(),
                max_maintenance_fee = mapFiltersView!!.getFee(),
                price = mapFiltersView!!.getPrice(),
                front_feet = mapFiltersView!!.getFeet(),
                square_footage = mapFiltersView!!.getSquareFootage(),
                rental_yield_range = mapFiltersView!!.getRentalYieldRange(),
                school_score_range = mapFiltersView!!.getSchoolScoreRange()
            )
        } else {
            val mapNearList: ArrayList<Marker> = ArrayList()
            nearbyMarkerInfoMap.forEach {
                it.value.localMarker?.let { it1 -> mapNearList.add(it1) }
            }
            mapLibreMap.removeAnnotations(mapNearList)
            nearbyMarkerInfoMap.clear()
        }

        val showSchool = MMKVUtils.getBoolean("map_pop_school_show", false)
        // 学校的marker要zoom大于13才显示
        if (cameraZoom >= 13 && showSchool) {

//            Logger.e("schoolCatholic "+schoolCatholic+
//                    ",schoolElementary "+
//                schoolElementary+" schoolMatch_score "+
//                schoolMatch_score+" schoolPublic"+
//               schoolPublic+" schoolSecondary"+
//                schoolSecondary)
            mapViewModel.getSearchMapSearchSchool(
                catholic = schoolCatholic,
                elementary = schoolElementary,
                lat1 = lat1,
                lat2 = lat2,
                lon1 = lon1,
                lon2 = lon2,
                match_score = schoolMatch_score,
                public = schoolPublic,
                secondary = schoolSecondary,
            )
        } else {
            delSchoolMarkers()

//            delSchoolPolygon() #DEV-4060 School boundary disappear when zooming out
        }
//        Logger.e("reloadMapData zoom =" + zoom+" showSchool="+showSchool)

    }


    private fun delSchoolMarkers() {
        val schoolMarkerList: ArrayList<Marker> = ArrayList()
        schoolMarkerInfoMap.forEach {
            it.value.localMarker?.let { it1 -> schoolMarkerList.add(it1) }
        }
        mapLibreMap.removeAnnotations(schoolMarkerList)
        schoolMarkerInfoMap.clear()
    }

    private fun delSchoolPolygon() {
        for (schoolPolygon in schoolPolygons) {
            mapLibreMap.removePolygon(schoolPolygon)
        }
        for (schoolPolyline in schoolPolylines) {
            mapLibreMap.removePolyline(schoolPolyline)
        }
    }


    override fun onStart() {
        super.onStart()
        mapView.onStart()
    }

    override fun onResume() {
        super.onResume()
        mapView.onResume()
        isMapViewActive = true
        GALog.page("map")
    }

    override fun onPause() {
        super.onPause()
        isMapViewActive = false
        mapView.onPause()
    }

    override fun onStop() {
        super.onStop()
        mapView.onStop()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mapView.onSaveInstanceState(outState)
    }

    override fun onLowMemory() {
        super.onLowMemory()
        mapView.onLowMemory()
    }

    override fun onDestroy() {
        super.onDestroy()
        mapView.onDestroy()
        binding.progress.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
        EventBus.getDefault().postSticky(MessageEvent(MessageType.NEED_REQUEST_WATCHLISTLISTINGIDS))
        if (viewType=="map"){
            getListingPreviewMany()
        } else {
            changeListView()
        }
    }

    private fun showListingPreview(){
        // 先GONE，再展开，解决preview list个数为1的时候，bottomSheetBehavior悬浮在手机的最中间，底部留白的问题
        binding.llListings.visibility = View.GONE
        binding.llListings.visibility = View.VISIBLE
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_COLLAPSED
    }

    private fun hiddenListingPreview(){
        binding.llListings.visibility = View.GONE
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
    }


    private suspend fun reloadListingMarkers() {
        listingMarkerInfoMap.forEach { (_, markerInfo) ->
            val marker = markerInfo.localMarker ?: return@forEach
            val isLastClicked = marker.id == lastClickMarker?.id

            // update marker watched status
            when (markerInfo.marker) {
                "sold" -> mapHelper.updateSoldMarker(markerInfo, marker, !isLastClicked)
                "de-listed" -> mapHelper.updateDeListedMarker(markerInfo, marker, !isLastClicked)
                "active" -> mapHelper.updateSaleMarker(markerInfo, marker, !isLastClicked)
            }
        }
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CODE_WATCHED_AREA && resultCode == RESULT_OK && data != null) {
            val watchedAreaName = data.getStringExtra("watchedAreaName")
            val centerLat = data.getDoubleExtra("centerLat", 0.0)
            val centerLng = data.getDoubleExtra("centerLng", 0.0)
            val cameraZoom = data.getDoubleExtra("cameraZoom", 0.0)
            val polygonPath = data.getStringExtra("polygonPath")

            showWatchedAreaSuccessDialog(watchedAreaName, centerLat, centerLng, cameraZoom, polygonPath)
        }
    }


    private fun showWatchedAreaSuccessDialog(
        watchedAreaName: String?,
        centerLat: Double,
        centerLng: Double,
        cameraZoom: Double,
        polygonPath: String? = null
    ) {
        val center = LatLng(centerLat, centerLng)
        val watchedAreaSuccessFragment = WatchedAreaSuccessFragment.newInstance(watchedAreaName, center, cameraZoom, polygonPath)
        watchedAreaSuccessFragment.show(supportFragmentManager, "")
        watchedAreaSuccessFragment.setWatchedAreaSuccessCallback(object :
            WatchedAreaSuccessFragment.WatchedAreaSuccessCallback {
            override fun onSuccess() {
                val intent = Intent(this@MapActivity, MainActivity::class.java)
                startActivity(intent)
                EventBus.getDefault().post(MessageEvent(MessageType.WATCHED_AREAS_CHANGED))
                EventBus.getDefault().post(MessageEvent(MessageType.JUMP_HOME_WATCHED))
                EventBus.getDefault().post(MessageEvent(MessageType.JUMP_WATCHED_AREA))
                finish()
            }
        })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        try {
            when (event.type) {
                MessageType.TRIGGER_NOT_INTERESTED_LISTING -> {
                    if (viewType=="map") {
                        // refresh preview list card watched status
                        mListingPreviewAdapter?.notifyDataSetChanged()
                    } else {
                        mListingViewAdapter?.notifyDataSetChanged()
                    }
                }

                MessageType.RELOAD_WATCHED_STATUS -> {
                    if (viewType=="map") {
                        // refresh preview list card watched status
                        mListingPreviewAdapter?.notifyDataSetChanged()
                        // refresh map marker watched status
                        CoroutineScope(Dispatchers.Main).launch {
                            reloadListingMarkers()
                        }
                    } else {
                        mListingViewAdapter?.notifyDataSetChanged()
                    }
                }


                MessageType.PASSWORD_CHANGE -> {
                    // 修改密码后，刷新preview和listview下的adapter数据源
                    if (viewType=="map") {
                        getListingPreviewMany()
                    } else {
                        changeListView()
                    }
                }

                MessageType.RELOAD_MAP -> {
                    getMapFilterList()
                }

                MessageType.REMOVE_COMMUNITY_BOUNDARIES -> {
                    removeCommunityBoundaries()
                }

                else -> {}
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}