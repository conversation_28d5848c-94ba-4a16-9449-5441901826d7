package com.housesigma.android.ui.map

import android.content.Context
import android.util.Log
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.ListingFilter
import com.housesigma.android.utils.MMKVUtils

//For sale sold & De-listed 部分
class MapFilterSaleSoldAdapter(context: Context, isLeft: <PERSON>olean,isSale:Boolean) :
    BaseQuickAdapter<ListingFilter, BaseViewHolder>(R.layout.item_map_filter) {

    private var context1 = context
    private var isLeft = isLeft
    private var isSale = isSale
    override fun convert(holder: BaseViewHolder, item: ListingFilter) {
        holder.setText(R.id.tv_filter, item.name)


        if (isLeft){
            if ((MMKVUtils.getStr(isSale.toString() +"map_fs_ld_abbr")?:"All").equals(item.abbr)) {
                holder.setBackgroundResource(R.id.iv_filter,R.drawable.ic_filter_point_selected)
                holder.setTextColor(R.id.tv_filter, context1.resources.getColor(R.color.app_main_color))
            }else{
                holder.setTextColor(R.id.tv_filter, context1.resources.getColor(R.color.color_black))
                holder.setBackgroundResource(R.id.iv_filter,R.drawable.ic_filter_point_select)
            }
        }else{
            if ((MMKVUtils.getStr(isSale.toString() +"map_s_ld_abbr")?:"90d").equals(item.abbr)) {
                holder.setBackgroundResource(R.id.iv_filter,R.drawable.ic_filter_point_selected)
                holder.setTextColor(R.id.tv_filter, context1.resources.getColor(R.color.app_main_color))
            }else{
                holder.setTextColor(R.id.tv_filter, context1.resources.getColor(R.color.color_black))
                holder.setBackgroundResource(R.id.iv_filter,R.drawable.ic_filter_point_select)
            }
        }

    }
}