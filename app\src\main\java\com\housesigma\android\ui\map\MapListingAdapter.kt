package com.housesigma.android.ui.map

import android.graphics.Color
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.Paint
import android.text.TextUtils
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.HSApp
import com.housesigma.android.R
import com.housesigma.android.model.House
import com.housesigma.android.model.SortScore
import com.housesigma.android.model.listingOnSearchStatusTypeV2
import com.housesigma.android.ui.notinterested.NotInterestedHelper
import com.housesigma.android.ui.watched.WatchedHelper
import com.housesigma.android.utils.Constants
import com.housesigma.android.utils.ScreenUtils
import com.housesigma.android.views.HSHtmlTextView
import jp.wasabeef.glide.transformations.RoundedCornersTransformation


class MapListingAdapter :
    BaseQuickAdapter<House, BaseViewHolder>(R.layout.item_map_listing) {

    init {
        addChildClickViewIds(
            R.id.ll,
            R.id.tv_agreement_required,
            R.id.tv_login_required,
            R.id.tv_contact_agent,
            R.id.tv_not_available
        )
    }


    override fun convert(holder: BaseViewHolder, item: House) {
        val corner = ScreenUtils.dpToPx(6f)
        val multi = MultiTransformation(
            CenterCrop(),
            RoundedCornersTransformation(
                corner.toInt(),
                0,
                RoundedCornersTransformation.CornerType.ALL
            )
        )


        if (item.price == null) {
            holder.setGone(R.id.tv_price, true)
        } else {
            holder.setVisible(R.id.tv_price, true)
            holder.setText(R.id.tv_price, "$${item.price}")
        }



        holder.setText(R.id.tv_list_status, item?.list_status?.text?:"")


        // REBGV
        if (TextUtils.isEmpty(item.brokerage_text)) {
            holder.setGone(R.id.tv_bc_brokerage_text, true)
        } else {
            holder.setVisible(R.id.tv_bc_brokerage_text, true)
            holder.setText(R.id.tv_bc_brokerage_text, item.brokerage_text)
        }


        // #DEV-2234 I revised Figma to look like this. City should be on the second line so it leaves more room for the address on the first line.
        // If a sold listing,
        // the sold price takes the first line of the address,
        // and the address is fit onto the second line. @石殿轩shidianxuan please help translate if necessary
        // 是否显示 sold 信息的判断逻辑
        val tvSoldPrice = holder.getView<TextView>(R.id.tv_price_sold)
        if (item.list_status.sold == 1 && !TextUtils.isEmpty(item.price_sold)) {
            val soldTag = if (item.list_status.status.equals("LSD", ignoreCase = true)) "Leased: " else "Sold: "
            holder.setText(R.id.tv_sold_tag, soldTag)
            holder.setText(R.id.tv_price_sold, "$" + item.price_sold)
            holder.setVisible(R.id.tv_sold_tag, true)
            holder.setVisible(R.id.tv_price_sold, true)
            holder.setVisible(R.id.tv_address, true)

            var address = item.municipality_name
            address = item.address + ", " + address
            holder.setText(R.id.tv_address, address)

            tvSoldPrice.setTextColor(Color.parseColor("#FB1815"))
            tvSoldPrice.paint.flags = 0
            tvSoldPrice.paint.isAntiAlias = true
        } else if (item.isDFT() && !TextUtils.isEmpty(item.price_sold)) {
//            DEV-3032 DFT 的售价得是灰色，带中划线
            val soldTag = if (item.list_status.status.equals("LSD", ignoreCase = true)) "Leased: " else "Sold: "
            holder.setText(R.id.tv_sold_tag, soldTag)
            holder.setText(R.id.tv_price_sold, "$" + item.price_sold)
            holder.setVisible(R.id.tv_sold_tag, true)
            holder.setVisible(R.id.tv_price_sold, true)
            holder.setVisible(R.id.tv_address, true)

            var address = item.municipality_name
            address = item.address + ", " + address
            holder.setText(R.id.tv_address, address)

            tvSoldPrice.setTextColor(Color.parseColor("#BBBBBB"))
            tvSoldPrice.paint.flags = (Paint.STRIKE_THRU_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG)//中划线
        }  else {
            holder.setText(R.id.tv_price_sold, "")
            holder.setGone(R.id.tv_sold_tag, true)
            holder.setGone(R.id.tv_price_sold, true)
            holder.setVisible(R.id.tv_address, true)

            val address = item.address + ", "+ item.municipality_name
            holder.setText(R.id.tv_address, address)

            tvSoldPrice.setTextColor(Color.parseColor("#FB1815"))
            tvSoldPrice.paint.flags = 0
            tvSoldPrice.paint.isAntiAlias = true
        }

        if (!TextUtils.isEmpty(item?.text?.date_preview ?: "")) {
            holder.setVisible(R.id.tv_date_times, true)
            holder.setText(R.id.tv_date_times, item?.text?.date_preview ?: "")
        } else {
            holder.setGone(R.id.tv_date_times, true)
        }

        // open house时间显示
        if (item.open_house_date.size == 0) {
            holder.setGone(R.id.tv_open_house_time, true)
            holder.setGone(R.id.ll_open_house, true)
        } else {
            holder.setVisible(R.id.tv_open_house_time, true)
            holder.setVisible(R.id.ll_open_house, true)
            if (item.open_house_date.size > 1) {
                holder.setVisible(R.id.tv_open_house_time_more, true)
                holder.setText(R.id.tv_open_house_time, item.open_house_date[0].text)
            } else {
                holder.setGone(R.id.tv_open_house_time_more, true)
                holder.setText(R.id.tv_open_house_time, item.open_house_date[0].text)
            }
        }

        if (item.list_status.public <= -10) {
            holder.setVisible(R.id.tv_data_private1, true)
            holder.setVisible(R.id.tv_data_private2, true)
            holder.setGone(R.id.tv_sold_tag, true)
            holder.setGone(R.id.tv_price_sold, true)
            holder.setGone(R.id.tv_address, true)
            holder.setVisible(R.id.tv_date_times, false)
            if (item.list_status.agent_available) {
                holder.setVisible(R.id.tv_contact_agent, true)
            } else {
                holder.setGone(R.id.tv_contact_agent, true)
            }
            holder.setGone(R.id.ll_score,true)
        } else {
            holder.setVisible(R.id.ll_score,true)
            holder.setGone(R.id.tv_contact_agent, true)
            holder.setGone(R.id.tv_data_private1, true)
            holder.setGone(R.id.tv_data_private2, true)

            handleTag(item,holder)
        }

        holder.setText(R.id.tv_house_type_name, item.house_type_name)


        if (item.bedroom_string==null) {
            holder.setGone(R.id.tv_bedroom_string, true)
        } else {
            holder.setVisible(R.id.tv_bedroom_string, true)
            holder.setText(R.id.tv_bedroom_string, item.bedroom_string)
        }

        if (item.washroom==null) {
            holder.setGone(R.id.tv_washroom, true)
        } else {
            holder.setVisible(R.id.tv_washroom, true)
            holder.setText(R.id.tv_washroom, item.washroom)
        }

        if (item.parking.total==null) {
            holder.setGone(R.id.tv_garage, true)
        } else {
            holder.setVisible(R.id.tv_garage, true)
            holder.setText(R.id.tv_garage, item.parking.total)
        }


        Glide.with(context)
            .load(item.photo_url)
            .transform(multi)
            .error(R.drawable.shape_preview_pic_place_holder)
            .placeholder(R.drawable.shape_preview_pic_place_holder)
            .into(holder.getView(R.id.iv_house_pic))
        var tvPrice = holder.getView<TextView>(R.id.tv_price)
        setPriceTextStyle(tvPrice, item)


        holder.setGone(R.id.tv_trreb_expired_body, true)

        if (item.isAgreementRequired()) {
            holder.setGone(R.id.tv_not_available, true)
            holder.setVisible(R.id.tv_agreement_required, true)
            holder.setGone(R.id.tv_login_required, true)
            holder.setVisible(R.id.masked, true)
        } else if (item.isNotAvailable()){
            holder.setVisible(R.id.tv_not_available, true)
            holder.setGone(R.id.tv_agreement_required, true)
            holder.setGone(R.id.tv_login_required, true)
            holder.setVisible(R.id.masked, true)
        } else if (item.isLoginRequired() || item.isPasswordExpired()) {
            holder.setGone(R.id.tv_not_available, true)
            var string =  "Login Required"
            if (item.isPasswordExpired()){
                string = "Password Expired"
            }
            holder.setText(R.id.tv_login_required, string)
            holder.setGone(R.id.tv_agreement_required, true)
            holder.setVisible(R.id.tv_login_required, true)
            holder.setVisible(R.id.masked, true)
        }  else if (item.isNeedReLogin()) {
            val tvTrrebExpiredBody = holder.getView<TextView>(R.id.tv_trreb_expired_body)
            val explainText = HSApp.initApp?.timeout_explain?.explain_text?:""

            holder.setVisible(R.id.tv_trreb_expired_body, true)
            HSHtmlTextView.setHtml(context, explainText,tvTrrebExpiredBody)
            holder.setText(R.id.tv_login_required, Constants.TEXT_VIEW_TRREB_TIMEOUT)

            holder.setGone(R.id.tv_not_available, true)
            holder.setGone(R.id.tv_agreement_required, true)
            holder.setVisible(R.id.tv_login_required, true)
            holder.setVisible(R.id.masked, true)
        } else {
            holder.setGone(R.id.tv_not_available, true)
            holder.setGone(R.id.tv_agreement_required, true)
            holder.setGone(R.id.tv_login_required, true)
            holder.setGone(R.id.masked, true)
        }
//        是否显示 sold 信息的判断逻辑
//        list_status.sold === 1 && price_sold
        if (item.list_status.sold == 1 && !TextUtils.isEmpty(item.price_sold)) {
            val soldTag = if (item.list_status.status.equals("LSD", ignoreCase = true)) "Leased: " else "Sold: "
            holder.setText(R.id.tv_sold_tag, soldTag)
            holder.setText(R.id.tv_price_sold, "$" + item.price_sold)
        } else if (item.isDFT() && !TextUtils.isEmpty(item.price_sold)) {
            val soldTag = if (item.list_status.status.equals("LSD", ignoreCase = true)) "Leased: " else "Sold: "
            holder.setText(R.id.tv_sold_tag, soldTag)
            holder.setText(R.id.tv_price_sold, "$" + item.price_sold)
        } else {
            holder.setText(R.id.tv_sold_tag, "Listing: ")
            holder.setText(R.id.tv_price_sold, "$" + item.price)
        }

        if (WatchedHelper.findWatchedByListingId(item.id_listing)) {
            holder.setVisible(R.id.tv_watched_status, true)
        } else {
            holder.setGone(R.id.tv_watched_status, true)
        }

        if (item.isAgreementRequired()||item.isNotAvailable()||item.isLoginRequired() || item.isPasswordExpired()||item.isNeedReLogin()){
            holder.setGone(R.id.tv_not_interested,true)
            holder.setGone(R.id.view_not_interested,true)
        } else {
            val rootViewCMSaturation: Float
            if (NotInterestedHelper.findNotInterestedByListingId(item.id_listing)){
                holder.setVisible(R.id.tv_not_interested,true)
                holder.setVisible(R.id.view_not_interested,true)
                rootViewCMSaturation = 0f
            } else {
                holder.setGone(R.id.tv_not_interested,true)
                holder.setGone(R.id.view_not_interested,true)
                rootViewCMSaturation = 1f
            }
            val rootView = holder.getView<LinearLayout>(R.id.ll)
            val paint = Paint()
            val cm = ColorMatrix()
            cm.setSaturation(rootViewCMSaturation)
            paint.colorFilter = ColorMatrixColorFilter(cm)
            rootView.setLayerType(View.LAYER_TYPE_HARDWARE, paint)
        }


    }


    private fun handleTag(
        item: House,
        holder: BaseViewHolder
    ) {
        if (item.scores.rent != null) {
            holder.setText(R.id.tv_rental, "Rental " + item.scores.rent + "/10")
            holder.setVisible(R.id.tv_rental, true)
        } else {
            holder.setGone(R.id.tv_rental, true)
        }

        if (item.scores.school != null) {
            holder.setText(R.id.tv_school, "School " + item.scores.school + "/10")
            holder.setVisible(R.id.tv_school, true)
        } else {
            holder.setGone(R.id.tv_school, true)
        }

        if (item.scores.growth != null) {
            holder.setText(R.id.tv_growth, "Growth " + item.scores.growth + "/10")
            holder.setVisible(R.id.tv_growth, true)
        } else {
            holder.setGone(R.id.tv_growth, true)
        }

        adjustScoreViewOrder(holder, item)
    }

    /**
     * 调整分数显示顺序，第一顺序是分数，第二顺序分数相同，按School、Rental、Growth再排序
     */
    private fun adjustScoreViewOrder(
        holder: BaseViewHolder,
        item: House
    ) {
        val tvSchool = holder.getView<TextView>(R.id.tv_school)
        val tvRental = holder.getView<TextView>(R.id.tv_rental)
        val tvGrowth = holder.getView<TextView>(R.id.tv_growth)
        val tvAssignment = holder.getView<TextView>(R.id.tv_assignment)
        val llScoreView = holder.getView<LinearLayout>(R.id.ll_score)

        llScoreView.removeAllViews()

        if (!TextUtils.isEmpty(item.text.hs_exclusive_tag)) {
            // Assignment 房源不显示 school，rent 等 标签
            holder.setText(R.id.tv_assignment,item.text.hs_exclusive_tag)
            llScoreView.addView(tvAssignment)
            return
        }

        val rentScore = if (item.scores.rent != null) item.scores.rent.toInt() else 0
        val schoolScore = if (item.scores.school != null) item.scores.school.toInt() else 0
        val growthScore = if (item.scores.growth != null) item.scores.growth.toInt() else 0

        val scoreList = listOf(
            SortScore(schoolScore, "school"),
            SortScore(rentScore, "rent"),
            SortScore(growthScore, "growth")
        ).sortedByDescending { it.score }


        scoreList.forEachIndexed { index, sortScore ->
            run {
                if (index != 2) {
                    when (sortScore.scoreType) {
                        "rent" -> {
                            llScoreView.addView(tvRental)
                        }
                        "school" -> {
                            llScoreView.addView(tvSchool)
                        }
                        "growth" -> {
                            llScoreView.addView(tvGrowth)
                        }
                    }
                }
            }
        }

    }


    /**
    按照岳说的逻辑：

    现在不需要判断已售，所以基本上live 的使用场景就只有
    list_status.live === 1
    显示绿色的挂牌价
    else
    显示灰色的带中划线的挂牌价..
     */
    private fun setPriceTextStyle(tvPrice: TextView, item: House) {
        when (item.statusTypeV2()) {
            listingOnSearchStatusTypeV2.onSaleOrOnRent
            -> {
                tvPrice.setTextColor(Color.parseColor("#28A3B3"))
                tvPrice.paint.flags = 0
                tvPrice.paint.isAntiAlias = true
            }
            else -> {
                tvPrice.setTextColor(Color.parseColor("#BBBBBB"))
                tvPrice.paint.flags = (Paint.STRIKE_THRU_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG)//中划线

            }
        }
    }


}