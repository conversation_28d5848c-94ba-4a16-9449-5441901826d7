package com.housesigma.android.ui.map

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.CitySummaryMunicipality


class MapMuniciplitiesAdapter :
    BaseQuickAdapter<CitySummaryMunicipality, BaseViewHolder>(R.layout.item_municipalities_listing) {
    private var isSale: Boolean = false //是否是卖，有两种，一种租，一种买卖
    override fun convert(holder: BaseViewHolder, item: CitySummaryMunicipality) {
        holder.setText(R.id.tv_municipalities_name, "${item.municipalityName}")
        if (isSale){
            holder.setText(R.id.tv_type, " homes for sale")
        }else{
            holder.setText(R.id.tv_type, " homes for lease")
        }
    }
    fun setType(sale: Boolean) {
        isSale = sale
    }
}