package com.housesigma.android.ui.map

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.databinding.ItemMapScoreBinding
import com.housesigma.android.model.ScoreJsonV2

class MapScoreAdapter : BaseQuickAdapter<ScoreJsonV2?, BaseViewHolder>(R.layout.item_map_score) {

    private var binding: ItemMapScoreBinding? = null
    override fun convert(holder: BaseViewHolder, item: ScoreJsonV2?) {
        binding = ItemMapScoreBinding.bind(holder.itemView)
        item?.let {
//           规则 {year} {rank_name}: {rank_value} {score_name}: {score_value}
            binding?.tvScore?.text =
                item.year + " " + item.rank_name + ": " + item.rank_value + " " + item.score_name + ": " + item.score_value
        }
    }

}