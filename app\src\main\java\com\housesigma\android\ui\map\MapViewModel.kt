package com.housesigma.android.ui.map

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.housesigma.android.base.SingleLiveEvent
import com.housesigma.android.model.*
import com.housesigma.android.network.NetClient
import com.housesigma.android.network.launch
import com.housesigma.android.utils.HSLog
import com.housesigma.android.utils.log.Logger
import retrofit2.http.Field

class MapViewModel : ViewModel() {

    var communityDetail: MutableLiveData<CommunityDetail> = MutableLiveData()
    var mapList: MutableLiveData<MapList> = MutableLiveData()
    var mapSearchV3List: MutableLiveData<MapSearchV3List> = MutableLiveData()
    val mapSearchV3ListNoData = MutableLiveData<Boolean>()
    var mapNearByList: MutableLiveData<MapList> = MutableLiveData()
    var mapSearchFeature: MutableLiveData<List<MapMarkerInfo>> = MutableLiveData()
    var mapSearchSchool: MutableLiveData<MapSearchSchool> = MutableLiveData()
    var listingPreViewMany: MutableLiveData<ListingPreViewMany> = MutableLiveData()
    var listingPreViewManyWithMarker: MutableLiveData<ListingPreViewMany> = MutableLiveData()
    var mapFilterList: MutableLiveData<ArrayList<SaveMapFilter>> = MutableLiveData()
    var deleteMapFilterMsg: MutableLiveData<MsgRes> = MutableLiveData()
    var saveMapFilterMsg: SingleLiveEvent<MsgRes> = SingleLiveEvent()
    var mapFilter: MutableLiveData<MapFilter> = MutableLiveData()
    var citySummary: MutableLiveData<CitySummary> = MutableLiveData()
    var schoolDeatils: MutableLiveData<SchoolDetails> = MutableLiveData()
    val loadingLiveData = MutableLiveData<Boolean>()

    fun getSearchSchoolDetails(id:Int) {
        launch({
            NetClient.apiService.getSearchSchoolDetails(id)
        }, {
            schoolDeatils.postValue(it)
        })
    }

    fun getMapFilter() {
        launch({
            NetClient.apiService.getMapFilter()
        }, {
            mapFilter.postValue(it)
        })
    }


    fun updateProfileCoordinate(lat:String,lon:String) {
        launch({
            NetClient.apiService.updateProfileCoordinate(DataCoordinate(Coordinate(lat,lon)))
        },{})
    }



    fun deleteMapFilterById(id:String) {
        launch({
            NetClient.apiService.deleteMapFilterById(id)
        }, {
            deleteMapFilterMsg.postValue(it)
        })
    }

    fun getMapFilterList(mapType: String) {
        launch({
            NetClient.apiService.getMapFilterList(mapType)
        }, {
            mapFilterList.postValue(it)
        })
    }

    fun getListingPreviewMany(id_listing: List<String>,withMarker:Boolean) {
        launch({
            NetClient.apiService.getListingPreviewMany(id_listing)
        }, {
            if (withMarker) {
                listingPreViewManyWithMarker.postValue(it)
            } else {
                listingPreViewMany.postValue(it)
            }
        })
    }


    fun getMapSearchv2Feature(
                              zoom: Double,
                              list_type: List<String>,
                              basement: List<String>,
                              bedroom_range: List<String>,
                              bathroom_min: String,
                              garage_min: String,
                              open_house_date: String,
                              description: String,
                              max_maintenance_fee: String,
                              price: List<String>,
                              front_feet: List<String>,
                              square_footage: List<String>,
                              rental_yield_range: List<String>?=null,
                              school_score_range: List<String>?=null,) {
        launch({
            NetClient.cancelCallWithTag("api/search/mapsearchv2/feature")
            NetClient.apiService.getMapSearchv2Feature( basement = basement,
                bathroom_min = bathroom_min,
                bedroom_range = bedroom_range,
                description = description,
                front_feet = front_feet,
                garage_min = garage_min,
                zoom = zoom+1,
                list_type = list_type,
                open_house_date = open_house_date,
                price = price,
                max_maintenance_fee = max_maintenance_fee,
                square_footage = square_footage,
                rental_yield_min = rental_yield_range?.getOrNull(0),
                rental_yield_max = rental_yield_range?.getOrNull(1),
                school_score_min = school_score_range?.getOrNull(0),
                school_score_max = school_score_range?.getOrNull(1))
        }, {
            mapSearchFeature.postValue(it)
        })
    }


    fun getSearchMapSearchSchool(
        catholic: Int,
        elementary: Int,
        lat1: Double,
        lat2: Double,
        lon1: Double,
        lon2: Double,
        match_score: Int,
        public: Int,
        secondary: Int,
    ) {
        launch({
            NetClient.apiService.getSearchMapSearchSchool(
                catholic = catholic,
                elementary = elementary,
                lat1 = lat1,
                lat2 = lat2,
                lon1 = lon1,
                lon2 = lon2,
                match_score = match_score,
                public = public,
                secondary = secondary,
            )
        }, {
            mapSearchSchool.postValue(it)
        })
    }

    fun getMapListing2(
        houseType: ArrayList<String> = arrayListOf("all"),
        lat1: Double,
        lat2: Double,
        lon1: Double,
        lon2: Double,
        zoom: Double,
        listing_days: String,
        de_list_days: String,
        list_type: List<String>,
        basement: List<String>,
        bedroom_range: List<String>,
        bathroom_min: String,
        garage_min: String,
        open_house_date: String,
        description: String,
        max_maintenance_fee: String,
        price: List<String>,
        front_feet: List<String>,
        square_footage: List<String>,
        listing_type: List<String>,
        lot_size : List<String>?=null,
        building_age: List<String>?=null,
        rental_yield_range: List<String>?=null,
        school_score_range: List<String>?=null,
    ) {
        launch({
            NetClient.cancelCallWithTag("api/search/mapsearchv3/listing")
            NetClient.apiService.getMapListing22(
                basement = basement,
                bathroom_min = bathroom_min,
                bedroom_range = bedroom_range,
                description = description,
                front_feet = front_feet,
                garage_min = garage_min,
                house_type = houseType,
                lat1 = lat1,
                lat2 = lat2,
                lon1 = lon1,
                lon2 = lon2,
                zoom = zoom+1,
                listing_days = listing_days,
                de_list_days = de_list_days,
                sold_days = de_list_days,
                list_type = list_type,
                open_house_date = open_house_date,
                price = price,
                max_maintenance_fee = max_maintenance_fee,
                square_footage = square_footage,
                listing_type = listing_type,
                lot_size = lot_size,
                building_age = building_age,
                rental_yield_min = rental_yield_range?.getOrNull(0),
                rental_yield_max = rental_yield_range?.getOrNull(1),
                school_score_min = school_score_range?.getOrNull(0),
                school_score_max = school_score_range?.getOrNull(1)
            )
        }, {
            it.requestZoom = zoom
            mapList.postValue(it)

            val map = HashMap<String, Any>()
            map["description"] = description
            map["maintenance_fee"] = max_maintenance_fee
            HSLog.userInput(eventName = "user_input_map_filters",map)
        })
    }

    fun getCitySummary(id_municipality:String) {
        launch({
            NetClient.apiService.getCitySummary(id_municipality)
        }, {
            citySummary.postValue(it)
        })
    }


    fun saveMapFilter(saveMapFilter: SaveMapFilter) {
        launch({
            NetClient.apiService.saveMapFilter(saveMapFilter)
        }, {
            saveMapFilterMsg.postValue(it)
        })
    }

    fun getMapSearchV3List(
        houseType: ArrayList<String> = arrayListOf("all"),
        lat1: Double,
        lat2: Double,
        lon1: Double,
        lon2: Double,
        zoom: Double,
        listing_days: String,
        de_list_days: String,
        list_type: List<String>,
        basement: List<String>,
        bedroom_range: List<String>,
        bathroom_min: String,
        garage_min: String,
        open_house_date: String,
        description: String,
        max_maintenance_fee: String,
        price: List<String>,
        front_feet: List<String>,
        square_footage: List<String>,
        page:Int,
        sortType:String,
        listing_type: List<String>,
        lot_size : List<String>?=null,
        building_age: List<String>?=null,
        rental_yield_range: List<String>?=null,
        school_score_range: List<String>?=null,
    ) {
        launch({
            NetClient.apiService.getMapsearchv3List(
                basement = basement,
                bathroom_min = bathroom_min,
                bedroom_range = bedroom_range,
                description = description,
                front_feet = front_feet,
                garage_min = garage_min,
                house_type = houseType,
                lat1 = lat1,
                lat2 = lat2,
                lon1 = lon1,
                lon2 = lon2,
                zoom = zoom+1,
                listing_days = listing_days,
                de_list_days = de_list_days,
                sold_days = de_list_days,
                list_type = list_type,
                open_house_date = open_house_date,
                price = price,
                max_maintenance_fee = max_maintenance_fee,
                square_footage = square_footage,
                page = page,
                sort_type = sortType,
                listing_type = listing_type,
                lot_size = lot_size,
                building_age = building_age,
                rental_yield_min = rental_yield_range?.getOrNull(0),
                rental_yield_max = rental_yield_range?.getOrNull(1),
                school_score_min = school_score_range?.getOrNull(0),
                school_score_max = school_score_range?.getOrNull(1)
            )
        }, {
            mapSearchV3List.postValue(it)
        },{
            loadingLiveData.postValue(true)
        },{
            Logger.e("mapSearchV3ListNoData...")
            mapSearchV3ListNoData.postValue(true)
        })
    }




    fun getSearchV2NearBySold(
        houseType: ArrayList<String>,
        lat1: Double,
        lat2: Double,
        lon1: Double,
        lon2: Double,
        zoom: Double,
        listing_days: String,
        de_list_days: String,
        list_type: List<String>,
        basement: List<String>,
        bedroom_range: List<String>,
        bathroom_min: String,
        garage_min: String,
        open_house_date: String,
        description: String,
        max_maintenance_fee: String,
        price: List<String>,
        front_feet: List<String>,
        square_footage: List<String>,
        rental_yield_range: List<String>?=null,
        school_score_range: List<String>?=null,
    ) {
        launch({
            NetClient.cancelCallWithTag("api/search/mapsearchv3/nearbysold")
            NetClient.apiService.getSearchV2NearBySold(
                basement = basement,
                bathroom_min = bathroom_min,
                bedroom_range = bedroom_range,
                description = description,
                front_feet = front_feet,
                garage_min = garage_min,
                house_type = houseType,
                lat1 = lat1,
                lat2 = lat2,
                lon1 = lon1,
                lon2 = lon2,
                zoom = zoom+1,
                listing_days = listing_days,
                de_list_days = de_list_days,
                list_type = list_type,
                open_house_date = open_house_date,
                price = price,
                max_maintenance_fee = max_maintenance_fee,
                square_footage = square_footage,
                rental_yield_min = rental_yield_range?.getOrNull(0),
                rental_yield_max = rental_yield_range?.getOrNull(1),
                school_score_min = school_score_range?.getOrNull(0),
                school_score_max = school_score_range?.getOrNull(1)
            )
        }, {
            mapNearByList.postValue(it)
        })
    }

    fun getCommunityDetail(idCommunity: String) {
        launch({
            NetClient.apiService.getCommunityDetail(idCommunity)
        }, {
            communityDetail.postValue(it)
        })
    }


}