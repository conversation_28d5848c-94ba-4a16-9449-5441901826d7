package com.housesigma.android.ui.map

import android.text.TextUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.Precon
import com.housesigma.android.utils.ScreenUtils
import jp.wasabeef.glide.transformations.RoundedCornersTransformation

class PreconMapListingAdapter :
    BaseQuickAdapter<Precon, BaseViewHolder>(R.layout.item_precon_map_listing) {

    init {
        addChildClickViewIds(
            R.id.rl,
        )
    }


    override fun convert(holder: BaseViewHolder, item: Precon) {
        val corner = ScreenUtils.dpToPx(8f)
        val multi = MultiTransformation(
            CenterCrop(),
            RoundedCornersTransformation(
                corner.toInt(),
                0,
                RoundedCornersTransformation.CornerType.ALL
            )
        )

        holder.setText(R.id.tv_project_name, item.project_name?:"")
        if (TextUtils.isEmpty(item.price)) {
            holder.setGone(R.id.ll_price, true)
        } else {
            holder.setVisible(R.id.ll_price, true)
            holder.setText(R.id.tv_price, "$${item.price}")
        }
        holder.setText(R.id.tv_address, item.addr?:"")
        holder.setText(R.id.tv_house_type_name, item.property_type_text?:"")
        holder.setText(R.id.tv_project_status_text, item.project_status_text?:"")

        if (TextUtils.isEmpty(item.bedroom_text)) {
            holder.setGone(R.id.tv_bedroom_string, true)
        } else {
            holder.setVisible(R.id.tv_bedroom_string, true)
            holder.setText(R.id.tv_bedroom_string, item.bedroom_text?:"")
        }

        if (TextUtils.isEmpty(item.sqft_text)) {
            holder.setGone(R.id.tv_sqft, true)
        } else {
            holder.setVisible(R.id.tv_sqft, true)
            holder.setText(R.id.tv_sqft, item.sqft_text?:"")
        }

        Glide.with(context)
            .load(item.photo_url)
            .transform(multi)
            .error(R.drawable.shape_pic_place_holder)
            .placeholder(R.drawable.shape_pic_place_holder)
            .into(holder.getView(R.id.iv_house_pic))
    }


}