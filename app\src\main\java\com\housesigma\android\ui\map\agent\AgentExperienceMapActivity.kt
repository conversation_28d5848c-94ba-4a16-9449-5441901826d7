package com.housesigma.android.ui.map.agent

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.databinding.ActivityAgentExperienceMapBinding
import com.housesigma.android.model.House
import com.housesigma.android.model.ListingPreViewMany
import com.housesigma.android.model.Location
import com.housesigma.android.model.MapList
import com.housesigma.android.model.MapMarkerInfo
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.map.MapListingAdapter
import com.housesigma.android.ui.tos.TosDialog
import com.housesigma.android.ui.tos.VowTosDialog
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.Constants
import com.housesigma.android.utils.ConstantsHelper
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MapUtils
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.HSDecoration
import org.maplibre.android.MapLibre
import org.maplibre.android.annotations.IconFactory
import org.maplibre.android.annotations.Marker
import org.maplibre.android.annotations.MarkerOptions
import org.maplibre.android.camera.CameraPosition
import org.maplibre.android.camera.CameraUpdateFactory
import org.maplibre.android.geometry.LatLng
import org.maplibre.android.geometry.LatLngBounds
import org.maplibre.android.maps.MapView
import org.maplibre.android.maps.MapLibreMap
import org.maplibre.android.maps.Style
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.math.BigDecimal
import java.util.concurrent.ConcurrentHashMap


class AgentExperienceMapActivity : AppCompatActivity(), LoginFragment.LoginCallback {


    //====================Map相关的参数start=========================

    private var lastListingInfo: MapMarkerInfo? = null
    private var lastClickMarker: Marker? = null


    private var lastMarkerInfoIds: List<String> = ArrayList()
    private lateinit var mapView: MapView
    private lateinit var mapLibreMap: MapLibreMap
    private var isMapViewActive: Boolean = false // 用于标记地图是否激活状态
    private var isMapStyleLoaded: Boolean = false // 用于标记地图样式是否加载成功


    // 各个api 同等类型的marker单独储存，去重逻辑单独处理 优势 更清晰好维护
    private var listingMarkerInfoMap = ConcurrentHashMap<Long, MapMarkerInfo>() //listing2
//    private var viewInMapInfoMap = HashMap<Long, String>()


    //====================Map相关的参数end===========================

    private lateinit var binding: ActivityAgentExperienceMapBinding
    private lateinit var mapHelper: AgentExperienceMapHelper
    private lateinit var mapViewModel: AgentExperienceMapViewModel

    private var mStatus = ArrayList<AgentExperienceMapStatus>()



    // 中间三个按钮，其中第一个sale是默认不可选择的，另外两个是可以选择的
    // ? 带参数进来的，又可以选择第一个按钮
    private var selectBought: Boolean = false
    private var selectListed: Boolean = false
    private var selectToured: Boolean = false

    private lateinit var bottomSheetBehavior : BottomSheetBehavior<LinearLayout>



    private var mAgentId: String = ""
    private var mAgentSlug: String = ""
    private var mFirstLoad = true

    private var loginDialog: LoginFragment? = null

    private val mListingPreviewAdapter by lazy { initListingPreviewMany() }

    /**
     * prepare params
     */
    private fun getParamsFromIntent() {
        mStatus = (intent.getSerializableExtra("map_type")?:ArrayList<AgentExperienceMapStatus>()) as ArrayList<AgentExperienceMapStatus>

        mAgentId = intent.getStringExtra("agent_id") ?: ""
        mAgentSlug = intent.getStringExtra("agent_slug") ?: ""

        Logger.e("mapType: $mStatus")
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        getParamsFromIntent()
        MapLibre.getInstance(this)
        super.onCreate(savedInstanceState)
        mapViewModel = ViewModelProvider(this).get(AgentExperienceMapViewModel::class.java)
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        binding = ActivityAgentExperienceMapBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initViews(savedInstanceState)
        initLoginDialog()
    }


    private fun initLoginDialog() {
        if (loginDialog != null) return
        loginDialog = LoginFragment()
    }

    private fun initData() {
        mapViewModel.listingPreViewMany.observe(this) {
            val listing = it.houseList.getOrNull(0)
            listing?.let {
                updateCameraPosition(Location(listing.map.lat, listing.map.lon))
            }
            handleListingPreview(it)
        }

        mapViewModel.loadingLiveData.observe(this) {
            hideProgress()
            binding.llSchoolDetail.visibility = View.GONE
            hiddenListingPreview()
        }

        mapViewModel.mapListings.observe(this) { that ->
            CoroutineScope(Dispatchers.Main).launch {
                handleMapListing(that)
            }
        }

        getAgentListing()
    }

    private fun getAgentListing(){
        mapViewModel.getAgentListing(mStatus,mAgentId,mAgentSlug)
    }

    private fun handleListingPreview(it: ListingPreViewMany) {
        showListingPreview()
        binding.llSchoolDetail.visibility = View.GONE
        mListingPreviewAdapter.setOnItemChildClickListener { adapter, view, position ->
            handleListViewClick(adapter, position, view)
        }

        mListingPreviewAdapter.data = it.houseList.toMutableList()
        mListingPreviewAdapter.notifyDataSetChanged()

        (binding.rvListings.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(0, 0)
        (binding.rvListings.layoutManager as LinearLayoutManager).setStackFromEnd(true)

        if (it.houseList.size <= 1) {
            binding.tvListingsSize.text = "${it.houseList.size} Listing in total"
        } else {
            binding.tvListingsSize.text = "${it.houseList.size} Listings in total"
        }
    }

    private fun handleListViewClick(
        adapter: BaseQuickAdapter<*, *>,
        position: Int,
        view: View
    ) {
        val item = adapter.getItem(position) as House
        when (view.id) {
            R.id.tv_contact_agent -> {
                if (item.isAgreementRequired()||item.isNeedReLogin() || item.isLoginRequired() || item.isNotAvailable() || item.isPasswordExpired()) {
                    return
                }
                this@AgentExperienceMapActivity?.let {
                    WebViewHelper.jumpHouseContact(
                        it,
                        item.id_listing
                    )
                }
            }


            R.id.ll -> {
                if (item.isAgreementRequired()||item.isNeedReLogin() || item.isLoginRequired() || item.isNotAvailable() || item.isPasswordExpired()) {
                    return
                }

                this@AgentExperienceMapActivity?.let {
                    GALog.log("preview_click", "experience_map_mode")
                    WebViewHelper.jumpHouseDetail(
                        it,
                        item.id_listing,
                        item.seo_suffix,
                        eventSource = "agent_experience"
                    )
                }
            }

            R.id.tv_agreement_required -> {
                this@AgentExperienceMapActivity?.let {
                    TosDialog(it, it, it, item.tos_source,
                        object : TosDialog.TosCallback {
                            override fun onSuccess() {
                                getListingPreviewMany()
                                getAgentListing()
                            }
                        }).show()
                }

            }

            R.id.tv_not_available -> {
                this@AgentExperienceMapActivity?.let {
                    VowTosDialog(item.id_listing,it,it,it).show()
                }
            }

            R.id.tv_login_required -> {
                val tvLoginRequiredStr = (view as TextView).text.toString()
                if (Constants.TEXT_VIEW_TRREB_TIMEOUT.equals(tvLoginRequiredStr)){
                    showLoginDialog(reLogin = LoginFragment.RE_LOGIN_TRREB_TIMEOUT)
                } else if ("Password Expired".equals(tvLoginRequiredStr)) {
                    showLoginDialog(reLogin = LoginFragment.RE_LOGIN_VALIDATE)
                }else{
                    showLoginDialog()
                }
            }

        }
    }


    private fun initListingPreviewMany(): MapListingAdapter {
        val divider = HSDecoration(this, DividerItemDecoration.VERTICAL, false)
        binding.rvListings.addItemDecoration(divider)
        binding.rvListings.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        val adapter = MapListingAdapter()
        binding.rvListings.adapter = adapter
        return adapter
    }


    private suspend fun handleMapListing(that: MapList) {
        val notSameMapList = ArrayList<Marker>()
        // api返回的marker，判断现有marker列表中有无，
        // 如果marker列表中没有，需要添加上
        // 如果marker列表中有，则不需要添加
        // 只需要保留比较本地和网络重叠的部分，去掉不同的。
        val cameraZoom = mapHelper.getCameraPositionZoom(mapLibreMap)
        for (entry in listingMarkerInfoMap) {
            var find = false
            for (mapMarkerInfo in that.list) {
                mapMarkerInfo.setAgentPageZoom(cameraZoom)
                if (entry.value.equals(mapMarkerInfo)) {
                    find = true
                    break
                }
            }
            if (!find) {
                entry.value.localMarker?.let {
                    notSameMapList.add(it)
                }
            }
        }

        notSameMapList.forEach {
            listingMarkerInfoMap.remove(it.id)
        }


        mapLibreMap.removeAnnotations(notSameMapList)


        val start = System.currentTimeMillis()
        that.list.forEach {
            var find = false
            for (mutableEntry in listingMarkerInfoMap) {
                if (mutableEntry.value.equals(it)) {
                    find = true
//                    Logger.e( "find same element in hashMapMapInfo")
                    break
                }
            }

            if (!find) {
                var marker: Marker? = null
                if ("sold".equals(it.marker)) {
                    marker = mapHelper.addSoldMarker(it)
                } else if ("de-listed".equals(it.marker)) {
                    marker = mapHelper.addDeListedMarker(it)
                } else if ("active".equals(it.marker)) {
                    marker = mapHelper.addSaleMarker(it)
                }

                marker?.let { markerThat ->
                    it.localMarker = markerThat
                    it.setAgentPageZoom(cameraZoom)
                    listingMarkerInfoMap.put(
                        markerThat.id, it
                    )
                }
            }
        }

        val end = System.currentTimeMillis()
        Logger.d("function cast time is " + (end - start));
        
        if (mFirstLoad) {
            mFirstLoad = false
        } else {
            return
        }

        // 重新计算latLngBounds
        val latLngBounds = LatLngBounds.Builder()
        // 遍历listingMarkerInfoMap，添加到latLngBounds中，include进来，MapLibreMap.animateCamera自动移动
        for (entry in listingMarkerInfoMap) {
            latLngBounds.include(LatLng(entry.value.location.lat, entry.value.location.lon))
        }

        if (listingMarkerInfoMap.size >= 2) {
            // 如果有多个marker，就移动到这些marker的位置，zoom自动调整，但最大不超过11
            val cameraUpdate = CameraUpdateFactory.newLatLngBounds(latLngBounds.build(), 100)
            mapLibreMap.moveCamera(cameraUpdate)
            val targetCameraPosition = mapLibreMap.cameraPosition
            if (targetCameraPosition.zoom > 11) {
                // 创建一个新的相机位置，将缩放级别设置为 11
                val adjustedCameraPosition = CameraPosition.Builder()
                    .target(targetCameraPosition.target)
                    .zoom(11.0)
                    .build()
                mapLibreMap.moveCamera(CameraUpdateFactory.newCameraPosition(adjustedCameraPosition))
            } else {
                mapLibreMap.moveCamera(cameraUpdate)
            }
        } else if (listingMarkerInfoMap.size == 1){
            // 如果只有一个marker，就直接移动到这个marker的位置，zoom固定为11
            listingMarkerInfoMap[listingMarkerInfoMap.keys.first()]?.let {
                val cameraUpdate = CameraUpdateFactory.newLatLngZoom(
                    LatLng(it.location.lat, it.location.lon),
                    11.0
                )
                mapLibreMap.moveCamera(cameraUpdate)
            }
        }


        if (listingMarkerInfoMap.size == 0) {
            return
        }

        //#DEV-8774 Apply the following new logic to the map on the agent profile page:
        // When zoom level >=7, keep existing logic
        // When zoom level <7:
        // Center the map on the pin with the median lat/long of all displayed pins
        // Set zoom to 7
        if (mapLibreMap.cameraPosition.zoom >=7) {
            return
        }
        val latLngList = ArrayList<LatLng>()

        for (entry in listingMarkerInfoMap) {
            latLngList.add(LatLng(entry.value.location.lat, entry.value.location.lon))
        }
        MapUtils.getMedianLatLng(latLngList)?.let {
            val cameraUpdateZoom = 7.0
            val cameraUpdate = CameraUpdateFactory.newLatLngZoom(
                LatLng(LatLng(it.latitude, it.longitude)),
                cameraUpdateZoom
            )
            mapLibreMap.moveCamera(cameraUpdate)
        }
    }



    @SuppressLint("WrongConstant")
    private fun initViews(savedInstanceState: Bundle?) {
        binding.ivClose.setOnClickListener { finish() }
        bottomSheetBehavior = BottomSheetBehavior.from(binding.llListings)
        binding.tvForSale.text = "Bought"
        binding.tvSold.text = "Listed"
        binding.tvDelisted.text = "Toured"


        refreshListType()



        binding.tvForSale.setOnClickListener {
            GALog.log("map_filters_click")

            if (!selectBought) {
                binding.tvForSale.setBackgroundResource(R.drawable.shape_map_left_selected)
                binding.tvForSale.setTextColor(resources.getColor(R.color.color_white))
                if (!mStatus.contains(AgentExperienceMapStatus.Bought)) {
                    mStatus.add(AgentExperienceMapStatus.Bought)
                }
            } else {
                if (mStatus.size == 1) {
                    return@setOnClickListener
                }

                binding.tvForSale.setBackgroundResource(R.drawable.shape_map_left)
                binding.tvForSale.setTextColor(resources.getColor(R.color.color_gray_dark))

                if (mStatus.contains(AgentExperienceMapStatus.Bought)) {
                    mStatus.remove(AgentExperienceMapStatus.Bought)
                }
            }
            selectBought = !selectBought
            reloadMapData()
            getAgentListing()
        }

        binding.tvSold.setOnClickListener {
            GALog.log("map_filters_click")

            if (!selectListed) {
                binding.tvSold.setBackgroundResource(R.drawable.shape_map_center_selected)
                binding.tvSold.setTextColor(resources.getColor(R.color.color_white))

                if (!mStatus.contains(AgentExperienceMapStatus.Listed)) {
                    mStatus.add(AgentExperienceMapStatus.Listed)
                }
            } else {
                if (mStatus.size == 1) {
                    return@setOnClickListener
                }
                binding.tvSold.setBackgroundResource(R.drawable.shape_map_center)
                binding.tvSold.setTextColor(resources.getColor(R.color.color_gray_dark))

                if (mStatus.contains(AgentExperienceMapStatus.Listed)) {
                    mStatus.remove(AgentExperienceMapStatus.Listed)
                }
            }
            selectListed = !selectListed
            reloadMapData()
            getAgentListing()
        }


        binding.tvDelisted.setOnClickListener {
            GALog.log("map_filters_click")

            if (!selectToured) {
                binding.tvDelisted.setBackgroundResource(R.drawable.shape_map_right_selected)
                binding.tvDelisted.setTextColor(resources.getColor(R.color.color_white))

                if (!mStatus.contains(AgentExperienceMapStatus.Toured)) {
                    mStatus.add(AgentExperienceMapStatus.Toured)
                }
            } else {
                if (mStatus.size == 1) {
                    return@setOnClickListener
                }
                binding.tvDelisted.setBackgroundResource(R.drawable.shape_map_right)
                binding.tvDelisted.setTextColor(resources.getColor(R.color.color_gray_dark))

                if (mStatus.contains(AgentExperienceMapStatus.Toured)) {
                    mStatus.remove(AgentExperienceMapStatus.Toured)
                }
            }

            selectToured = !selectToured
            reloadMapData()
            getAgentListing()
        }


        binding.ivCloseMapPreviewList.setOnClickListener {
            hiddenListingPreview()
        }

        mapView = binding.mapView
        mapView.onCreate(savedInstanceState)
        mapView.getMapAsync { map ->
            mapLibreMap = map
            mapLibreMap.uiSettings.isLogoEnabled = false
            mapLibreMap.uiSettings.isAttributionEnabled = false
            mapLibreMap.uiSettings.isRotateGesturesEnabled = false
            mapLibreMap.setMaxPitchPreference(0.0)//关闭仰角
            mapLibreMap.setMinPitchPreference(0.0)
            mapHelper = AgentExperienceMapHelper(AgentExperienceAgentExperienceMapActivity@ this, layoutInflater, mapLibreMap)
            setUpMapboxMap(ConstantsHelper.getMapVector())
            initData()
        }

    }

    private fun refreshListType() {
        binding.tvForSale.setBackgroundResource(R.drawable.shape_map_left)
        binding.tvForSale.setTextColor(resources.getColor(R.color.color_gray_dark))
        binding.tvSold.setBackgroundResource(R.drawable.shape_map_center)
        binding.tvSold.setTextColor(resources.getColor(R.color.color_gray_dark))
        binding.tvDelisted.setBackgroundResource(R.drawable.shape_map_right)
        binding.tvDelisted.setTextColor(resources.getColor(R.color.color_gray_dark))
        if (mStatus?.contains(AgentExperienceMapStatus.Bought) == true) {
            selectBought = true
        } else {
            selectBought = false
        }
        if (mStatus?.contains(AgentExperienceMapStatus.Bought) == true) {
            if (selectBought) {
                binding.tvForSale.setBackgroundResource(R.drawable.shape_map_left_selected)
                binding.tvForSale.setTextColor(resources.getColor(R.color.color_white))

                if (!mStatus.contains(AgentExperienceMapStatus.Bought)) {
                    mStatus.add(AgentExperienceMapStatus.Bought)
                }
            }
        }
        if (mStatus?.contains(AgentExperienceMapStatus.Listed) == true) {
            selectListed = true
        }else{
            selectListed = false
        }
        if (selectListed) {
            binding.tvSold.setBackgroundResource(R.drawable.shape_map_center_selected)
            binding.tvSold.setTextColor(resources.getColor(R.color.color_white))

            if (!mStatus.contains(AgentExperienceMapStatus.Listed)) {
                mStatus.add(AgentExperienceMapStatus.Listed)
            }


        }

        if (mStatus?.contains(AgentExperienceMapStatus.Toured) == true) {
            selectToured = true
        }else{
            selectToured = false
        }

        //        isSelectDelisted = MMKVUtils.getBoolean("map_select_de_listed", false)
        if (selectToured) {
            binding.tvDelisted.setBackgroundResource(R.drawable.shape_map_right_selected)
            binding.tvDelisted.setTextColor(resources.getColor(R.color.color_white))

            if (!mStatus.contains(AgentExperienceMapStatus.Toured)) {
                mStatus.add(AgentExperienceMapStatus.Toured)
            }

        }
    }


    private fun showLoginDialog(reLogin: String = "") {
        GALog.log("login_button_click")
        initLoginDialog()
        if (loginDialog?.isAdded == true) return
        val bundle = Bundle()
        bundle.putString("reLogin", reLogin)
        loginDialog?.arguments = bundle
        loginDialog?.show(supportFragmentManager, "")
    }



    private fun setUpMapboxMap(styleUrl: String) {
        mapLibreMap.addOnMapClickListener(object : MapLibreMap.OnMapClickListener {
            override fun onMapClick(point: LatLng): Boolean {
                return false
            }
        })


        mapLibreMap.setMaxZoomPreference(20.0)
        mapLibreMap.setMinZoomPreference(4.0)

        mapLibreMap.setOnMarkerClickListener(object : MapLibreMap.OnMarkerClickListener {
            override fun onMarkerClick(marker: Marker): Boolean {
                updateCameraPosition(Location(marker.position.latitude, marker.position.longitude))
                mapView.postDelayed({
                    CoroutineScope(Dispatchers.Main).launch {
                        handleMarkerOnclick(marker)
                    }
                }, 100)
                return true
            }
        })
        isMapStyleLoaded = false
        mapLibreMap.setStyle(styleUrl, object : Style.OnStyleLoaded {
            override fun onStyleLoaded(style: Style) {
                isMapStyleLoaded = true

                addOnCameraMoveListener()

                val markerCenterLat = intent.getDoubleExtra("marker_center_lat", 0.0)
                val markerCenterLon = intent.getDoubleExtra("marker_center_lon", 0.0)
                if (markerCenterLat != 0.0 || markerCenterLon != 0.0) {
                    val iconFactory = IconFactory.getInstance(this@AgentExperienceMapActivity)
                    val options = MarkerOptions()
                        .position(LatLng(markerCenterLat, markerCenterLon))
                        .icon(iconFactory.fromResource(R.drawable.ic_map_location))

                    mapLibreMap.addMarker(options)
                }

            }
        })
    }

    private suspend fun handleMarkerOnclick(marker: Marker): Boolean {
        GALog.log("marker_click")
        // 有很多种marker ，需要挨个遍历改样式
        val listingMarkerInfo = listingMarkerInfoMap.get(marker.id)


        if (lastClickMarker != null) {
            lastListingInfo?.let {
                if ("sold".equals(it.marker)) {
                    mapHelper.updateSoldMarker(
                        lastListingInfo!!,
                        lastClickMarker!!,
                        true
                    )
                }else if ("de-listed".equals(it.marker)) {
                    mapHelper.updateDeListedMarker(
                        lastListingInfo!!,
                        lastClickMarker!!,
                        true
                    )
                } else if ("active".equals(it.marker)) {
                    mapHelper.updateSaleMarker(
                        lastListingInfo!!,
                        lastClickMarker!!,
                        true
                    )
                } else {
                    // nothing
                }
            }
        }
        lastClickMarker = marker
        lastListingInfo = listingMarkerInfo


        listingMarkerInfo?.let { markerInfo ->
            val cameraZoom = mapHelper.getCameraPositionZoom(mapLibreMap)
            markerInfo.count?.let {
                // 对于group marker，数字大于等于25的时候，点击marker，应该放大地图。小于25的时候显示房源列表
                if (markerInfo.count <= 25 || cameraZoom >= 17) {
                    Logger.e("markerInfo: " + markerInfo)

                    if ("sold".equals(markerInfo.marker)) {
                        mapHelper.updateSoldMarker(markerInfo, marker)
                    } else if ("de-listed".equals(markerInfo.marker)) {
                        mapHelper.updateDeListedMarker(markerInfo, marker)
                    } else if ("active".equals(markerInfo.marker)) {
                        mapHelper.updateSaleMarker(markerInfo, marker)
                    }

                    lastMarkerInfoIds = markerInfo.ids
                    getListingPreviewMany()
                    return true
                } else {

                    var addzoom = 1.0
                    if (cameraZoom < 10) {
                        addzoom = 3.0
                    } else if (cameraZoom <= 14) {
                        addzoom = 2.0
                    }

                    val finalMoveZoom =
                        BigDecimal(cameraZoom + addzoom).setScale(1, BigDecimal.ROUND_HALF_UP)
                            .toDouble()
                    mapLibreMap.animateCamera(
                        CameraUpdateFactory.newCameraPosition(
                            CameraPosition.Builder()
                                .target(
                                    LatLng(markerInfo.location.lat, markerInfo.location.lon)
                                )
                                .zoom(finalMoveZoom)
                                .build()
                        )
                    )
                    return false
                }
            }
        }
        return true
    }


    private fun getListingPreviewMany(withMarker: Boolean = false) {
        lastMarkerInfoIds?.let { mapViewModel.getListingPreviewMany(it, withMarker) }
    }

    /**
     * 小圆点在zoom 11前后切换
     * 点击小圆点后 zoom = 12
     */
    private fun updateCameraPosition(location: Location, targetZoom: Double = 12.0) {
        val currentZoom = mapLibreMap.cameraPosition.zoom
        if (currentZoom > 11) {
            return
        }
        mapLibreMap.cameraPosition = CameraPosition.Builder()
            .target(LatLng(location.lat, location.lon))
            .zoom(targetZoom)
            .build()
    }


    /**
     * 监听camera move，如在60ms内有变化，就可以获取屏幕可见经纬度范围，请求api接口
     */
    private fun addOnCameraMoveListener() {
//        https://stackoverflow.com/questions/38727517/oncamerachangelistener-is-deprecated
//        用addOnCameraIdleListener监听代替OnCameraChangeListener
        mapLibreMap.addOnCameraIdleListener {
            Logger.e("move camera idle.........")
            // 可见区域
            reloadMapData()
        }
    }


    private fun reloadMapData() {
        requestDataWithinVisibleMapArea()
        hideProgress()
    }

    private fun showProgress() {
    }


    private fun hideProgress() {
    }

    private fun requestDataWithinVisibleMapArea() {
//        Logger.e( "debounceReloadMapData2: "+System.currentTimeMillis())
        Logger.e( "list type " + mStatus)
        showProgress()
        val cameraZoom = mapHelper.getCameraPositionZoom(mapLibreMap)
        var agentMapZoom = 0
        if (cameraZoom>= 11){
            agentMapZoom = 1
        }

        if ((listingMarkerInfoMap.size>0)) {
            // listingMarkerInfoMap 随便拿一个value
            val markerInfo = listingMarkerInfoMap.values.iterator().next()
            if (markerInfo.agentMapZoom != agentMapZoom) {

                listingMarkerInfoMap.forEach {
                    it.value.setAgentPageZoom(cameraZoom)
                    GlobalScope.launch(Dispatchers.Main) {
                        if ("sold".equals(it.value.marker)) {
                            mapHelper.updateSoldMarker(it.value,it.value.localMarker,true)
                        } else if ("de-listed".equals(it.value.marker)) {
                            mapHelper.updateDeListedMarker(it.value,it.value.localMarker,true)
                        } else if ("active".equals(it.value.marker)) {
                            mapHelper.updateSaleMarker(it.value,it.value.localMarker,true)
                        }
                    }
                }

            }
        }

    }


    override fun onStart() {
        super.onStart()
        mapView.onStart()
    }

    override fun onResume() {
        super.onResume()
        mapView.onResume()
        GALog.page("agent_experience_map")
        isMapViewActive = true
    }

    override fun onPause() {
        super.onPause()
        isMapViewActive = false
        mapView.onPause()
    }

    override fun onStop() {
        super.onStop()
        mapView.onStop()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mapView.onSaveInstanceState(outState)
    }

    override fun onLowMemory() {
        super.onLowMemory()
        mapView.onLowMemory()
    }

    override fun onDestroy() {
        super.onDestroy()
        mapView.onDestroy()
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
        getListingPreviewMany()
        getAgentListing()
    }
    private fun showListingPreview(){
        // 先GONE，再展开，解决preview list个数为1的时候，bottomSheetBehavior悬浮在手机的最中间，底部留白的问题
        binding.llListings.visibility = View.GONE
        binding.llListings.visibility = View.VISIBLE
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_COLLAPSED
    }

    private fun hiddenListingPreview(){
        binding.llListings.visibility = View.GONE
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
    }

}