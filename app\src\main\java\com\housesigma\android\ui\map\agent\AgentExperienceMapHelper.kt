package com.housesigma.android.ui.map.agent

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.animation.BounceInterpolator
import android.widget.RelativeLayout
import android.widget.TextView
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.housesigma.android.HSApp
import com.housesigma.android.R
import com.housesigma.android.databinding.*
import com.housesigma.android.model.House
import com.housesigma.android.model.MapMarkerInfo
import com.housesigma.android.model.SchoolInfo
import com.housesigma.android.ui.notinterested.NotInterestedHelper
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.MapUtils
import com.housesigma.android.utils.ScreenUtils
import org.maplibre.android.annotations.Icon
import org.maplibre.android.annotations.IconFactory
import org.maplibre.android.annotations.Marker
import org.maplibre.android.annotations.MarkerOptions
import org.maplibre.android.geometry.LatLng
import org.maplibre.android.location.LocationComponentActivationOptions
import org.maplibre.android.location.LocationComponentOptions
import org.maplibre.android.location.modes.CameraMode
import org.maplibre.android.maps.MapLibreMap
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.lang.reflect.Type
import java.math.BigDecimal


open class AgentExperienceMapHelper(
    context: Context,
    layoutInflater: LayoutInflater,
    MapLibreMap: MapLibreMap
) {

    private var context: Context = context
    private var layoutInflater: LayoutInflater = layoutInflater
    private var MapLibreMap: MapLibreMap = MapLibreMap
    private var viewedMarkerList: ArrayList<String> = ArrayList()

    companion object {
        const val MAP_FILTER_TAG = "mapFilterJsonV4"
        const val PRECON_MAP_FILTER_TAG = "preconMapFilterJsonV4"
        var lat1: Double = 45.76564085405049
        var lat2: Double = 42.61891628556286
        var lon1: Double = -78.58055638270632
        var lon2: Double = -81.01519252786112
        var zoom: Double = 8.0

    }


    init {
        val cacheViewedMarker = MMKVUtils.getStr("viewed_marker_list") ?: "[]"
        val type: Type = object : TypeToken<ArrayList<String?>?>() {}.type
        val list: List<String> = Gson().fromJson(cacheViewedMarker, type)
        viewedMarkerList.clear()
        viewedMarkerList.addAll(list)
    }

    @SuppressLint("MissingPermission")
    fun showMapboxLocationComponent(MapLibreMap: MapLibreMap){
        // DEV-7434 fix crash bug: Calling getSourceAs when a newer style is loading/has loaded.
        if (context==null) return
        if ((MapLibreMap.style?.isFullyLoaded != true)) return
        val activationOptions = MapLibreMap.style?.let {
            LocationComponentActivationOptions
                .builder(context, it)
                .locationComponentOptions(
                    LocationComponentOptions.builder(context)
                        .pulseEnabled(false)
                        .pulseAlpha(0f)
                        .accuracyColor(context.getColor(R.color.color_transparent))
                        .build()
                ).build()
        }

        val locationComponent = MapLibreMap.locationComponent

        if (locationComponent==null) return
        if (activationOptions==null) return
        locationComponent.activateLocationComponent(activationOptions)
        locationComponent.isLocationComponentEnabled = true
        locationComponent.cameraMode = CameraMode.TRACKING
    }


    @SuppressLint("MissingPermission")
    fun onDestroyMapboxLocationComponent(MapLibreMap: MapLibreMap?){
        try {
            // DEV-7434 Calling getSourceAs when a newer style is loading/has loaded.
            // DEV-7435 java.lang.NullPointerException: Attempt to invoke virtual method 'java.lang.Class java.lang.Object.getClass()' on a null object reference
            MapLibreMap?.locationComponent?.cameraMode = CameraMode.NONE
            MapLibreMap?.locationComponent?.isLocationComponentEnabled = false
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }

        try {
            MapLibreMap?.locationComponent?.onStop()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }

        try {
            MapLibreMap?.locationComponent?.onDestroy()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }


    /**
     * 用先进先出，最多存200个
     */
    private fun saveViewedMarkerList(ids: String) {
        if (!TextUtils.isEmpty(ids)) {
            val limitCacheSize = 200
            if (viewedMarkerList.size >= limitCacheSize) {
                viewedMarkerList.removeAt(limitCacheSize - 1)
            }
            viewedMarkerList.remove(ids)
            viewedMarkerList.add(0, ids)
            val json = Gson().toJson(viewedMarkerList)
            MMKVUtils.saveStr("viewed_marker_list", json)
        }
    }

    fun getCameraPositionZoom(MapLibreMap:MapLibreMap): Double {
        return BigDecimal(MapLibreMap.cameraPosition.zoom).setScale(1, BigDecimal.ROUND_HALF_UP).toDouble()
    }


    fun addEdmontonCenterMarker(): Marker {
        val iconFactory = IconFactory.getInstance(context)
        val binding = MapMarkerEdmontonCenterBinding.inflate(layoutInflater)
        val bitmap = MapUtils.convertViewToBitmap(binding.root)
        val fromResource = iconFactory.fromBitmap(bitmap)
        val options=  MarkerOptions()
            .icon(fromResource)
            .position(LatLng(53.5461245,-113.4938229))
        return MapLibreMap.addMarker(options)
    }

    suspend fun addFeatureMarker(markerInfo: MapMarkerInfo): Marker {
        val iconFactory = IconFactory.getInstance(context)
        val binding = MapMarkerFeatureBinding.inflate(layoutInflater)
        binding.tvNumber.text = markerInfo.label.toString()
        val options = withContext(Dispatchers.IO) {
            val bitmap = MapUtils.convertViewToNormalBitmap(binding.root)
            val fromResource = iconFactory.fromBitmap(bitmap)
            MarkerOptions()
                .icon(fromResource)
                .position(LatLng(markerInfo.location.lat, markerInfo.location.lon))
        }
        return MapLibreMap.addMarker(options)
    }

    fun updateFeatureMarker(
        markerInfo: MapMarkerInfo,
        marker: Marker,
        isDeSelect: Boolean = false
    ) {
        if (isDeSelect) {
            val iconFactory = IconFactory.getInstance(context)
            val binding = MapMarkerFeatureBinding.inflate(layoutInflater)
            binding.tvNumber.text = markerInfo.label.toString()
            val bitmap = MapUtils.convertViewToNormalBitmap(binding.root)
            val fromResource = iconFactory.fromBitmap(bitmap)
            marker.icon = fromResource
            MapLibreMap.updateMarker(marker)
        } else {
            val iconFactory = IconFactory.getInstance(context)
            val binding = MapMarkerFeatureSelectedBinding.inflate(layoutInflater)
            binding.tvNumber.text = markerInfo.label.toString()
            val bitmap = MapUtils.convertViewToNormalBitmap(binding.root)
            val fromResource = iconFactory.fromBitmap(bitmap)
            marker.icon = fromResource
            MapLibreMap.updateMarker(marker)
        }
    }

    suspend fun addSaleMarker(markerInfo: MapMarkerInfo): Marker {
        return updateSaleMarker(markerInfo)
    }

    suspend fun updateSaleMarker(
        markerInfo: MapMarkerInfo,
        marker: Marker? = null,
        isDeSelect: Boolean = false
    ): Marker {
        val ids = markerInfo.ids.toString()
        val resID: Int
        if (isDeSelect || marker == null) {
            if (NotInterestedHelper.findNotInterestedByListingId(ids)){
                if (markerInfo.agentMapZoom == 1) {
                    resID = R.layout.map_marker_not_interested
                } else {
                    resID = R.layout.map_marker_agnet_grey_dot
                }
            } else if (viewedMarkerList.contains(ids)) {
                if (markerInfo.agentMapZoom == 1) {
                    resID = R.layout.map_marker_blue_viewed
                } else {
                    resID = R.layout.map_marker_agnet_blue_dot
                }
            } else {
                if (markerInfo.agentMapZoom == 1) {
                    resID = R.layout.map_marker_blue
                } else {
                    resID = R.layout.map_marker_agnet_dark_blue_dot
                }
            }
        } else {
            if (NotInterestedHelper.findNotInterestedByListingId(ids)){
                resID = R.layout.map_marker_de_listed_not_interested_selected
            } else {
                resID = R.layout.map_marker_blue_selected
            }

            saveViewedMarkerList(markerInfo.ids.toString())
        }
        val iconFactory = IconFactory.getInstance(context)
        val view = LayoutInflater.from(context).inflate(resID, null)
        val tvNumber = view.findViewById<TextView>(R.id.tv_number)
        tvNumber?.text = markerInfo.label.toString()

        val bitmap = withContext(Dispatchers.IO) {
            if (NotInterestedHelper.findNotInterestedByListingId(ids)){
                MapUtils.convertViewToNormalBitmap(view)
            } else {
                MapUtils.convertViewToBitmap(view)
            }
        }

        marker?.let {
            withContext(Dispatchers.Main) {
                marker.icon = iconFactory.fromBitmap(bitmap)
                MapLibreMap.updateMarker(marker)
                marker
            }
            return marker
        }

        val options = withContext(Dispatchers.IO) {
            val options = MarkerOptions()
            options
                .icon(iconFactory.fromBitmap(bitmap))
                .position(LatLng(markerInfo.location.lat, markerInfo.location.lon))
        }
        return MapLibreMap.addMarker(options)
    }


    suspend fun addSoldMarker(markerInfo: MapMarkerInfo): Marker {
        return updateSoldMarker(markerInfo)
    }

     suspend fun updateSoldMarker(
        markerInfo: MapMarkerInfo,
        marker: Marker? = null,
        isDeSelect: Boolean = false
    ): Marker {
        val ids = markerInfo.ids.toString()
        val resID: Int
        if (isDeSelect || marker == null) {
            if (NotInterestedHelper.findNotInterestedByListingId(ids)){
                if (markerInfo.agentMapZoom == 1) {
                    resID = R.layout.map_marker_not_interested
                } else {
                    resID = R.layout.map_marker_agnet_grey_dot
                }
            } else if (viewedMarkerList.contains(ids)) {
                if (markerInfo.agentMapZoom == 1) {
                    resID = R.layout.map_marker_sold_viewed
                } else {
                    resID = R.layout.map_marker_agnet_purple_dot
                }
            } else {
                if (markerInfo.agentMapZoom == 1) {
                    resID = R.layout.map_marker_sold
                } else {
                    resID = R.layout.map_marker_agnet_dark_purple_dot
                }
            }
        } else {
            if (NotInterestedHelper.findNotInterestedByListingId(ids)){
                resID = R.layout.map_marker_de_listed_not_interested_selected
            } else {
                resID = R.layout.map_marker_sold_selected
            }
            saveViewedMarkerList(markerInfo.ids.toString())
        }
        val iconFactory = IconFactory.getInstance(context)
        val view = LayoutInflater.from(context).inflate(resID, null)
        val tvNumber = view.findViewById<TextView>(R.id.tv_number)
        tvNumber?.text = markerInfo.label.toString()


        marker?.let {
            val bitmap:Bitmap
            if (NotInterestedHelper.findNotInterestedByListingId(ids)){
                bitmap = MapUtils.convertViewToNormalBitmap(view)
            } else {
                bitmap = MapUtils.convertViewToBitmap(view)
            }
            marker.icon = iconFactory.fromBitmap(bitmap)
            MapLibreMap.updateMarker(marker)
            return marker
        }

        val options = withContext(Dispatchers.IO) {
            val bitmap :Bitmap
            if (NotInterestedHelper.findNotInterestedByListingId(ids)){
                bitmap = MapUtils.convertViewToNormalBitmap(view)
            } else {
                bitmap = MapUtils.convertViewToBitmap(view)
            }
            MarkerOptions()
                .icon(iconFactory.fromBitmap(bitmap))
                .position(LatLng(markerInfo.location.lat, markerInfo.location.lon))
        }
        return MapLibreMap.addMarker(options)
    }


    @SuppressLint("MissingInflatedId")
    suspend fun updateNearBySoldMarker(
        markerInfo: MapMarkerInfo,
        marker: Marker? = null,
        isDeSelect: Boolean = false,
    ): Marker {
        val resID: Int
        if (isDeSelect || marker == null) {
            if ((markerInfo.nearZoom == 2)) {
                resID = R.layout.map_marker_near_by_sold
            } else if((markerInfo.nearZoom == 1)) {
                resID = R.layout.map_marker_near_by_sold_middle_point
            } else { // < 17 >= 16
                resID = R.layout.map_marker_near_by_sold_small_point
            }
        } else {
            if ((markerInfo.nearZoom == 2)) {
                resID = R.layout.map_marker_near_by_sold_selected
            } else if((markerInfo.nearZoom == 1)) {
                resID = R.layout.map_marker_near_by_sold_middle_point_select
            } else { // < 17 >= 16
                resID = R.layout.map_marker_near_by_sold_small_point_select
            }
        }
        val iconFactory = IconFactory.getInstance(context)
        val view = LayoutInflater.from(context).inflate(resID, null)
        val tvNumber = view.findViewById<TextView>(R.id.tv_number)
        if (tvNumber != null) {
            tvNumber.text = markerInfo.label.toString()
        }

        marker?.let {
            val fromBitmap = withContext(Dispatchers.IO) {
                val bitmap = MapUtils.convertViewToBitmap(view)
                iconFactory.fromBitmap(bitmap)
            }
            val updateMarker = withContext(Dispatchers.Main) {
                marker.icon = fromBitmap
                MapLibreMap.updateMarker(marker)
                marker
            }
            return updateMarker
        }
        val options = withContext(Dispatchers.IO) {
            val bitmap = MapUtils.convertViewToBitmap(view)
            MarkerOptions()
                .icon(iconFactory.fromBitmap(bitmap))
                .position(LatLng(markerInfo.location.lat, markerInfo.location.lon))
        }
        return MapLibreMap.addMarker(options)
    }

    suspend fun addNearBySoldMarker(markerInfo: MapMarkerInfo): Marker {
        return updateNearBySoldMarker(markerInfo)
    }


    suspend fun addDeListedMarker(markerInfo: MapMarkerInfo): Marker {
        return updateDeListedMarker(markerInfo)
    }

    suspend fun updateDeListedMarker(
        markerInfo: MapMarkerInfo,
        marker: Marker? = null,
        isDeSelect: Boolean = false
    ): Marker {
        val ids = markerInfo.ids.toString()
        val resID: Int
        if (isDeSelect || marker == null) {
            if (NotInterestedHelper.findNotInterestedByListingId(ids)){
                if (markerInfo.agentMapZoom == 1) {
                    resID = R.layout.map_marker_not_interested
                } else {
                    resID = R.layout.map_marker_agnet_grey_dot
                }
            } else if (viewedMarkerList.contains(ids)) {
                if (markerInfo.agentMapZoom == 1) {
                    resID = R.layout.map_marker_de_listed_viewed
                } else {
                    resID = R.layout.map_marker_agnet_light_grey_dot
                }
            } else {
                if (markerInfo.agentMapZoom == 1) {
                    resID = R.layout.map_marker_de_listed
                } else {
                    resID = R.layout.map_marker_agnet_grey_dot
                }
            }
        } else {
            if (NotInterestedHelper.findNotInterestedByListingId(ids)){
                resID = R.layout.map_marker_de_listed_not_interested_selected
            }else{
                resID = R.layout.map_marker_de_listed_selected
            }

            saveViewedMarkerList(markerInfo.ids.toString())
        }
        val iconFactory = IconFactory.getInstance(context)
        val view = LayoutInflater.from(context).inflate(resID, null)
        val tvNumber = view.findViewById<TextView>(R.id.tv_number)
        tvNumber?.text = markerInfo.label.toString()


        marker?.let {
            val bitmap :Bitmap
            if (NotInterestedHelper.findNotInterestedByListingId(ids)){
                bitmap = MapUtils.convertViewToNormalBitmap(view)
            }else{
                bitmap = MapUtils.convertViewToBitmap(view)
            }
            marker.icon = iconFactory.fromBitmap(bitmap)
            MapLibreMap.updateMarker(marker)
            return marker
        }

        val options = withContext(Dispatchers.IO) {
            val bitmap :Bitmap
            if (NotInterestedHelper.findNotInterestedByListingId(ids)){
                bitmap = MapUtils.convertViewToNormalBitmap(view)
            }else{
                bitmap = MapUtils.convertViewToBitmap(view)
            }
            MarkerOptions()
                .icon(iconFactory.fromBitmap(bitmap))
                .position(LatLng(markerInfo.location.lat, markerInfo.location.lon))
        }
        return MapLibreMap.addMarker(options)
    }


    suspend fun addSchoolMarker(markerInfo: SchoolInfo): Marker {
        val iconFactory = IconFactory.getInstance(context)
        val binding = MapMarkerSchoolBinding.inflate(layoutInflater)
        if (markerInfo.score != 0.0) {
            binding.tvNumber.text = markerInfo.score.toString()
        }
        val options = MarkerOptions()
        withContext(Dispatchers.IO) {
            val bitmap = MapUtils.convertViewToNormalBitmap(binding.root)
            val fromResource = iconFactory.fromBitmap(bitmap)
            options.icon(fromResource)
                .position(LatLng(markerInfo.lat, markerInfo.lng))
        }
        return MapLibreMap.addMarker(options)
    }

    fun updateSchoolMarker(markerInfo: SchoolInfo, marker: Marker, isDeSelect: Boolean = false) {
        if (isDeSelect) {
            val iconFactory = IconFactory.getInstance(context)
            val binding = MapMarkerSchoolBinding.inflate(layoutInflater)
            if (markerInfo.score != 0.0) {
                binding.tvNumber.text = markerInfo.score.toString()
            }
            val bitmap = MapUtils.convertViewToNormalBitmap(binding.root)
            val fromResource = iconFactory.fromBitmap(bitmap)
            marker.icon = fromResource
            MapLibreMap.updateMarker(marker)
        } else {
            val iconFactory = IconFactory.getInstance(context)
            val binding = MapMarkerSchoolSelectedBinding.inflate(layoutInflater)
            if (markerInfo.score != 0.0) {
                binding.tvNumber.text = markerInfo.score.toString()
            }
            val bitmap = MapUtils.convertViewToNormalBitmap(binding.root)
            val fromResource = iconFactory.fromBitmap(bitmap)
            marker.icon = fromResource
            MapLibreMap.updateMarker(marker)
        }

    }


    /**
     * 几个房源聚集成一个点的
     */
    suspend fun addGroupMarker(markerInfo: MapMarkerInfo): Marker {
        val iconFactory = IconFactory.getInstance(context)
        val markerNormalBinding = MapMarkerNormalBinding.inflate(layoutInflater)
        val markerCount = markerInfo.count
        when {
            markerCount >= 10000 -> {
                markerNormalBinding.tvNumber.layoutParams =
                    RelativeLayout.LayoutParams(
                        ScreenUtils.dpToPx(68f).toInt(),
                        ScreenUtils.dpToPx(68f).toInt()
                    )
            }
            markerCount >= 1000 -> {
                markerNormalBinding.tvNumber.layoutParams =
                    RelativeLayout.LayoutParams(
                        ScreenUtils.dpToPx(52f).toInt(),
                        ScreenUtils.dpToPx(52f).toInt()
                    )
            }
            markerCount >= 100 -> {
                markerNormalBinding.tvNumber.layoutParams =
                    RelativeLayout.LayoutParams(
                        ScreenUtils.dpToPx(43f).toInt(),
                        ScreenUtils.dpToPx(43f).toInt()
                    )
            }
            else -> {
                markerNormalBinding.tvNumber.layoutParams =
                    RelativeLayout.LayoutParams(
                        ScreenUtils.dpToPx(35f).toInt(),
                        ScreenUtils.dpToPx(35f).toInt()
                    )
            }
        }

        markerNormalBinding.tvNumber.text = markerInfo.count.toString()
        val options = MarkerOptions()
            .position(LatLng(markerInfo.location.lat, markerInfo.location.lon))
        withContext(Dispatchers.IO) {// Dispatchers.IO (main-safety block)
            val bitmap = MapUtils.convertViewToNormalBitmap(markerNormalBinding.root)
            val fromResource = iconFactory.fromBitmap(bitmap)
            options.icon(fromResource)
        }
        // Dispatchers.IO (main-safety block)
        return MapLibreMap.addMarker(options)
    }


    /**
     * 几个房源聚集成一个点的
     */
    suspend fun updateGroupMarker(
        markerInfo: MapMarkerInfo,
        marker: Marker,
        isDeSelect: Boolean = false
    ) {
        val iconFactory = IconFactory.getInstance(context)
        if (isDeSelect) {
            var markerNormalBinding = MapMarkerNormalBinding.inflate(layoutInflater)
            val markerCount = markerInfo.count
            when {
                markerCount >= 10000 -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(68f).toInt(),
                            ScreenUtils.dpToPx(68f).toInt()
                        )
                }
                markerCount >= 1000 -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(52f).toInt(),
                            ScreenUtils.dpToPx(52f).toInt()
                        )
                }
                markerCount >= 100 -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(43f).toInt(),
                            ScreenUtils.dpToPx(43f).toInt()
                        )
                }
                else -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(35f).toInt(),
                            ScreenUtils.dpToPx(35f).toInt()
                        )
                }
            }

            markerNormalBinding.tvNumber.text = markerInfo.count.toString()

            val fromResource: Icon
            withContext(Dispatchers.IO) {
                val bitmap = MapUtils.convertViewToNormalBitmap(markerNormalBinding.root)
                fromResource = iconFactory.fromBitmap(bitmap)
            }
            marker.icon = fromResource
            MapLibreMap.updateMarker(marker)
        } else {
            var markerNormalBinding = MapMarkerNormalSelectedBinding.inflate(layoutInflater)
            val markerCount = markerInfo.count
            when {
                markerCount >= 10000 -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(68f).toInt(),
                            ScreenUtils.dpToPx(68f).toInt()
                        )
                }
                markerCount >= 1000 -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(52f).toInt(),
                            ScreenUtils.dpToPx(52f).toInt()
                        )
                }
                markerCount >= 100 -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(43f).toInt(),
                            ScreenUtils.dpToPx(43f).toInt()
                        )
                }
                else -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(35f).toInt(),
                            ScreenUtils.dpToPx(35f).toInt()
                        )
                }
            }

            markerNormalBinding.tvNumber.text = markerInfo.count.toString()
            val fromResource: Icon
            withContext(Dispatchers.IO) {
                val bitmap = MapUtils.convertViewToNormalBitmap(markerNormalBinding.root)
                fromResource = iconFactory.fromBitmap(bitmap)
            }
            marker.icon = fromResource
            MapLibreMap.updateMarker(marker)
        }

    }


//    DEV-4998 View in Full Map on Listings 逻辑
    fun addViewInFullMapMarker(context:Context,MapLibreMap:MapLibreMap,house: House): Marker {
        val iconFactory = IconFactory.getInstance(context)
        val binding = MapMarkerViewInFullMapBinding.inflate(layoutInflater)
        binding.tvNumber.text = house.marker_label ?: ""
        val bitmap = MapUtils.convertViewToBitmap(binding.root)
        val fromResource = iconFactory.fromBitmap(bitmap)
        val options = MarkerOptions()
            .icon(fromResource)
            .position(LatLng(house.map.lat, house.map.lon))
        return MapLibreMap.addMarker(options)
    }




    /**
     * 剔除掉同View in Full map类型相同经纬度，相同id_listing的marker
     */
    fun removeDuplicateMarkers(hashMarker: Map<Long, MapMarkerInfo>, idListing: String) {
        hashMarker.values
            .filter { it.ids.size == 1 && it.ids.contains(idListing) }
            .mapNotNull { it.localMarker }
            .forEach { MapLibreMap.removeMarker(it) }
    }


    /**
     * 判断View in Full map类型是否和其他类型的marker是相同的
     */
    fun isSameLocationAndIds(
        hashMapLocationInfo: Map<Long, String>,
        markerInfo: MapMarkerInfo
    ): Boolean {
        if (markerInfo.ids==null) return false
        if (markerInfo.ids.size != 1) return false
        return hashMapLocationInfo.values.any { value -> markerInfo.ids.contains(value) }
    }

}