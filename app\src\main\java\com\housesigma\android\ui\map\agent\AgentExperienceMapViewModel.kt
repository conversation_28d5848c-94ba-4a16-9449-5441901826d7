package com.housesigma.android.ui.map.agent

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.housesigma.android.model.*
import com.housesigma.android.network.NetClient
import com.housesigma.android.network.launch

class AgentExperienceMapViewModel : ViewModel() {

    var mapListings: MutableLiveData<MapList> = MutableLiveData()
    var listingPreViewMany: MutableLiveData<ListingPreViewMany> = MutableLiveData()
    var listingPreViewManyWithMarker: MutableLiveData<ListingPreViewMany> = MutableLiveData()
    val loadingLiveData = MutableLiveData<Boolean>()

    fun getListingPreviewMany(id_listing: List<String>,withMarker:Boolean) {
        launch({
            NetClient.apiService.getListingPreviewMany(id_listing)
        }, {
            if (withMarker) {
                listingPreViewManyWithMarker.postValue(it)
            } else {
                listingPreViewMany.postValue(it)
            }
        })
    }

    fun getAgentListing(
        category: List<AgentExperienceMapStatus>,
        idAgent: String,
        agentSlug: String,
    ) {
        val categoryStrList = mutableListOf<String>()
        category.forEach { status ->
            categoryStrList.add(status.name.lowercase())
        }
        launch({
            NetClient.apiService.getAgentListing(
                category = categoryStrList,
                id_agent = idAgent,
                slug = agentSlug,
            )
        }, {
            mapListings.postValue(it)
        })
    }



}