package com.housesigma.android.ui.map.filters

import android.text.TextUtils
import com.donkingliang.labels.LabelsView
import com.housesigma.android.model.MapFilter
import com.housesigma.android.model.MapFilterIdName
import com.housesigma.android.utils.Callback0
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.log.Logger

class LabelManager(isSale: Boolean) {

    private var mMapFilter: MapFilter? = null


    private var mBedroom: List<String>? = null
    private var mBasement: List<String>? = null
    private var mListingType: List<String>? = null
    private var mBathroom: String? = null
    private var mGarage: String? = null
    private var mOpenHouse: String? = null
    private var mIsSale = isSale


    fun getBedroomRange(): ArrayList<String> {
        return mBedroom?.toMutableList() as ArrayList<String>? ?: arrayListOf()
    }

    fun getBasement(): ArrayList<String> {
        // 去掉空的选项
        val basement : ArrayList<String> = arrayListOf()
        mBasement?.forEach {
            if (!TextUtils.isEmpty(it)) {
                basement.add(it)
            }
        }
        return basement
    }

    fun getListingType(): ArrayList<String> {
        return mListingType?.toMutableList() as ArrayList<String>? ?: arrayListOf()
    }


    fun getBathroomMin(): String {
        return mBathroom ?: "0"
    }

    fun getGarageMin(): String {
        return mGarage ?: "0"
    }

    fun getOpenHouseDate(): String {
        return mOpenHouse ?: "0"
    }

    private fun checkMapFilterStatus() {
        if (mMapFilter == null) {
            throw Exception("mMapFilter is null")
        }
    }

    fun setMapFilter(mapFilter: MapFilter): LabelManager {
        mMapFilter = mapFilter
        return this
    }

    private fun getKey(key:String): String {
        return key + if(mIsSale) "_sale" else "_lease"
    }

    fun setBedroomFilterLabel(labelsView: LabelsView, ids: List<String>) {
        checkMapFilterStatus()

        labelsView.setLabels(mMapFilter?.bedroom_filter) { _, _, data -> data.name }

        findPositionByIds(mMapFilter?.bedroom_filter, ids).let {
            labelsView.setSelects(it)
        }

        mBedroom = ids
        MMKVUtils.saveStr(getKey("filter_bedroom"), ids.joinToString(","))
    }

    fun setBasementFilterLabel(labelsView: LabelsView, ids: List<String>) {
        checkMapFilterStatus()

        labelsView.setLabels(mMapFilter?.basement_filter) { _, _, data -> data.name }

        findPositionByIds(mMapFilter?.basement_filter, ids).let {
            labelsView.setSelects(it)
        }

        mBasement = ids
        MMKVUtils.saveStr(getKey("filter_basement"), ids.joinToString(","))
    }

    fun setListingTypeFilterLabel(labelsView: LabelsView, ids: List<String>) {
        checkMapFilterStatus()

        labelsView.setLabels(mMapFilter?.listing_type_filter) { _, _, data -> data.name }

        findPositionByIds(mMapFilter?.listing_type_filter, ids).let {
            labelsView.setSelects(it)
        }

        mListingType = ids
        MMKVUtils.saveStr(getKey("filter_listing_type"), ids.joinToString(","))
    }

    /**
     * 根据ids找到position
     *
     * @param idNames
     * @param ids
     * @return ArrayList<Int>
     */
    private fun findPositionByIds(
        idNames: List<MapFilterIdName>? = null,
        ids: List<String>
    ): ArrayList<Int> {
        val positions = ArrayList<Int>()
        ids.forEach { id ->
            idNames?.forEachIndexed { index, item ->
                if (item.id == id) {
                    Logger.e("id: $id, item.id: ${item.id}")
                    positions.add(index)
                }
            }
        }
        Logger.e("根据ids找到position: " + positions.toString())
        return positions
    }


    /**
     * 根据id找到position
     *
     * @param idNames
     * @param id
     */
    private fun findPositionById(
        idNames: List<MapFilterIdName>? = null,
        id: String
    ): ArrayList<Int> {
        val positions = ArrayList<Int>()
        idNames?.forEachIndexed { index, item ->
            if (item.id == id) {
                positions.add(index)
            }
        }
        Logger.e("根据id找到position: " + positions.toString())
        return positions
    }


    fun setBathroomsFilterLabel(labelsView: LabelsView, id: String) {
        checkMapFilterStatus()

        labelsView.setLabels(mMapFilter?.bathroom_filter) { _, _, data -> data.name }

        findPositionById(mMapFilter?.bathroom_filter, id).let {
            labelsView.setSelects(it)
        }

        mBathroom = id
        MMKVUtils.saveStr(getKey("filter_bathroom"), id)
    }


    fun setGarageFilterLabel(labelsView: LabelsView, id: String) {
        checkMapFilterStatus()

        labelsView.setLabels(mMapFilter?.garage_filter) { _, _, data -> data.name }

        findPositionById(mMapFilter?.garage_filter, id).let {
            labelsView.setSelects(it)
        }

        mGarage = id
        MMKVUtils.saveStr(getKey("filter_garage"), id)
    }

    fun setOpenHouseDate(labelsView: LabelsView, id: String) {
        checkMapFilterStatus()

        labelsView.setLabels(mMapFilter?.open_house_filter) { _, _, data -> data.name }
        findPositionById(mMapFilter?.open_house_filter, id).let {
            labelsView.setSelects(it)
        }

        mOpenHouse = id
        MMKVUtils.saveStr(getKey("filter_open_house"), id)
    }

    fun initLabelsView(
        bedroomView: LabelsView,
        basementView: LabelsView,
        listingTypeView: LabelsView,
        bathroomView: LabelsView,
        garageView: LabelsView,
        openHouseView: LabelsView,
        cb: Callback0? = null
    ) {
        checkMapFilterStatus()

        basementView.selectType = LabelsView.SelectType.MULTI
        basementView.setLabels(mMapFilter?.basement_filter) { _, _, data -> data.name }


        bedroomView.selectType = LabelsView.SelectType.MULTI
        bedroomView.setLabels(mMapFilter?.bedroom_filter) { _, _, data -> data.name }


        bathroomView.selectType = LabelsView.SelectType.SINGLE_IRREVOCABLY
        bathroomView.setLabels(mMapFilter?.bathroom_filter) { _, _, data -> data.name }


        garageView.selectType = LabelsView.SelectType.SINGLE_IRREVOCABLY
        garageView.setLabels(mMapFilter?.garage_filter) { _, _, data -> data.name }

        openHouseView.selectType = LabelsView.SelectType.SINGLE_IRREVOCABLY
        openHouseView.setLabels(mMapFilter?.open_house_filter) { _, _, data -> data.name }

        listingTypeView.selectType = LabelsView.SelectType.MULTI
        listingTypeView.setLabels(mMapFilter?.listing_type_filter) { _, _, data -> data.name }


        mBasement = (MMKVUtils.getStr(getKey("filter_basement")) ?: "").split(",").map { it.toString() }
        mBasement?.let { basement ->
            findPositionByIds(mMapFilter?.basement_filter, basement).let {
                basementView.setSelects(it)
            }
        }
        basementView.setOnLabelClickListener { label, data, position ->
            handleOnLabelClick( FilterLabelType.BASEMENT,basementView, cb,position)
        }

        Logger.e("@@@@@@@@@@@@@@@@!!!!!!!"+MMKVUtils.getStr(getKey("filter_bedroom")))
        mBedroom = (MMKVUtils.getStr(getKey("filter_bedroom")) ?: "").split(",").map { it.toString() }
        Logger.e("@@@@@@@@@@@@@@@@!!!!!!!"+mBedroom)
        mBedroom?.let { bedroom ->
            findPositionByIds(mMapFilter?.bedroom_filter, bedroom).let {
                bedroomView.setSelects(it)
            }
        }
        bedroomView.setOnLabelClickListener { label, data, position ->
            handleOnLabelClick(FilterLabelType.BEDROOMS,bedroomView, cb ,position)
        }


        mListingType = (MMKVUtils.getStr(getKey("filter_listing_type")) ?: "").split(",").map { it.toString() }
        mListingType?.let { listingType ->
            findPositionByIds(mMapFilter?.listing_type_filter, listingType).let {
                listingTypeView.setSelects(it)
            }
        }
        listingTypeView.setOnLabelClickListener { label, data, position ->
            handleOnLabelClick(FilterLabelType.LISTING_TYPE,listingTypeView, cb ,position)
        }

        mBathroom = MMKVUtils.getStr(getKey("filter_bathroom")) ?:"0"
        mBathroom?.let { bathroom ->
            findPositionById(mMapFilter?.bathroom_filter, bathroom).let {
                bathroomView.setSelects(it)
            }
        }
        bathroomView.setOnLabelClickListener { label, data, position ->
            handleOnLabelClick(FilterLabelType.BATHROOMS,bathroomView, cb ,position)
        }


        mGarage = MMKVUtils.getStr(getKey("filter_garage")) ?:"0"
        mGarage?.let { garage ->
            findPositionById(mMapFilter?.garage_filter, garage).let {
                garageView.setSelects(it)
            }
        }
        garageView.setOnLabelClickListener { label, data, position ->
            handleOnLabelClick(FilterLabelType.GARAGE,garageView, cb ,position)
        }

        mOpenHouse = MMKVUtils.getStr(getKey("filter_open_house")) ?:"0"
        mOpenHouse?.let { openHouse ->
            findPositionById(mMapFilter?.open_house_filter, openHouse).let {
                openHouseView.setSelects(it)
            }
        }
        openHouseView.setOnLabelClickListener { label, data, position ->
            handleOnLabelClick(FilterLabelType.OPEN_HOUSE,openHouseView, cb ,position)
        }

    }

    private fun handleOnLabelClick(
        type: FilterLabelType,
        labelsView: LabelsView,
        cb: Callback0?,
        position: Int

    ) {
        val ids = applyLabelSelectionLogic(type,labelsView,position)

        updateLabelData(type,ids)

        saveLabelData(type,ids)

        cb?.onData()

        sendGAEvent(type)
    }

    private fun applyLabelSelectionLogic(
        type: FilterLabelType,
        labelsView: LabelsView,
        position: Int
    ): java.util.ArrayList<String> {

        val ids = ArrayList<String>()
        when (type) {
            FilterLabelType.BEDROOMS -> {
//                 对选项进行处理 选了all就不能选择其他的，选择其他的就要去掉all
                if (position == 0) {
                    labelsView.setSelects(0)
                } else {
                    val selectLabels = labelsView.selectLabels
                    selectLabels.remove(0)
                    labelsView.setSelects(selectLabels)
                }
                var selectLabelDatas =
                    labelsView.getSelectLabelDatas<MapFilterIdName>()

                if (selectLabelDatas.size == 0) {
                    labelsView.setSelects(0)
                    selectLabelDatas =
                        labelsView.getSelectLabelDatas()
                }

                val allLabel = labelsView.getLabels<MapFilterIdName>()
                if (selectLabelDatas.size - 1 == allLabel.size - 2) {
                    labelsView.setSelects(0)
                    selectLabelDatas = labelsView.getSelectLabelDatas()
                }

                selectLabelDatas.forEach {
                    ids.add(it.id)
                }

                Logger.e("$type ~~~~~~~~~~~~~~~~~"  + selectLabelDatas)
            }

            FilterLabelType.BASEMENT -> {
                var selectLabelDatas = labelsView.getSelectLabelDatas<MapFilterIdName>()

                if (selectLabelDatas.size == 0) {
                    labelsView.clearAllSelect()
                    selectLabelDatas = labelsView.getSelectLabelDatas()
                }

                selectLabelDatas.forEach {
                    ids.add(it.id)
                }

                Logger.e("$type ~~~~~~~~~~~~~~~~~" + selectLabelDatas)
            }

            FilterLabelType.LISTING_TYPE -> {
                if (position == 0) {
                    labelsView.setSelects(0)
                } else {
                    val selectLabels = labelsView.selectLabels
                    selectLabels.remove(0)
                    labelsView.setSelects(selectLabels)
                }

                var selectLabelDatas =
                    labelsView.getSelectLabelDatas<MapFilterIdName>()
                if (selectLabelDatas.size == 0) {
                    labelsView.clearAllSelect()
                    //所有选择都取消后，需要默认选择 all
                    labelsView.setSelects(0)
                    selectLabelDatas =
                        labelsView.getSelectLabelDatas()
                }

                val allLabel = labelsView.getLabels<MapFilterIdName>()
                if (selectLabelDatas.size - 1 == allLabel.size - 2) {
                    labelsView.setSelects(0)
                    selectLabelDatas =
                        labelsView.getSelectLabelDatas()
                }
                selectLabelDatas.forEach {
                    ids.add(it.id)
                }

                Logger.e("$type ~~~~~~~~~~~~~~~~~" + selectLabelDatas)
            }

            FilterLabelType.BATHROOMS -> {
                val selectLabelDatas = labelsView.getSelectLabelDatas<MapFilterIdName>()
                selectLabelDatas.forEach {
                    ids.add(it.id)
                }

                Logger.e("$type ~~~~~~~~~~~~~~~~~" + position)
            }

            FilterLabelType.GARAGE -> {
                val selectLabelDatas = labelsView.getSelectLabelDatas<MapFilterIdName>()
                selectLabelDatas.forEach {
                    ids.add(it.id)
                }

                Logger.e("$type ~~~~~~~~~~~~~~~~~" + position)
            }

            FilterLabelType.OPEN_HOUSE -> {
                val selectLabelDatas = labelsView.getSelectLabelDatas<MapFilterIdName>()
                selectLabelDatas.forEach {
                    ids.add(it.id)
                }

                Logger.e("$type ~~~~~~~~~~~~~~~~~" + position)
            }
        }

        return ids
    }

    private fun saveLabelData(type: FilterLabelType, ids: java.util.ArrayList<String>) {
        when (type) {
            FilterLabelType.BEDROOMS -> {
                MMKVUtils.saveStr(getKey("filter_bedroom"), ids.joinToString(","))
            }

            FilterLabelType.BASEMENT -> {
                MMKVUtils.saveStr(getKey("filter_basement"), ids.joinToString(","))
            }

            FilterLabelType.LISTING_TYPE -> {
                MMKVUtils.saveStr(getKey("filter_listing_type"), ids.joinToString(","))
            }

            FilterLabelType.BATHROOMS -> {
                MMKVUtils.saveStr(getKey("filter_bathroom"), ids[0])
            }

            FilterLabelType.GARAGE -> {
                MMKVUtils.saveStr(getKey("filter_garage"), ids[0])
            }

            FilterLabelType.OPEN_HOUSE -> {
                MMKVUtils.saveStr(getKey("filter_open_house"), ids[0])
            }
        }
    }

    private fun updateLabelData(type: FilterLabelType,ids: List<String>) {
        when (type) {
            FilterLabelType.BEDROOMS -> {
                mBedroom = ids
            }

            FilterLabelType.BASEMENT -> {
                mBasement = ids
            }

            FilterLabelType.LISTING_TYPE -> {
                mListingType = ids
            }

            FilterLabelType.BATHROOMS -> {
                mBathroom = ids[0]
            }

            FilterLabelType.GARAGE -> {
                mGarage = ids[0]
            }

            FilterLabelType.OPEN_HOUSE -> {
                mOpenHouse = ids[0]
            }
        }

        // 把上面提到的变量都打印出来
        Logger.e("mBedroom: $mBedroom")
        Logger.e("mBasement: $mBasement")
        Logger.e("mListingType: $mListingType")
        Logger.e("mBathroom: $mBathroom")
        Logger.e("mGarage: $mGarage")
        Logger.e("mOpenHouse: $mOpenHouse")

    }

    private fun sendGAEvent(type: FilterLabelType) {
        when (type) {
            FilterLabelType.BEDROOMS -> {
                GALog.log("map_filters_click", "bedrooms")
            }

            FilterLabelType.BASEMENT -> {
                GALog.log("map_filters_click", "basement")
            }

            FilterLabelType.LISTING_TYPE -> {
                GALog.log("map_filters_click", "listing_type")
            }

            FilterLabelType.BATHROOMS -> {
                GALog.log("map_filters_click", "bathrooms")
            }

            FilterLabelType.GARAGE -> {
                GALog.log("map_filters_click", "garage_covered_parking")
            }

            FilterLabelType.OPEN_HOUSE -> {
                GALog.log("map_filters_click", "open_house")
            }
        }
    }


}