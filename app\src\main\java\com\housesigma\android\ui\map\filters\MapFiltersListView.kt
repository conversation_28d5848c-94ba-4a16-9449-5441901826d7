package com.housesigma.android.ui.map.filters

import android.content.Context
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.housesigma.android.R
import com.housesigma.android.model.SaveMapFilter
import com.housesigma.android.ui.map.MapType
import com.housesigma.android.ui.map.propertype.MapFilterListAdapter
import com.housesigma.android.utils.Callback1
import com.housesigma.android.utils.MMKVUtils
import com.lxj.xpopup.core.AttachPopupView
import java.util.ArrayList

open class MapFiltersListView(context: Context, mapType: MapType) : AttachPopupView(context) {

    private var mList: ArrayList<SaveMapFilter> = ArrayList()
    private val mAdapter = MapFilterListAdapter(context,mapType)

    override fun getImplLayoutId(): Int {
        return R.layout.popwindow_map_filters_list
    }

     var cb:Callback1 ?= null

    override fun onCreate() {
        super.onCreate()
        val rv = findViewById<RecyclerView>(R.id.rv)
        rv.adapter = mAdapter
        rv.layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)

        mAdapter.setOnItemChildClickListener { adapter, view, position ->
            when (view.id) {
                R.id.ll -> {
                    setSelectFilterByFilterName(mList[position].filterName)
                    mAdapter.notifyDataSetChanged()
                    cb?.onData(position)
                    dismiss()
                }
            }
        }
    }


    fun setSelectFilterByFilterName(filterName: String) {
        MMKVUtils.saveStr(mAdapter.getKey("select_filter_name"),filterName)
    }


    fun setSelectClickListener(cb: Callback1) {
        this.cb = cb
    }

    fun setData(list: ArrayList<SaveMapFilter>) {
        mList = list
        mAdapter.setList(mList)
    }

    fun notifyDataSetChanged(){
        mAdapter.notifyDataSetChanged()
    }


}