package com.housesigma.android.ui.map.filters

import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import com.housesigma.android.databinding.PopwindowMapFilterMoreBinding
import com.housesigma.android.model.DefaultFilter
import com.housesigma.android.model.MapFilter
import com.housesigma.android.model.MapFilters
import com.housesigma.android.utils.Callback0
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.RentalSeekBar
import com.housesigma.android.views.SaleSeekBar
import com.jaygoo.widget.OnRangeChangedListener
import com.jaygoo.widget.RangeSeekBar

open class SeekBarManager {

    private var mMapFilter: MapFilter? = null
    var isSale: Boolean = true

    var rightMaxValue: Int = 10000
    var priceLeft: Int = 0
    var priceRight: Int = rightMaxValue


    var feetLeft: Int = 0
    var feetRight: Int = 100

    var squareFootageLeft: Int = 0
    var squareFootageRight: Int = 4000

    var lotSizeMaxValue: Int = 15
    var lotSizeLeft: Int = 0
    var lotSizeRight: Int = lotSizeMaxValue

    var buildingAgeMaxValue: Int = 24
    var buildingAgeLeft: Int = 0
    var buildingAgeRight: Int = buildingAgeMaxValue

    // 租金回报率筛选 (0% - 10%+, 步进0.5%)
    var rentalYieldMaxValue: Float = 10.0f
    var rentalYieldLeft: Float = 0.0f
    var rentalYieldRight: Float = rentalYieldMaxValue

    // 学校评分筛选 (0 - 10, 步进0.5)
    var schoolScoreMaxValue: Float = 10.0f
    var schoolScoreLeft: Float = 0.0f
    var schoolScoreRight: Float = schoolScoreMaxValue

    var description: String = ""
    var fee: String = "0"


    open fun getPrice(): ArrayList<String> {
        return arrayListOf(priceLeft.toString(),priceRight.toString())
    }

    open fun getFeet(): ArrayList<String> {
        return arrayListOf(feetLeft.toString(),feetRight.toString())
    }

    open fun getSquareFootage(): ArrayList<String> {
        return arrayListOf(squareFootageLeft.toString(),squareFootageRight.toString())
    }

    open fun getLotSize(): ArrayList<String>? {
        return arrayListOf((HSUtil.getLotSize(lotSizeLeft)?.value?:0).toString(),(HSUtil.getLotSize(lotSizeRight)?.value?:0).toString())
    }

    open fun getBuildingAge(): ArrayList<String>? {
        return arrayListOf((HSUtil.getBuildingAge(buildingAgeLeft)?.value?:0).toString(),(HSUtil.getBuildingAge(buildingAgeRight)?.value?:0).toString())
    }

    /**
     * 获取租金回报率范围
     */
    open fun getRentalYieldRange(): ArrayList<String>? {
        return if (rentalYieldLeft == 0.0f && rentalYieldRight == rentalYieldMaxValue) {
            null // 返回null表示不筛选
        } else {
            arrayListOf(rentalYieldLeft.toString(), rentalYieldRight.toString())
        }
    }

    /**
     * 获取学校评分范围
     */
    open fun getSchoolScoreRange(): ArrayList<String>? {
        return if (schoolScoreLeft == 0.0f && schoolScoreRight == schoolScoreMaxValue) {
            null // 返回null表示不筛选
        } else {
            arrayListOf(schoolScoreLeft.toString(), schoolScoreRight.toString())
        }
    }

    private fun checkMapFilterStatus() {
        if (mMapFilter == null) {
            throw Exception("mMapFilter is null")
        }
    }

    fun setMapFilter(mapFilter: MapFilter): SeekBarManager {
        mMapFilter = mapFilter
        return this
    }


    fun initSeekBar(binding: PopwindowMapFilterMoreBinding, isSale: Boolean, mcb: Callback0?) {
        this.isSale = isSale
        if (!isSale) {
            binding.saleSb.visibility = View.GONE
            binding.rentalSb.visibility = View.VISIBLE

            binding.rentalSb.setDefaultValue()
            priceLeft = binding.rentalSb.priceLeft
            priceRight = binding.rentalSb.priceRight
            rightMaxValue = binding.rentalSb.priceRight
            binding.rentalSb.setOnChangeListener(object : RentalSeekBar.OnChangeListener {
                override fun onChange(showPrice: String) {
                    binding.tvPrice.text = showPrice
                    priceLeft = binding.rentalSb.priceLeft
                    priceRight = binding.rentalSb.priceRight

                    MMKVUtils.saveInt(isSale.toString() +"rental_sb_left",priceLeft)
                    MMKVUtils.saveInt(isSale.toString() +"rental_sb_right",priceRight)
                }

                override fun onStopTrackingTouch() {
                    if (mcb != null) {
                        (mcb as Callback0).onData()
                    }
                }
            })

        } else {
            binding.saleSb.visibility = View.VISIBLE
            binding.rentalSb.visibility = View.GONE

            binding.saleSb.setDefaultValue()
            rightMaxValue = binding.saleSb.priceRight
            priceLeft = binding.saleSb.priceLeft
            priceRight = binding.saleSb.priceRight
            binding.saleSb.setOnChangeListener(object : SaleSeekBar.OnChangeListener {
                override fun onChange(showPrice: String) {
                    binding.tvPrice.text = showPrice
                    priceLeft = binding.saleSb.priceLeft
                    priceRight = binding.saleSb.priceRight

                    MMKVUtils.saveInt(isSale.toString() +"sale_sb_left",priceLeft)
                    MMKVUtils.saveInt(isSale.toString() +"sale_sb_right",priceRight)
                }

                override fun onStopTrackingTouch() {
                    if (mcb != null) {
                        (mcb as Callback0).onData()
                    }
                }
            })
        }
        binding.rsbFeet.setOnRangeChangedListener(object : OnRangeChangedListener {
            override fun onRangeChanged(
                view: RangeSeekBar?,
                leftValue: Float,
                rightValue: Float,
                isFromUser: Boolean
            ) {
                feetLeft = leftValue.toInt()
                feetRight = rightValue.toInt()

                if (leftValue.toInt() == 0 && rightValue.toInt() == 100) {
                    binding.tvFeet.text =
                        "Lot Front (feet): Unspecified" + " - 100+"
                } else if (rightValue.toInt() == 100) {
                    binding.tvFeet.text =
                        "Lot Front (feet): ".plus(leftValue.toInt()) + " - 100+"
                } else if (leftValue.toInt() == 0) {
                    binding.tvFeet.text =
                        "Lot Front (feet): Unspecified" + " - " + rightValue.toInt()
                } else {
                    binding.tvFeet.text =
                        "Lot Front (feet): ".plus(leftValue.toInt()) + " - " + rightValue.toInt()
                }


                MMKVUtils.saveInt(isSale.toString() +"rsb_feet_left",feetLeft)
                MMKVUtils.saveInt(isSale.toString() +"rsb_feet_right",feetRight)
            }

            override fun onStartTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
            }

            override fun onStopTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
                if (mcb != null) {
                    (mcb as Callback0).onData()
                }
                GALog.log("map_filters_click","lot_front")
            }
        })


        binding.rsbBuildingAge.setOnRangeChangedListener(object : OnRangeChangedListener {
            override fun onRangeChanged(
                view: RangeSeekBar?,
                leftValue: Float,
                rightValue: Float,
                isFromUser: Boolean
            ) {
                buildingAgeLeft = Math.round(leftValue.toDouble()).toInt()
                buildingAgeRight = Math.round(rightValue.toDouble()).toInt()

                val buildingAgeFilterLeft = HSUtil.getBuildingAge(buildingAgeLeft)
                val buildingAgeFilterRight = HSUtil.getBuildingAge(buildingAgeRight)

                binding.tvBuildingAge.text =  "Building Age (years): "+buildingAgeFilterLeft?.text + " - "+buildingAgeFilterRight?.text

                MMKVUtils.saveInt(isSale.toString() +"rsb_building_age_left", buildingAgeLeft)
                MMKVUtils.saveInt(isSale.toString() +"rsb_building_age_right", buildingAgeRight)
            }

            override fun onStartTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
            }

            override fun onStopTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
                if (mcb != null) {
                    (mcb as Callback0).onData()
                }
                GALog.log("map_filters_click","building_age")
            }
        })

        binding.rsbLotSize.setOnRangeChangedListener(object : OnRangeChangedListener {
            override fun onRangeChanged(
                view: RangeSeekBar?,
                leftValue: Float,
                rightValue: Float,
                isFromUser: Boolean
            ) {
                lotSizeLeft = Math.round(leftValue.toDouble()).toInt()
                lotSizeRight = Math.round(rightValue.toDouble()).toInt()

                val lotSizeFilterLeft = HSUtil.getLotSize(lotSizeLeft)
                val lotSizeFilterRight = HSUtil.getLotSize(lotSizeRight)

                binding.tvLotSize.text =  "Lot Size: "+lotSizeFilterLeft?.text + " - "+lotSizeFilterRight?.text

                MMKVUtils.saveInt(isSale.toString() +"rsb_lot_size_left", lotSizeLeft)
                MMKVUtils.saveInt(isSale.toString() +"rsb_lot_size_right", lotSizeRight)
            }

            override fun onStartTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
            }

            override fun onStopTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
                if (mcb != null) {
                    (mcb as Callback0).onData()
                }
                GALog.log("map_filters_click","lot_size")
            }
        })

        binding.rsbSquare.setOnRangeChangedListener(object : OnRangeChangedListener {
            override fun onRangeChanged(
                view: RangeSeekBar?,
                leftValue: Float,
                rightValue: Float,
                isFromUser: Boolean
            ) {

                squareFootageLeft = leftValue.toInt()
                squareFootageRight = rightValue.toInt()
                if (leftValue.toInt() == 0 && rightValue.toInt() == 4000) {
                    binding.tvSquare.text =
                        "Square Footage: Unspecified" + " - Max"
                } else if (rightValue.toInt() == 4000) {
                    binding.tvSquare.text =
                        "Square Footage: ".plus(leftValue.toInt()) + " - Max"
                } else if (leftValue.toInt() == 0) {
                    binding.tvSquare.text =
                        "Square Footage: Unspecified" + " - " + rightValue.toInt()
                } else {
                    binding.tvSquare.text =
                        "Square Footage: ".plus(leftValue.toInt()) + " - " + rightValue.toInt()
                }

                MMKVUtils.saveInt(isSale.toString() +"rsb_square_left", squareFootageLeft)
                MMKVUtils.saveInt(isSale.toString() +"rsb_square_right", squareFootageRight)
            }

            override fun onStartTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
            }

            override fun onStopTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
                if (mcb != null) {
                    (mcb as Callback0).onData()
                }
                GALog.log("map_filters_click","square_footage")
            }
        })

        binding.etDescription.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                Logger.d( "description onTextChanged:$p0")
                description = p0.toString()

                MMKVUtils.saveStr(isSale.toString() + "description", description)
            }

            override fun afterTextChanged(p0: Editable?) {
            }

        })

        binding.etDescription.onFocusChangeListener = View.OnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                // 此处为得到焦点时的处理内容
            } else {
                GALog.log("map_filters_click", "description_contains_keywords")
            }
        }

        binding.etFee.onFocusChangeListener = View.OnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                // 此处为得到焦点时的处理内容
            } else {
                GALog.log("map_filters_click", "max_maintenance_fee")
            }
        }



        binding.etFee.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                Logger.d( "fee onTextChanged:$p0")
                fee = p0.toString()

                MMKVUtils.saveStr(isSale.toString() + "fee", fee)
            }

            override fun afterTextChanged(p0: Editable?) {
            }

        })

        initSeekBar(binding)
    }

    fun setValue(defaultFilter: MapFilters, binding: PopwindowMapFilterMoreBinding, hasLotSize: Boolean, hasBuildingAge: Boolean) {
        val descriptionStr = defaultFilter.description
        val max_maintenance_fee = defaultFilter.max_maintenance_fee
        val lot_front_feet_min = defaultFilter.lot_front_feet_min
        val lot_front_feet_max = defaultFilter.lot_front_feet_max
        val square_footage_min = defaultFilter.square_footage_min
        val square_footage_max = defaultFilter.square_footage_max
        val price_sale_min = defaultFilter.price?.get(0)?.toInt()?:0
        val price_sale_max =  defaultFilter.price?.get(1)?.toInt()?:0
        val price_rent_min =  defaultFilter.price?.get(0)?.toInt()?:0
        val price_rent_max =  defaultFilter.price?.get(1)?.toInt()?:0

        if (hasLotSize) {
            val lotSizeMax  = (defaultFilter.lot_size_max?:"10000000").toInt()
            val lotSizeMin = (defaultFilter.lot_size_min?:"0").toInt()
            lotSizeLeft = HSUtil.getPositionByLotSizeValue(lotSizeMin)
            lotSizeRight = HSUtil.getPositionByLotSizeValue(lotSizeMax)
            MMKVUtils.saveInt(isSale.toString() + "rsb_lot_size_left", lotSizeLeft)
            MMKVUtils.saveInt(isSale.toString() + "rsb_lot_size_right", lotSizeRight)
        }

        if (hasBuildingAge) {
            val buildingAgeMax  = (defaultFilter.building_age_max?:"0").toInt()
            val buildingAgeMin = (defaultFilter.building_age_min?:"999").toInt()
            buildingAgeLeft = HSUtil.getPositionByBuildingAgeValue(buildingAgeMin)
            buildingAgeRight = HSUtil.getPositionByBuildingAgeValue(buildingAgeMax)
            MMKVUtils.saveInt(isSale.toString() + "rsb_building_age_right", buildingAgeRight)
            MMKVUtils.saveInt(isSale.toString() + "rsb_building_age_left", buildingAgeLeft)
        }

        MMKVUtils.saveStr(isSale.toString() + "init","true")
        MMKVUtils.saveStr(isSale.toString() + "description",descriptionStr)
        MMKVUtils.saveStr(isSale.toString() + "fee",max_maintenance_fee)
        MMKVUtils.saveInt(isSale.toString() + "rsb_feet_left",lot_front_feet_min?.toInt()?:0)
        MMKVUtils.saveInt(isSale.toString() + "rsb_feet_right",lot_front_feet_max?.toInt()?:0)
        MMKVUtils.saveInt(isSale.toString() + "rsb_square_left",square_footage_min?.toInt()?:0)
        MMKVUtils.saveInt(isSale.toString() + "rsb_square_right",square_footage_max?.toInt()?:0)

        // 处理租金回报率和学校评分
        val rentalYieldMin = defaultFilter.rental_yield_min?.toFloatOrNull() ?: 0.0f
        val rentalYieldMax = defaultFilter.rental_yield_max?.toFloatOrNull() ?: rentalYieldMaxValue
        val schoolScoreMin = defaultFilter.school_score_min?.toFloatOrNull() ?: 0.0f
        val schoolScoreMax = defaultFilter.school_score_max?.toFloatOrNull() ?: schoolScoreMaxValue

        MMKVUtils.saveFloat(isSale.toString() + "rental_yield_left", rentalYieldMin)
        MMKVUtils.saveFloat(isSale.toString() + "rental_yield_right", rentalYieldMax)
        MMKVUtils.saveFloat(isSale.toString() + "school_score_left", schoolScoreMin)
        MMKVUtils.saveFloat(isSale.toString() + "school_score_right", schoolScoreMax)

        if (isSale) {
            MMKVUtils.saveInt(isSale.toString() + "sale_sb_left",price_sale_min.toInt())
            MMKVUtils.saveInt(isSale.toString() + "sale_sb_right",price_sale_max.toInt())
            priceLeft =  price_sale_min.toInt()
            rightMaxValue = price_sale_max.toInt()
        } else {
            MMKVUtils.saveInt(isSale.toString() + "rental_sb_left",price_rent_min.toInt())
            MMKVUtils.saveInt(isSale.toString() + "rental_sb_right",price_rent_max.toInt())
            priceLeft =  price_rent_min.toInt()
            rightMaxValue = price_rent_max.toInt()
        }
        initSeekBar(binding, null)
    }

    fun setValue(defaultFilter: DefaultFilter, binding: PopwindowMapFilterMoreBinding, hasLotSize:Boolean,hasBuildingAge:Boolean) {
        val descriptionStr = defaultFilter.description
        val max_maintenance_fee = defaultFilter.max_maintenance_fee
        val lot_front_feet_min = defaultFilter.lot_front_feet_min
        val lot_front_feet_max = defaultFilter.lot_front_feet_max
        val square_footage_min = defaultFilter.square_footage_min
        val square_footage_max = defaultFilter.square_footage_max
        val price_sale_min = defaultFilter.price_sale_min
        val price_sale_max = defaultFilter.price_sale_max
        val price_rent_min = defaultFilter.price_rent_min
        val price_rent_max = defaultFilter.price_rent_max


        if (hasLotSize) {
            val lotSizeMax  = (defaultFilter.lot_size_max?:"10000000").toInt()
            val lotSizeMin = (defaultFilter.lot_size_min?:"0").toInt()
            lotSizeLeft = HSUtil.getPositionByLotSizeValue(lotSizeMin)
            lotSizeRight = HSUtil.getPositionByLotSizeValue(lotSizeMax)
            MMKVUtils.saveInt(isSale.toString() + "rsb_lot_size_left", lotSizeLeft)
            MMKVUtils.saveInt(isSale.toString() + "rsb_lot_size_right", lotSizeRight)
        }

        if (hasBuildingAge) {
            val buildingAgeMax  = (defaultFilter.building_age_max?:"0").toInt()
            val buildingAgeMin = (defaultFilter.building_age_min?:"999").toInt()
            buildingAgeLeft = HSUtil.getPositionByBuildingAgeValue(buildingAgeMin)
            buildingAgeRight = HSUtil.getPositionByBuildingAgeValue(buildingAgeMax)
            MMKVUtils.saveInt(isSale.toString() + "rsb_building_age_right", buildingAgeRight)
            MMKVUtils.saveInt(isSale.toString() + "rsb_building_age_left", buildingAgeLeft)
        }


        // 处理租金回报率和学校评分
        val rentalYieldMin = defaultFilter.rental_yield_min?.toFloatOrNull() ?: 0.0f
        val rentalYieldMax = defaultFilter.rental_yield_max?.toFloatOrNull() ?: rentalYieldMaxValue
        val schoolScoreMin = defaultFilter.school_score_min?.toFloatOrNull() ?: 0.0f
        val schoolScoreMax = defaultFilter.school_score_max?.toFloatOrNull() ?: schoolScoreMaxValue

        MMKVUtils.saveFloat(isSale.toString() + "rental_yield_left", rentalYieldMin)
        MMKVUtils.saveFloat(isSale.toString() + "rental_yield_right", rentalYieldMax)
        MMKVUtils.saveFloat(isSale.toString() + "school_score_left", schoolScoreMin)
        MMKVUtils.saveFloat(isSale.toString() + "school_score_right", schoolScoreMax)

        MMKVUtils.saveStr(isSale.toString() + "init","true")
        MMKVUtils.saveStr(isSale.toString() + "description",descriptionStr)
        MMKVUtils.saveStr(isSale.toString() + "fee",max_maintenance_fee)
        MMKVUtils.saveInt(isSale.toString() + "rsb_feet_left",lot_front_feet_min.toInt())
        MMKVUtils.saveInt(isSale.toString() + "rsb_feet_right",lot_front_feet_max.toInt())
        MMKVUtils.saveInt(isSale.toString() + "rsb_square_left",square_footage_min.toInt())
        MMKVUtils.saveInt(isSale.toString() + "rsb_square_right",square_footage_max.toInt())

        if (isSale) {
            MMKVUtils.saveInt(isSale.toString() + "sale_sb_left",price_sale_min.toInt())
            MMKVUtils.saveInt(isSale.toString() + "sale_sb_right",price_sale_max.toInt())
            priceLeft =  price_sale_min.toInt()
            rightMaxValue = price_sale_max.toInt()
        } else {
            MMKVUtils.saveInt(isSale.toString() + "rental_sb_left",price_rent_min.toInt())
            MMKVUtils.saveInt(isSale.toString() + "rental_sb_right",price_rent_max.toInt())
            priceLeft =  price_rent_min.toInt()
            rightMaxValue = price_rent_max.toInt()
        }

        initSeekBar(binding, mcb)
    }

    private fun initSeekBar(
        binding: PopwindowMapFilterMoreBinding,
        mcb: Callback0?
    ) {
        val etDescription = binding.etDescription
        val etFee = binding.etFee
        val rsbFeet = binding.rsbFeet
        val rsbSquare = binding.rsbSquare
        val rsbLotSize = binding.rsbLotSize
        val rsbBuildingAge = binding.rsbBuildingAge

        description = MMKVUtils.getStr(isSale.toString() + "description") ?: ""
        fee = MMKVUtils.getStr(isSale.toString() + "fee") ?: "0"

        feetLeft = MMKVUtils.getInt(isSale.toString() + "rsb_feet_left", feetLeft)
        feetRight = MMKVUtils.getInt(isSale.toString() + "rsb_feet_right", feetRight)
        rsbFeet.setProgress(feetLeft.toFloat(), feetRight.toFloat())

        squareFootageLeft =
            MMKVUtils.getInt(isSale.toString() + "rsb_square_left", squareFootageLeft)
        squareFootageRight =
            MMKVUtils.getInt(isSale.toString() + "rsb_square_right", squareFootageRight)
        rsbSquare.setProgress(squareFootageLeft.toFloat(), squareFootageRight.toFloat())

        lotSizeLeft = MMKVUtils.getInt(isSale.toString() + "rsb_lot_size_left", lotSizeLeft)
        lotSizeRight = MMKVUtils.getInt(isSale.toString() + "rsb_lot_size_right", lotSizeRight)
        rsbLotSize.setProgress(lotSizeLeft.toFloat(), lotSizeRight.toFloat())

        buildingAgeLeft =
            MMKVUtils.getInt(isSale.toString() + "rsb_building_age_left", buildingAgeLeft)
        buildingAgeRight =
            MMKVUtils.getInt(isSale.toString() + "rsb_building_age_right", buildingAgeRight)
        rsbBuildingAge.setProgress(buildingAgeLeft.toFloat(), buildingAgeRight.toFloat())

        if (isSale) {
            val left = HSUtil.calRevertSquareFootageCurve(
                MMKVUtils.getInt(
                    isSale.toString() + "sale_sb_left",
                    priceLeft
                )
            )
            val right = HSUtil.calRevertSquareFootageCurve(
                MMKVUtils.getInt(
                    isSale.toString() + "sale_sb_right",
                    rightMaxValue
                )
            )

            binding.saleSb.setProgress(left.toFloat(), right.toFloat())
        } else {
            val left = HSUtil.calRevertSquareFootageCurveRenal(
                MMKVUtils.getInt(
                    isSale.toString() + "rental_sb_left",
                    priceLeft
                )
            )
            val right = HSUtil.calRevertSquareFootageCurveRenal(
                MMKVUtils.getInt(
                    isSale.toString() + "rental_sb_right",
                    rightMaxValue
                )
            )

            binding.rentalSb.setProgress(left.toFloat(), right.toFloat())
        }

        if (!TextUtils.isEmpty(description)) {
            etDescription.setText(description)
        } else {
            etDescription.setText("")
        }

        if (!TextUtils.isEmpty(fee)) {
            etFee.setText(fee)
        } else {
            etFee.setText("")
        }

        // 初始化租金回报率滑块
        initRentalYieldSeekBar(binding, mcb)

        // 初始化学校评分滑块
        initSchoolScoreSeekBar(binding, mcb)
    }

    /**
     * 初始化租金回报率滑块
     */
    private fun initRentalYieldSeekBar(binding: PopwindowMapFilterMoreBinding, mcb: Callback0?) {
        val rsbRentalYield = binding.rsbRentalYield
        val tvRentalYield = binding.tvRentalYield

        // 从缓存恢复数值
        rentalYieldLeft = MMKVUtils.getFloat(isSale.toString() + "rental_yield_left", rentalYieldLeft)
        rentalYieldRight = MMKVUtils.getFloat(isSale.toString() + "rental_yield_right", rentalYieldRight)

        // 设置滑块进度 (0-20对应0%-10%)
        rsbRentalYield.setProgress(rentalYieldLeft * 2, rentalYieldRight * 2)

        // 更新显示文本
        updateRentalYieldText(tvRentalYield)

        rsbRentalYield.setOnRangeChangedListener(object : OnRangeChangedListener {
            override fun onRangeChanged(
                view: RangeSeekBar?,
                leftValue: Float,
                rightValue: Float,
                isFromUser: Boolean
            ) {
                if (isFromUser) {
                    rentalYieldLeft = leftValue / 2 // 转换回0%-10%范围
                    rentalYieldRight = rightValue / 2

                    // 保存到缓存
                    MMKVUtils.saveFloat(isSale.toString() + "rental_yield_left", rentalYieldLeft)
                    MMKVUtils.saveFloat(isSale.toString() + "rental_yield_right", rentalYieldRight)

                    // 更新显示文本
                    updateRentalYieldText(tvRentalYield)
                }
            }

            override fun onStartTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {}

            override fun onStopTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
                mcb?.onData()
            }
        })
    }

    /**
     * 初始化学校评分滑块
     */
    private fun initSchoolScoreSeekBar(binding: PopwindowMapFilterMoreBinding, mcb: Callback0?) {
        val rsbSchoolScore = binding.rsbSchoolScore
        val tvSchoolScore = binding.tvSchoolScore

        // 从缓存恢复数值
        schoolScoreLeft = MMKVUtils.getFloat(isSale.toString() + "school_score_left", schoolScoreLeft)
        schoolScoreRight = MMKVUtils.getFloat(isSale.toString() + "school_score_right", schoolScoreRight)

        // 设置滑块进度 (0-20对应0-10)
        rsbSchoolScore.setProgress(schoolScoreLeft * 2, schoolScoreRight * 2)

        // 更新显示文本
        updateSchoolScoreText(tvSchoolScore)

        rsbSchoolScore.setOnRangeChangedListener(object : OnRangeChangedListener {
            override fun onRangeChanged(
                view: RangeSeekBar?,
                leftValue: Float,
                rightValue: Float,
                isFromUser: Boolean
            ) {
                if (isFromUser) {
                    schoolScoreLeft = leftValue / 2 // 转换回0-10范围
                    schoolScoreRight = rightValue / 2

                    // 保存到缓存
                    MMKVUtils.saveFloat(isSale.toString() + "school_score_left", schoolScoreLeft)
                    MMKVUtils.saveFloat(isSale.toString() + "school_score_right", schoolScoreRight)

                    // 更新显示文本
                    updateSchoolScoreText(tvSchoolScore)
                }
            }

            override fun onStartTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {}

            override fun onStopTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
                mcb?.onData()
            }
        })
    }

    /**
     * 更新租金回报率显示文本
     */
    private fun updateRentalYieldText(tvRentalYield: TextView) {
        val leftText = if (rentalYieldLeft == 0.0f) "0%" else String.format("%.1f%%", rentalYieldLeft)
        val rightText = if (rentalYieldRight >= rentalYieldMaxValue) "10%+" else String.format("%.1f%%", rentalYieldRight)
        tvRentalYield.text = "Rental Yield: $leftText - $rightText"
    }

    /**
     * 更新学校评分显示文本
     */
    private fun updateSchoolScoreText(tvSchoolScore: TextView) {
        val leftText = if (schoolScoreLeft == 0.0f) "0" else String.format("%.1f", schoolScoreLeft)
        val rightText = if (schoolScoreRight >= schoolScoreMaxValue) "10" else String.format("%.1f", schoolScoreRight)
        tvSchoolScore.text = "School Score: $leftText - $rightText"
    }
}