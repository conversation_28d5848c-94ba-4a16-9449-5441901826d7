package com.housesigma.android.ui.map.helper

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.text.TextUtils
import android.util.SparseArray
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.housesigma.android.R
import com.housesigma.android.databinding.*
import com.housesigma.android.model.House
import com.housesigma.android.model.MapMarkerInfo
import com.housesigma.android.model.SchoolInfo
import com.housesigma.android.ui.notinterested.NotInterestedHelper
import com.housesigma.android.ui.watched.WatchedHelper
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.MapUtils
import com.housesigma.android.utils.ScreenUtils
import com.housesigma.android.utils.log.Logger
import org.maplibre.android.annotations.Icon
import org.maplibre.android.annotations.IconFactory
import org.maplibre.android.annotations.Marker
import org.maplibre.android.annotations.MarkerOptions
import org.maplibre.android.geometry.LatLng
import org.maplibre.android.location.LocationComponentActivationOptions
import org.maplibre.android.location.LocationComponentOptions
import org.maplibre.android.location.modes.CameraMode
import org.maplibre.android.maps.MapLibreMap
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.lang.reflect.Type
import java.math.BigDecimal


open class MapHelper(
    context: Context,
    layoutInflater: LayoutInflater,
    MapLibreMap: MapLibreMap
) {

    private var context: Context = context
    private var layoutInflater: LayoutInflater = layoutInflater
    private var mapLibreMap: MapLibreMap = MapLibreMap
    private var viewedMarkerList: ArrayList<String> = ArrayList()

    companion object {
        const val MAP_FILTER_TAG = "mapFilterJsonV4"
        const val PRECON_MAP_FILTER_TAG = "preconMapFilterJsonV4"
        var lat1: Double = 45.76564085405049
        var lat2: Double = 42.61891628556286
        var lon1: Double = -78.58055638270632
        var lon2: Double = -81.01519252786112
        var zoom: Double = 8.0
    }

    /**
     * Consider using enums or defining constants to replace the raw string literals of markerType,
     * in order to reduce potential input errors and improve code clarity.
     */
    enum class MarkerType(val value: String) {
        SALE("sale"),
        SOLD("sold"),
        DELISTED("delisted"),
        NEARBY_SOLD("nearbysold");
    }

    init {
        val cacheViewedMarker = MMKVUtils.getStr("viewed_marker_list") ?: "[]"
        val type: Type = object : TypeToken<ArrayList<String?>?>() {}.type
        val list: List<String> = Gson().fromJson(cacheViewedMarker, type)
        viewedMarkerList.clear()
        viewedMarkerList.addAll(list)
    }

    @SuppressLint("MissingPermission")
    fun showMapboxLocationComponent(MapLibreMap: MapLibreMap){
        // DEV-7434 fix crash bug: Calling getSourceAs when a newer style is loading/has loaded.
        if (context==null) return
        if ((MapLibreMap.style?.isFullyLoaded != true)) return
        val activationOptions = MapLibreMap.style?.let {
            LocationComponentActivationOptions
                .builder(context, it)
                .locationComponentOptions(
                    LocationComponentOptions.builder(context)
                        .pulseEnabled(false)
                        .pulseAlpha(0f)
                        .accuracyColor(context.getColor(R.color.color_transparent))
                        .build()
                ).build()
        }

        val locationComponent = MapLibreMap.locationComponent

        if (locationComponent==null) return
        if (activationOptions==null) return
        locationComponent.activateLocationComponent(activationOptions)
        locationComponent.isLocationComponentEnabled = true
        locationComponent.cameraMode = CameraMode.TRACKING
    }


    @SuppressLint("MissingPermission")
    fun onDestroyMapboxLocationComponent(MapLibreMap: MapLibreMap?){
        try {
            // DEV-7434 Calling getSourceAs when a newer style is loading/has loaded.
            // DEV-7435 java.lang.NullPointerException: Attempt to invoke virtual method 'java.lang.Class java.lang.Object.getClass()' on a null object reference
            MapLibreMap?.locationComponent?.cameraMode = CameraMode.NONE
            MapLibreMap?.locationComponent?.isLocationComponentEnabled = false
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }

        try {
            MapLibreMap?.locationComponent?.onStop()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }

        try {
            MapLibreMap?.locationComponent?.onDestroy()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }


    /**
     * 用先进先出，最多存200个
     */
    private fun saveViewedMarkerList(ids: String) {
        if (!TextUtils.isEmpty(ids)) {
            val limitCacheSize = 200
            if (viewedMarkerList.size >= limitCacheSize) {
                viewedMarkerList.removeAt(limitCacheSize - 1)
            }
            viewedMarkerList.remove(ids)
            viewedMarkerList.add(0, ids)
            val json = Gson().toJson(viewedMarkerList)
            MMKVUtils.saveStr("viewed_marker_list", json)
        }
    }

    fun getCameraPositionZoom(MapLibreMap:MapLibreMap): Double {
        return BigDecimal(MapLibreMap.cameraPosition.zoom).setScale(1, BigDecimal.ROUND_HALF_UP).toDouble()
    }


    fun addEdmontonCenterMarker(): Marker {
        val iconFactory = IconFactory.getInstance(context)
        val binding = MapMarkerEdmontonCenterBinding.inflate(layoutInflater)
        val bitmap = MapUtils.convertViewToBitmap(binding.root)
        val fromResource = iconFactory.fromBitmap(bitmap)
        val options=  MarkerOptions()
            .icon(fromResource)
            .position(LatLng(53.5461245,-113.4938229))
        return mapLibreMap.addMarker(options)
    }

    suspend fun addFeatureMarker(markerInfo: MapMarkerInfo): Marker {
        val iconFactory = IconFactory.getInstance(context)
        val binding = MapMarkerFeatureBinding.inflate(layoutInflater)
        binding.tvNumber.text = markerInfo.label.toString()
        val options = withContext(Dispatchers.IO) {
            val bitmap = MapUtils.convertViewToNormalBitmap(binding.root)
            val fromResource = iconFactory.fromBitmap(bitmap)
            MarkerOptions()
                .icon(fromResource)
                .position(LatLng(markerInfo.location.lat, markerInfo.location.lon))
        }
        return mapLibreMap.addMarker(options)
    }

    fun updateFeatureMarker(
        markerInfo: MapMarkerInfo,
        marker: Marker,
        isDeSelect: Boolean = false
    ) {
        if (isDeSelect) {
            val iconFactory = IconFactory.getInstance(context)
            val binding = MapMarkerFeatureBinding.inflate(layoutInflater)
            binding.tvNumber.text = markerInfo.label.toString()
            val bitmap = MapUtils.convertViewToNormalBitmap(binding.root)
            val fromResource = iconFactory.fromBitmap(bitmap)
            marker.icon = fromResource
            mapLibreMap.updateMarker(marker)
        } else {
            val iconFactory = IconFactory.getInstance(context)
            val binding = MapMarkerFeatureSelectedBinding.inflate(layoutInflater)
            binding.tvNumber.text = markerInfo.label.toString()
            val bitmap = MapUtils.convertViewToNormalBitmap(binding.root)
            val fromResource = iconFactory.fromBitmap(bitmap)
            marker.icon = fromResource
            mapLibreMap.updateMarker(marker)
        }
    }

    /**
     * Generic method for handling markers, used to create or update different types of markers
     * @param markerInfo Information about the marker
     * @param marker Existing marker, null means creating a new marker
     * @param isDeSelect Whether to deselect the marker
     * @param markerType Type of marker, such as "sale", "sold", "delisted"
     * @return Created or updated marker
     */
    private suspend fun processMarker(
        markerInfo: MapMarkerInfo,
        marker: Marker? = null,
        isDeSelect: Boolean = false,
        markerType: MarkerType
    ): Marker {
        val ids = markerInfo.ids.toString()
        val resID: Int
        val isNotInterested = NotInterestedHelper.findNotInterestedByListingId(ids)
        var isWatched = false
        if (markerInfo.ids.size == 1) {
            markerInfo.ids.getOrNull(0)?.let {
                isWatched = WatchedHelper.findWatchedByListingId(it)
            }
        }

        if (isDeSelect || marker == null) {
            val isViewed = viewedMarkerList.contains(ids)
            resID = when (markerType) {
                MarkerType.SALE -> when {
                    isNotInterested -> R.layout.map_marker_not_interested
                    isViewed && isWatched -> R.layout.map_marker_sale_watched_viewed
                    isViewed -> R.layout.map_marker_blue_viewed
                    isWatched -> R.layout.map_marker_sale_watched
                    else -> R.layout.map_marker_sale
                }
                MarkerType.SOLD -> when {
                    isNotInterested -> R.layout.map_marker_not_interested
                    isViewed && isWatched -> R.layout.map_marker_sold_watched_viewed
                    isViewed -> R.layout.map_marker_sold_viewed
                    isWatched -> R.layout.map_marker_sold_watched
                    else -> R.layout.map_marker_sold
                }
                MarkerType.DELISTED -> when {
                    isNotInterested -> R.layout.map_marker_not_interested
                    isViewed && isWatched -> R.layout.map_marker_de_listed_watched_viewed
                    isViewed -> R.layout.map_marker_de_listed_viewed
                    isWatched -> R.layout.map_marker_de_listed_watched
                    else -> R.layout.map_marker_de_listed
                }
                MarkerType.NEARBY_SOLD -> when (markerInfo.nearZoom) {
                    2 -> R.layout.map_marker_near_by_sold
                    1 -> R.layout.map_marker_near_by_sold_middle_point
                    else -> R.layout.map_marker_near_by_sold_small_point // < 17 >= 16
                }
            }
        } else {
            resID = when (markerType) {
                MarkerType.SALE -> when {
                    isNotInterested -> R.layout.map_marker_de_listed_not_interested_selected
                    isWatched -> R.layout.map_marker_sold_watched_selected
                    else -> R.layout.map_marker_blue_selected
                }
                MarkerType.SOLD -> when {
                    isNotInterested -> R.layout.map_marker_de_listed_not_interested_selected
                    isWatched -> R.layout.map_marker_sold_watched_selected
                    else -> R.layout.map_marker_sold_selected
                }
                MarkerType.DELISTED -> when {
                    isNotInterested -> R.layout.map_marker_de_listed_not_interested_selected
                    isWatched -> R.layout.map_marker_sold_watched_selected
                    else -> R.layout.map_marker_de_listed_selected
                }
                MarkerType.NEARBY_SOLD -> when (markerInfo.nearZoom) {
                    2 -> R.layout.map_marker_near_by_sold_selected
                    1 -> R.layout.map_marker_near_by_sold_middle_point_select
                    else -> R.layout.map_marker_near_by_sold_small_point_select // < 17 >= 16
                }
            }
            
            // Only regular types of markers need to record view status
            if (markerType != MarkerType.NEARBY_SOLD) {
                saveViewedMarkerList(markerInfo.ids.toString())
            }
        }

        val iconFactory = IconFactory.getInstance(context)
        val view = LayoutInflater.from(context).inflate(resID, null)
        val tvNumber = view.findViewById<TextView>(R.id.tv_number)
        tvNumber?.text = markerInfo.label.toString()

        marker?.let {
            // Create Bitmap and Icon in background thread
            val icon = withContext(Dispatchers.IO) {
                val bitmap = if (isNotInterested) {
                    MapUtils.convertViewToNormalBitmap(view)
                } else {
                    MapUtils.convertViewToBitmap(view)
                }
                iconFactory.fromBitmap(bitmap)
            }
            
            // Update Marker in main thread
            marker.icon = icon
            mapLibreMap.updateMarker(marker)
            return marker
        }

        // Create new Marker
        val options = withContext(Dispatchers.IO) {
            val bitmap = if (isNotInterested) {
                MapUtils.convertViewToNormalBitmap(view)
            } else {
                MapUtils.convertViewToBitmap(view)
            }
            MarkerOptions()
                .icon(iconFactory.fromBitmap(bitmap))
                .position(LatLng(markerInfo.location.lat, markerInfo.location.lon))
        }
        return mapLibreMap.addMarker(options)
    }

    suspend fun addSaleMarker(markerInfo: MapMarkerInfo): Marker {
        return processMarker(markerInfo, markerType = MarkerType.SALE)
    }

    suspend fun updateSaleMarker(
        markerInfo: MapMarkerInfo,
        marker: Marker? = null,
        isDeSelect: Boolean = false
    ): Marker {
        return processMarker(markerInfo, marker, isDeSelect, MarkerType.SALE)
    }

    suspend fun addSoldMarker(markerInfo: MapMarkerInfo): Marker {
        return processMarker(markerInfo, markerType = MarkerType.SOLD)
    }

    suspend fun updateSoldMarker(
        markerInfo: MapMarkerInfo,
        marker: Marker? = null,
        isDeSelect: Boolean = false
    ): Marker {
        return processMarker(markerInfo, marker, isDeSelect, MarkerType.SOLD)
    }

    suspend fun addDeListedMarker(markerInfo: MapMarkerInfo): Marker {
        return processMarker(markerInfo, markerType = MarkerType.DELISTED)
    }

    suspend fun updateDeListedMarker(
        markerInfo: MapMarkerInfo,
        marker: Marker? = null,
        isDeSelect: Boolean = false
    ): Marker {
        return processMarker(markerInfo, marker, isDeSelect, MarkerType.DELISTED)
    }

    @SuppressLint("MissingInflatedId")
    suspend fun updateNearBySoldMarker(
        markerInfo: MapMarkerInfo,
        marker: Marker? = null,
        isDeSelect: Boolean = false,
    ): Marker {
        return processMarker(markerInfo, marker, isDeSelect, MarkerType.NEARBY_SOLD)
    }

    suspend fun addNearBySoldMarker(markerInfo: MapMarkerInfo): Marker {
        return processMarker(markerInfo, markerType = MarkerType.NEARBY_SOLD)
    }


    suspend fun addSchoolMarker(markerInfo: SchoolInfo): Marker {
        val iconFactory = IconFactory.getInstance(context)
        val binding = MapMarkerSchoolBinding.inflate(layoutInflater)
        if (markerInfo.score != 0.0) {
            binding.tvNumber.text = markerInfo.score.toString()
        }
        val options = MarkerOptions()
        withContext(Dispatchers.IO) {
            val bitmap = MapUtils.convertViewToNormalBitmap(binding.root)
            val fromResource = iconFactory.fromBitmap(bitmap)
            options.icon(fromResource)
                .position(LatLng(markerInfo.lat, markerInfo.lng))
        }
        return mapLibreMap.addMarker(options)
    }

    fun updateSchoolMarker(markerInfo: SchoolInfo, marker: Marker, isDeSelect: Boolean = false) {
        if (isDeSelect) {
            val iconFactory = IconFactory.getInstance(context)
            val binding = MapMarkerSchoolBinding.inflate(layoutInflater)
            if (markerInfo.score != 0.0) {
                binding.tvNumber.text = markerInfo.score.toString()
            }
            val bitmap = MapUtils.convertViewToNormalBitmap(binding.root)
            val fromResource = iconFactory.fromBitmap(bitmap)
            marker.icon = fromResource
            mapLibreMap.updateMarker(marker)
        } else {
            val iconFactory = IconFactory.getInstance(context)
            val binding = MapMarkerSchoolSelectedBinding.inflate(layoutInflater)
            if (markerInfo.score != 0.0) {
                binding.tvNumber.text = markerInfo.score.toString()
            }
            val bitmap = MapUtils.convertViewToNormalBitmap(binding.root)
            val fromResource = iconFactory.fromBitmap(bitmap)
            marker.icon = fromResource
            mapLibreMap.updateMarker(marker)
        }

    }

    private val markerViewCache = SparseArray<TextView>()

    private fun getCachedTextView(size: Int): TextView {
        return markerViewCache[size] ?: MapMarkerNormalBinding.inflate(layoutInflater).tvNumber.apply {
            markerViewCache.put(size, this)
        }
    }

    /**
     * 几个房源聚集成一个点的
     */
    suspend fun addGroupMarker(markerInfo: MapMarkerInfo): Marker {
        val iconFactory = IconFactory.getInstance(context)
        val markerCount = markerInfo.count
        val groupMarkerHeight = when {
            markerCount >= 10000 -> {
                ScreenUtils.dpToPx(68f).toInt()
            }

            markerCount >= 1000 -> {
                ScreenUtils.dpToPx(52f).toInt()
            }

            markerCount >= 100 -> {
                ScreenUtils.dpToPx(43f).toInt()
            }

            else -> {
                ScreenUtils.dpToPx(35f).toInt()
            }
        }
        val textView = getCachedTextView(groupMarkerHeight).apply {
            width = groupMarkerHeight
            height = groupMarkerHeight
            text = markerCount.toString()
        }
        val options = MarkerOptions()
            .position(LatLng(markerInfo.location.lat, markerInfo.location.lon))
        val bitmap = MapUtils.convertViewToNormalBitmap(textView)
        val fromResource = iconFactory.fromBitmap(bitmap)
        options.icon(fromResource)
        return mapLibreMap.addMarker(options)
    }


    /**
     * 几个房源聚集成一个点的
     */
    suspend fun updateGroupMarker(
        markerInfo: MapMarkerInfo,
        marker: Marker,
        isDeSelect: Boolean = false
    ) {
        val iconFactory = IconFactory.getInstance(context)
        if (isDeSelect) {
            var markerNormalBinding = MapMarkerNormalBinding.inflate(layoutInflater)
            val markerCount = markerInfo.count
            when {
                markerCount >= 10000 -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(68f).toInt(),
                            ScreenUtils.dpToPx(68f).toInt()
                        )
                }
                markerCount >= 1000 -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(52f).toInt(),
                            ScreenUtils.dpToPx(52f).toInt()
                        )
                }
                markerCount >= 100 -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(43f).toInt(),
                            ScreenUtils.dpToPx(43f).toInt()
                        )
                }
                else -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(35f).toInt(),
                            ScreenUtils.dpToPx(35f).toInt()
                        )
                }
            }

            markerNormalBinding.tvNumber.text = markerInfo.count.toString()

            val fromResource: Icon
            withContext(Dispatchers.IO) {
                val bitmap = MapUtils.convertViewToNormalBitmap(markerNormalBinding.root)
                fromResource = iconFactory.fromBitmap(bitmap)
            }
            marker.icon = fromResource
            mapLibreMap.updateMarker(marker)
        } else {
            var markerNormalBinding = MapMarkerNormalSelectedBinding.inflate(layoutInflater)
            val markerCount = markerInfo.count
            when {
                markerCount >= 10000 -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(68f).toInt(),
                            ScreenUtils.dpToPx(68f).toInt()
                        )
                }
                markerCount >= 1000 -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(52f).toInt(),
                            ScreenUtils.dpToPx(52f).toInt()
                        )
                }
                markerCount >= 100 -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(43f).toInt(),
                            ScreenUtils.dpToPx(43f).toInt()
                        )
                }
                else -> {
                    markerNormalBinding.tvNumber.layoutParams =
                        RelativeLayout.LayoutParams(
                            ScreenUtils.dpToPx(35f).toInt(),
                            ScreenUtils.dpToPx(35f).toInt()
                        )
                }
            }

            markerNormalBinding.tvNumber.text = markerInfo.count.toString()
            val fromResource: Icon
            withContext(Dispatchers.IO) {
                val bitmap = MapUtils.convertViewToNormalBitmap(markerNormalBinding.root)
                fromResource = iconFactory.fromBitmap(bitmap)
            }
            marker.icon = fromResource
            mapLibreMap.updateMarker(marker)
        }

    }


//    DEV-4998 View in Full Map on Listings 逻辑
    fun addViewInFullMapMarker(context:Context,MapLibreMap:MapLibreMap,house: House): Marker {
        val iconFactory = IconFactory.getInstance(context)
        val binding = MapMarkerViewInFullMapBinding.inflate(layoutInflater)
        binding.tvNumber.text = house.marker_label ?: ""
        val bitmap = MapUtils.convertViewToBitmap(binding.root)
        val fromResource = iconFactory.fromBitmap(bitmap)
        val options = MarkerOptions()
            .icon(fromResource)
            .position(LatLng(house.map.lat, house.map.lon))
        return MapLibreMap.addMarker(options)
    }




    /**
     * 剔除掉同View in Full map类型相同经纬度，相同id_listing的marker
     */
    fun removeDuplicateMarkers(hashMarker: Map<Long, MapMarkerInfo>, idListing: String) {
        hashMarker.values
            .filter { it.ids.size == 1 && it.ids.contains(idListing) }
            .mapNotNull { it.localMarker }
            .forEach { mapLibreMap.removeMarker(it) }
    }


    /**
     * 判断View in Full map类型是否和其他类型的marker是相同的
     */
    fun isSameLocationAndIds(
        hashMapLocationInfo: Map<Long, String>,
        markerInfo: MapMarkerInfo
    ): Boolean {
        if (markerInfo.ids==null) return false
        if (markerInfo.ids.size != 1) return false
        return hashMapLocationInfo.values.any { value -> markerInfo.ids.contains(value) }
    }


    fun getZoomLevelRange(zoomLevel: Double): Pair<Double, Double> {
        return when {
            zoomLevel < 4 -> Pair(0.0, 4.0) //只有4
            zoomLevel <= 5 -> Pair(4.1, 5.0)
            zoomLevel <= 6 -> Pair(5.1, 6.0)
            zoomLevel <= 7 -> Pair(6.1, 7.0)
            zoomLevel <= 8 -> Pair(7.1, 8.0)
            zoomLevel <= 9 -> Pair(8.1, 9.0)
            zoomLevel <= 10 -> Pair(9.1, 10.0)
            zoomLevel <= 11 -> Pair(10.1, 11.0)
            zoomLevel <= 12 -> Pair(11.1, 12.0)
            zoomLevel <= 13 -> Pair(12.1, 13.0)
            zoomLevel <= 14 -> Pair(13.1, 14.0)
            zoomLevel <= 15 -> Pair(14.1, 15.0)
            else -> Pair(0.0, 0.0)
        }
    }

    /**
     * 上次与这次，相同级别的zoom，继续加载marker，不同的zoom 抛弃掉
     */
    fun areZoomLevelsInSameRange(zoom1: Double, zoom2: Double): Boolean {
        val range1 = getZoomLevelRange(zoom1)
        val range2 = getZoomLevelRange(zoom2)
        if (range1.first == 0.0 || range2.first == 0.0) { // 这种情况直接过滤掉，不应该处理 比较稀疏了
            return true
        }
        return range1 == range2
    }
}