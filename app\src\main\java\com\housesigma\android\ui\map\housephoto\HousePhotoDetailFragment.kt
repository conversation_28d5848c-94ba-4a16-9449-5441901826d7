package com.housesigma.android.ui.map.housephoto

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.drawable.toBitmap
import androidx.fragment.app.Fragment
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.housesigma.android.databinding.ActivityHousePhotoDetailBinding
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.FingerDragHelper
import com.housesigma.android.views.FingerDragHelper.OnExitListener
import com.housesigma.android.views.subsampling.ImageSource
import com.housesigma.android.views.subsampling.SubsamplingScaleImageView
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


class HousePhotoDetailFragment : Fragment() {
    private var _binding: ActivityHousePhotoDetailBinding? = null
    private val binding get() = _binding!!

    companion object {
        private const val ARG_PHOTO_URL = "photo_url"

        fun newInstance(url: String): HousePhotoDetailFragment {
            val fragment = HousePhotoDetailFragment()
            val args = Bundle()
            args.putString(ARG_PHOTO_URL, url)
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = ActivityHousePhotoDetailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initData()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.fingerDragHelper.setOnExitListener(null)
        binding.fingerDragHelper.setOnAlphaChangeListener(null)
        binding.photoView.setOnImageEventListener(null)
        _binding = null
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onResetScaleEvent(event: ResetScaleEvent) {
        Logger.d("HousePhotoDetailFragment - Received ResetScaleEvent")
        binding.photoView.resetScaleAndCenter()
    }

    private fun initView() {
        binding.fingerDragHelper.setOnExitListener(object : OnExitListener {
            override fun onExit() {
                activity?.finish()
            }
        })
        binding.fingerDragHelper.background.alpha = 255
        binding.fingerDragHelper.setOnAlphaChangeListener(object :
            FingerDragHelper.onAlphaChangedListener {
            override fun onTranslationYChanged(event: MotionEvent?, translationY: Float) {
                val percent: Float = translationY / 1000
                var mAlpha = (255 * (1 - percent)).toInt()
                if (mAlpha > 255) {
                    mAlpha = 255
                } else if (mAlpha < 0) {
                    mAlpha = 0
                }
                binding.fingerDragHelper.background.alpha = mAlpha
            }
        })

        // Show loading indicator
        binding.loadingContainer.visibility = View.VISIBLE
    }

    private fun initData() {
        val url = arguments?.getString(ARG_PHOTO_URL)
        if (url != null) {
            Glide.with(this)
                .load(url)
                .listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>?,
                        isFirstResource: Boolean
                    ): Boolean {
                        // Hide loading indicator on failure
                        _binding?.loadingContainer?.visibility = View.GONE
                        return false
                    }

                    override fun onResourceReady(
                        resource: Drawable?,
                        model: Any?,
                        target: Target<Drawable>?,
                        dataSource: DataSource?,
                        isFirstResource: Boolean
                    ): Boolean {
                        if (_binding == null) return false
                        
                        val photoView = binding.photoView
                        photoView.orientation = SubsamplingScaleImageView.ORIENTATION_USE_EXIF
                        photoView.setOnImageEventListener(object :
                            SubsamplingScaleImageView.OnImageEventListener {
                            override fun onReady() {
                                if (_binding == null) return
                                
                                val scale4X = photoView.scale * 4
                                val scale8X = photoView.scale * 8
                                photoView.maxScale = scale8X
                                photoView.setDoubleTapZoomScale(scale4X)
                                
                                // Hide loading indicator when image is ready
                                binding.loadingContainer.visibility = View.GONE
                            }

                            override fun onImageLoaded() {
                            }

                            override fun onPreviewLoadError(e: Exception?) {
                                // Hide loading indicator on preview load error
                                _binding?.loadingContainer?.visibility = View.GONE
                            }

                            override fun onImageLoadError(e: Exception?) {
                                // Hide loading indicator on image load error
                                _binding?.loadingContainer?.visibility = View.GONE
                            }

                            override fun onTileLoadError(e: Exception?) {
                            }

                            override fun onPreviewReleased() {
                            }
                        })
                        resource?.let {
                            val copyBitmap = it.toBitmap().copy(Bitmap.Config.RGB_565, true)
                            photoView.setImage(ImageSource.bitmap(copyBitmap))
                        }
                        return false
                    }

                })
                .preload()


        } else {
            ToastUtils.showLong("url is null!")
            // Hide loading indicator if URL is null
            binding.loadingContainer.visibility = View.GONE
        }
    }


}