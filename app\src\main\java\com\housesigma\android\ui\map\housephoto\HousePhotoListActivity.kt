package com.housesigma.android.ui.map.housephoto

import android.content.Intent
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.View
import android.widget.LinearLayout
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityHousePhotoListBinding
import com.housesigma.android.databinding.HeaderHousePhotoListBinding
import com.housesigma.android.model.*
import com.housesigma.android.ui.home.HomeViewModel
import com.housesigma.android.ui.listing.ContactDialogType
import com.housesigma.android.ui.listing.ContactUsWithAgentFragment
import com.housesigma.android.ui.listing.JoinUsFragment
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.watched.WatchedViewModel
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.views.HSWebView
import com.housesigma.android.views.SelectWatchListDialog
import com.lxj.xpopup.XPopup
import me.jessyan.autosize.AutoSizeConfig
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class HousePhotoListActivity : BaseActivity(), LoginFragment.LoginCallback {

    private lateinit var binding: ActivityHousePhotoListBinding
    private lateinit var headerBinding: HeaderHousePhotoListBinding
    private lateinit var homeViewModel: HomeViewModel
    private lateinit var watchedViewModel: WatchedViewModel
    private var idListing: String? = null
    private var loginDialog: LoginFragment? = null
    private val adapter = PhotoAdapter()
//    private var watchlist:MultipleWatchList? = null
    private var webView: HSWebView? = null
    private var selectWatchListDialog: SelectWatchListDialog? = null

    private var selectWatchlists: MultipleWatchList? = null
    private val orderPhotoList = ArrayList<OrderPhotoModel>()

    override fun getLayout(): Any {
        binding = ActivityHousePhotoListBinding.inflate(layoutInflater)
        val animator = binding.rv.itemAnimator as SimpleItemAnimator
        animator.supportsChangeAnimations = false
        animator.addDuration = 0
        animator.removeDuration = 0
        animator.moveDuration = 0
        animator.changeDuration = 0
        return binding.root
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
        ContactUsWithAgentFragment.destroyTrustPilotView()
        JoinUsFragment.destroyTrustPilotView()
    }

    override fun onResume() {
        super.onResume()
        GALog.page("listing_photos")
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)

            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }

        binding.rv.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        binding.rv.adapter = adapter

        binding.ivClose.setOnClickListener {
            finish()
        }

        adapter.addChildClickViewIds(
            R.id.rl
        )
        adapter.setOnItemChildClickListener { adapter, view, position ->
            when (view.id) {
                R.id.rl -> {
                    AutoSizeConfig.getInstance().stop(this)
                    val intent = Intent(this, HousePhotoListDetailActivity::class.java)
                    intent.putExtra("photoList", orderPhotoList)
                    intent.putExtra("position", position)
                    startActivity(intent)
                    overridePendingTransition(R.anim.fade_in, R.anim.fade_out)
                }
            }
        }

        headerBinding = HeaderHousePhotoListBinding.inflate(layoutInflater)
        adapter.addHeaderView(
            headerBinding.root,
            orientation = LinearLayout.VERTICAL
        )


    }

    override fun initData() {
        idListing = intent.getStringExtra("id_listing")
        if (idListing == null) {
            ToastUtils.showLong("id_listing is null!")
            finish()
        }
        homeViewModel = ViewModelProvider(this).get(HomeViewModel::class.java)
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)

        watchedViewModel.updateWatchlistUpdateListingsMsg.observe(this) {
            selectWatchListDialog?.dismiss()
            ToastUtils.showLong(it.message)
            //理论上需要刷新是房源详情页的页面，这里先把webview全部刷新
            EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_PAGE_TOS_UPDATED))
            getListingInfoPhotos()
            getUserWatchlistList()
        }

        homeViewModel.housePhotos.observe(this) {
            it.meta?.let {
                handleShareClick(it)
            }

            handleHouseStatus(it)
            if (adapter.data.isEmpty()) {
                handleHousePhotos(it)
            }
            binding.llBottomTool.visibility = View.VISIBLE
        }

        watchedViewModel.multipleWatchList.observe(this) {
            selectWatchlists = it
            if (selectWatchListDialog?.isShow==true) {
                selectWatchListDialog?.notifyDataSetChangedAndAutoSelectTop1(selectWatchlists)
            }
        }

        getUserWatchlistList()
        getListingInfoPhotos()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        when (event.type) {

            MessageType.WATCHED_MULTIPLE_WATCHLIST_NEWED -> {
                getUserWatchlistList()
            }

            else -> {}
        }
    }

    private fun getUserWatchlistList() {
//      DEV-7057 未登录状态下进入图片页会提示需要登录，未登录时不需要调用以下api
        if (!LoginFragment.isLogin()) return
        idListing?.let { watchedViewModel.getMultipleWatchList(it) }
    }

    private fun handleShareClick(meta: PhotoMeta){
        val shareContent = meta.title_share + " " + meta.url_share_android
        binding.ivShare.setOnClickListener {
            GALog.log("page_share_click","listing_photos")
            HSUtil.share(
                this@HousePhotoListActivity,
                shareContent
            )
        }
    }


    private fun handleHousePhotos(photosInfo: HousePhotosInfo) {
        orderPhotoList.clear()
        val photoList = photosInfo.picture.photo_list
        val thumbPhotoList = photosInfo.picture.thumb_photo_list
        val size = minOf(photoList.size, thumbPhotoList.size)
        for (i in 0 until size) {
            orderPhotoList.add(OrderPhotoModel(photoList[i], thumbPhotoList[i]))
        }
        adapter.data.clear()
        adapter.addData(orderPhotoList)
        imageOrderLoad(orderPhotoList)
    }

    private fun handleHouseStatus(photosInfo: HousePhotosInfo) {
        if (photosInfo?.list_status?.isForSellType() == true) {
            if (photosInfo?.list_status?.isWatched() == true) {
                binding.tvLeft.text = "Watched"
                binding.rlLeft.setBackgroundResource(R.drawable.shape_10radius_broder_main_color_fill_cyan)
                binding.tvLeft.setCompoundDrawablesWithIntrinsicBounds(
                    resources.getDrawable(R.drawable.ic_listing_sold_watched),
                    null,
                    null,
                    null
                )
            } else {
                binding.tvLeft.text = "Sold Watch"
                binding.rlLeft.setBackgroundResource(R.drawable.shape_10radius_broder_main_color_fill_white)
                binding.tvLeft.setCompoundDrawablesWithIntrinsicBounds(
                    resources.getDrawable(R.drawable.ic_listing_sold_watch),
                    null,
                    null,
                    null
                )
            }

            binding.tvRightBtn.text = getString(R.string.schedule_viewing)
        } else {
            if (photosInfo?.list_status?.isWatched() == true) {
                binding.tvLeft.text = "Saved"
                binding.rlLeft.setBackgroundResource(R.drawable.shape_10radius_broder_main_color_fill_cyan)
                binding.tvLeft.setCompoundDrawablesWithIntrinsicBounds(
                    resources.getDrawable(R.drawable.ic_listing_saved),
                    null,
                    null,
                    null
                )
            } else {
                binding.tvLeft.text = "Save"
                binding.rlLeft.setBackgroundResource(R.drawable.shape_10radius_broder_main_color_fill_white)
                binding.tvLeft.setCompoundDrawablesWithIntrinsicBounds(
                    resources.getDrawable(R.drawable.ic_listing_save),
                    null,
                    null,
                    null
                )
            }

            binding.tvRightBtn.text = getString(R.string.contact_agent)
        }

        if (photosInfo?.list_status?.isWatched() == true) {
            photosInfo?.ml_num?.let { ml_num ->
                binding.btnLeft.setOnClickListener {
                    if (LoginFragment.isLogin()) {
                        idListing?.let { idListingIt ->
                            // 取消关注 -> 传递空白array，后端删除该房源所有watchlist关注数据

                            selectWatchlists?.let {
                                if (it.size <= 1) {
                                    watchedViewModel.updateWatchlistUpdateListings(
                                        idListingIt,
                                        ml_num,
                                        arrayListOf(),
                                        "watch_listing_click"
                                    )
                                } else {
                                    selectWatchListDialog = SelectWatchListDialog(
                                        idListingIt,
                                        ml_num,
                                        selectWatchlists,
                                        this,
                                        object :
                                            SelectWatchListDialog.NewWatchListCallback {
                                            override fun onSuccess(
                                                id_listing: String,
                                                ml_num: String,
                                                ids_user_watchlist: List<String>
                                            ) {
                                                watchedViewModel.updateWatchlistUpdateListings(
                                                    id_listing,
                                                    ml_num,
                                                    ids_user_watchlist,
                                                    "watchlists_actions"
                                                )
                                            }

                                            override fun onCancel() {
                                                getListingInfoPhotos()
                                            }
                                        })
                                    XPopup.Builder(this)
                                        .enableDrag(false)
                                        .asCustom(selectWatchListDialog)
                                        .show()
                                }
                            }
                        }
                    } else {
                        showLoginDialog()
                    }
                }
            }
        } else {
            photosInfo?.ml_num?.let { ml_num ->
                binding.btnLeft.setOnClickListener {
                    if (LoginFragment.isLogin()) {
                        idListing?.let { idListing ->
//                            watchedViewModel.addWatchlistUpdateListings(idListing, ml_num, listOf(""))
                            selectWatchlists?.let {
                                if (it.size == 1) {
                                    watchedViewModel.updateWatchlistUpdateListings(
                                        idListing,
                                        ml_num,
                                        listOf(it[0].id),
                                        "watch_listing_click"
                                    )
                                } else {
                                    selectWatchListDialog = SelectWatchListDialog(
                                        idListing,
                                        ml_num,
                                        selectWatchlists,
                                        this,
                                        object :
                                            SelectWatchListDialog.NewWatchListCallback {
                                            override fun onSuccess(
                                                id_listing: String,
                                                ml_num: String,
                                                ids_user_watchlist: List<String>
                                            ) {
                                                watchedViewModel.updateWatchlistUpdateListings(
                                                    id_listing,
                                                    ml_num,
                                                    ids_user_watchlist,
                                                    "watchlists_actions"
                                                )
                                            }

                                            override fun onCancel() {
                                                getListingInfoPhotos()
                                            }
                                        })
                                    XPopup.Builder(this)
                                        .enableDrag(false)
                                        .asCustom(selectWatchListDialog)
                                        .show()
                                }
                            }
                        }
                    } else {
                        showLoginDialog()
                    }
                }
            }
        }

        if (!TextUtils.isEmpty(photosInfo.picture.virtual_tour)) {
            headerBinding.tvVirtualTour.setOnClickListener {
                GALog.log("virtual_tour_click")
                WebViewHelper.jumpInnerWebView(
                    this,
                    photosInfo.picture.virtual_tour,
                    hasTool = true
                )
            }
            headerBinding.tvVirtualTour.visibility = View.VISIBLE
        } else {
            headerBinding.tvVirtualTour.visibility = View.GONE
        }





        binding.tvRightBtn.setOnClickListener {
            val label: String = if (photosInfo.bind_agent_user?.name!=null) {
                "listing_photos"
            } else {
                "transaction_hs_agent"
            }

            GALog.log("contact_agent_click", label)
            if (photosInfo.agentAvailable()) {
                // DEV-5646 分为2种情况，有bind agent和无bind agent
                var message = ""
                var tag: String? = null
                var idAgent: Int? = null
                if (label == "listing_photos") {
                    message = if (photosInfo.list_status.isForSellType())
                        (photosInfo.schedule_message?:"") else (photosInfo.contact_message?:"")
                    tag = null
                    idAgent = photosInfo.bind_agent_user?.id
                } else if (label =="transaction_hs_agent") {
                    message = photosInfo.transactions?.contact_agent_message?:""
                    tag = photosInfo.transactions?.contact_agent_tag
                    idAgent = photosInfo.transactions?.agent?.id
                }

                ContactUsWithAgentFragment.newInstance(
                    if (photosInfo.list_status.isForSellType())
                        ContactDialogType.SCHEDULE else ContactDialogType.CONTACT,
                    message,
                    photosInfo.ml_num?:"",
                    idListing,
                    photosInfo.bind_agent_user?.name?:photosInfo.transactions?.agent?.name?:"",
                    photosInfo.bind_agent_user?.picture?:photosInfo.transactions?.agent?.picture?:"",
                    "",
                    photosInfo.show_trustpilot?:false,
                    photosInfo.bind_agent_user?.slug?:photosInfo.transactions?.agent?.slug,
                    gaHsLabel = label, tag = tag,idAgent = idAgent)
                    .show(supportFragmentManager, "")
            } else {
                JoinUsFragment.newInstance(
                    photosInfo.isForSellType(),
                    photosInfo.municipality_name,
                    photosInfo.show_trustpilot?:false).show(supportFragmentManager, "")
            }
        }

        if (photosInfo.show_trustpilot == true) {
            if (photosInfo.agentAvailable()) {
                ContactUsWithAgentFragment.loadTrustPilotView()
            } else {
                JoinUsFragment.loadTrustPilotView()
            }
        }
    }

    private fun getListingInfoPhotos() {
        idListing?.let {
            homeViewModel.getListingInfoPhotos(it)
        }
    }


    /**
     * 递归方式依次加载图片
     */
    private fun imageOrderLoad(photoList: List<OrderPhotoModel>, position: Int = 0) {
        if (this.isFinishing) return
        if (position == photoList.size) return
        val item = photoList[position]
        Glide.with(this)
            .load(item.photoUrlThumb)
            .listener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>?,
                    isFirstResource: Boolean
                ): Boolean {
                    downImageAndLoadNext()
                    return false
                }

                override fun onResourceReady(
                    resource: Drawable?,
                    model: Any?,
                    target: Target<Drawable>?,
                    dataSource: DataSource?,
                    isFirstResource: Boolean
                ): Boolean {
                    downImageAndLoadNext()
                    return false
                }

                private fun downImageAndLoadNext() {
//                    item.isLoaded = true
                    adapter.notifyItemChanged(position + 1)
                    imageOrderLoad(photoList, position + 1)
                }
            })
            .preload()

    }


    private fun showLoginDialog() {
        GALog.log("login_button_click")
        supportFragmentManager.let {
            if (loginDialog == null) {
                loginDialog = LoginFragment()
            }
            if (loginDialog?.isAdded == true) return
            loginDialog?.show(it, "")
        }
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
        //DEV-7057 重新登录后，需要拉取当前用户的收藏状态、watchlist list
        getListingInfoPhotos()
        getUserWatchlistList()
    }


}