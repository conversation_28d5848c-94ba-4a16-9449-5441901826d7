package com.housesigma.android.ui.map.housephoto

import android.annotation.SuppressLint
import android.app.Activity
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.content.res.TypedArray
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.RelativeLayout
import androidx.viewpager.widget.ViewPager
import com.github.chrisbanes.photoview.PhotoView
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityHousePhotoListDetailBinding
import com.housesigma.android.model.OrderPhotoModel
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.ScreenUtils
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.subsampling.SubsamplingScaleImageView
import me.jessyan.autosize.AutoSizeCompat
import me.jessyan.autosize.AutoSizeConfig
import org.greenrobot.eventbus.EventBus
import java.lang.reflect.Field
import java.lang.reflect.Method
import java.util.logging.Handler

class HousePhotoListDetailActivity : BaseActivity() {

    private lateinit var binding: ActivityHousePhotoListDetailBinding
    private lateinit var mAdapter: HousePhotoPagerAdapter
    private var hasScrolled = false
    private var lastPosition = 0 // Add this variable to track the last position

    override fun getLayout(): Any {
        binding = ActivityHousePhotoListDetailBinding.inflate(layoutInflater)
        return binding.root
    }

    @SuppressLint("WrongConstant")
    private fun fixOrientation(): Boolean {
        try {
            val field: Field = Activity::class.java.getDeclaredField("mActivityInfo")
            field.setAccessible(true)
            val o = field.get(this) as ActivityInfo
            o.screenOrientation = -1
            field.setAccessible(false)
            return true
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return false
    }

    private fun isTranslucentOrFloating(): Boolean {
        var isTranslucentOrFloating = false
        try {
            val styleableRes = Class.forName("com.android.internal.R\$styleable")
                .getField("Window")[null] as IntArray
            val ta = obtainStyledAttributes(styleableRes)
            val m: Method = ActivityInfo::class.java.getMethod(
                "isTranslucentOrFloating",
                TypedArray::class.java
            )
            m.setAccessible(true)
            isTranslucentOrFloating = m.invoke(null, ta) as Boolean
            m.setAccessible(false)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return isTranslucentOrFloating
    }

    override fun setRequestedOrientation(requestedOrientation: Int) {
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.O && isTranslucentOrFloating()) {
            Logger.e("avoid calling setRequestedOrientation when Oreo.")
            return
        }
        super.setRequestedOrientation(requestedOrientation)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.O && isTranslucentOrFloating()) {
            val result: Boolean = fixOrientation()
            Logger.e("onCreate fixOrientation when Oreo, result = $result")
        }
        super.onCreate(savedInstanceState)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        val currentOrientation = when (newConfig.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> "landscape"
            Configuration.ORIENTATION_PORTRAIT -> "portrait"
            else -> "unknown orientation"
        }
        Logger.d("HousePhotoListDetailActivity - Screen rotation event triggered: Current orientation is ${currentOrientation}")
        

        when (newConfig.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> {
                // Horizontal
                updateOrientationUI(true)
                Logger.d("HousePhotoListDetailActivity - Device rotated from portrait to landscape")
                GALog.log("listingphotos_viewer_orientation_shift", "photoswiper_landscape")
                binding.root.postDelayed({
                    // Use EventBus to send reset scale event
                    Logger.d("HousePhotoListDetailActivity - Sending ResetScaleEvent")
                    EventBus.getDefault().post(ResetScaleEvent())
                }, 100)
            }
            Configuration.ORIENTATION_PORTRAIT -> {
                // Vertical
                updateOrientationUI(false)
                Logger.d("HousePhotoListDetailActivity - Device rotated from landscape to portrait")
                GALog.log("listingphotos_viewer_orientation_shift", "photoswiper_portrait")
                binding.root.postDelayed({
                    // Use EventBus to send reset scale event
                    Logger.d("HousePhotoListDetailActivity - Sending ResetScaleEvent")
                    EventBus.getDefault().post(ResetScaleEvent())
                }, 100)
            }
            else -> {
                Logger.d("HousePhotoListDetailActivity - Device rotated to unknown orientation")
            }
        }
    }

    private fun updateOrientationUI(isLandscape : Boolean = resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
        val statusBarHeight = ScreenUtils.getStatusBarHeight(this)
        if (isLandscape) {
            window.decorView.systemUiVisibility = window.decorView.systemUiVisibility or
                View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY

            val lp2 = binding.ivClose.layoutParams as RelativeLayout.LayoutParams
            lp2.topMargin = 0
            binding.ivClose.layoutParams = lp2

            binding.tvPageLandscape.visibility = View.VISIBLE
            binding.tvPage.visibility = View.GONE
        } else {
            window.decorView.systemUiVisibility = window.decorView.systemUiVisibility and
                View.SYSTEM_UI_FLAG_FULLSCREEN.inv() and View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY.inv()

            val lp2 = binding.ivClose.layoutParams as RelativeLayout.LayoutParams
            lp2.topMargin = statusBarHeight
            binding.ivClose.layoutParams = lp2

            binding.tvPageLandscape.visibility = View.GONE
            binding.tvPage.visibility = View.VISIBLE
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        AutoSizeConfig.getInstance().restart()
    }

    override fun initView() {
        val photos = intent.getParcelableArrayListExtra<OrderPhotoModel>("photoList")
        val position = intent.getIntExtra("position", 0)
        if (photos == null || photos.isEmpty()) {
            finish()
            return
        }

        transparentStatusBar(this)
        transparentNavBar(this)

        val statusBarHeight = ScreenUtils.getStatusBarHeight(this)
        val lp1 = binding.tvPage.layoutParams as RelativeLayout.LayoutParams
        lp1.topMargin = ScreenUtils.dpToPx(16f).toInt() + statusBarHeight
        binding.tvPage.layoutParams = lp1

        val lp2 = binding.ivClose.layoutParams as RelativeLayout.LayoutParams
        lp2.topMargin = statusBarHeight
        binding.ivClose.layoutParams = lp2

        val lp3 = binding.tvPageLandscape.layoutParams as RelativeLayout.LayoutParams
        lp3.topMargin = ScreenUtils.dpToPx(12f).toInt()
        binding.tvPageLandscape.layoutParams = lp3

        updateOrientationUI()

        mAdapter = HousePhotoPagerAdapter(supportFragmentManager, photos)
        binding.vp.adapter = mAdapter
        binding.vp.setCurrentItem(0, false)
        binding.ivClose.setOnClickListener {
            finish()
        }
        binding.tvPage.text = "1/${mAdapter.count}"
        binding.tvPageLandscape.text =  "1/${mAdapter.count}"
        binding.vp.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                binding.tvPage.text = "${position + 1}/${mAdapter.count}"
                binding.tvPageLandscape.text = "${position + 1}/${mAdapter.count}"
                if (!hasScrolled) {
                    hasScrolled = true
                } else {
                    GALog.log("hs_photos_swipe", "detail_photos")
                }

                // Only reset scale when changing pages
                if (position != lastPosition) {
                    // Use EventBus to send reset scale event to previous fragment
                    Logger.d("HousePhotoListDetailActivity - Sending ResetScaleEvent for page change")
                    EventBus.getDefault().post(ResetScaleEvent())
                    lastPosition = position
                }
            }

            override fun onPageScrollStateChanged(state: Int) {
            }
        })

        binding.vp.setCurrentItem(position, false)
    }

    override fun initData() {
    }


    private fun transparentStatusBar(activity: Activity) {
        transparentStatusBar(activity.window)
    }

    private fun transparentStatusBar(window: Window) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) return
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            val option = View.SYSTEM_UI_FLAG_LAYOUT_STABLE or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            val vis = window.decorView.systemUiVisibility
            window.decorView.systemUiVisibility = option or vis
            window.statusBarColor = Color.TRANSPARENT
        } else {
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        }
    }

    private fun transparentNavBar(activity: Activity) {
        transparentNavBar(activity.window)
    }

    private fun transparentNavBar(window: Window) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.JELLY_BEAN) return
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            window.isNavigationBarContrastEnforced = false
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.navigationBarColor = Color.TRANSPARENT
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            if (window.attributes.flags and WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION == 0) {
                window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)
            }
        }
        val decorView = window.decorView
        val vis = decorView.systemUiVisibility
        val option =
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        decorView.systemUiVisibility = vis or option
    }
}