package com.housesigma.android.ui.map.housephoto;

import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.housesigma.android.model.OrderPhotoModel;
import com.housesigma.android.ui.account.AccountFragment;
import com.housesigma.android.ui.home.HomeFragment;
import com.housesigma.android.ui.map.MapFragment;
import com.housesigma.android.ui.market.MarketFragment;
import com.housesigma.android.ui.watched.WatchedFragment;

import java.util.ArrayList;


public class HousePhotoPagerAdapter extends FragmentPagerAdapter {
    private ArrayList<OrderPhotoModel> photoUrls;
    private FragmentManager fragmentManager;

    public HousePhotoPagerAdapter(@NonNull FragmentManager fm, ArrayList<OrderPhotoModel> photoUrls) {
        super(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
        this.fragmentManager = fm;
        this.photoUrls = photoUrls;
    }


    @Override
    public int getCount() {
        return photoUrls.size();
    }

    @Override
    public Object instantiateItem(ViewGroup vg, int position) {
        return super.instantiateItem(vg, position);
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        Fragment fragment = (Fragment) object;
        // 清理 Fragment 资源
        if (fragment.isAdded()) {
            fragmentManager.beginTransaction().remove(fragment).commit();
        }
        super.destroyItem(container, position, object);
    }

    @NonNull
    @Override
    public Fragment getItem(int position) {
        String url = photoUrls.get(position).getPhotoUrl();
        return HousePhotoDetailFragment.Companion.newInstance(url);
    }

    // 可选：获取当前显示的 Fragment
    public Fragment getCurrentFragment(ViewPager viewPager,int position) {
        try {
            if (position < 0 || position >= getCount()) {
                return null;
            }
            return (Fragment) viewPager.getAdapter().instantiateItem(viewPager, position);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
