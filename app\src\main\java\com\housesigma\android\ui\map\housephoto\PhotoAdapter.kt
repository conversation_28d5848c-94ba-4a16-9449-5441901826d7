package com.housesigma.android.ui.map.housephoto

import com.bumptech.glide.Glide
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.OrderPhotoModel


class PhotoAdapter : BaseQuickAdapter<OrderPhotoModel, BaseViewHolder>(R.layout.item_photos) {

    override fun convert(holder: BaseViewHolder, item: OrderPhotoModel) {
            Glide.with(context)
                .load(item.photoUrlThumb)
                .onlyRetrieveFromCache(true)
                .error(R.color.color_gray)
                .placeholder(R.color.color_gray)
                .into(holder.getView(R.id.iv_house_pic))
    }

}