package com.housesigma.android.ui.map.housephoto

import android.content.Intent
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.View
import android.widget.LinearLayout
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityPreconHousePhotoListBinding
import com.housesigma.android.databinding.HeaderHousePhotoListBinding
import com.housesigma.android.model.*
import com.housesigma.android.ui.home.HomeViewModel
import com.housesigma.android.ui.listing.ContactDialogType
import com.housesigma.android.ui.listing.ContactUsWithAgentFragment
import com.housesigma.android.ui.listing.JoinUsFragment
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.watched.WatchedViewModel
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.ToastUtils
import me.jessyan.autosize.AutoSizeConfig

class PreconHousePhotoListActivity : BaseActivity(), LoginFragment.LoginCallback {

    private lateinit var binding: ActivityPreconHousePhotoListBinding
    private lateinit var headerBinding: HeaderHousePhotoListBinding
    private lateinit var homeViewModel: HomeViewModel
    private lateinit var watchedViewModel: WatchedViewModel
    private var idProject: String? = null
    private var loginDialog: LoginFragment? = null
    private val adapter = PhotoAdapter()
//    private var watchlist:MultipleWatchList? = null
    private val orderPhotoList = ArrayList<OrderPhotoModel>()

    override fun getLayout(): Any {
        binding = ActivityPreconHousePhotoListBinding.inflate(layoutInflater)
        val animator = binding.rv.itemAnimator as SimpleItemAnimator
        animator.supportsChangeAnimations = false
        animator.addDuration = 0
        animator.removeDuration = 0
        animator.moveDuration = 0
        animator.changeDuration = 0
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        GALog.page("precon_photos")
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)

            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }

        binding.rv.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        binding.rv.adapter = adapter

        binding.ivClose.setOnClickListener {
            finish()
        }

        adapter.addChildClickViewIds(
            R.id.rl
        )
        adapter.setOnItemChildClickListener { adapter, view, position ->
            when (view.id) {
                R.id.rl -> {
                    AutoSizeConfig.getInstance().stop(this)
                    val intent = Intent(this, HousePhotoListDetailActivity::class.java)
                    intent.putExtra("photoList", orderPhotoList)
                    intent.putExtra("position", position)
                    startActivity(intent)
                    overridePendingTransition(R.anim.fade_in, R.anim.fade_out)
                }
            }
        }

        headerBinding = HeaderHousePhotoListBinding.inflate(layoutInflater)
        adapter.addHeaderView(
            headerBinding.root,
            orientation = LinearLayout.VERTICAL
        )


    }

    override fun onDestroy() {
        super.onDestroy()
        ContactUsWithAgentFragment.destroyTrustPilotView()
    }

    override fun initData() {
        idProject = intent.getStringExtra("id_project")
        if (idProject == null) {
            ToastUtils.showLong("id_project is null!")
            finish()
        }
        homeViewModel = ViewModelProvider(this).get(HomeViewModel::class.java)
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)


        homeViewModel.housePhotos.observe(this) {
            it.meta?.let {
                handleShareClick(it)
            }
            handleHouseStatus(it)
            if (adapter.data.isEmpty()) {
                handleHousePhotos(it)
            }
            binding.llBottomTool.visibility = View.VISIBLE
        }

        getPreconPhotos()
    }

    private fun handleShareClick(meta: PhotoMeta){
        val shareContent = meta.title_share + " " + meta.url_share_android
        binding.ivShare.setOnClickListener {
            GALog.log("page_share_click","precon_photos")
            HSUtil.share(
                this@PreconHousePhotoListActivity,
                shareContent
            )
        }
    }


    private fun handleHousePhotos(photosInfo: HousePhotosInfo) {
        orderPhotoList.clear()
        val photoList = photosInfo.picture.photo_list
        val thumbPhotoList = photosInfo.picture.photo_list
        val size = minOf(photoList.size, thumbPhotoList.size)
        for (i in 0 until size) {
            orderPhotoList.add(OrderPhotoModel(photoList[i], thumbPhotoList[i]))
        }
        adapter.data.clear()
        adapter.addData(orderPhotoList)
        imageOrderLoad(orderPhotoList)
    }

    private fun handleHouseStatus(photosInfo: HousePhotosInfo) {
        if (photosInfo?.list_status?.isForSellType() == true) {
            binding.tvRightBtn.text = getString(R.string.schedule_viewing)
        } else {
            binding.tvRightBtn.text = getString(R.string.contact_agent)
        }

        if (!TextUtils.isEmpty(photosInfo.picture.virtual_tour)) {
            headerBinding.tvVirtualTour.setOnClickListener {
                GALog.log("virtual_tour_click")
                WebViewHelper.jumpInnerWebView(
                    this,
                    photosInfo.picture.virtual_tour,
                    hasTool = true
                )
            }
            headerBinding.tvVirtualTour.visibility = View.VISIBLE
        } else {
            headerBinding.tvVirtualTour.visibility = View.GONE
        }

        binding.tvRightBtn.setOnClickListener {
            GALog.log("contact_agent_click", "precon_photos")
            ContactUsWithAgentFragment.newInstance(
                ContactDialogType.PRECON,
                if (photosInfo.list_status?.isForSellType()?:false)
                    (photosInfo.schedule_message?:"") else (photosInfo.contact_message?:""),
                photosInfo.ml_num?:"",
                "",
                photosInfo.bind_agent_user?.name?:"",
                photosInfo.bind_agent_user?.picture?:"",
                idProject,
                photosInfo.show_trustpilot?:false,
                photosInfo.bind_agent_user?.slug,
                gaHsLabel = "precon_photos")
                .show(supportFragmentManager, "")
        }

        if (photosInfo.show_trustpilot == true) {
            ContactUsWithAgentFragment.loadTrustPilotView()
        }
    }

    private fun getPreconPhotos() {
        idProject?.let {
            homeViewModel.getPreconPhotos(it)
        }
    }


    /**
     * 递归方式依次加载图片
     */
    private fun imageOrderLoad(photoList: List<OrderPhotoModel>, position: Int = 0) {
        if (this.isFinishing) return
        if (position == photoList.size) return
        val item = photoList[position]
        Glide.with(this)
            .load(item.photoUrlThumb)
            .listener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>?,
                    isFirstResource: Boolean
                ): Boolean {
                    downImageAndLoadNext()
                    return false
                }

                override fun onResourceReady(
                    resource: Drawable?,
                    model: Any?,
                    target: Target<Drawable>?,
                    dataSource: DataSource?,
                    isFirstResource: Boolean
                ): Boolean {
                    downImageAndLoadNext()
                    return false
                }

                private fun downImageAndLoadNext() {
//                    item.isLoaded = true
                    adapter.notifyItemChanged(position + 1)
                    imageOrderLoad(photoList, position + 1)
                }
            })
            .preload()

    }


    private fun showLoginDialog() {
        GALog.log("login_button_click")
        supportFragmentManager.let {
            if (loginDialog == null) {
                loginDialog = LoginFragment()
            }
            if (loginDialog?.isAdded == true) return
            loginDialog?.show(it, "")
        }
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
    }


}