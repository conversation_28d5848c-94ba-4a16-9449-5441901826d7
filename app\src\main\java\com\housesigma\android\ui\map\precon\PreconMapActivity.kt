package com.housesigma.android.ui.map.precon

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Color
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Bundle
import android.os.Handler
import android.provider.Settings
import android.text.TextUtils
import android.view.*
import android.widget.LinearLayout
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.gson.Gson
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.AbsSuperApplication
import com.housesigma.android.BuildConfig
import com.housesigma.android.R
import com.housesigma.android.databinding.ActivityPreconMapBinding
import com.housesigma.android.helper.ProvinceHelper
import com.housesigma.android.model.*
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.map.BaseMapActivity
import com.housesigma.android.ui.map.MapType
import com.housesigma.android.ui.map.PreconMapListingAdapter
import com.housesigma.android.ui.map.helper.MapHelper
import com.housesigma.android.ui.map.helper.PreconMapHelper
import com.housesigma.android.ui.map.propertype.MapListViewSortTypeView
import com.housesigma.android.ui.map.propertype.PreconMapSettingDialog
import com.housesigma.android.ui.search.SearchActivity
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.*
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.HSAlertDialog
import com.housesigma.android.views.HSDecoration
import com.housesigma.android.views.HSPopWindow
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import org.maplibre.android.MapLibre
import org.maplibre.android.annotations.*
import org.maplibre.android.camera.CameraPosition
import org.maplibre.android.camera.CameraUpdateFactory
import org.maplibre.android.geometry.LatLng
import org.maplibre.android.geometry.LatLngBounds
import org.maplibre.android.maps.MapView
import org.maplibre.android.maps.MapLibreMap
import org.maplibre.android.maps.Style
import kotlinx.coroutines.*
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import permissions.dispatcher.*
import java.math.BigDecimal
import java.util.concurrent.ConcurrentHashMap


@RuntimePermissions
class PreconMapActivity : BaseMapActivity(), LoginFragment.LoginCallback {


    //====================Map相关的参数start=========================

    private var lastListingInfo: PreconMarker? = null
    private var lastClickMarker: Marker? = null


    private var lastMarkerProjectIds: List<String> = ArrayList()
    private lateinit var mapView: MapView
    private lateinit var mapLibreMap: MapLibreMap
    private var isMapViewActive: Boolean = false // 用于标记地图是否激活状态
    private var isMapStyleLoaded: Boolean = false // 用于标记地图样式是否加载成功


    // 各个api 同等类型的marker单独储存，去重逻辑单独处理 优势 更清晰好维护
    private var listingMarkerInfoMap = ConcurrentHashMap<Long, PreconMarker>() //listing2
    private var viewInMapInfoMap = ConcurrentHashMap<Long, String>()

    private var mapSettingsDialog: PreconMapSettingDialog? = null

    private var lon1: Double = 0.0
    private var lon2: Double = 0.0
    private var lat1: Double = 0.0
    private var lat2: Double = 0.0

    /**
     * 真实的经纬度参数
     */
    private var realLon1: Double = 0.0
    private var realLon2: Double = 0.0
    private var realLat1: Double = 0.0
    private var realLat2: Double = 0.0


    //====================Map相关的参数end===========================



    private lateinit var binding: ActivityPreconMapBinding
    private lateinit var mapHelper: PreconMapHelper
    private lateinit var mapViewModel: PreconMapViewModel

    // 1. 2. 3分别对应Selling Now，Registration，Sold Out
    private var listType = ArrayList<String>()

    // sale/sold/de listed筛选数据，其中"sale"包含了delisted和sold两个条件了
    private var mapFilter: PreconMapFilter? = null

    private var mapPropertyView: PreconMapPropertyView? = null
    private var mapConstructionStatusView: PreconMapConstructionStatusView? = null
    private var mapMoreFiltersView: PreconMapMoreFiltersView? = null
    private var mapListViewSortTypeView: MapListViewSortTypeView? = null

    // 中间三个按钮，其中第一个sale是默认不可选择的，另外两个是可以选择的
    // ? 带参数进来的，又可以选择第一个按钮
    private var isSelectOne: Boolean = false
    private var isSelectSold: Boolean = false
    private var isSelectDelisted: Boolean = false

    // 过滤器popwindow
    private lateinit var popWindowFilter: HSPopWindow
    private lateinit var xPopWindow: BasePopupView

    private lateinit var bottomSheetBehavior : BottomSheetBehavior<LinearLayout>


    private var ready: Boolean = false //请求filter接口回调的标记位

    private var isSale: Boolean = false //是否是卖，有两种，一种租，一种买卖
    private var projectStatus: ArrayList<PreconProjectStatus> = ArrayList()

    private var loginDialog: LoginFragment? = null

    private var idMunicipality:String = ""
    private var viewType:String = "map" //这个页面有2种形式，map和list

    private var watchAreaPolygon: ArrayList<com.housesigma.android.model.Polygon>? = null
    private val mListingPreviewAdapter by lazy { initListingPreviewMany() }

    /**
     * prepare params
     */
    private fun getParamsFromIntent() {
        isSale = intent.getBooleanExtra("is_sale", true)
        projectStatus = (intent.getSerializableExtra("map_type")?:ArrayList<PreconProjectStatus>()) as ArrayList<PreconProjectStatus>
        watchAreaPolygon = intent.getParcelableArrayListExtra("watch_area_polygon")
        idMunicipality = intent.getStringExtra("municipality_id")?:""
        Logger.e("isSale: $isSale, mapType: $projectStatus, watchAreaPolygon: $watchAreaPolygon," +
                " idMunicipality: $idMunicipality, viewType: $viewType")
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        getParamsFromIntent()
        MapLibre.getInstance(this)
        super.onCreate(savedInstanceState)
        mapViewModel = ViewModelProvider(this).get(PreconMapViewModel::class.java)
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        binding = ActivityPreconMapBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initViews(savedInstanceState)
        initLoginDialog()
    }


    private fun initLoginDialog() {
        if (loginDialog != null) return
        loginDialog = LoginFragment()
    }

    private fun initData() {
        mapViewModel.preconMapFilter.observe(this) {
            loadMapFilter(it)
        }

        mapViewModel.listingPreViewManyWithMarker.observe(this) {
            try {
                if (it.size == 1) {
                    val house = it.getOrNull(0)
                    house?.let {
                        house.location?.let {
                            if (house.location.lat != 0.0 || house.location.lon != 0.0) {
                                moveCameraToPoint(Location(house.location.lat, house.location.lon))

                                val marker = mapHelper.addViewInFullMapMarker(this,mapLibreMap,house)
                                marker?.let { thatMarker ->
                                    // 这里要单独存放，不然区别不出来是哪种类型的marker
                                    viewInMapInfoMap.put(
                                        thatMarker.id, house.id_project
                                    )


                                    // 剔除掉同View in Full map类型相同经纬度，相同id_listing的marker
                                    mapHelper.removeDuplicateMarkers(listingMarkerInfoMap,house.id_project)
//                                    mapHelper.removeDuplicateMarkers(nearbyMarkerInfoMap,house.id_listing)
//                                    mapHelper.removeDuplicateMarkers(featureMarkerInfoMap,house.id_listing)
                                }

                            }
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

            handleListingPreview(it)
        }

        mapViewModel.listingPreViewMany.observe(this) {
            handleListingPreview(it)
        }

        mapViewModel.loadingLiveData.observe(this) {
            hideProgress()
            binding.llSchoolDetail.visibility = View.GONE
            hiddenListingPreview()
            Handler().postDelayed({
                setChangeMapVisibility()
            },10)
        }

        mapViewModel.mapList.observe(this) { that ->
            CoroutineScope(Dispatchers.Main).launch {
                handleMapListing(that)
            }
        }

        val mapFilterJson = MMKVUtils.getStr(MapHelper.PRECON_MAP_FILTER_TAG)
        if (TextUtils.isEmpty(mapFilterJson)) {
            mapViewModel.getPreconMapFilter()
        } else {
            val mapFilter = Gson().fromJson(mapFilterJson, PreconMapFilter::class.java)
            loadMapFilter(mapFilter)
        }

//        val listingId = intent.getStringExtra("id_listing") ?: ""
//        if (!TextUtils.isEmpty(listingId)) {
//            getListingPreviewMany(listingId)
//        }

        val listingId = intent.getStringExtra("id_listing") ?: ""
        if (!TextUtils.isEmpty(listingId)) {
            lastMarkerProjectIds = arrayListOf(listingId)
            getListingPreviewPreconMany(true)
        }


        mapViewModel.saveMapFilterMsg.observe(this) {
            ToastUtils.showSuccess(it.message)
            getMapFilterList()
        }

        mapViewModel.mapFilterList.observe(this) {
            mapMoreFiltersView?.removeFilterIfRemoteDeleted(it)
            mapMoreFiltersView?.bindSaveFilterSelectList(this@PreconMapActivity,it,object : Callback1 {
                override fun onData(any: Any) {
                    val saveMapFilter = any as MapFilters

                    val propertyType =   saveMapFilter.property_type?:ArrayList()
                    val constructionStatus =  saveMapFilter.construction_status?:ArrayList()



                    val tempHouseTypeLocal = ArrayList<String>()
                    mapFilter?.let {
                        it.property_type_filter.forEachIndexed { index, houseTypeFilter ->
                            for (defaultHouseType in propertyType) {
                                if (defaultHouseType.equals(houseTypeFilter.id)) {
                                    tempHouseTypeLocal.add(index.toString())
                                }
                            }
                        }
                        it.default_filter.preconPropertyLocal = tempHouseTypeLocal
                        MMKVUtils.saveStr("preconPropertyLabels",  it.default_filter.preconPropertyLocal.joinToString(","))
                        tempHouseTypeLocal.clear()


                        it.construction_status_filter.forEachIndexed { index, houseTypeFilter ->
                            for (defaultHouseType in constructionStatus) {
                                if (defaultHouseType.equals(houseTypeFilter.id)) {
                                    tempHouseTypeLocal.add(index.toString())
                                }
                            }
                        }
                        it.default_filter.preconConstructionStatusLocal = tempHouseTypeLocal

                        MMKVUtils.saveStr("preconConstrutionStatusLabels",  it.default_filter.preconConstructionStatusLocal.joinToString(","))



                    }

                    mapPropertyView!!.setLabelValueFromCache()
                    mapConstructionStatusView!!.setLabelValueFromCache()

                    reloadMapData()
                }
            })
        }

        mapViewModel.deleteMapFilterMsg.observe(this) {
            ToastUtils.showDel(it.message)
            getMapFilterList()
        }

        getMapFilterList()


    }


    private fun deleteMapFilterById(id: String) {
        mapViewModel.deleteMapFilterById(id)
    }

    /**
     * 未登录，不显示MapFilterSave模块，save filter的视图状态取决于api返回的数据
     */
    private fun getMapFilterList(){
        if (!LoginFragment.isLogin()) {
            mapMoreFiltersView?.goneSaveFilterViews()
            return
        }
       mapViewModel.getMapFilterList(MapType.PRECON.value)
    }

    private fun handleListingPreview(it: List<Precon>) {
        showListingPreview()
        binding.llSchoolDetail.visibility = View.GONE
        mListingPreviewAdapter.setOnItemChildClickListener { adapter, view, position ->
            handleListViewClick(adapter, position, view)
        }

        mListingPreviewAdapter.data = it.toMutableList()
        mListingPreviewAdapter.notifyDataSetChanged()

        (binding.rvListings.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(0, 0)
        (binding.rvListings.layoutManager as LinearLayoutManager).setStackFromEnd(true)

//        if (it.size <= 1) {
//            binding.tvListingsSize.text = "${it.size} Listing in total"
//        } else {
//            binding.tvListingsSize.text = "${it.size} Listings in total"
//        }
    }

    private fun handleListViewClick(
        adapter: BaseQuickAdapter<*, *>,
        position: Int,
        view: View
    ) {
        val item = adapter.getItem(position) as Precon
        when (view.id) {
            R.id.rl -> {
//                if (item.isAgreementRequired()||item.isNeedReLogin() || item.isLoginRequired() || item.isNotAvailable()) {
//                    return
//                }
//
                GALog.log("preview_click", "precon_map")
                this@PreconMapActivity?.let {
                    if (MapUtils.isCollectLocation()) {
                        collectUserLocation()
                    }
                    WebViewHelper.jumpPreconDetail(
                        it,
                        item.id_project,
                        eventSource = "preconmap"
                    )
                }
            }
        }
    }




    private fun initListingPreviewMany(): PreconMapListingAdapter {
        val divider = HSDecoration(this, DividerItemDecoration.VERTICAL, false)
        binding.rvListings.addItemDecoration(divider)
        binding.rvListings.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        val adapter = PreconMapListingAdapter()
        binding.rvListings.adapter = adapter
        return adapter
    }


    private suspend fun handleMapListing(that: List<PreconMarker>) {
        val notSameMapList = ArrayList<Marker>()
        // api返回的marker，判断现有marker列表中有无，
        // 如果marker列表中没有，需要添加上
        // 如果marker列表中有，则不需要添加
        // 只需要保留比较本地和网络重叠的部分，去掉不同的。
        for (entry in listingMarkerInfoMap) {
            var find = false
            for (mapMarkerInfo in that) {
                if (entry.value.equals(mapMarkerInfo)) {
                    find = true
                    break
                }
            }
            if (!find) {
                entry.value.localMarker?.let {
                    notSameMapList.add(it)
                }
            }
        }

        notSameMapList.forEach {
            listingMarkerInfoMap.remove(it.id)
        }


        mapLibreMap.removeAnnotations(notSameMapList)


        val start = System.currentTimeMillis()

        that.forEach {
            var find = false
            for (mutableEntry in listingMarkerInfoMap) {
                if (mutableEntry.value.equals(it)) {
                    find = true
//                    Logger.e( "find same element in hashMapMapInfo")
                    break
                }
            }
            if (!find) find = mapHelper.isSameLocationAndIds(viewInMapInfoMap, it)

            if (!find) {
                var marker: Marker? = null
                if ("precon_selling_now".equals(it.marker)) {
                    marker = mapHelper.addSaleMarker(it)
                } else if ("precon_upcoming".equals(it.marker)) {
                    marker = mapHelper.addRegistrationMarker(it)
                } else if ("precon_sold".equals(it.marker)) {
                    marker = mapHelper.addSoldMarker(it)
                }

                marker?.let { markerThat ->
                    it.localMarker = markerThat
                    listingMarkerInfoMap.put(
                        markerThat.id, it
                    )
                }
            }
        }
        val end = System.currentTimeMillis()
        Logger.d("function cast time is " + (end - start));



        binding.progress.visibility = View.GONE
    }

    private fun loadMapFilter(it: PreconMapFilter?) {
        mapFilter = it

        mapPropertyView?.setData(mapFilter!!)
        mapConstructionStatusView?.setData(mapFilter!!)
        mapMoreFiltersView?.setData(mapFilter!!)

//        mapPropertyLayout!!.readCache()
        mapPropertyView!!.setLabelValueFromCache()
        mapConstructionStatusView!!.setLabelValueFromCache()

//        if (propertyCache is String) {
//            binding.tvPropertyTypes.text = propertyCache
//        } else if (propertyCache is List<*>) {
//            if ((propertyCache as List<PreconPropertyTypeFilter>).size > 1) {
//                binding.tvPropertyMore.visibility = View.VISIBLE
//                binding.tvPropertyTypes.text =
//                    (propertyCache as List<PreconPropertyTypeFilter>)[0].name
//                binding.tvPropertyMore.text =
//                    ((propertyCache as List<PreconPropertyTypeFilter>).size - 1).toString() + "+"
//            } else if ((propertyCache as List<PreconPropertyTypeFilter>).size == 1) {
//                binding.tvPropertyMore.visibility = View.GONE
//                binding.tvPropertyTypes.text =
//                    (propertyCache as List<PreconPropertyTypeFilter>)[0].name
//            } else {
//                binding.tvPropertyMore.visibility = View.GONE
//                binding.tvPropertyMore.text = ""
//            }
//        }



        mapMoreFiltersView!!.readCache()



        if (projectStatus.size == 0) {
            setDefaultListType()
            refreshListType()
        }


        ready = true
        reloadMapData()
    }

    private fun setDefaultListType() {
        mapFilter?.let {
            listType?.clear()
            projectStatus?.clear()
            if (it.default_filter.project_status.contains("1")) {
                projectStatus?.add(PreconProjectStatus.SELLING_NOW)
            } else if (it.default_filter.project_status.contains("2")) {
                projectStatus?.add(PreconProjectStatus.REGISTRATION)
            } else if (it.default_filter.project_status.contains("3")) {
                projectStatus?.add(PreconProjectStatus.SOLD_OUT)
            } else {
            }
        }
    }

    private fun initListViewViews(){
        mapListViewSortTypeView = MapListViewSortTypeView(this)
        mapListViewSortTypeView?.setSelectClickListener(object :Callback1{
            override fun onData(any: Any) {
                xPopWindow.dismiss()
                binding.tvMapListViewSortType.text = mapListViewSortTypeView!!.sortStr
                changeListView()
            }
        })
        binding.tvMapListViewSortType.setOnClickListener {
            binding.tvMapListViewSortType.post {
                xPopWindow =  XPopup.Builder(MapActivity@this)
                    .atView(binding.tvMapListViewSortType)
                    .animationDuration(0)
                    .asCustom(mapListViewSortTypeView)
                    .show()
            }
        }

    }

    @SuppressLint("WrongConstant")
    private fun initViews(savedInstanceState: Bundle?) {
        bottomSheetBehavior = BottomSheetBehavior.from(binding.llListings)
        initListViewViews()
        if (isSale) {
            binding.tvForSale.text = "Selling Now"
            binding.tvSold.text = "Upcoming"
            binding.tvDelisted.text = "Sold Out"
        } else {
            binding.llWatch.visibility = View.GONE
            binding.tvForSale.text = "For Lease"
            binding.tvSold.text = "Leased"
            binding.tvDelisted.text = "De-listed"
        }


        binding.llSearch.setOnClickListener {
            GALog.log("search_start")
            try {
                AbsSuperApplication.finishActivity(SearchActivity::class.java)
            } catch (exception: Exception) {
                exception.printStackTrace()
            }
            val intent = Intent(this, SearchActivity::class.java)
            intent.putExtra("is_sale", isSale)
            startActivity(intent)
        }
        refreshListType()


        mapPropertyView = PreconMapPropertyView().createView(layoutInflater)
        mapPropertyView!!.setLabelTextView(binding.tvPropertyTypes,binding.tvPropertyMore)

        mapConstructionStatusView = PreconMapConstructionStatusView().createView(layoutInflater)
        mapConstructionStatusView!!.setLabelTextView(binding.tvConstructionStatus,binding.tvConstructionStatusMore)

        mapMoreFiltersView = PreconMapMoreFiltersView().createView(layoutInflater)


        binding.llFiltersMore.setOnClickListener {


            mapMoreFiltersView!!.setDeleteFiltersClickListener(this@PreconMapActivity,cb = object : Callback1 {
                override fun onData(any: Any) {
                    val id = any as String
                    deleteMapFilterById(id)
                }
            })

            mapMoreFiltersView!!.setSaveFiltersClickListener(
                this@PreconMapActivity,
                cb = object : Callback1 {
                    override fun onData(filtersName: Any) {
                        val mapFilters = MapFilters()
                        mapFilters.bathroom = mapMoreFiltersView!!.bathroomMin
                        mapFilters.bedroom =  mapMoreFiltersView!!.bedroomRange
                        mapFilters.description =  mapMoreFiltersView!!.description

                        mapFilters.price =  mapMoreFiltersView!!.getPrice()
                        mapFilters.square_footage_max =  mapMoreFiltersView!!.getSquareFootage()?.get(1)?:"0"
                        mapFilters.square_footage_min =  mapMoreFiltersView!!.getSquareFootage()?.get(0)?:"0"

                        mapFilters.est_completion_year = mapMoreFiltersView!!.estCompletionYearFilter
                        mapFilters.property_type = mapPropertyView!!.getPropertyTypes() //筛选条件左边第一个
                        mapFilters.construction_status = mapConstructionStatusView!!.getPropertyTypes() //筛选条件左边第二个

                        // mapFilters剩下的字段全部置为null
                        mapFilters.basement = null
                        mapFilters.garage = null
                        mapFilters.house_type = null
                        mapFilters.max_maintenance_fee = null
                        mapFilters.open_house_date = null
                        mapFilters.building_age_max = null
                        mapFilters.building_age_min = null
                        mapFilters.lot_size_max = null
                        mapFilters.lot_size_min = null
                        mapFilters.lot_front_feet_max = null
                        mapFilters.lot_front_feet_min = null
                        mapFilters.listing_type = null
                        mapFilters.de_list_days = null
                        mapFilters.sold_days = null
                        mapFilters.listing_days = null


                        filtersName as String
                        val saveMapFilter = SaveMapFilter(filterName = filtersName, category = MapType.PRECON.toString(), filter = mapFilters)
                        mapViewModel.saveMapFilter(saveMapFilter)
                    }
                })
            // 筛选 最右侧 更多筛选条件 布局




            mapMoreFiltersView!!.setOnLabelClickListener(cb = object : Callback0 {
                override fun onData() {
                    goToFirstPageIfViewTypeIsList()
                    reloadMapData()
                }
            })

            mapMoreFiltersView!!.setApplyClickListener(cb = object : Callback1 {
                override fun onData(any: Any) {
                    popWindowFilter.dissmiss()
                    goToFirstPageIfViewTypeIsList()
                    reloadMapData()
                }
            })

            mapMoreFiltersView!!.setClearClickListener(cb = object : Callback1 {
                override fun onData(any: Any) {
                    popWindowFilter.dissmiss()
                    goToFirstPageIfViewTypeIsList()
                    reloadMapData()
                }
            })

            GALog.log("filter_select", "filters")
            val screenHeight = ScreenUtils.getDeviceHeight(this)
            binding.vLine.post {
                val popHeight = screenHeight - binding.vLine.bottom
                Logger.e("popHeight: " + popHeight)
                popWindowFilter = HSPopWindow.PopupWindowBuilder(this)
                    .setInputMethodMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
                    .setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
                    .setView(mapMoreFiltersView!!.getLayout().root)
                    .size(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        popHeight
                    )
                    .enableBackgroundDark(false) //弹出popWindow时，背景是否变暗
                    .setBgDarkAlpha(0.5f)
                    .create()
                    .showAsDropDown(binding.vLine, 0, 0)
            }
        }

        binding.llPropertyTypes.setOnClickListener {
            mapPropertyView!!.setOnLabelClickListener(cb = object : Callback1 {
                override fun onData(name: Any) {

                    val list = name as List<PreconPropertyTypeFilter>
                    binding.tvPropertyTypes.text = list[0].name
                    if (list.size > 1) {
                        binding.tvPropertyMore.visibility = View.VISIBLE
                        binding.tvPropertyMore.text = (list.size - 1).toString().plus("+")
                    } else {
                        binding.tvPropertyMore.visibility = View.GONE
                        binding.tvPropertyMore.text = ""
                    }

                    reloadMapData()
                }
            })



            mapPropertyView!!.setClearClickListener(cb = object : Callback1 {
                override fun onData(name: Any) {
                    popWindowFilter.dissmiss()
                    reloadMapData()
                }
            })


            mapPropertyView!!.setApplyClickListener(cb = object : Callback1 {
                override fun onData(name: Any) {
                    popWindowFilter.dissmiss()
                    reloadMapData()
                }
            })
            GALog.log("filter_select", "property_type")
            popWindowFilter = HSPopWindow.PopupWindowBuilder(this)
                .setView(mapPropertyView!!.getLayout().root)
                .size(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                .enableBackgroundDark(false) //弹出popWindow时，背景是否变暗
                .setBgDarkAlpha(0.5f)
                .create()
                .showAsDropDown(binding.vLine, 0, 0)
        }

        binding.llForSaleDelised.setOnClickListener {
            mapConstructionStatusView!!.setOnLabelClickListener(cb = object : Callback1 {
                override fun onData(name: Any) {

                    val list = name as List<ConstructionStatusFilter>
                    binding.tvConstructionStatus.text = list[0].name
                    if (list.size > 1) {
                        binding.tvConstructionStatusMore.visibility = View.VISIBLE
                        binding.tvConstructionStatusMore.text = (list.size - 1).toString().plus("+")
                    } else {
                        binding.tvConstructionStatusMore.visibility = View.GONE
                        binding.tvConstructionStatusMore.text = ""
                    }

                    reloadMapData()
                }
            })

            mapConstructionStatusView!!.setClearClickListener(cb = object : Callback1 {
                override fun onData(name: Any) {
                    popWindowFilter.dissmiss()
                    reloadMapData()
                }
            })


            mapConstructionStatusView!!.setApplyClickListener(cb = object : Callback1 {
                override fun onData(name: Any) {
                    popWindowFilter.dissmiss()
                    reloadMapData()
                }
            })

            GALog.log("filter_select", "construction_status")
            popWindowFilter = HSPopWindow.PopupWindowBuilder(this)
                .setView(mapConstructionStatusView!!.getLayout().root)
                .size(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                .enableBackgroundDark(false) //弹出popWindow时，背景是否变暗
                .setBgDarkAlpha(0.5f)
                .create()
                .showAsDropDown(binding.vLine, 0, 0)
        }

        binding.tvForSale.setOnClickListener {
            GALog.log("filter_select", "selling_now")

            reverseFilterSelect()
            if (!isSelectOne) {
                binding.tvForSale.setBackgroundResource(R.drawable.shape_map_left_selected)
                binding.tvForSale.setTextColor(resources.getColor(R.color.color_white))
                if (!listType.contains("1")) {
                    listType.add("1")
                }
            } else {
                if (listType.size == 1) {
                    return@setOnClickListener
                }

                binding.tvForSale.setBackgroundResource(R.drawable.shape_map_left)
                binding.tvForSale.setTextColor(resources.getColor(R.color.color_gray_dark))

                if (listType.contains("1")) {
                    listType.remove("1")
                }
            }
            isSelectOne = !isSelectOne
            reloadMapData()
        }

        binding.tvSold.setOnClickListener {
            GALog.log("filter_select", "upcoming")

            reverseFilterSelect()
            if (!isSelectSold) {
                binding.tvSold.setBackgroundResource(R.drawable.shape_precon_map_center_selected)
                binding.tvSold.setTextColor(resources.getColor(R.color.color_white))

                if (!listType.contains("2")) {
                    listType.add("2")
                }
            } else {
                if (listType.size == 1) {
                    return@setOnClickListener
                }
                binding.tvSold.setBackgroundResource(R.drawable.shape_map_center)
                binding.tvSold.setTextColor(resources.getColor(R.color.color_gray_dark))

                if (listType.contains("2")) {
                    listType.remove("2")
                }
            }
            isSelectSold = !isSelectSold
            reloadMapData()
        }


        binding.tvDelisted.setOnClickListener {
            GALog.log("filter_select", "sold_out")

            reverseFilterSelect()
            if (!isSelectDelisted) {
                binding.tvDelisted.setBackgroundResource(R.drawable.shape_map_right_selected)
                binding.tvDelisted.setTextColor(resources.getColor(R.color.color_white))

                if (!listType.contains("3")) {
                    listType.add("3")
                }
            } else {
                if (listType.size == 1) {
                    return@setOnClickListener
                }
                binding.tvDelisted.setBackgroundResource(R.drawable.shape_map_right)
                binding.tvDelisted.setTextColor(resources.getColor(R.color.color_gray_dark))

                if (listType.contains("3")) {
                    listType.remove("3")
                }
            }

            isSelectDelisted = !isSelectDelisted
            reloadMapData()
        }


        binding.ivCloseMapListings.setOnClickListener {
            hiddenListingPreview()
        }

        binding.ivClose.setOnClickListener { finish() }
        mapView = binding.mapView
        mapView.onCreate(savedInstanceState)
        mapView.getMapAsync { map ->
            mapLibreMap = map
            mapLibreMap.uiSettings.isLogoEnabled = false
            mapLibreMap.uiSettings.isAttributionEnabled = false
            mapLibreMap.uiSettings.isRotateGesturesEnabled = false
            mapLibreMap.setMaxPitchPreference(0.0)//关闭仰角
            mapLibreMap.setMinPitchPreference(0.0)
            mapHelper = PreconMapHelper(MapActivity@ this, layoutInflater, mapLibreMap)
            setUpMapboxMap(ConstantsHelper.getMapVector())
            initData()
        }

        binding.svListView.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            setChangeMapVisibility()
        }


        binding.ivToolLocation.setOnClickListener {
            GALog.log("current_position_click")
            // 申请定位
            requestLocationWithPermissionCheck()
        }

        binding.ivToolSettings.setOnClickListener {
            GALog.log("map_type_layer_click")
            if (mapSettingsDialog == null) {
                mapLibreMap.style?.uri?.let { it1 -> Logger.e(it1) }
                mapSettingsDialog = PreconMapSettingDialog()
                mapSettingsDialog?.setMapSettingsCallback(object :
                    PreconMapSettingDialog.MapSettingsCallback {
                    override fun changeStyle(isSatellite:Boolean) {
                        isMapStyleLoaded = false
                        if (isSatellite) {
                            GALog.log("satellite_click", "satellite")
                            mapLibreMap.setStyle(ConstantsHelper.getMapSatellite()) {
                                isMapStyleLoaded = true
                            }
                        } else {
                            GALog.log("satellite_click", "street")
                            mapLibreMap.setStyle(ConstantsHelper.getMapVector()) {
                                isMapStyleLoaded = true
                            }
                        }
                    }
                })
            }
            mapLibreMap.style?.uri?.let { it1 ->  mapSettingsDialog?.setStyle(it1==ConstantsHelper.getMapSatellite())}
            mapSettingsDialog?.show(supportFragmentManager, "")
        }
    }

    private fun reverseFilterSelect() {
        isSelectOne = false
        isSelectDelisted = false
        isSelectSold = false
        binding.tvForSale.setBackgroundResource(R.drawable.shape_map_left)
        binding.tvForSale.setTextColor(resources.getColor(R.color.color_gray_dark))
        listType.clear()
        binding.tvSold.setBackgroundResource(R.drawable.shape_map_center)
        binding.tvSold.setTextColor(resources.getColor(R.color.color_gray_dark))
        binding.tvDelisted.setBackgroundResource(R.drawable.shape_map_right)
        binding.tvDelisted.setTextColor(resources.getColor(R.color.color_gray_dark))
    }

    /**
     * 判断 viewType 是否是 ViewType.LIST，如果是，则将 currentPage 设置为切换到第一页。
     */
    private fun goToFirstPageIfViewTypeIsList() {
    }


    /**
     * 如果内部changeMap按钮可见，就隐藏外部的changeMap按钮，反之显示
     */
    private fun setChangeMapVisibility() {
    }

    private fun changeListView() {
    }

    private fun changeMapView() {
    }

    private fun refreshListType() {
        binding.tvForSale.setBackgroundResource(R.drawable.shape_map_left)
        binding.tvForSale.setTextColor(resources.getColor(R.color.color_gray_dark))
        binding.tvSold.setBackgroundResource(R.drawable.shape_map_center)
        binding.tvSold.setTextColor(resources.getColor(R.color.color_gray_dark))
        binding.tvDelisted.setBackgroundResource(R.drawable.shape_map_right)
        binding.tvDelisted.setTextColor(resources.getColor(R.color.color_gray_dark))
        if (projectStatus?.contains(PreconProjectStatus.SELLING_NOW) == true) {
            isSelectOne = true
        } else {
            isSelectOne = false
        }
        if (projectStatus?.contains(PreconProjectStatus.SELLING_NOW) == true) {
            if (isSelectOne) {
                binding.tvForSale.setBackgroundResource(R.drawable.shape_map_left_selected)
                binding.tvForSale.setTextColor(resources.getColor(R.color.color_white))

                if (!listType.contains("1")) {
                    listType.add("1")
                }
            }
        }
        if (projectStatus?.contains(PreconProjectStatus.REGISTRATION) == true) {
            isSelectSold = true
        }else{
            isSelectSold = false
        }
        //        isSelectSold = MMKVUtils.getBoolean("map_select_sold", false)
        if (isSelectSold) {
            binding.tvSold.setBackgroundResource(R.drawable.shape_precon_map_center_selected)
            binding.tvSold.setTextColor(resources.getColor(R.color.color_white))

            if (!listType.contains("2")) {
                listType.add("2")
            }


        }

        if (projectStatus?.contains(PreconProjectStatus.SOLD_OUT) == true) {
            isSelectDelisted = true
        }else{
            isSelectDelisted = false
        }

        //        isSelectDelisted = MMKVUtils.getBoolean("map_select_de_listed", false)
        if (isSelectDelisted) {
            binding.tvDelisted.setBackgroundResource(R.drawable.shape_map_right_selected)
            binding.tvDelisted.setTextColor(resources.getColor(R.color.color_white))

            if (!listType.contains("3")) {
                listType.add("3")
            }

        }
    }

    private fun updateMapFilterRedPoint() {
        mapMoreFiltersView?.let {
            if (it.isModified()) {
                binding.ivFilterMorePoint.visibility = View.VISIBLE
            } else {
                binding.ivFilterMorePoint.visibility = View.GONE
            }
        }
    }

    private fun showLoginDialog() {
        GALog.log("login_button_click")
        initLoginDialog()
        if (loginDialog?.isAdded == true) return
        loginDialog?.show(supportFragmentManager, "")
    }

    @SuppressLint("MissingPermission")
    @NeedsPermission(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    fun requestLocation() {
        try {
            if (!LocationUtils.isLocServiceEnable(this)) {
                HSAlertDialog(
                    this,
                    "Location Permission Required",
                    "Please enable location permissions in settings.",
                    "Cancel",
                    "Settings",
                    object : HSAlertDialog.HSAlertCallback {
                        override fun onSuccess() {
                            val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                            startActivityForResult(intent, 0)
                        }
                    }).show()
                return
            }
            getHSLocation()
            collectUserLocation()
        } catch (e: Exception) {
//         #fixbug  有设备没有network provider，可能是模拟器用户
//         Fatal Exception: java.lang.IllegalArgumentException
//                    provider doesn't exist: network
            e.printStackTrace()
            moveRightRegion(null)
        }
    }

    /**
     * 先使用android自带定位api简单判断经纬度是否在指定范围内
     * 如果在范围内，则开启mapbox我的位置功能，mapbox我的位置功能使用的是play框架的混合定位
     * 反复测试过，以上这种效果最好
     */
    private fun getHSLocation() {
        val netWorkLocation = LocationUtils.getNetWorkLocation(this)
        if (netWorkLocation!=null) {
            moveRightRegion(netWorkLocation)
        }
        LocationUtils.addLocationListener(
            this,
            LocationManager.NETWORK_PROVIDER,
            object : LocationListener,
                LocationUtils.ILocationListener {
                override fun onSuccess(location: Location?) {
                    LocationUtils.unRegisterListener(this@PreconMapActivity)
                    moveRightRegion(location)
                }

                override fun onFail() {
                    moveRightRegion(null)
                }

                override fun onLocationChanged(location: Location) {
                }
            })
    }


    /**
     * 移动到正确位置
     * @param resultLocation 位置信息
     */
    private fun moveRightRegion(resultLocation: Location?) {
        // DEV-2285 如果用户点了定位，记住定位地点, 点击定位后，把用户真实的定位地址发给后端
        resultLocation?.let {
            if (LoginFragment.isLogin()) { //未登录时，不请求 api 保存 coordinate
                mapViewModel.updateProfileCoordinate(it.latitude.toString(),it.longitude.toString())
            }
        }

        // 跳转到我的位置 但是有限制，不会跳出省
        // 超出界限时，将定位到默认地址 ，目前是多伦多
        val neLatLng = LatLng(65.0, -50.0)
        val swLatLng = LatLng(40.0, -137.0)
        //           BC @49.246292,-123.116226
        //           ON @43.955259, -79.346008
        var FAILBACK_LATLON = LatLng(43.955259, -79.346008)

        val abbreviationFromCache = ProvinceHelper.getAbbreviationFromCache("ON")
        if ("ON".equals(abbreviationFromCache)) {
            FAILBACK_LATLON = LatLng(43.955259, -79.346008)
        } else if ("BC".equals(abbreviationFromCache)) {
            FAILBACK_LATLON = LatLng(49.246292, -123.116226)
        } else if ("AB".equals(abbreviationFromCache)) {
            FAILBACK_LATLON = LatLng(51.045005, -114.072129)
        }

        val FAILBACK_ZOOM = 7.0

        if (resultLocation != null) {
            var latitude = resultLocation.latitude
            var longitude = resultLocation.longitude
            var zoom = MapHelper.zoom - 1
            if (!MapUtils.inBounds(
                    LatLng(
                        latitude,
                        longitude
                    ), neLatLng, swLatLng
                )
            ) {
                latitude = FAILBACK_LATLON.latitude
                longitude = FAILBACK_LATLON.longitude
                zoom = FAILBACK_ZOOM
                mapLibreMap.cameraPosition = CameraPosition.Builder()
                    .target(
                        LatLng(
                            latitude,
                            longitude
                        )
                    )
                    .zoom(zoom)
                    .build()
            } else {
                if (isMapViewActive&&isMapStyleLoaded){
                    zoom = 14.0
                    mapLibreMap.cameraPosition = CameraPosition.Builder()
                        .zoom(zoom)
                        .build()
                    mapHelper.showMapboxLocationComponent(mapLibreMap)
                }
            }
        }
    }

    @OnShowRationale(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    fun showRationaleForRequestLocation(request: PermissionRequest) {
        HSAlertDialog(
            this,
            "Location Permission Required",
            "Please enable location permissions in settings.",
            "Cancel",
            "Settings",
            object : HSAlertDialog.HSAlertCallback {
                override fun onSuccess() {
                    request.proceed()
                }
            }).show()
    }

    @OnPermissionDenied(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    fun requestLocationDenied() {
        ToastUtils.showLong("Please enable location permissions in settings.")
    }

    @OnNeverAskAgain(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    fun requestLocationNeverAskAgain() {
        ToastUtils.showLong("Please enable location permissions in settings.")
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        onRequestPermissionsResult(requestCode, grantResults)
        if (intArrayOf(0, 0).contentEquals(grantResults)) {
            GALog.log("location_permission_request", "true")
        } else {
            GALog.log("location_permission_request", "false")
        }
    }


    private fun setUpMapboxMap(styleUrl: String) {
        mapLibreMap.addOnMapClickListener(object : MapLibreMap.OnMapClickListener {
            override fun onMapClick(point: LatLng): Boolean {
                return false
            }
        })


        mapLibreMap.setMaxZoomPreference(20.0)
        mapLibreMap.setMinZoomPreference(4.0)

        mapLibreMap.setOnMarkerClickListener(object : MapLibreMap.OnMarkerClickListener {
            override fun onMarkerClick(marker: Marker): Boolean {
                var result = CoroutineScope(Dispatchers.Main).launch {
                    handleMarkerOnclick(marker)
                }
//                return handleMarkerOnclick(marker)
                return true
            }
        })
        isMapStyleLoaded = false
        mapLibreMap.setStyle(styleUrl, object : Style.OnStyleLoaded {
            override fun onStyleLoaded(style: Style) {
                isMapStyleLoaded = true
                if (watchAreaPolygon == null) {
                    moveCameraToCenter()
                } else {
                    createEditArea()
                }

                addOnCameraMoveListener()

                val markerCenterLat = intent.getDoubleExtra("marker_center_lat", 0.0)
                val markerCenterLon = intent.getDoubleExtra("marker_center_lon", 0.0)
                if (markerCenterLat != 0.0 || markerCenterLon != 0.0) {
                    val iconFactory = IconFactory.getInstance(this@PreconMapActivity)
                    val options = MarkerOptions()
                        .position(LatLng(markerCenterLat, markerCenterLon))
                        .icon(iconFactory.fromResource(R.drawable.ic_map_location))

                    mapLibreMap.addMarker(options)
                }

            }
        })
    }

    private suspend fun handleMarkerOnclick(marker: Marker): Boolean {
        GALog.log("marker_click")
        // 有很多种marker ，需要挨个遍历改样式
        val listingMarkerInfo = listingMarkerInfoMap.get(marker.id)
        val viewInMapInfo = viewInMapInfoMap.get(marker.id)



        if (lastClickMarker != null) {
            lastListingInfo?.let {
                if ("precon_selling_now".equals(it.marker)) {
                    mapHelper.updateSaleMarker(
                        lastListingInfo!!,
                        lastClickMarker!!,
                        true
                    )
                }else if ("precon_upcoming".equals(it.marker)) {
                    mapHelper.updateRegistrationMarker(
                        lastListingInfo!!,
                        lastClickMarker!!,
                        true
                    )
                } else if ("precon_sold".equals(it.marker)) {
                    mapHelper.updateSoldMarker(
                        lastListingInfo!!,
                        lastClickMarker!!,
                        true
                    )
                } else {
                    // nothing
                }
            }
        }
        lastClickMarker = marker
        lastListingInfo = listingMarkerInfo


        viewInMapInfo?.let {
            lastMarkerProjectIds = arrayListOf(it.toString())
            getListingPreviewPreconMany()
        }


        listingMarkerInfo?.let { markerInfo ->
            val cameraZoom = mapHelper.getCameraPositionZoom(mapLibreMap)
            markerInfo.count?.let {
                // 对于group marker，数字大于等于25的时候，点击marker，应该放大地图。小于25的时候显示房源列表
                if (markerInfo.count <= 25 || cameraZoom >= 17) {
                    Logger.e("markerInfo: " + markerInfo)

                    if ("precon_selling_now".equals(markerInfo.marker)) {
                        mapHelper.updateSaleMarker(markerInfo, marker)
                    } else if ("precon_upcoming".equals(markerInfo.marker)) {
                        mapHelper.updateRegistrationMarker(markerInfo, marker)
                    } else if ("precon_sold".equals(markerInfo.marker)) {
                        mapHelper.updateSoldMarker(markerInfo, marker)
                    }

                    lastMarkerProjectIds = markerInfo.ids
                    getListingPreviewPreconMany()
                    return true
                } else {

                    var addzoom = 1.0
                    if (cameraZoom < 10) {
                        addzoom = 3.0
                    } else if (cameraZoom <= 14) {
                        addzoom = 2.0
                    }

                    val finalMoveZoom =
                        BigDecimal(cameraZoom + addzoom).setScale(1, BigDecimal.ROUND_HALF_UP)
                            .toDouble()
                    mapLibreMap.animateCamera(
                        CameraUpdateFactory.newCameraPosition(
                            CameraPosition.Builder()
                                .target(
                                    LatLng(markerInfo.location.lat, markerInfo.location.lon)
                                )
                                .zoom(finalMoveZoom)
                                .build()
                        )
                    )
                    return false
                }
            }
        }
        return true
    }


    private fun getListingPreviewPreconMany(withMarker: Boolean = false) {
        lastMarkerProjectIds?.let { mapViewModel.getListingPreviewPreconMany(it, withMarker) }
    }

    /**
     * 移动camera 到某个位置
     */
    private fun moveCameraToPoint(point: com.housesigma.android.model.Location,zoom:Double = 14.0) {
        var currentMapCenter = LatLng(point.lat, point.lon)
        var currentMapZoom = zoom
        mapLibreMap.cameraPosition = CameraPosition.Builder()
            .target(
                currentMapCenter
            )
            .zoom(currentMapZoom)
            .build()
    }

    private fun createEditArea() {
        if (watchAreaPolygon == null) return
        val polygon = watchAreaPolygon!!
        if (polygon.size < 2) return
        // 在地图上放置一个watch area的多边形
        val polygonLatLngList = ArrayList<LatLng>()
        val polylineLatLngList = ArrayList<LatLng>()
        for (polygonItem in polygon) {
            polygonLatLngList.add(LatLng(polygonItem.lat, polygonItem.lon))
            polylineLatLngList.add(LatLng(polygonItem.lat, polygonItem.lon))
        }
        polylineLatLngList.add(LatLng(polygon[0].lat, polygon[0].lon))

        mapLibreMap.addPolygon(
            PolygonOptions()
                .addAll(polygonLatLngList)
                .fillColor(Color.parseColor("#1092F0"))
                .alpha(0.3f)
        )
        mapLibreMap.addPolyline(
            PolylineOptions()
                .addAll(polylineLatLngList)
                .color(Color.parseColor("#1E91FB"))
                .width(3.0f)
                .alpha(1f)
        )



        if (polygon != null) {
            val latLngBounds = LatLngBounds.Builder()
            polygon.forEach {
                latLngBounds.include(MapUtils.polygon2LatLng(it))
            }
            mapLibreMap.animateCamera(
                CameraUpdateFactory.newLatLngBounds(
                    latLngBounds.build(), 100
                )
            )
        }
    }

    /**
     * 移动camera 到中心点位置，在这儿的位置是固定的几个点
     */
    private fun moveCameraToCenter() {
        // 这里的位置和选择的location相关  数据来源于 /api/init/app的 provinces中

        var currentMapCenter = MapUtils.getCurrentMapCenter()
        var currentMapZoom = MapUtils.getCurrentMapZoom()

        mapLibreMap.cameraPosition = CameraPosition.Builder()
            .target(
                currentMapCenter
            )
            .zoom(currentMapZoom)
            .build()
    }

    /**
     * 监听camera move，如在60ms内有变化，就可以获取屏幕可见经纬度范围，请求api接口
     */
    private fun addOnCameraMoveListener() {
        getMapRequestRegion()

//        https://stackoverflow.com/questions/38727517/oncamerachangelistener-is-deprecated
//        用addOnCameraIdleListener监听代替OnCameraChangeListener
        mapLibreMap.addOnCameraIdleListener {
            if (!ready) return@addOnCameraIdleListener
            Logger.e("move camera idle.........")
            // 可见区域
            getMapRequestRegion()
            reloadMapData()
        }

    }

    private fun getMapRequestRegion() {
        val visibleRegion = mapLibreMap.projection.visibleRegion
//        val farLeft = visibleRegion.farLeft //可视区域的左上角。
//        val nearRight = visibleRegion.nearRight //可视区域的右下角。
        val bounds = visibleRegion.latLngBounds
        val center = visibleRegion.latLngBounds.center
        val cameraZoom = mapHelper.getCameraPositionZoom(mapLibreMap)

        if (!"prod".equals(BuildConfig.FLAVOR)) {
            binding.tvTestZoom.text = "zoom:" + cameraZoom
        }

        bounds.let {
            val west = it.longitudeWest
            val south = it.latitudeSouth
            val east = it.longitudeEast
            val north = it.latitudeNorth

            val extraLat = Math.abs(south - north) / 2
            val extraLon = Math.abs(west - east) / 2

            //真实的经纬度，listview模式下list列表接口需要
            realLat1 = it.latitudeNorth
            realLon1 = it.longitudeEast
            realLat2 = it.latitudeSouth
            realLon2 = it.longitudeWest

            //扩大后的经纬度，linting接口需要
            lat1 = it.latitudeNorth + (if (north > south) extraLat else -extraLat)
            lon1 = it.longitudeEast + (if (east > west) extraLon else -extraLon)
            lat2 = it.latitudeSouth + (if (south > north) extraLat else -extraLat)
            lon2 = it.longitudeWest + (if (west > east) extraLon else -extraLon)
        }

        MapUtils.setCurrentMapCenter(LatLng(center.latitude, center.longitude))
        MapUtils.setCurrentMapZoom(cameraZoom)
    }

    private fun isListView(): Boolean {
        return viewType == "list"
    }

    val reloadMapData = debounce(250L) {
        if (isListView()) {//list-view
            updateMapFilterRedPoint() // 在list模式下更新小红点
            changeListView()
        } else {//map-view
            requestDataWithinVisibleMapArea()
            hideProgress()
        }
    }


    private fun showProgress() {
        binding.progress.visibility = View.VISIBLE
    }


    private fun hideProgress() {
        binding.progress.visibility = View.GONE
    }

    private fun requestDataWithinVisibleMapArea() {
//        Logger.e( "debounceReloadMapData2: "+System.currentTimeMillis())
        if (lat1 == 0.0) return
        Logger.e( "list type " + listType)
        showProgress()
        updateMapFilterRedPoint()
        binding.progress.visibility = View.VISIBLE
        val cameraZoom = mapHelper.getCameraPositionZoom(mapLibreMap)
        mapConstructionStatusView!!.getPropertyTypes()
        mapViewModel.searchPreconMapProjects(
            bedroom_range = mapMoreFiltersView!!.bedroomRange,

            bathroom_min = mapMoreFiltersView!!.bathroomMin,

            project_status = listType,

            construction_status = mapConstructionStatusView!!.getPropertyTypes(),

            est_completion_year = mapMoreFiltersView!!.estCompletionYearFilter,

            property_type = mapPropertyView!!.getPropertyTypes(),

            description = mapMoreFiltersView!!.description,

            price = mapMoreFiltersView!!.getPrice(),

            square_footage = mapMoreFiltersView!!.getSquareFootage(),

            lon1 = lon1,
            lon2 = lon2,
            lat1 = lat1,
            lat2 = lat2,
            zoom = cameraZoom
        )
    }





    override fun onStart() {
        super.onStart()
        mapView.onStart()
    }

    override fun onResume() {
        super.onResume()
        mapView.onResume()
        GALog.page("precon_map")
        isMapViewActive = true
    }

    override fun onPause() {
        super.onPause()
        isMapViewActive = false
        mapView.onPause()
    }

    override fun onStop() {
        super.onStop()
        mapView.onStop()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mapView.onSaveInstanceState(outState)
    }

    override fun onLowMemory() {
        super.onLowMemory()
        mapView.onLowMemory()
    }

    override fun onDestroy() {
        super.onDestroy()
        mapView.onDestroy()
        binding.progress.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
    }
    private fun showListingPreview(){
        // 先GONE，再展开，解决preview list个数为1的时候，bottomSheetBehavior悬浮在手机的最中间，底部留白的问题
        binding.llListings.visibility = View.GONE
        binding.llListings.visibility = View.VISIBLE
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_COLLAPSED
    }

    private fun hiddenListingPreview(){
        binding.llListings.visibility = View.GONE
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        try {
            when (event.type) {
                MessageType.RELOAD_MAP -> {
                    getMapFilterList()
                }

                else -> {}
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}