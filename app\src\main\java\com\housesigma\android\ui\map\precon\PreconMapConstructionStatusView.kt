package com.housesigma.android.ui.map.precon

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import com.donkingliang.labels.LabelsView
import com.housesigma.android.databinding.PopwindowMapFilterTypeBinding
import com.housesigma.android.model.ConstructionStatusFilter
import com.housesigma.android.model.PreconMapFilter
import com.housesigma.android.utils.Callback1
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.log.Logger

open class PreconMapConstructionStatusView {


    private lateinit var binding: PopwindowMapFilterTypeBinding
    private var propertyTypes: ArrayList<String> = ArrayList()
    private var mapFilter: PreconMapFilter? = null
    private var mPropertyTextView :TextView? = null
    private var mPropertyCountTextView: TextView? = null


    fun getPropertyTypes(): ArrayList<String> {
        Logger.d( "getPropertyTypes " + propertyTypes.toString())
        if (propertyTypes.size == 0) {
            Logger.d( "getPropertyTypes == 0" )
            return arrayListOf("all")
        }
        Logger.d("hkj", "getPropertyTypes != 0" )
        return propertyTypes
    }

    fun getLayout(): PopwindowMapFilterTypeBinding {
        return binding
    }

    fun createView(layoutInflater: LayoutInflater): PreconMapConstructionStatusView {
        binding = PopwindowMapFilterTypeBinding.inflate(layoutInflater)
        val propertyLabels = binding.labels
        propertyLabels.selectType = LabelsView.SelectType.MULTI
        return this
    }


    fun setApplyClickListener(cb: Callback1) {
        binding.tvApply.setOnClickListener {
            cb.onData("")
        }
    }



    fun setData(data: PreconMapFilter) {
        mapFilter = data
        val propertyLabels = binding.labels
        propertyLabels.setLabels(data?.construction_status_filter) { _, _, data -> data.name }

        val tempHouseTypeLocal = ArrayList<String>()
        data.construction_status_filter.forEachIndexed { index, houseTypeFilter ->
            for (defaultHouseType in data.default_filter.construction_status) {
                if (defaultHouseType.equals(houseTypeFilter.id)) {
                    tempHouseTypeLocal.add(index.toString())
                }
            }
        }
        data.default_filter.preconConstructionStatusLocal = tempHouseTypeLocal

        if (TextUtils.isEmpty(MMKVUtils.getStr( "preconConstrutionStatusLabels"))){
            MMKVUtils.saveStr("preconConstrutionStatusLabels",  data.default_filter.preconConstructionStatusLocal.joinToString(","))
        }
    }


    fun setClearClickListener(cb: Callback1) {
        binding.tvClearAllFilters.setOnClickListener {
            clearFilters()
            cb.onData("")
        }
    }

    fun clearFilters() {
        mapFilter?.let {
            MMKVUtils.removeData("preconConstrutionStatusLabels")
            setData(it)
        }
        setLabelValueFromCache()
    }


    fun setOnLabelClickListener(cb: Callback1) {

        var propertyLabels = binding.labels

        propertyLabels.setOnLabelClickListener { label, data, position ->
            // 对选项进行处理 选了all就不能选择其他的，选择其他的就要去掉all
            if (position == 0) {
                propertyLabels.setSelects(0)
            } else {
                val selectLabels = propertyLabels.selectLabels
                selectLabels.remove(0)
                propertyLabels.setSelects(selectLabels)
            }
            var selectLabelDatas =
                propertyLabels.getSelectLabelDatas<ConstructionStatusFilter>()

            if (selectLabelDatas.size == 0) {
                propertyLabels.setSelects(0)
                selectLabelDatas =
                    propertyLabels.getSelectLabelDatas<ConstructionStatusFilter>()
            }


            //除了所有房源类型之外的房源类型条件都选中的情况应变为 选中所有房源类型条件，除此之外的房源类型条件变为未选择状态
            val allLabel = propertyLabels.getLabels<ConstructionStatusFilter>()
            if (selectLabelDatas.size - 1 == allLabel.size - 2) {
                propertyLabels.setSelects(0)
                selectLabelDatas =
                    propertyLabels.getSelectLabelDatas<ConstructionStatusFilter>()
            }

            propertyTypes?.clear()
            for (selectLabelData in selectLabelDatas) {
                propertyTypes?.add(selectLabelData.id)
            }


            // 持久化 写入cache
            val cacheProperty = propertyLabels.selectLabels.toString()
                .replace("[", "")
                .replace("]", "")
                .replace(" ", "")
            MMKVUtils.saveStr("preconConstrutionStatusLabels", cacheProperty)
            if (selectLabelDatas.size > 0) {
                cb.onData(selectLabelDatas)
            } else {
                cb.onData("All")
            }

        }
    }

    
    fun setLabelValueFromCache() {
        val propertyCache = readCache()
        if (propertyCache is String) {
            mPropertyTextView?.text = propertyCache
        } else if (propertyCache is List<*>) {
            if ((propertyCache as List<ConstructionStatusFilter>).size > 1) {
                mPropertyCountTextView?.visibility = View.VISIBLE
                mPropertyTextView?.text =
                    (propertyCache as List<ConstructionStatusFilter>)[0].name
                mPropertyCountTextView?.text =
                    ((propertyCache as List<ConstructionStatusFilter>).size - 1).toString() + "+"
            } else if ((propertyCache as List<ConstructionStatusFilter>).size == 1) {
                mPropertyCountTextView?.visibility = View.GONE
                mPropertyTextView?.text =
                    (propertyCache as List<ConstructionStatusFilter>)[0].name
            } else {
                mPropertyCountTextView?.visibility = View.GONE
                mPropertyCountTextView?.text = ""
            }
        }
    }

    
    fun setLabelTextView(propertyTextView :TextView,propertyCountTextView: TextView) {
        mPropertyTextView = propertyTextView
        mPropertyCountTextView = propertyCountTextView
    }

    /**
     * return ArrayList<String>  和 String
     *
     */
    fun readCache(): Any {
        var propertyLabels = binding.labels

        val str = MMKVUtils.getStr( "preconConstrutionStatusLabels")
        if (!TextUtils.isEmpty(str)) {
            var split = str?.split(",")!!
            var list = ArrayList<Int>()
            for (s in split) {
                list.add(s.toInt())
            }
            propertyLabels.setSelects(list)

            val selectLabelDatas =
                propertyLabels.getSelectLabelDatas<ConstructionStatusFilter>()
            propertyTypes?.clear()
            for (selectLabelData in selectLabelDatas) {
                propertyTypes?.add(selectLabelData.id)
            }

            if (selectLabelDatas.size > 0) {
//           tvPropertyTypes = selectLabelDatas[0].name
                return selectLabelDatas
            } else {
                return "All"
            }
        } else {
            propertyLabels.setSelects(0)
        }
        return "All"
    }

}