package com.housesigma.android.ui.map.precon

import android.content.Context
import android.graphics.PointF
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.donkingliang.labels.LabelsView
import com.housesigma.android.R
import com.housesigma.android.databinding.PopwindowPreconMapFilterMoreBinding
import com.housesigma.android.model.*
import com.housesigma.android.ui.map.MapType
import com.housesigma.android.ui.map.filters.MapFiltersListView
import com.housesigma.android.utils.Callback0
import com.housesigma.android.utils.Callback1
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.HSAlertDialog
import com.housesigma.android.views.PreconSeekBar
import com.housesigma.android.views.SaveFiltersDialog
import com.jaygoo.widget.OnRangeChangedListener
import com.jaygoo.widget.RangeSeekBar
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.enums.PopupPosition

open class PreconMapMoreFiltersView() {

    private val TAG = "PreconMapFilterLayout_V2"

    private lateinit var binding: PopwindowPreconMapFilterMoreBinding

    var bedroomRange: ArrayList<String> = ArrayList()

    var estCompletionYearFilter: ArrayList<String> = ArrayList()

    var bathroomMin: String = "0"

    var description: String = ""

    var rightMaxValue: Int = 6000000
    var priceLeft: Int = 0
    var priceRight: Int = rightMaxValue



    var squareFootageLeft: Int = 0
    var squareFootageRight: Int = 4000


    var mcb: Callback0? = null

    private var defaultFilter:PreconDefaultFilter?=null
    private var mMapFilter:PreconMapFilter?=null


    private var mSaveMapFilterList: ArrayList<SaveMapFilter> = ArrayList()
    private var mSelectSaveMapFilter: SaveMapFilter?=null
    private val mMapFiltersListLayout : MapFiltersListView by lazy {
        MapFiltersListView(binding.root.context,MapType.PRECON)
    }

    fun getLayout(): PopwindowPreconMapFilterMoreBinding {
        return binding
    }


    open fun getPrice(): ArrayList<String> {
        return arrayListOf(priceLeft.toString(),priceRight.toString())
    }


    open fun getSquareFootage(): ArrayList<String> {
        return arrayListOf(squareFootageLeft.toString(),squareFootageRight.toString())
    }

    /*
      * type:true为售卖，false为租赁
     */
    fun createView(layoutInflater: LayoutInflater): PreconMapMoreFiltersView {
        binding = PopwindowPreconMapFilterMoreBinding.inflate(layoutInflater)
        binding.saleSb.visibility = View.VISIBLE
        binding.rentalSb.visibility = View.GONE

        binding.saleSb.setDefaultValue()
        rightMaxValue = binding.saleSb.priceRight
        priceLeft = binding.saleSb.priceLeft
        priceRight = binding.saleSb.priceRight
        binding.saleSb.setOnChangeListener(object : PreconSeekBar.OnChangeListener {
            override fun onChange(showPrice: String) {
                binding.tvPrice.text = showPrice
                priceLeft = binding.saleSb.priceLeft
                priceRight = binding.saleSb.priceRight

                MMKVUtils.saveInt(TAG +"sale_sb_left",priceLeft)
                MMKVUtils.saveInt(TAG +"sale_sb_right",priceRight)
            }

            override fun onStopTrackingTouch() {
                if (mcb != null) {
                    (mcb as Callback0).onData()
                }
            }
        })





        binding.rsbSquare.setOnRangeChangedListener(object : OnRangeChangedListener {
            override fun onRangeChanged(
                view: RangeSeekBar?,
                leftValue: Float,
                rightValue: Float,
                isFromUser: Boolean
            ) {

                squareFootageLeft = leftValue.toInt()
                squareFootageRight = rightValue.toInt()
                if (leftValue.toInt() == 0 && rightValue.toInt() == 4000) {
                    binding.tvSquare.text =
                        "Square Footage: Unspecified" + " - Max"
                } else if (rightValue.toInt() == 4000) {
                    binding.tvSquare.text =
                        "Square Footage: ".plus(leftValue.toInt()) + " - Max"
                } else if (leftValue.toInt() == 0) {
                    binding.tvSquare.text =
                        "Square Footage: Unspecified" + " - " + rightValue.toInt()
                } else {
                    binding.tvSquare.text =
                        "Square Footage: ".plus(leftValue.toInt()) + " - " + rightValue.toInt()
                }

                MMKVUtils.saveInt(TAG +"rsb_square_left", squareFootageLeft)
                MMKVUtils.saveInt(TAG +"rsb_square_right", squareFootageRight)
            }

            override fun onStartTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
            }

            override fun onStopTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
                if (mcb != null) {
                    (mcb as Callback0).onData()
                }
                GALog.log("map_filters_click","square_footage")
            }
        })


        binding.etDescription.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                Logger.d( "description onTextChanged:$p0")
                description = p0.toString()

                MMKVUtils.saveStr(TAG + "description", description)
            }

            override fun afterTextChanged(p0: Editable?) {

            }

        })





        return this
    }


    fun setData(mapFilter: PreconMapFilter) {
        mMapFilter = mapFilter
//        val tempBasementLocal = ArrayList<String>()
//        mapFilter.basement_filter.forEachIndexed { index, basementFilter ->
//            for (defaultBasement in mapFilter.default_filter.basement) {
//                if (defaultBasement.equals(basementFilter.id)) {
//                    tempBasementLocal.add(index.toString())
//                }
//            }
//        }
//        mapFilter.default_filter.basementLocal = tempBasementLocal

        val tempBedroomLocal = ArrayList<String>()
        mapFilter.bedrooms_filter.forEachIndexed { index, bedRoomFilter ->
            for (defaultBedroom in mapFilter.default_filter.bedroom) {
                if (defaultBedroom.equals(bedRoomFilter.id.toString())) {
                    tempBedroomLocal.add(index.toString())
                }
            }
        }
        mapFilter.default_filter.bedroomLocal = tempBedroomLocal


        val tempEstCompletionYearLocal = ArrayList<String>()
        mapFilter.est_completion_year_filter.forEachIndexed { index, listingType ->
            for (defaultEstCompletionYear in mapFilter.default_filter.est_completion_year) {
                if (defaultEstCompletionYear.equals(listingType.id.toString())) {
                    tempEstCompletionYearLocal.add(index.toString())
                }
            }
        }
        mapFilter.default_filter.estCompletionYearLocal = tempEstCompletionYearLocal



//
//        if (TextUtils.isEmpty(MMKVUtils.getStr(TAG + "map_listing_days"))){
//            var itemListingDay : ListingFilter ?= null
//            for (listingFilter in mapFilter.days_filter_all.listing) {
//                if (mapFilter.default_filter.listing_days.equals(listingFilter.id)){
//                    itemListingDay = listingFilter
//                    break
//                }
//            }
//            itemListingDay?.let {
//                MMKVUtils.saveStr(TAG + "map_listing_days",itemListingDay.id)//listing_days
//                MMKVUtils.saveStr(TAG + "map_fs_ld_abbr", itemListingDay.abbr)
//            }
//
//            var itemSoldDay : ListingFilter ?= null
//            for (listingFilter in mapFilter.days_filter_all.de_list) {
//                if (mapFilter.default_filter.de_list_days.equals(listingFilter.id)){
//                    itemSoldDay = listingFilter
//                    break
//                }
//            }
//            itemSoldDay?.let {
//                MMKVUtils.saveStr(TAG + "map_s_ld",itemSoldDay.id)//listing_days
//                MMKVUtils.saveStr(TAG + "map_s_ld_abbr", itemSoldDay.abbr)
//            }
//        }






        defaultFilter = mapFilter.default_filter
        val labelsBathroom = binding.labelsBathroom
        val labelsBedroom = binding.labelsBeadroom
        val labelsEstCompletionYear = binding.labelsEstCompletionYear



        labelsBedroom.selectType = LabelsView.SelectType.MULTI
        labelsBedroom.setLabels(mapFilter?.bedrooms_filter) { _, _, data -> data.name }



        labelsBathroom.selectType = LabelsView.SelectType.SINGLE_IRREVOCABLY
        labelsBathroom.setLabels(mapFilter?.bathrooms_filter) { _, _, data -> data.name }


        labelsEstCompletionYear.selectType = LabelsView.SelectType.MULTI
        labelsEstCompletionYear.setLabels(mapFilter?.est_completion_year_filter) { _, _, data -> data.name }
    }


    fun setApplyClickListener(cb: Callback1) {
        binding.tvApply.setOnClickListener {
            cb.onData("")
        }
    }


    fun setClearClickListener(cb: Callback1) {
        binding.tvClearAllFilters.setOnClickListener {
            // savefilter组件恢复默认值
            restoreSaveFilterSettings()

            // mapfilter恢复默认值
            cleanCache()
            readCache()

            // 回调请求api
            cb.onData("")
        }
    }


    fun setOnLabelClickListener(cb: Callback0) {

        mcb = cb
        val labelsBathroom = binding.labelsBathroom
        val labelsBedroom = binding.labelsBeadroom
        val labelsEstCompletionYear = binding.labelsEstCompletionYear
//        val sbNearbySold = binding.sbNearbySold

//        nearbySold = MMKVUtils.getBoolean(TAG + "nearbySold", true)
//        sbNearbySold.setOnCheckedChangeListener(object : SwitchButton.OnCheckedChangeListener {
//
//            override fun onCheckedChanged(button: SwitchButton, checked: Boolean) {
//                nearbySold = checked
//                MMKVUtils.saveBoolean(TAG + "nearbySold", checked)
//                cb.onData()
//            }
//        })


        labelsBathroom.let {
            it.setOnLabelClickListener { label, data, position ->
                bathroomMin = position.toString()
                // 持久化 写入cache
                MMKVUtils.saveStr(TAG + "labelsBathroom", bathroomMin)
                cb.onData()//回调回去
            }
        }

        labelsEstCompletionYear.let {
            it.setOnLabelClickListener { label, data, position ->
                // 对选项进行处理 选了all就不能选择其他的，选择其他的就要去掉all
                if (position == 0) {
                    it.setSelects(0)
                } else {
                    val selectLabels = it.selectLabels
                    selectLabels.remove(0)
                    it.setSelects(selectLabels)
                }

                var selectLabelDatas =
                    it.getSelectLabelDatas<EstCompletionYearFilter>()
                if (selectLabelDatas.size == 0) {
                    it.clearAllSelect()
                    //所有选择都取消后，需要默认选择 all
                    it.setSelects(0)
                    selectLabelDatas =
                        it.getSelectLabelDatas()
                }

                val allLabel = it.getLabels<EstCompletionYearFilter>()
                if (selectLabelDatas.size - 1 == allLabel.size - 2) {
                    it.setSelects(0)
                    selectLabelDatas =
                        it.getSelectLabelDatas()
                }

                estCompletionYearFilter?.clear()
                for (selectLabelData in selectLabelDatas) {
                    estCompletionYearFilter?.add(selectLabelData.id)
                }

                // 持久化 写入cache
                val cacheProperty = it.selectLabels.toString()
                    .replace("[", "")
                    .replace("]", "")
                    .replace(" ", "")

                MMKVUtils.saveStr(TAG + "labelsEstCompletionYear", cacheProperty)
                cb.onData()//回调回去
            }
        }

        labelsBedroom.let {
            it.setOnLabelClickListener { label, data, position ->
                // 对选项进行处理 选了all就不能选择其他的，选择其他的就要去掉all
                if (position == 0) {
                    it.setSelects(0)
                } else {
                    val selectLabels = it.selectLabels
                    selectLabels.remove(0)
                    it.setSelects(selectLabels)
                }
                var selectLabelDatas =
                    it.getSelectLabelDatas<MapFilterIdName>()

                if (selectLabelDatas.size == 0) {
                    it.setSelects(0)
                    selectLabelDatas =
                        it.getSelectLabelDatas()
                }

                val allLabel = it.getLabels<MapFilterIdName>()
                if (selectLabelDatas.size - 1 == allLabel.size - 2) {
                    it.setSelects(0)
                    selectLabelDatas =
                        it.getSelectLabelDatas()
                }

                bedroomRange?.clear()
                for (selectLabelData in selectLabelDatas) {
                    bedroomRange?.add(selectLabelData.id.toString())
                }


                // 持久化 写入cache
                val cacheProperty = it.selectLabels.toString()
                    .replace("[", "")
                    .replace("]", "")
                    .replace(" ", "")
                MMKVUtils.saveStr(TAG + "labelsBeadroom", cacheProperty)
                cb.onData()//回调回去
            }
        }


    }

    /**
     * 是否被修改过，修改过返回ture，显示红点
     */
    fun isModified(): Boolean {
        defaultFilter?.let { defaultFilter->
            val minPrice = defaultFilter.price_min.toInt()
            if ((!(bedroomRange.containsAll(defaultFilter.bedroom) && defaultFilter.bedroom.containsAll(bedroomRange)))
                || bathroomMin != defaultFilter.bathroom
                || description != defaultFilter.description
                || priceLeft != minPrice
                || priceRight != rightMaxValue
                || squareFootageLeft != defaultFilter.square_footage_min.toInt()
                || squareFootageRight != defaultFilter.square_footage_max.toInt()
                || !(estCompletionYearFilter.containsAll(defaultFilter.est_completion_year) && defaultFilter.est_completion_year.containsAll(estCompletionYearFilter))
            ) {
                return true
            }
        }
        return false
    }

    fun cleanCache() {
        MMKVUtils.removeData(TAG + "init")
        bedroomRange.clear()
        bathroomMin = "0"
        description = ""

        priceLeft = 0
        priceRight = rightMaxValue

        squareFootageLeft = 0
        squareFootageRight = 4000

        binding.saleSb.setDefaultValue()
        priceLeft = binding.saleSb.priceLeft
        priceRight = binding.saleSb.priceRight
        rightMaxValue = binding.saleSb.priceRight

        MMKVUtils.removeData(TAG +"sale_sb_left")
        MMKVUtils.removeData(TAG +"sale_sb_right")
        MMKVUtils.removeData(TAG +"labelsEstCompletionYear")


        MMKVUtils.removeData(TAG +"rsb_feet_left")
        MMKVUtils.removeData(TAG +"rsb_feet_right")
        MMKVUtils.removeData(TAG +"rsb_square_left")
        MMKVUtils.removeData(TAG +"rsb_square_right")
        MMKVUtils.removeData(TAG +"description")
        MMKVUtils.removeData(TAG +"fee")
        MMKVUtils.removeData(TAG +"labelsBathroom")
        MMKVUtils.removeData(TAG +"labelsGarageParking")
        MMKVUtils.removeData(TAG +"labelsOpenHouse")
        MMKVUtils.removeData(TAG +"labelsBasement")
        MMKVUtils.removeData(TAG +"labelsBeadroom")
    }

    fun readCache() {
        initDefaultFilter()

        val description = MMKVUtils.getStr(TAG + "description")

        val labelsBeadroom = binding.labelsBeadroom

        val labelsBathroom = binding.labelsBathroom
        val labelsEstCompletionYear = binding.labelsEstCompletionYear

        val etDescription = binding.etDescription

        val rsbSquare = binding.rsbSquare

        squareFootageLeft = MMKVUtils.getInt(TAG + "rsb_square_left",squareFootageLeft)
        squareFootageRight = MMKVUtils.getInt(TAG + "rsb_square_right",squareFootageRight)
        rsbSquare.setProgress(squareFootageLeft.toFloat(), squareFootageRight.toFloat())


        val left = HSUtil.calRevertSquareFootageCurve(MMKVUtils.getInt(TAG + "sale_sb_left",priceLeft))
        val right = HSUtil.calRevertSquareFootageCurve(MMKVUtils.getInt(TAG + "sale_sb_right",rightMaxValue))


        binding.saleSb.setProgress(left.toFloat(),right.toFloat())


        if (!TextUtils.isEmpty(description)) {
            etDescription.setText(description)
            this.description = description!!
        } else {
            etDescription.setText("")
            this.description = ""
        }


        labelsBathroom.let {
            val str = MMKVUtils.getStr(TAG + "labelsBathroom")
            if (!TextUtils.isEmpty(str)) {
                it.setSelects(str!!.toInt())
                bathroomMin = str
            } else {
                it.setSelects(0)
            }
        }


        labelsEstCompletionYear.let {
            val str = MMKVUtils.getStr(TAG + "labelsEstCompletionYear")
            if (!TextUtils.isEmpty(str)) {
                var split = str?.split(",")!!
                var list = ArrayList<Int>()
                for (s in split) {
                    list.add(s.toInt())
                }
                it.setSelects(list)
            } else {
                it.setSelects(0)
            }

            val selectLabelDatas =
                it.getSelectLabelDatas<EstCompletionYearFilter>()
            estCompletionYearFilter?.clear()
            for (selectLabelData in selectLabelDatas) {
                estCompletionYearFilter?.add(selectLabelData.id)
            }
        }



        labelsBeadroom.let {
            val str = MMKVUtils.getStr(TAG + "labelsBeadroom")
            if (!TextUtils.isEmpty(str)) {
                var split = str?.split(",")!!
                var list = ArrayList<Int>()
                for (s in split) {
                    list.add(s.toInt())
                }
                it.setSelects(list)

                val selectLabelDatas =
                    it.getSelectLabelDatas<MapFilterIdName>()
                bedroomRange?.clear()
                for (selectLabelData in selectLabelDatas) {
                    bedroomRange?.add(selectLabelData.id.toString())
                }
            } else {
                it.setSelects(0)
            }
        }


    }

    private fun initDefaultFilter() {
        defaultFilter?.let { defaultFilter->
            val descriptionStr = defaultFilter.description
            val square_footage_min = defaultFilter.square_footage_min
            val square_footage_max = defaultFilter.square_footage_max
            val bathroom = defaultFilter.bathroom
            val price_sale_min = defaultFilter.price_min
            val price_sale_max = defaultFilter.price_max
            val bedroom = defaultFilter.bedroomLocal.joinToString(",")

            if (TextUtils.isEmpty(MMKVUtils.getStr(TAG + "init"))){
                MMKVUtils.saveStr(TAG + "init","true")
                MMKVUtils.saveStr(TAG + "description",descriptionStr)
                MMKVUtils.saveInt(TAG + "rsb_square_left",square_footage_min.toInt())
                MMKVUtils.saveInt(TAG + "rsb_square_right",square_footage_max.toInt())

                MMKVUtils.saveInt(TAG + "sale_sb_left",price_sale_min.toInt())
                MMKVUtils.saveInt(TAG + "sale_sb_right",price_sale_max.toInt())

                priceLeft =  price_sale_min.toInt()
                rightMaxValue = price_sale_max.toInt()
                MMKVUtils.saveStr(TAG + "labelsBathroom", bathroom)
                MMKVUtils.saveStr(TAG + "labelsBeadroom",bedroom)
            }
        }





    }


    fun getKey(key:String): String {
        return key + "_precon"
    }

    // save filter 逻辑开始
    fun saveFiltersName(filtersName: String){
        MMKVUtils.saveStr(getKey("select_filter_name"),filtersName)
    }

    fun setSaveFiltersClickListener(context: AppCompatActivity,
                                    cb: Callback1) {
        binding.tvSaveFilterCreate.setOnClickListener {
            val dialog = SaveFiltersDialog(
                object: SaveFiltersDialog.SaveFiltersCallback {
                    override fun onSuccess(filtersName: String) {
                        saveFiltersName(filtersName)
                        cb.onData(filtersName)
                    }
                }, mSelectSaveMapFilter)
            dialog.show(context.supportFragmentManager,"")
        }

        binding.tvSaveFilterSave.setOnClickListener {
            val dialog = SaveFiltersDialog(
                object: SaveFiltersDialog.SaveFiltersCallback {
                    override fun onSuccess(filtersName: String) {
                        saveFiltersName(filtersName)
                        cb.onData(filtersName)
                    }
                }, mSelectSaveMapFilter)
            dialog.show(context.supportFragmentManager,"")
        }
    }

    fun setDeleteFiltersClickListener(context: Context, cb: Callback1) {
        binding.tvSaveFilterDelete.setOnClickListener {
            if (mSelectSaveMapFilter==null) return@setOnClickListener
            val selectFilterName = mSelectSaveMapFilter?.filterName ?:"---"
            val selectId = mSelectSaveMapFilter?.id ?: ""
            HSAlertDialog(
                context, "Delete Filter", "Are you sure you want to Delete filter \""+  selectFilterName +"\"",
                "Cancel", "OK",
                object : HSAlertDialog.HSAlertCallback {
                    override fun onSuccess() {
                        binding.spinnerSaveFilter.text = "---"
                        mSelectSaveMapFilter = null

                        binding.tvSaveFilterDelete.setBackgroundResource(R.drawable.shape_10radius_gray_fill)
                        binding.tvSaveFilterDelete.setTextColor(context.resources.getColor(R.color.color_gray))

                        MMKVUtils.removeData(getKey("select_filter_name"))
                        cb.onData(selectId)
                    }
                }).show()
        }
    }


    fun bindSaveFilterSelectList(context: Context, list:ArrayList<SaveMapFilter>, cb: Callback1) {
        mSaveMapFilterList = list
        if (mSaveMapFilterList.size==0) {
            binding.spinnerSaveFilter.visibility = View.GONE
            binding.tvSaveFilterCreate.visibility = View.VISIBLE
            binding.tvSaveFilterSave.visibility = View.GONE
            binding.tvSaveFiltersTitle.visibility = View.GONE
            binding.tvSaveFilterDelete.visibility = View.GONE
        } else {
            binding.spinnerSaveFilter.visibility = View.VISIBLE
            binding.tvSaveFilterCreate.visibility = View.GONE
            binding.tvSaveFilterSave.visibility = View.VISIBLE
            binding.tvSaveFiltersTitle.visibility = View.VISIBLE
            binding.tvSaveFilterDelete.visibility = View.VISIBLE
        }
        binding.viewLineSaveFilters.visibility = View.VISIBLE

        val selectFilterName = MMKVUtils.getStr(getKey("select_filter_name"))?:"---"
        binding.spinnerSaveFilter.text = selectFilterName

        mSaveMapFilterList.forEach {
            if (it.filterName == selectFilterName) {
                mSelectSaveMapFilter = it
                binding.tvSaveFilterDelete.setBackgroundResource(R.drawable.shape_10radius_main_color)
                binding.tvSaveFilterDelete.setTextColor(context.resources.getColor(R.color.app_main_color))
            }
        }


        mMapFiltersListLayout.setData(mSaveMapFilterList)
        mMapFiltersListLayout?.setSelectClickListener(object :Callback1{
            override fun onData(any: Any) {
                val position = (any as Int)
                try {
                    GALog.log("saved_filters_select_click")
                    mSelectSaveMapFilter = mSaveMapFilterList[position]
                    val filter = mSelectSaveMapFilter?.filter
                    binding.spinnerSaveFilter.text = mSelectSaveMapFilter?.filterName

                    binding.tvSaveFilterDelete.setBackgroundResource(R.drawable.shape_10radius_main_color)
                    binding.tvSaveFilterDelete.setTextColor(context.resources.getColor(R.color.app_main_color))
                    filter?.let {
                        restoreFromMapFilters(filter)
                        cb.onData(filter)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        })

        binding.spinnerSaveFilter.setOnClickListener {
            val spinnerHeight =  binding.spinnerSaveFilter.measuredHeight
            val spinnerWidth =  binding.spinnerSaveFilter.measuredWidth

            // 重新计算组件在屏幕的位置
            val locations = IntArray(2)
            binding.spinnerSaveFilter.getLocationOnScreen(locations)

            XPopup.Builder(context)
                .isCenterHorizontal(true)
                .atPoint(PointF(locations[0].toFloat(),locations[1].toFloat()))
                .popupPosition(PopupPosition.Bottom)
                .navigationBarColor(context.resources.getColor(R.color.navigation_bar_color))
                .popupWidth(spinnerWidth)
                .offsetY(spinnerHeight)
                .offsetX(spinnerWidth/2)
                .hasShadowBg(false)
                .animationDuration(0)
                .asCustom(mMapFiltersListLayout)
                .show()
        }
    }

    private fun restoreFromMapFilters(selectFilters: MapFilters) {
        val descriptionStr = selectFilters.description?:"0"
        val square_footage_min = selectFilters.square_footage_min?:"0"
        val square_footage_max = selectFilters.square_footage_max?:"0"
        val bathroom = selectFilters.bathroom
        val price_sale_min = selectFilters.price?.getOrNull(0) ?: "0"
        val price_sale_max = selectFilters.price?.getOrNull(1) ?: "0"

        val positions = ArrayList<Int>()
        selectFilters.bedroom?.forEach {
            mMapFilter?.bedrooms_filter?.forEachIndexed { index, item ->
                if (item.id == it) {
                    positions.add(index)
                }
            }
        }
        val bedroom = positions.joinToString(",")

        positions.clear()
        selectFilters.est_completion_year?.forEach {
            mMapFilter?.est_completion_year_filter?.forEachIndexed { index, item ->
                if (item.id == it) {
                    positions.add(index)
                }
            }
        }
        val estYear = positions.joinToString(",")


        MMKVUtils.saveStr(TAG + "description",descriptionStr)
        MMKVUtils.saveInt(TAG + "rsb_square_left",square_footage_min.toInt())
        MMKVUtils.saveInt(TAG + "rsb_square_right",square_footage_max.toInt())

        MMKVUtils.saveInt(TAG + "sale_sb_left",price_sale_min.toInt())
        MMKVUtils.saveInt(TAG + "sale_sb_right",price_sale_max.toInt())

        priceLeft =  price_sale_min.toInt()
        rightMaxValue = price_sale_max.toInt()
        MMKVUtils.saveStr(TAG + "labelsBathroom", bathroom)
        MMKVUtils.saveStr(TAG + "labelsBeadroom",bedroom)
        MMKVUtils.saveStr(TAG + "labelsEstCompletionYear",estYear)

        readCache()
    }


    /**
     * 恢复Save Filters的默认值
     */
    private fun restoreSaveFilterSettings() {
        binding.spinnerSaveFilter.text = "---"
        mSelectSaveMapFilter = null

        binding.tvSaveFilterDelete.setBackgroundResource(R.drawable.shape_10radius_gray_fill)
        binding.tvSaveFilterDelete.setTextColor(binding.root.context.resources.getColor(R.color.color_gray))

        MMKVUtils.removeData(getKey("select_filter_name"))

        mMapFiltersListLayout.notifyDataSetChanged()
    }

    fun goneSaveFilterViews() {
        binding.tvSaveFilterCreate.visibility = View.GONE
        binding.tvSaveFilterSave.visibility = View.GONE
        binding.tvSaveFiltersTitle.visibility = View.GONE
        binding.tvSaveFilterDelete.visibility = View.GONE
        binding.viewLineSaveFilters.visibility = View.GONE
        binding.spinnerSaveFilter.visibility = View.GONE
    }

    fun removeFilterIfRemoteDeleted(list: java.util.ArrayList<SaveMapFilter>?) {
        val selectFilterName = MMKVUtils.getStr(getKey("select_filter_name"))
        if (TextUtils.isEmpty(selectFilterName)) {
            return
        }
        // 从mapFilterList里面循环寻找selectFilterName，如果找不到，说明在其他端删除掉了，就清空掉MMKVUtils的值
        var find = false
        list?.forEach {
            if (it.filterName == selectFilterName) {
                find = true
            }
        }
        if (!find) {
            MMKVUtils.removeData(getKey("select_filter_name"))
        }
    }

    // Save filter 逻辑结束

}