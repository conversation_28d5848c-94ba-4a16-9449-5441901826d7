package com.housesigma.android.ui.map.precon

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.housesigma.android.base.SingleLiveEvent
import com.housesigma.android.model.*
import com.housesigma.android.network.NetClient
import com.housesigma.android.network.launch
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.LanguageUtils
import com.housesigma.android.utils.MMKVUtils
import retrofit2.http.Field
import com.housesigma.android.utils.log.Logger

class PreconMapViewModel : ViewModel() {

    var mapList: MutableLiveData<List<PreconMarker>> = MutableLiveData()
    var mapSearchV3List: MutableLiveData<MapSearchV3List> = MutableLiveData()
    val mapSearchV3ListNoData = MutableLiveData<Boolean>()
    var mapNearByList: MutableLiveData<MapList> = MutableLiveData()
    var saveMapFilterMsg: SingleLiveEvent<MsgRes> = SingleLiveEvent()
    var deleteMapFilterMsg: MutableLiveData<MsgRes> = MutableLiveData()
    var mapFilterList: MutableLiveData<ArrayList<SaveMapFilter>> = MutableLiveData()
    var mapSearchFeature: MutableLiveData<List<MapMarkerInfo>> = MutableLiveData()
    var mapSearchSchool: MutableLiveData<MapSearchSchool> = MutableLiveData()
    var listingPreViewMany: MutableLiveData<List<Precon>> = MutableLiveData()
    var listingPreViewManyWithMarker: MutableLiveData<List<Precon>> = MutableLiveData()
    var mapFilter: MutableLiveData<MapFilter> = MutableLiveData()
    var preconMapFilter: MutableLiveData<PreconMapFilter> = MutableLiveData()
    var citySummary: MutableLiveData<CitySummary> = MutableLiveData()
    var schoolDeatils: MutableLiveData<SchoolDetails> = MutableLiveData()
    val loadingLiveData = MutableLiveData<Boolean>()

    fun getSearchSchoolDetails(id:Int) {
        launch({
            NetClient.apiService.getSearchSchoolDetails(id)
        }, {
            schoolDeatils.postValue(it)
        })
    }

    fun getPreconMapFilter() {
        launch({
            NetClient.apiService.getPreconMapFilter()
        }, {
            preconMapFilter.postValue(it)
        })
    }


    fun updateProfileCoordinate(lat:String,lon:String) {
        launch({
            NetClient.apiService.updateProfileCoordinate(DataCoordinate(Coordinate(lat,lon)))
        },{})
    }


    fun getListingPreviewPreconMany(id_project: List<String>,withMarker:Boolean) {
        launch({
            NetClient.apiService.getListingPreviewPreconMany(id_project)
        }, {
            if (withMarker) {
                listingPreViewManyWithMarker.postValue(it)
            } else {
                listingPreViewMany.postValue(it)
            }
        })
    }



    fun searchPreconMapProjects(
        property_type: ArrayList<String> = arrayListOf("all"),
        construction_status: ArrayList<String> = arrayListOf("all"),
        lat1: Double,
        lat2: Double,
        lon1: Double,
        lon2: Double,
        zoom: Double,
        bedroom_range: List<String>,
        bathroom_min: String,
        description: String,
        price: List<String>,
        square_footage: List<String>,
        est_completion_year: List<String>,
        project_status: List<String>,
    ) {
        launch({
            NetClient.cancelCallWithTag("api/search/preconmap/projects")
            NetClient.apiService.searchPreconMapProjects(
                bedroom = bedroom_range,
                bathroom = bathroom_min,
                project_status = project_status,
                construction_status = construction_status,
                est_completion_year = est_completion_year,
                property_type = property_type,
                description = description,
                price_max = price.get(1).toString(),
                price_min = price.get(0).toString(),
                square_footage_min = square_footage.get(0),
                square_footage_max = square_footage.get(1),
                lon1 = lon1,
                lon2 = lon2,
                lat1 = lat1,
                lat2 = lat2,
                zoom = zoom,
            )
        }, {
            mapList.postValue(it)
        })
    }

    fun getCitySummary(id_municipality:String) {
        launch({
            NetClient.apiService.getCitySummary(id_municipality)
        }, {
            citySummary.postValue(it)
        })
    }

    fun getMapSearchV3List(
        houseType: ArrayList<String> = arrayListOf("all"),
        lat1: Double,
        lat2: Double,
        lon1: Double,
        lon2: Double,
        zoom: Double,
        listing_days: String,
        de_list_days: String,
        list_type: List<String>,
        basement: List<String>,
        bedroom_range: List<String>,
        bathroom_min: String,
        garage_min: String,
        open_house_date: String,
        description: String,
        max_maintenance_fee: String,
        price: List<String>,
        front_feet: List<String>,
        square_footage: List<String>,
        page:Int,
        sortType:String,
    ) {
        launch({
            NetClient.apiService.getMapsearchv3List(
                basement = basement,
                bathroom_min = bathroom_min,
                bedroom_range = bedroom_range,
                description = description,
                front_feet = front_feet,
                garage_min = garage_min,
                house_type = houseType,
                lat1 = lat1,
                lat2 = lat2,
                lon1 = lon1,
                lon2 = lon2,
                zoom = zoom+1,
                listing_days = listing_days,
                de_list_days = de_list_days,
                sold_days = de_list_days,
                list_type = list_type,
                open_house_date = open_house_date,
                price = price,
                max_maintenance_fee = max_maintenance_fee,
                square_footage = square_footage,
                page = page,
                sort_type = sortType
            )
        }, {
            mapSearchV3List.postValue(it)
        },{
            loadingLiveData.postValue(true)
        },{
            Logger.e("mapSearchV3ListNoData...")
            mapSearchV3ListNoData.postValue(true)
        })
    }




    fun getSearchV2NearBySold(
        houseType: ArrayList<String>,
        lat1: Double,
        lat2: Double,
        lon1: Double,
        lon2: Double,
        zoom: Double,
        listing_days: String,
        de_list_days: String,
        list_type: List<String>,
        basement: List<String>,
        bedroom_range: List<String>,
        bathroom_min: String,
        garage_min: String,
        open_house_date: String,
        description: String,
        max_maintenance_fee: String,
        price: List<String>,
        front_feet: List<String>,
        square_footage: List<String>,
    ) {
        launch({
            NetClient.apiService.getSearchV2NearBySold(
                basement = basement,
                bathroom_min = bathroom_min,
                bedroom_range = bedroom_range,
                description = description,
                front_feet = front_feet,
                garage_min = garage_min,
                house_type = houseType,
                lat1 = lat1,
                lat2 = lat2,
                lon1 = lon1,
                lon2 = lon2,
                zoom = zoom+1,
                listing_days = listing_days,
                de_list_days = de_list_days,
                list_type = list_type,
                open_house_date = open_house_date,
                price = price,
                max_maintenance_fee = max_maintenance_fee,
                square_footage = square_footage,
            )
        }, {
            mapNearByList.postValue(it)
        })
    }

    fun saveMapFilter(saveMapFilter: SaveMapFilter) {
        launch({
            NetClient.apiService.saveMapFilter(saveMapFilter)
        }, {
            saveMapFilterMsg.postValue(it)
        })
    }

    fun deleteMapFilterById(id:String) {
        launch({
            NetClient.apiService.deleteMapFilterById(id)
        }, {
            deleteMapFilterMsg.postValue(it)
        })
    }

    fun getMapFilterList(mapType: String) {
        launch({
            NetClient.apiService.getMapFilterList(mapType)
        }, {
            mapFilterList.postValue(it)
        })
    }

}