package com.housesigma.android.ui.map.propertype

import android.content.Context
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.SaveMapFilter
import com.housesigma.android.ui.map.MapType
import com.housesigma.android.utils.MMKVUtils

class MapFilterListAdapter(context: Context,mapType: MapType) :
    BaseQuickAdapter<SaveMapFilter, BaseViewHolder>(R.layout.item_map_save_filter_list) {
    private var mContext = context
    private var mMapType = mapType

    init {
        addChildClickViewIds(R.id.ll)
    }

    override fun convert(holder: BaseViewHolder, item: SaveMapFilter) {
        holder.setText(R.id.tv_filter, item.filterName)
        if (MMKVUtils.getStr(getKey("select_filter_name")).equals(item.filterName)) {
            holder.setBackgroundResource(R.id.iv_filter, R.drawable.ic_filter_point_selected)
            holder.setTextColor(R.id.tv_filter, mContext.resources.getColor(R.color.app_main_color))
        } else {
            holder.setTextColor(R.id.tv_filter, mContext.resources.getColor(R.color.color_black))
            holder.setBackgroundResource(R.id.iv_filter, R.drawable.ic_filter_point_select)
        }
    }

    public fun getKey(key:String): String {
        when(mMapType){
            MapType.PRECON -> return key + "_precon"
            MapType.FOR_SALE -> return key + "_sale"
            MapType.FOR_LEASE -> return key + "_lease"
        }
    }

}