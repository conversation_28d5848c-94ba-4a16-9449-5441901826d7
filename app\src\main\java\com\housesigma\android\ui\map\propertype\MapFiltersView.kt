package com.housesigma.android.ui.map.propertype

import android.content.Context
import android.graphics.PointF
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.housesigma.android.R
import com.housesigma.android.databinding.PopwindowMapFilterMoreBinding
import com.housesigma.android.model.*
import com.housesigma.android.ui.map.MapType
import com.housesigma.android.ui.map.filters.LabelManager
import com.housesigma.android.ui.map.filters.MapFiltersListView
import com.housesigma.android.ui.map.filters.SeekBarManager
import com.housesigma.android.utils.Callback0
import com.housesigma.android.utils.Callback1
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.HSAlertDialog
import com.housesigma.android.views.SaveFiltersDialog
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.enums.PopupPosition


/**
 * 缓存在本地记录的是选中组件的position
 * 选中的position转换成id
 *
 *
 * 如果要api返回id，那么就要反推回去，根据id找到position
 */
open class MapFiltersView {


    private lateinit var binding: PopwindowMapFilterMoreBinding

    private var mCallBack: Callback0? = null
    private var mIsSale: Boolean = true
    private var mMapFilter: MapFilter? = null
    private var mHasBuildingAge: Boolean = false
    private var mHasLotSize: Boolean = false

    private var mSaveMapFilterList: ArrayList<SaveMapFilter> = ArrayList()
    private var mSelectSaveMapFilter: SaveMapFilter?=null
    private val mMapFiltersListView : MapFiltersListView by lazy {
        MapFiltersListView(binding.root.context,if(mIsSale) MapType.FOR_SALE else MapType.FOR_LEASE)
    }

    private val mLabelManager: LabelManager by lazy {
        LabelManager(mIsSale)
    }

    private val mSeekBarManager: SeekBarManager by lazy {
        SeekBarManager()
    }

    fun hasLotSize(): Boolean{
        return mHasLotSize
    }

    fun hasBuildingAge(): Boolean{
        return mHasBuildingAge
    }


    fun getLayout(): PopwindowMapFilterMoreBinding {
        return binding
    }

    fun getBedroomRange(): ArrayList<String> {
        return mLabelManager.getBedroomRange()
    }

    fun getBasement(): ArrayList<String> {
        return mLabelManager.getBasement()
    }

    fun getListingType(): ArrayList<String> {
        return mLabelManager.getListingType()
    }


    fun getBathroomMin(): String {
        return mLabelManager.getBathroomMin()
    }

    fun getGarageMin(): String {
        return mLabelManager.getGarageMin()
    }

    fun getOpenHouseDate(): String {
        return mLabelManager.getOpenHouseDate()
    }

    fun getDescription(): String {
        return mSeekBarManager.description
    }

    fun getFee(): String {
        return mSeekBarManager.fee
    }

    open fun getPrice(): ArrayList<String> {
        return mSeekBarManager.getPrice()
    }

    open fun getFeet(): ArrayList<String> {
        return mSeekBarManager.getFeet()
    }

    open fun getSquareFootage(): ArrayList<String> {
        return mSeekBarManager.getSquareFootage()
    }

    open fun getLotSize(): ArrayList<String>? {
        if (mHasLotSize) {
            return mSeekBarManager.getLotSize()
        }
        return null
    }

    open fun getBuildingAge(): ArrayList<String>? {
        if (mHasLotSize) {
            return mSeekBarManager.getBuildingAge()
        }
        return null
    }

    /*
      * type:true为售卖，false为租赁
     */
    fun createView(
        layoutInflater: LayoutInflater,
        isSale: Boolean,
        cb: Callback0
    ): MapFiltersView {
        mIsSale = isSale
        mCallBack = cb
        binding = PopwindowMapFilterMoreBinding.inflate(layoutInflater)
        return this
    }

    fun setup(mapFilter: MapFilter) {
        prepareData(mapFilter)
        initViews()
    }

    fun initViews() {
        mLabelManager.initLabelsView(
            binding.labelsBeadroom,
            binding.labelsBasement,
            binding.labelsListingType,
            binding.labelsBathroom,
            binding.labelsGarageParking,
            binding.labelsOpenHouse,
            cb = mCallBack
        )
        mSeekBarManager.initSeekBar(binding, mIsSale, mCallBack)

        showLotSizeView()
        showBuildingAgeView()
    }

    fun restoreFromSelectMapFilters(defaultFilter: MapFilters) {
            mSeekBarManager.setValue(defaultFilter, binding,hasLotSize(),hasBuildingAge())
            mLabelManager.setBedroomFilterLabel(
                binding.labelsBeadroom,
                defaultFilter.bedroom?:ArrayList()
            )
            mLabelManager.setBasementFilterLabel(
                binding.labelsBasement,
                defaultFilter.basement?:ArrayList()
            )
            mLabelManager.setListingTypeFilterLabel(
                binding.labelsListingType,
               defaultFilter.listing_type?:ArrayList()
            )
            mLabelManager.setBathroomsFilterLabel(
                binding.labelsBathroom,
                defaultFilter.bathroom?:"0"
            )
            mLabelManager.setGarageFilterLabel(
                binding.labelsGarageParking,
               defaultFilter.garage?:"0"
            )
            mLabelManager.setOpenHouseDate(
                binding.labelsOpenHouse,
                defaultFilter.open_house_date?:"0"
            )
    }

    /**
     * 从mapFilter api恢复组件的最初始的状态
     */
    fun restoreFromSelectMapFilters() {
        mMapFilter?.let { mapFilter ->
            mSeekBarManager.setValue(mapFilter.default_filter, binding,hasLotSize(),hasBuildingAge())
            mLabelManager.setBedroomFilterLabel(
                binding.labelsBeadroom,
                mapFilter.default_filter.bedroom
            )
            mLabelManager.setBasementFilterLabel(
                binding.labelsBasement,
                mapFilter.default_filter.basement
            )
            mLabelManager.setListingTypeFilterLabel(
                binding.labelsListingType,
                mapFilter.default_filter.listing_type
            )
            mLabelManager.setBathroomsFilterLabel(
                binding.labelsBathroom,
                mapFilter.default_filter.bathroom
            )
            mLabelManager.setGarageFilterLabel(
                binding.labelsGarageParking,
                mapFilter.default_filter.garage
            )
            mLabelManager.setOpenHouseDate(
                binding.labelsOpenHouse,
                mapFilter.default_filter.open_house_date
            )
        }
    }

    fun getKey(key:String): String {
        return key + if(mIsSale) "_sale" else "_lease"
    }

    /**
     * MapFilter初始化默认值，只在进入app的第一次才会加载，之后不再加载
     */
    private fun isMapFilterDefaultFirstTime(): Boolean {
        return MMKVUtils.getBoolean(getKey("map_default_is_first_time"),true)
    }

    /*
     * 设置第一次进入MapFilter
     */
    private fun setMapFilterDefaultFirstTime() {
        MMKVUtils.saveBoolean(getKey("map_default_is_first_time"), false)
    }

    /**
     * 准备Map Filter View所用到的数据
     *
     * @param mapFilter api返回的数据
     */
    private fun prepareData(mapFilter: MapFilter) {
        mMapFilter = mapFilter

        mSeekBarManager.setMapFilter(mapFilter)
        mLabelManager.setMapFilter(mapFilter)

        // 初始化默认值，只在进入app的第一次才会加载，之后不再加载
        if (isMapFilterDefaultFirstTime()) {
            Logger.e("isMapFilterDefaultFirstTime")
            restoreFromSelectMapFilters()
            setMapFilterDefaultFirstTime()
        }


        // 根据接口返回的数据，判断是否显示lot size 和 building age
        mHasLotSize = mapFilter.lot_size_filter != null
        mHasBuildingAge = mapFilter.building_age_filter != null


        // 历史包袱 暂时先放在这里吧
        if (TextUtils.isEmpty(MMKVUtils.getStr(mIsSale.toString() + "map_listing_days"))) {
            var itemListingDay: ListingFilter? = null
            for (listingFilter in mapFilter.days_filter_all.listing) {
                if (mapFilter.default_filter.listing_days.equals(listingFilter.id)) {
                    itemListingDay = listingFilter
                    break
                }
            }
            itemListingDay?.let {
                MMKVUtils.saveStr(
                    mIsSale.toString() + "map_listing_days",
                    itemListingDay.id
                )//listing_days
                MMKVUtils.saveStr(mIsSale.toString() + "map_fs_ld_abbr", itemListingDay.abbr)
            }

            var itemSoldDay: ListingFilter? = null
            for (listingFilter in mapFilter.days_filter_all.sold_v2) {
                if (mapFilter.default_filter.de_list_days.equals(listingFilter.id)) {
                    itemSoldDay = listingFilter
                    break
                }
            }
            itemSoldDay?.let {
                MMKVUtils.saveStr(mIsSale.toString() + "map_s_ld", itemSoldDay.id)//listing_days
                MMKVUtils.saveStr(mIsSale.toString() + "map_s_ld_abbr", itemSoldDay.abbr)
            }
        }
    }

    /**
     * 是否显示building age
     */
    private fun showBuildingAgeView() {
        if (mHasBuildingAge) {
            binding.rsbBuildingAge.visibility = View.VISIBLE
            binding.tvBuildingAge.visibility = View.VISIBLE
        } else {
            binding.rsbBuildingAge.visibility = View.GONE
            binding.tvBuildingAge.visibility = View.GONE
        }
    }

    /**
     * 是否显示lot size
     */
    private fun showLotSizeView() {
        if (mHasLotSize) {
            binding.rsbLotSize.visibility = View.VISIBLE
            binding.tvLotSize.visibility = View.VISIBLE
        } else {
            binding.rsbLotSize.visibility = View.GONE
            binding.tvLotSize.visibility = View.GONE
        }
    }

    // save filter 逻辑开始
    fun saveFiltersName(filtersName: String){
        MMKVUtils.saveStr(getKey("select_filter_name"),filtersName)
    }

    /**
     * Save Filters按钮 按下后的逻辑
     */
    fun setSaveFiltersClickListener(context: AppCompatActivity,
                                    cb: Callback1) {
        binding.tvSaveFilterCreate.setOnClickListener {
            val dialog = SaveFiltersDialog(
                object: SaveFiltersDialog.SaveFiltersCallback {
                    override fun onSuccess(filtersName: String) {
                        saveFiltersName(filtersName)
                        cb.onData(filtersName)
                    }
                }, mSelectSaveMapFilter)
            dialog.show(context.supportFragmentManager,"")
        }

        binding.tvSaveFilterSave.setOnClickListener {
            val dialog = SaveFiltersDialog(
                object: SaveFiltersDialog.SaveFiltersCallback {
                    override fun onSuccess(filtersName: String) {
                        saveFiltersName(filtersName)
                        cb.onData(filtersName)
                    }
                }, mSelectSaveMapFilter)
            dialog.show(context.supportFragmentManager,"")
        }
    }

    fun setDeleteFiltersClickListener(context: Context,cb: Callback1) {
        binding.tvSaveFilterDelete.setOnClickListener {
            if (mSelectSaveMapFilter==null) return@setOnClickListener
            val selectFilterName = mSelectSaveMapFilter?.filterName ?:"---"
            val selectId = mSelectSaveMapFilter?.id ?: ""
            HSAlertDialog(
                context, "Delete Filter", "Are you sure you want to Delete filter \""+  selectFilterName +"\"",
                "Cancel", "OK",
                object : HSAlertDialog.HSAlertCallback {
                    override fun onSuccess() {
                        binding.spinnerSaveFilter.text = "---"
                        mSelectSaveMapFilter = null

                        binding.tvSaveFilterDelete.setBackgroundResource(R.drawable.shape_10radius_gray_fill)
                        binding.tvSaveFilterDelete.setTextColor(context.resources.getColor(R.color.color_gray))

                        MMKVUtils.removeData(getKey("select_filter_name"))
                        cb.onData(selectId)
                    }
                }).show()
        }
    }


    fun bindSaveFilterSelectList(context: Context, list:ArrayList<SaveMapFilter>,cb: Callback1) {
        mSaveMapFilterList = list
        if (mSaveMapFilterList.size==0) {
            binding.spinnerSaveFilter.visibility = View.GONE
            binding.tvSaveFilterCreate.visibility = View.VISIBLE
            binding.tvSaveFilterSave.visibility = View.GONE
            binding.tvSaveFiltersTitle.visibility = View.GONE
            binding.tvSaveFilterDelete.visibility = View.GONE
        } else {
            binding.spinnerSaveFilter.visibility = View.VISIBLE
            binding.tvSaveFilterCreate.visibility = View.GONE
            binding.tvSaveFilterSave.visibility = View.VISIBLE
            binding.tvSaveFiltersTitle.visibility = View.VISIBLE
            binding.tvSaveFilterDelete.visibility = View.VISIBLE
        }
        binding.viewLineSaveFilters.visibility = View.VISIBLE

        val selectFilterName = MMKVUtils.getStr(getKey("select_filter_name"))?:"---"
        binding.spinnerSaveFilter.text = selectFilterName

        mSaveMapFilterList.forEach {
            if (it.filterName == selectFilterName) {
                mSelectSaveMapFilter = it
                binding.tvSaveFilterDelete.setBackgroundResource(R.drawable.shape_10radius_main_color)
                binding.tvSaveFilterDelete.setTextColor(context.resources.getColor(R.color.app_main_color))
            }
        }


        mMapFiltersListView.setData(mSaveMapFilterList)
        mMapFiltersListView?.setSelectClickListener(object :Callback1{
            override fun onData(any: Any) {
                val position = (any as Int)
                try {
                    GALog.log("saved_filters_select_click")
                    mSelectSaveMapFilter = mSaveMapFilterList[position]
                    val filter = mSelectSaveMapFilter?.filter
                    binding.spinnerSaveFilter.text = mSelectSaveMapFilter?.filterName

                    binding.tvSaveFilterDelete.setBackgroundResource(R.drawable.shape_10radius_main_color)
                    binding.tvSaveFilterDelete.setTextColor(context.resources.getColor(R.color.app_main_color))
                    filter?.let {
                        restoreFromSelectMapFilters(filter)
                        cb.onData(filter)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        })

        binding.spinnerSaveFilter.setOnClickListener {
           val spinnerHeight =  binding.spinnerSaveFilter.measuredHeight
           val spinnerWidth =  binding.spinnerSaveFilter.measuredWidth

            // 重新计算组件在屏幕的位置
            val locations = IntArray(2)
            binding.spinnerSaveFilter.getLocationOnScreen(locations)

            XPopup.Builder(context)
                .isCenterHorizontal(true)
                .atPoint(PointF(locations[0].toFloat(),locations[1].toFloat()))
                .popupPosition(PopupPosition.Bottom)
                .navigationBarColor(context.resources.getColor(R.color.navigation_bar_color))
                .popupWidth(spinnerWidth)
                .offsetY(spinnerHeight)
                .offsetX(spinnerWidth/2)
                .hasShadowBg(false)
                .animationDuration(0)
                .asCustom(mMapFiltersListView)
                .show()
        }
    }


    /**
     * 恢复Save Filters的默认值
     */
    fun restoreSaveFilterSettings() {
        binding.spinnerSaveFilter.text = "---"
        mSelectSaveMapFilter = null

        binding.tvSaveFilterDelete.setBackgroundResource(R.drawable.shape_10radius_gray_fill)
        binding.tvSaveFilterDelete.setTextColor(binding.root.context.resources.getColor(R.color.color_gray))

        MMKVUtils.removeData(getKey("select_filter_name"))

        mMapFiltersListView.notifyDataSetChanged()
    }

    // Save filter 逻辑结束


    /**
     * 点击这个按钮，并不会发生什么，只是会把当前弹窗消失掉
     */
    fun setApplyClickListener(cb: Callback1) {
        binding.tvApply.setOnClickListener {
            cb.onData("")
        }
    }


    /**
     * 点击这个按钮，会恢复到最初的状态，并且重新请求API
     */
    fun setClearClickListener(cb: Callback1) {
        binding.tvClearAllFilters.setOnClickListener {
            // 恢复默认值，并且回调为了请求api
            mMapFilter?.let {
                // savefilter恢复默认值
                restoreSaveFilterSettings()
                // mapfilter恢复默认值
                restoreFromSelectMapFilters()
                // 回调请求api
                cb.onData("")
            }
        }
    }

    /**
     * 是否被修改过，修改过返回ture，显示红点
     */
    fun isModified(): Boolean {
        mMapFilter?.let { mapFilter->

            val defaultFilter = mapFilter.default_filter

            var lotSizeChanged = false
            if (mHasLotSize) {
                if (getLotSize()!!.get(0) != defaultFilter.lot_size_min || getLotSize()!!.get(1) != defaultFilter.lot_size_max){
                    lotSizeChanged = true
                }
            }

            var buildingAgeChanged = false
            if (mHasBuildingAge) {
                if (getBuildingAge()!!.get(0) != defaultFilter.building_age_min || getBuildingAge()!!.get(1) != defaultFilter.building_age_max){
                    buildingAgeChanged = true
                }
            }

            var priceChanged = false
            if (mIsSale) {
                if (getPrice().get(0).toInt() != defaultFilter.price_sale_min.toInt() || getPrice().get(1).toInt() != defaultFilter.price_sale_max.toInt()) {
                    priceChanged = true
                }
            } else {
                if (getPrice().get(0).toInt() != defaultFilter.price_rent_min.toInt() || getPrice().get(1).toInt() != defaultFilter.price_rent_max.toInt()) {
                    priceChanged = true
                }
            }


            if ((!(getBedroomRange().containsAll(defaultFilter.bedroom) && defaultFilter.bedroom.containsAll(getBedroomRange())))
                || !(getBasement().containsAll(defaultFilter.basement) && defaultFilter.basement.containsAll(getBasement()))
                || getBathroomMin() != defaultFilter.bathroom
                || getGarageMin() != defaultFilter.garage
                || getOpenHouseDate() != defaultFilter.open_house_date
                || getDescription() != defaultFilter.description
                || getFee() != defaultFilter.max_maintenance_fee
                || getFeet().get(0).toInt() != defaultFilter.lot_front_feet_min.toInt()
                || getFeet().get(1).toInt()  !=  defaultFilter.lot_front_feet_max.toInt()
                || getSquareFootage().get(0).toInt()  != defaultFilter.square_footage_min.toInt()
                || getSquareFootage().get(1).toInt() != defaultFilter.square_footage_max.toInt()
                || !(getListingType().containsAll(defaultFilter.listing_type) && defaultFilter.listing_type.containsAll(getListingType()))
                || priceChanged
                || lotSizeChanged
                || buildingAgeChanged
            ) {
                return true
            }
        }
        return false
    }

    fun goneSaveFilterViews() {
        binding.tvSaveFilterCreate.visibility = View.GONE
        binding.tvSaveFilterSave.visibility = View.GONE
        binding.tvSaveFiltersTitle.visibility = View.GONE
        binding.tvSaveFilterDelete.visibility = View.GONE
        binding.viewLineSaveFilters.visibility = View.GONE
        binding.spinnerSaveFilter.visibility = View.GONE
    }

    fun removeFilterIfRemoteDeleted(list: java.util.ArrayList<SaveMapFilter>?) {
        val selectFilterName = MMKVUtils.getStr(getKey("select_filter_name"))
        if (TextUtils.isEmpty(selectFilterName)) {
            return
        }
        // 从mapFilterList里面循环寻找selectFilterName，如果找不到，说明在其他端删除掉了，就清空掉MMKVUtils的值
        var find = false
        list?.forEach {
            if (it.filterName == selectFilterName) {
                find = true
            }
        }
        if (!find) {
            MMKVUtils.removeData(getKey("select_filter_name"))
        }
    }


}