package com.housesigma.android.ui.map.propertype

import android.content.Context
import android.widget.ImageView
import android.widget.LinearLayout
import com.housesigma.android.R
import com.housesigma.android.utils.Callback1
import com.lxj.xpopup.core.AttachPopupView

open class MapListViewSortTypeView(context: Context) : AttachPopupView(context) {

    override fun getImplLayoutId(): Int {
        return R.layout.popwindow_map_list_view_sort_type
    }

    var sortValue: String = "1"
    var sortStr: String = ""

     var cb:Callback1 ?= null

    override fun onCreate() {
        super.onCreate()
        val ivDate = findViewById<ImageView>(R.id.iv_date)
        val ivPriceAscending = findViewById<ImageView>(R.id.iv_price_ascending)
        val ivPriceDescending = findViewById<ImageView>(R.id.iv_price_descending)

        findViewById<LinearLayout>(R.id.rl_date).setOnClickListener {
            ivDate.setBackgroundResource(R.drawable.ic_filter_point_selected)
            ivPriceAscending.setBackgroundResource(R.drawable.ic_filter_point_select)
            ivPriceDescending.setBackgroundResource(R.drawable.ic_filter_point_select)
            sortValue = "1"
            sortStr = "Newest"
            cb?.onData("1")
        }
        findViewById<LinearLayout>(R.id.rl_price_ascending).setOnClickListener {
            ivDate.setBackgroundResource(R.drawable.ic_filter_point_select)
            ivPriceAscending.setBackgroundResource(R.drawable.ic_filter_point_selected)
            ivPriceDescending.setBackgroundResource(R.drawable.ic_filter_point_select)
            sortValue = "2"
            sortStr = "Price Ascending"
            cb?.onData("2")
        }
        findViewById<LinearLayout>(R.id.rl_price_descending).setOnClickListener {
            ivDate.setBackgroundResource(R.drawable.ic_filter_point_select)
            ivPriceAscending.setBackgroundResource(R.drawable.ic_filter_point_select)
            ivPriceDescending.setBackgroundResource(R.drawable.ic_filter_point_selected)
            sortValue = "3"
            sortStr = "Price Descending"
            cb?.onData("3")
        }
    }




    fun setSelectClickListener(cb: Callback1) {
        this.cb = cb
    }


}