package com.housesigma.android.ui.map.propertype

import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.gyf.immersionbar.ktx.destroyImmersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseDialogFragment
import com.housesigma.android.databinding.DialogMapSettingsBinding
import com.housesigma.android.databinding.DialogPreconMapSettingsBinding
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.views.HSAlertDialog
import com.housesigma.android.views.SwitchButton


class PreconMapSettingDialog() : BaseDialogFragment() {

    private lateinit var binding: DialogPreconMapSettingsBinding
    private var mCallback: MapSettingsCallback?=null
    private var isSatelliteStyle: Boolean = false

    interface MapSettingsCallback {
        fun changeStyle(isSatellite:Boolean)
    }

    fun setMapSettingsCallback(cb: MapSettingsCallback){
        mCallback = cb
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreen)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogPreconMapSettingsBinding.inflate(inflater, container, false)
        initViews()
        return binding.root
    }

    private fun initViews() {
        if (isSatelliteStyle){
            binding.flStreet.setBackgroundResource(R.drawable.shape_map_settings_left_normal)
            binding.flSatellite.setBackgroundResource(R.drawable.shape_map_settings_right_selected)
            binding.tvStreet.setTextColor(resources.getColor(R.color.color_black))
            binding.tvSatellite.setTextColor(resources.getColor(R.color.color_white))
            binding.tvSatellite.setCompoundDrawablesWithIntrinsicBounds(
                resources.getDrawable(R.drawable.ic_map_settings_satellite_select),
                null,
                null,
                null
            )
            binding.tvStreet.setCompoundDrawablesWithIntrinsicBounds(
                resources.getDrawable(R.drawable.ic_map_settings_street_normal),
                null,
                null,
                null
            )
        }
        binding.flSatellite.setOnClickListener {
            binding.flStreet.setBackgroundResource(R.drawable.shape_map_settings_left_normal)
            binding.flSatellite.setBackgroundResource(R.drawable.shape_map_settings_right_selected)
            binding.tvStreet.setTextColor(resources.getColor(R.color.color_black))
            binding.tvSatellite.setTextColor(resources.getColor(R.color.color_white))

            binding.tvSatellite.setCompoundDrawablesWithIntrinsicBounds(
                resources.getDrawable(R.drawable.ic_map_settings_satellite_select),
                null,
                null,
                null
            )
            binding.tvStreet.setCompoundDrawablesWithIntrinsicBounds(
                resources.getDrawable(R.drawable.ic_map_settings_street_normal),
                null,
                null,
                null
            )

            mCallback?.changeStyle(true)
        }
        binding.flStreet.setOnClickListener {
            binding.flStreet.setBackgroundResource(R.drawable.shape_map_settings_left_selected)
            binding.flSatellite.setBackgroundResource(R.drawable.shape_map_settings_right_normal)
            binding.tvStreet.setTextColor(resources.getColor(R.color.color_white))
            binding.tvSatellite.setTextColor(resources.getColor(R.color.color_black))

            binding.tvSatellite.setCompoundDrawablesWithIntrinsicBounds(
                resources.getDrawable(R.drawable.ic_map_settings_satellite_normal),
                null,
                null,
                null
            )
            binding.tvStreet.setCompoundDrawablesWithIntrinsicBounds(
                resources.getDrawable(R.drawable.ic_map_settings_street_select),
                null,
                null,
                null
            )

            mCallback?.changeStyle(false)
        }
        binding.ivCloseDialog.setOnClickListener {



            dismiss()
        }


    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        val windowParams = window?.attributes
        windowParams?.dimAmount = 0.7f // 遮罩效果消失
        window?.setLayout(-1, -2)
        windowParams?.gravity = Gravity.BOTTOM
        window?.attributes = windowParams
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        getDialog()?.let { destroyImmersionBar(it) }
        arguments
    }

    fun setStyle(isSatelliteStyle: Boolean) {
        this.isSatelliteStyle = isSatelliteStyle
    }

}


