package com.housesigma.android.ui.market

import android.annotation.SuppressLint
import android.content.Intent
import android.content.MutableContextWrapper
import android.graphics.Bitmap
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.*
import androidx.annotation.RequiresApi
import androidx.core.view.isEmpty
import androidx.lifecycle.ViewModelProvider
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.gson.Gson
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.BuildConfig
import com.housesigma.android.HSApp
import com.housesigma.android.R
import com.housesigma.android.base.BaseHomeFragment
import com.housesigma.android.databinding.FragmentMarketBinding
import com.housesigma.android.helper.JumpHelper
import com.housesigma.android.hybrid.HybridUtils
import com.housesigma.android.model.*
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.map.MapActivity
import com.housesigma.android.ui.map.housephoto.HousePhotoListActivity
import com.housesigma.android.model.HybridUserInfo
import com.housesigma.android.ui.home.HomeViewModel
import com.housesigma.android.ui.map.housephoto.PreconHousePhotoListActivity
import com.housesigma.android.ui.map.precon.PreconMapActivity
import com.housesigma.android.ui.map.precon.PreconProjectStatus
import com.housesigma.android.ui.notinterested.NotInterestedHelper
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.ui.webview.WebViewViewModel
import com.housesigma.android.utils.*
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.HSWebView
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONException
import org.json.JSONObject


class MarketFragment : BaseHomeFragment(), LoginFragment.LoginCallback {

    private lateinit var binding: FragmentMarketBinding
    private var webView: HSWebView? = null
    private var loginDialog: LoginFragment? = null
    private var isOnPause:Boolean = false
    private lateinit var webViewViewModel: WebViewViewModel

    inner class AnalyticsWebInterface {

        @JavascriptInterface
        fun logEvent(name: String, jsonParams: String) {
            getActivity()?.let {
                if (it.isFinishing) return
                LOGD("GALog eventName:$name")
                LOGD("logEvent jsonParams:$jsonParams")
                if ("page_view" == name) {
                    // DEV-5797 native阻断hybrid发送的pv事件，由native独自发送
                } else {
                    // DEV-5797 当hybrid页处于非页面栈顶时，由native阻断hybrid页发送ga事件
                    if (!isOnPause) {
                        Firebase.analytics.logEvent(name, bundleFromJson(jsonParams))
                    }
                }
            }

        }

        @JavascriptInterface
        fun setUserProperty(name: String, value: String?) {
            LOGD("setUserProperty:$name")
            val firebaseAnalytics = Firebase.analytics
            firebaseAnalytics.setUserProperty(name, value)
        }

        private fun LOGD(message: String) {
            // Only log on debug builds, for privacy
            if (BuildConfig.DEBUG) {
                Logger.d(message)
            }
        }

        private fun bundleFromJson(json: String): Bundle {
            // [START_EXCLUDE]
            if (TextUtils.isEmpty(json)) {
                return Bundle()
            }
            val result = Bundle()
            try {
                val jsonObject = JSONObject(json)
                val keys = jsonObject.keys()
                while (keys.hasNext()) {
                    val key = keys.next()
                    val value = jsonObject[key]
                    if (value is String) {
                        result.putString(key, value)
                    } else if (value is Int) {
                        result.putInt(key, value)
                    } else if (value is Double) {
                        result.putDouble(key, value)
                    } else {
                        Logger.w("Value for key $key not one of [String, Integer, Double]")
                    }
                }
            } catch (e: JSONException) {
                Logger.e("Failed to parse JSON, returning empty Bundle.", e)
                return Bundle()
            }
            return result
            // [END_EXCLUDE]
        }

    }
    inner class JsObject {


        /**
         * 1.索取初始化参数
         */
        @JavascriptInterface
        fun requireInitialParameters(msg: String): String? {
            Logger.d("webview requireInitialParameters msg " + msg)
            val token = MMKVUtils.getStr(LoginFragment.LOGIN_TOKEN)
            val userStr =  HybridUtils.getUserForHybrid()
            val initAppStr = HybridUtils.getInitAppForHybrid()
            var province = MMKVUtils.getStr(LoginFragment.PROVINCE)
            if (province == null) {
                province = "ON"
            }
            val lang: String = LanguageUtils().getLANG()
            val isSignIn: Int
            if (TextUtils.isEmpty(userStr)) {
                isSignIn = 0
            } else {
                isSignIn = 1
            }

            val webUserInfo = HybridUserInfo(
                access_token = token!!,
                is_hide_header = 1,
                is_sign_in = isSignIn,
                lang = lang,
                province = province,
                user = userStr,
                app = initAppStr,
                secret = HSApp.secret
            )
            return GsonUtils.parseToStr(webUserInfo)
        }

        /**
         * Precon房源详情，点击地图按钮后，跳转到原生Precon地图界面
         */
        @JavascriptInterface
        fun showPreconOnNativeMap(id_listing: String) {
            Logger.d( "webview showPreconOnNativeMap id_listing $id_listing")
            activity?.runOnUiThread {
                val intent = Intent(getActivity(), PreconMapActivity::class.java)
                intent.putExtra("is_sale", true)
                intent.putExtra("map_type", arrayListOf(PreconProjectStatus.SELLING_NOW))
                intent.putExtra("id_listing", id_listing)
                startActivity(intent)
            }
        }

        /**
         * 2.房源详情，点击地图按钮后，跳转到原生地图界面
         */
        @JavascriptInterface
        fun showListingOnNativeMap(id_listing: String) {
            Logger.d("webview showListingOnNativeMap id_listing $id_listing")
            activity?.runOnUiThread {
                startActivity(Intent(getActivity(), MapActivity::class.java))
            }
        }

//        有4种登录成功的场景：
//        1.普通注册
//        2.普通登录
//        3.google  登录注册
//        4.Google 登录成功
        /**
         * 3. 需要登录时，H5 告知 原生，唤起登录；登录成功之后，原生再告知 H5 登录成功
         */
        @JavascriptInterface
        fun callNativeSignin(jsonStr: String) {
            Logger.d( "webview callNativeSignin msg $jsonStr")
            val request = Gson().fromJson(jsonStr, CallNativeSignInRequest::class.java)
            activity?.runOnUiThread {
                loginDialog = LoginFragment()
                if (loginDialog?.isAdded == true) return@runOnUiThread
                val bundle = Bundle()
                val loginType: String
                if (request.reLogin == true) {
                    loginType = LoginFragment.RE_LOGIN_TRREB_TIMEOUT
                } else if (request.reValidate == true) {
                    loginType = LoginFragment.RE_LOGIN_VALIDATE
                } else{
                    loginType = ""
                }
                bundle.putString("reLogin", loginType)
                loginDialog?.arguments = bundle
                loginDialog?.show(
                    childFragmentManager,
                    ""
                )
            }

        }

        /**
         * 4. h5 tos 更新后，告知 native
         */
        @JavascriptInterface
        fun tosUpdated(msg: String) {
            Logger.d("webview tosUpdated msg $msg")
            // android现在用不到
            //这个又需要原生协助了。 在tos_update 之后，reload掉market页面。
            activity?.runOnUiThread {
                EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_WEB_VIEW))
            }
        }

        /**
         * 4. 这部分是壳中就存在的，按原有的约定，继续使用
         */
        @JavascriptInterface
        fun openUrl(urlString: String) {
            Logger.d("webview openUrl msg $urlString")
            activity?.runOnUiThread {
                if (!TextUtils.isEmpty(urlString)) {
                    webView?.loadUrl(urlString)
                }
            }
        }

        @JavascriptInterface
        fun collectUserData(json :String) {
            Logger.d( "webview collectUserData : $json")
            val hsUserData = Gson().fromJson(json, HSUserData::class.java)
            hsUserData?.let {
                val ui = hsUserData.ep?.get("ui")
                if (ui != null) { //含有userInput的情况
                    HSLog.sendUserInputHybrid(hsUserData)
                    return
                } else if ((hsUserData.en == "page_view")) {
                    //屏蔽掉hybrid的page_view，由native HSLog.page处理，否则有生命周期计算问题
                    return
                } else {
                    HSLog.sendToHS(hsUserData)
                }
            }
        }


        /**
         * 4. 这部分是壳中就存在的，按原有的约定，继续使用
         */
        @JavascriptInterface
        fun share(title: String) {
            Logger.d("webview share title $title")

            if (!TextUtils.isEmpty(title)) {
                activity?.let { HSUtil.share(it, title) }
            }
        }

        /**
         * 4. 这部分是壳中就存在的，按原有的约定，继续使用
         */
        @JavascriptInterface
        fun ready(msg: String) {
            Logger.d("webview ready")
        }

        //壳中函数
        @JavascriptInterface
        fun getMeta(): String {
            val json = JSONObject()
            json.put("applicationId", BuildConfig.APPLICATION_ID)
            json.put("version", BuildConfig.VERSION_NAME)
            return json.toString()
        }

        //壳中函数
        @JavascriptInterface
        fun getToken() {
            Logger.d("getToken")
        }

        @JavascriptInterface
        fun triggerVibration(json:String) {
            // impact: light/medium/heavy
            Logger.d( "webview triggerVibration : $json")
            val jsTriggerVibrationModel = Gson().fromJson(json, JsTriggerVibrationModel::class.java)
            when (jsTriggerVibrationModel?.impact) {
                "light" -> AndroidHapticFeedback().tick()
                "medium" -> AndroidHapticFeedback().click()
                "heavy" -> AndroidHapticFeedback().heavyClick()
                else -> {}
            }
        }


        /**
         * Precon房源详情，图片列表，跳转到原生图片列表
         */
        @JavascriptInterface
        fun showPreconPhotos(id_project: String) {
            Logger.d( "webview showPreconPhotos $id_project")

            activity?.runOnUiThread {
                val intent = Intent(getActivity(), PreconHousePhotoListActivity::class.java)
                intent.putExtra("id_project", id_project)
                startActivity(intent)
            }
        }

        /**
         * 房源详情，图片列表，跳转到原生图片列表
         */
        @JavascriptInterface
        fun showListingPhotos(id_listing: String) {
            Logger.d("webview showListingPhotos $id_listing")

            activity?.runOnUiThread {
                val intent = Intent(getActivity(), HousePhotoListActivity::class.java)
                intent.putExtra("id_listing", id_listing)
                startActivity(intent)
            }
        }


        /**
         * 打开webview
         */
        @JavascriptInterface
        fun pushToWebView(url: String) {
            Logger.d("webview pushToWebView $url")

            activity?.runOnUiThread {
                if (TextUtils.isEmpty(url)) return@runOnUiThread
                activity?.let {
                    if (url.contains("openHsNativeType=2")) {
                        WebViewHelper.jumpInnerWebView(it, url, true)
                    } else if (url.contains("openHsNativeType=3")) {
                        WebViewHelper.jumpOuterWebView(it, url.replace("?openHsNativeType=3","").replace("&openHsNativeType=3",""))
                    } else {
                        WebViewHelper.jumpInnerWebView(it, url, false)
                    }
                }
            }
        }


        /**
         * h5发现过期之后(900)，调桥的接口，由原生这边来继续处理token过期的情况
         * 请求api中的error.code告诉native
         */
        @JavascriptInterface
        fun sendErrorCode(code: String) {
            Logger.d("webview sendErrorCode $code")
            activity?.runOnUiThread {
                if ("900".equals(code) || "910".equals(code)) {
                    EventBus.getDefault()
                        .postSticky(MessageEvent(MessageType.ReLogin).put(code.toInt()))
                }
            }
        }

        @JavascriptInterface
        fun showCurrentRoute(json: String) {
            activity?.runOnUiThread {
                val showCurrentRouteModel = Gson().fromJson(json, ShowCurrentRoute::class.java)
                if (!TextUtils.isEmpty(showCurrentRouteModel.title)) {
                    binding.tvTitle.text = showCurrentRouteModel.title
                }
            }
        }


        @JavascriptInterface
        fun getSecretKey(jsonStr: String) {
            Logger.d( "webview getSecretKey $jsonStr")
            val secretKeyModel = Gson().fromJson(jsonStr, SecretKeyModel::class.java)
            if (secretKeyModel.refreshDirectly) {
                webViewViewModel.refreshSecretKey()
                return
            }

            val secret = HSApp.secret
            val isEncrypted = secret?.isEncrypted
            val secretKey = secret?.secretKey
            val expiredAt = secret?.expiredAt
            if (secret == null || secretKey == null || expiredAt == null || isEncrypted == null) {
                webViewViewModel.refreshSecretKey()
                return
            }

            val expireTime = expiredAt - (60 * 1) // 提前1分钟刷新
            val currentTime = System.currentTimeMillis()/1000
            if (currentTime >= expireTime) {
                webViewViewModel.refreshSecretKey()
            }else{
                callbackGetSecretKey(secretKey, expiredAt,secret.isEncrypted?:0)
            }
        }

        @JavascriptInterface
        fun watchedListingsChanged(id_listing: String) {
        }

        @JavascriptInterface
        fun watchedCommunitiesChanged(id_listing: String) {
        }

        @JavascriptInterface
        fun refreshNotes() {
            Logger.d( "webview refreshNotes")
            activity?.runOnUiThread {
                EventBus.getDefault().postSticky(MessageEvent(MessageType.REFRESH_NOTES))
            }
        }

        @JavascriptInterface
        fun getFirebaseRemoteConfigParams(): String? {
            Logger.d( "webview getFirebaseRemoteConfigParams")
            val remoteConfig: FirebaseRemoteConfig = Firebase.remoteConfig
            return GsonUtils.parseToStr(remoteConfig.all)
        }

        @JavascriptInterface
        fun showLiveChat() {
            Logger.d( "webview showLiveChat")
            activity?.runOnUiThread {
                activity?.let {
                    LiveChatUtil.startActivity(it)
                }
            }
        }

        @JavascriptInterface
        fun findNotInterestedByListingId(json :String): Boolean {
            Logger.d( "webview findNotInterestedByListingId: $json")
            val notInterestedListing = Gson().fromJson(json, NotInterestedListing::class.java)
            if (notInterestedListing.listing_id==null) {
                return false
            } else {
                return NotInterestedHelper.findNotInterestedByListingId(notInterestedListing.listing_id)
            }
        }

        @JavascriptInterface
        fun navToAgentMap(json :String) {
            Logger.d( "webview navToAgentMap : $json")
            val bridgeAgentMap = Gson().fromJson(json, BridgeAgentMap::class.java)
            activity?.runOnUiThread {
                activity?.let {
                    JumpHelper.jumpAgentMapActivity(it, bridgeAgentMap.status,id = bridgeAgentMap.id,slug = bridgeAgentMap.slug)
                }
            }
        }

        @JavascriptInterface
        fun getLastLocation(): String? {
            Logger.d( "webview getLastLocation")
            val lat = MMKVUtils.getDouble("user_location_latitude")
            val lon = MMKVUtils.getDouble("user_location_longitude")
            val timestamp = MMKVUtils.getLong("user_location_timestamp")
            return Gson().toJson(GetLastLocationResponse(lat, lon, timestamp))
        }


    }


    private fun getResourceForShare() {
        webView?.evaluateJavascript("javascript:getResourceForShare()",
            object : ValueCallback<String> {
                override fun onReceiveValue(shareStr: String?) {
                    if (shareStr != null) {
                        val shareBean = Gson().fromJson(shareStr, ShareModel::class.java)
                        if (shareBean != null) {
                            GALog.log("page_share_click", "market")
                            activity?.let {
                                HSUtil.share(
                                    it,
                                    UrlUtil.getURLDecoderString(shareBean.title) + " " + shareBean.url
                                )
                            }
                        }
                    }
                }
            })
    }

    private fun evaluateJs(js: String) {
        Logger.d("javascript:$js")
        webView?.evaluateJavascript("javascript:$js") { value ->
            Log.d(
                "hkj",
                "evaluateJavascript onReceiveValue: $value"
            )
        }
    }

    override fun initImmersionBar() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
            titleBar(binding.vTop)
        }
    }

    override fun createView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        webViewViewModel = ViewModelProvider(this).get(WebViewViewModel::class.java)
        binding = FragmentMarketBinding.inflate(inflater, container, false)
        return binding.root
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        when (event.type) {
            MessageType.RELOAD_PAGE_TOS_UPDATED, MessageType.RELOAD_MARKET, MessageType.PASSWORD_CHANGE -> {
                webView?.loadUrl(WebViewHelper.MARKET_URL + "&t=" + System.currentTimeMillis())
            }

            MessageType.UPDATE_LAST_POSITION -> {
                val lat = MMKVUtils.getDouble("user_location_latitude")
                val lon = MMKVUtils.getDouble("user_location_longitude")
                val timestamp = MMKVUtils.getLong("user_location_timestamp")
                val js = "updateLastPosition(" + GsonUtils.parseToStr(GetLastLocationResponse(lat, lon, timestamp)) + ")"
                Logger.d( "market updateLastPosition js $js")
                evaluateJs(js)
            }
            else -> {}
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        destroyWebView()
        MMKVUtils.removeData("time")
        Logger.d("onDestroy........APP被杀死")
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun initView(root: View?) {
    }

    override fun initListener() {
    }

    override fun tag(): String {
        return "market"
    }

    override fun onStart() {
        super.onStart()
        val timeSeconds = System.currentTimeMillis()
        val lastTime = MMKVUtils.getStr("time") ?: "0"
        val cutTime = timeSeconds - lastTime.toLong()
        if (cutTime > 4 * 60 * 60 * 1000 && cutTime != timeSeconds) {
            webView?.reload()
        }
        MMKVUtils.removeData("time")
    }

    override fun onStop() {
        super.onStop()
        MMKVUtils.saveStr("time", System.currentTimeMillis().toString())
    }

    override fun refreshLoad() {
        super.refreshLoad()
    }

    override fun onVisibilityChanged(visible: Boolean) {
        super.onFragmentVisibilityChanged(visible)
        if (visible) {
            GALog.page(tag())
        }
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        createWebView()
    }
    override fun lazyLoad() {
        initViews()
        initData()
        loadData()
    }

    private fun initData() {
        webViewViewModel.refreshSecretKey.observe(viewLifecycleOwner, {
            if (it.secret?.secretKey != null && it.secret?.isEncrypted!=null && it.secret?.expiredAt!=null) {
                callbackGetSecretKey(it.secret?.secretKey?:"", it.secret?.expiredAt?:0,it.secret?.isEncrypted?:0)
            }
        })
    }

    private fun callbackGetSecretKey(secretKey: String, expiredAt: Long,isEncrypted: Int) {
        val js = "callbackGetSecretKey({\"secret_key\":\"$secretKey\",\"expired_at\":$expiredAt,\"is_encrypted\":$isEncrypted})"
        activity?.runOnUiThread {
            evaluateJs(js)
        }
    }

    override fun onVisible() {
        super.onVisible()
        Logger.e("可见")
    }

    override fun onInvisible() {
        super.onInvisible()
        Logger.e("不可见")
    }

    private fun loadData() {
    }

    private fun initViews() {
        binding.ivShare.setOnClickListener {
            getResourceForShare()
        }
        createWebView()
        replaceWebViewContext()
        addWebViewAndLoadUrl()
        setupRefreshListener()
    }

    private fun setupRefreshListener(){
        binding.refreshLayout.setOnRefreshListener { refreshLayout ->
            EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_MARKET))
            binding.refreshLayout.finishRefresh(true)
        }
    }

    private fun addWebViewAndLoadUrl() {
        if (binding.webviewContainer.isEmpty()) {
            binding.webviewContainer.addView(
                webView,
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            webView?.loadUrl(WebViewHelper.MARKET_URL)
        }
    }

    /**
     *  解决context一致性，替换application
     */
    private fun replaceWebViewContext() {
        val contextWrapper = (webView?.context as MutableContextWrapper)
        contextWrapper.baseContext = context
    }


    @SuppressLint("SetJavaScriptEnabled")
    private fun createWebView() {
        if (webView != null) return
        try {
            webView = HSApp.appContext?.let { HSWebView(MutableContextWrapper(it)) }
        } catch (e:Exception) {
            ToastUtils.showLong("Chromium WebView package does not exist")
            e.printStackTrace()
        }

        webView?.setRefreshStateListener(object :HSWebView.RefreshStateListener{
            override fun refreshState(canRefresh: Boolean) {
                binding.refreshLayout.setEnableRefresh(canRefresh)
            }
        })

        webView?.let {
            it.setInitialScale(1)
            it.addJavascriptInterface(JsObject(), "bridge")
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                it.addJavascriptInterface(
                    AnalyticsWebInterface(),"AnalyticsWebInterface"
                )
            } else {
                Log.w(
                    "hkj",
                    "Not adding JavaScriptInterface, API Version: " + Build.VERSION.SDK_INT
                )
            }
            if (!"prod".equals(BuildConfig.FLAVOR)) {
                WebView.setWebContentsDebuggingEnabled(true)
            }

            it.settings.userAgentString =
                it.settings.userAgentString + "_HouseSigma_Android_" + BuildConfig.VERSION_NAME
            it.settings.javaScriptEnabled = true
            it.settings.useWideViewPort = true
            it.settings.loadWithOverviewMode = true
            it.settings.builtInZoomControls = true
            it.settings.setSupportZoom(true)
            it.settings.displayZoomControls = false
            it.settings.layoutAlgorithm =
                WebSettings.LayoutAlgorithm.TEXT_AUTOSIZING
            it.settings.javaScriptCanOpenWindowsAutomatically = true
            it.settings.domStorageEnabled = true

            it.settings.setTextSize(WebSettings.TextSize.NORMAL)
            it.settings.cacheMode = WebSettings.LOAD_DEFAULT
            it.settings.databaseEnabled = true
            it.settings.textZoom = 100

            it.webViewClient = object : WebViewClient() {
                override fun onLoadResource(view: WebView?, url: String?) {
                }

                override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                    super.onPageStarted(view, url, favicon)
                }

                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                }

                @RequiresApi(api = Build.VERSION_CODES.O)
                override fun onRenderProcessGone(
                    view: WebView?,
                    detail: RenderProcessGoneDetail?
                ): Boolean {
//                    原生webView在8.0系统后默认是多进程，Render进程和主进程隔离
                    try {
                        if (webView == null) {
                            return false
                        }
                        destroyWebView()
                        createWebView()
                        addWebViewAndLoadUrl()
                        detail?.let { renderGoneDetail->
                            if (renderGoneDetail.didCrash()){
                                Firebase.crashlytics.log("The WebView rendering process crashed!")
                            } else {
                                Firebase.crashlytics.log("System killed the WebView rendering process to reclaim memory. Recreating...")
                            }
                        }
                        Firebase.crashlytics.log("The app continues executing")
                        // #DEV-1988 hybird 假死后杀掉 webview 返回之前的页面并上报Crashlytics
//                         #DEV-2968 已经做兼容处理，只需要打LOG到crashlytics就可以，崩溃后再追踪
                        Firebase.crashlytics.log("onRenderProcessGone")
                        return true
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    return true
                }
            }



        }


    }


    private fun destroyWebView(){
        if (webView == null) {
            return
        }
        try {
            val webViewContainer = binding.webviewContainer
            webViewContainer.removeView(webView)
        } catch (e:Exception) {
            e.printStackTrace()
        }
        try {
            webView?.destroy()
            webView = null
        } catch (e:Exception) {
            e.printStackTrace()
        }
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
    }


}