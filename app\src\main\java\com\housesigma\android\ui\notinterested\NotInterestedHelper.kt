package com.housesigma.android.ui.notinterested

import android.text.TextUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.log.Logger
import java.lang.reflect.Type

class NotInterestedHelper {




    companion object{
        private var notInterestedList: ArrayList<String> = ArrayList()

        init {
            val cacheListingId = MMKVUtils.getStr("not_interested_list") ?: "[]"
            val type: Type = object : TypeToken<ArrayList<String?>?>() {}.type
            val list: List<String> = Gson().fromJson(cacheListingId, type)
            notInterestedList.clear()
            notInterestedList.addAll(list)
        }

        /**
         * 用先进先出，最多存300个
         */
         fun saveNotInterested(listingId: String) {
            if (!TextUtils.isEmpty(listingId)) {
                val limitCacheSize = 300
                if (notInterestedList.size >= limitCacheSize) {
                    notInterestedList.removeAt(limitCacheSize - 1)
                }
                notInterestedList.remove(listingId)
                notInterestedList.add(0, listingId)
                val json = Gson().toJson(notInterestedList)
                MMKVUtils.saveStr("not_interested_list", json)
            }
            Logger.e("save list: ${notInterestedList.toString()}")
        }

        fun delNotInterested(delListingId: String) {
            if (!TextUtils.isEmpty(delListingId)) {
                for (i in notInterestedList.count() - 1 downTo 0) {
                    if (delListingId == notInterestedList[i]) {
                        notInterestedList.remove(delListingId)
                    }
                }
                val json = Gson().toJson(notInterestedList)
                MMKVUtils.saveStr("not_interested_list", json)
            }
            Logger.e("del list: ${notInterestedList.toString()}")
        }


        /**
         * 根据listingId查找是否存在NotInterestedListings缓存中
         */
        fun findNotInterestedByListingId(listingId: String): Boolean {
            val sListingId = listingId.replace("[","").replace("]","")
            for (i in notInterestedList.count() - 1 downTo 0) {
                if (sListingId == notInterestedList[i]) {
                    return true
                }
            }
            return false
        }
    }




}