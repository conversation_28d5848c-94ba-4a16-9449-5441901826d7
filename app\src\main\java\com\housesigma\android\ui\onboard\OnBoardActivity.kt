package com.housesigma.android.ui.onboard

import android.content.Intent
import android.text.TextUtils
import android.util.Log
import android.view.KeyEvent
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityOnBoardBinding
import com.housesigma.android.model.InitApp
import com.housesigma.android.ui.home.ChooseProvinceAdapter
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.AppManager
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.MMKVUtils

/**
 * 第一次注册成功的用户填写一些信息
 */
class OnBoardActivity : BaseActivity() {

    private lateinit var binding: ActivityOnBoardBinding
    private lateinit var onBoardViewModel: OnBoardViewModel

    override fun getLayout(): Any {
        binding = ActivityOnBoardBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        GALog.page("onboarding")
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }

        binding.ivClose.setOnClickListener {
            onBoardViewModel.updateProfileShowOnBoard(0)
        }
        binding.tvSkip.setOnClickListener {
            GALog.log("onboarding_action", "province_skip")
            onBoardViewModel.updateProfileShowOnBoard(0)
        }

        val initApp = HSUtil.getInitApp()
        if (initApp == null) return
        val adapter = ChooseProvinceAdapter()
        binding.rv.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        binding.rv.adapter = adapter
        adapter.addData(initApp.provinces)
        adapter.notifyDataSetChanged()

        adapter.setOnItemChildClickListener { adapter, view, position ->

            when (view.id) {
                R.id.ll -> {
                    GALog.log("onboarding_action", "province")
                    val id = initApp.provinces.get(position).id
                    MMKVUtils.saveStr(LoginFragment.PROVINCE, id)
                    AppManager.getManager().addActivity(this)
                    startActivity(Intent(this, OnBoardFilterActivity::class.java))
                }
            }
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        AppManager.getManager().finishActivity(this)
    }


    override fun initData() {
        onBoardViewModel = ViewModelProvider(this).get(OnBoardViewModel::class.java)
        onBoardViewModel.updateMsg.observe(this) {
            finish()
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBoardViewModel.updateProfileShowOnBoard(0)
            return false
        }
        return super.onKeyDown(keyCode, event)
    }


}