package com.housesigma.android.ui.onboard

import android.util.Log
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityOnBoardFilterBinding
import com.housesigma.android.model.CustomizedMunicipalityFilter
import com.housesigma.android.ui.recommended.RecommendModel
import com.housesigma.android.utils.AppManager
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.utils.log.Logger

/**
 * Onboard选择页
 */
class OnBoardFilterActivity : BaseActivity() {

    private lateinit var binding: ActivityOnBoardFilterBinding
    private lateinit var recommendModel: RecommendModel
    private var onBoardFilterAdapter = OnBoardFilterAdapter()

    private lateinit var onBoardViewModel: OnBoardViewModel


    override fun onResume() {
        super.onResume()
        GALog.page("onboarding")
    }

    override fun getLayout(): Any {
        recommendModel = ViewModelProvider(this).get(RecommendModel::class.java)
        onBoardViewModel = ViewModelProvider(this).get(OnBoardViewModel::class.java)
        binding = ActivityOnBoardFilterBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        binding.ivClose.setOnClickListener {
            finish()
        }
        binding.tvSkip.setOnClickListener {
            GALog.log("onboarding_action", "city_skip")
            onBoardViewModel.updateProfileShowOnBoard(0)
        }

        binding.ivClose.setOnClickListener { finish() }
        binding.rv.adapter = onBoardFilterAdapter
        binding.rv.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)

        onBoardFilterAdapter.addChildClickViewIds(
            R.id.tv_select_all
        )
        onBoardFilterAdapter.setOnItemChildClickListener { adapter, view, position ->
            when (view.id) {
                R.id.tv_select_all -> {
                    val item = adapter.getItem(position) as CustomizedMunicipalityFilter
                    item.isSelect = !item.isSelect
                    for (municipalityItemFilter in item.list) {
                        municipalityItemFilter.isSelect = item.isSelect
                    }
                    adapter.notifyItemChanged(position)
                }
            }
        }

        binding.tvNext.setOnClickListener {
            GALog.log("onboarding_action", "city_next")
            val municipalityIds = ArrayList<Int>()
            for (datum in onBoardFilterAdapter.data) {
                for (municipalityItemFilter in datum.list) {
                    if (municipalityItemFilter.isSelect) {
                        municipalityIds.add(municipalityItemFilter.id)
                    }
                }
            }

            Logger.d( ">>>>$municipalityIds")

            val cacheProperty = municipalityIds.toString()
                .replace("[", "")
                .replace("]", "")
                .replace(" ", "")
            // 持久化 写入cache
            MMKVUtils.saveStr("label_filter_city", cacheProperty)
            recommendModel.searchHomePageCustomize(municipalityIds)
        }
    }

    override fun initData() {
        recommendModel.recommendFilter.observe(this) {
            onBoardFilterAdapter.addData(it.municipality_filter)
//            binding.labels.setLabels(it.house_type_filter) { _, _, data -> data.name }
        }

        onBoardViewModel.updateMsg.observe(this) {
            AppManager.getManager().finishActivity(OnBoardActivity::class.java)
            finish()
        }

        recommendModel.getRecommendv2Filter()
        recommendModel.msgRes.observe(this) {
            ToastUtils.showLong(it.message)
            onBoardViewModel.updateProfileShowOnBoard(0)
        }
    }


}