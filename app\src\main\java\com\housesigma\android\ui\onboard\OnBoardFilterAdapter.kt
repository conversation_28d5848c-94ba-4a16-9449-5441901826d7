package com.housesigma.android.ui.onboard

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.donkingliang.labels.LabelsView
import com.housesigma.android.R
import com.housesigma.android.model.CustomizedMunicipalityFilter

class OnBoardFilterAdapter :
    BaseQuickAdapter<CustomizedMunicipalityFilter, BaseViewHolder>(R.layout.item_customize) {

    override fun convert(holder: BaseViewHolder, item: CustomizedMunicipalityFilter) {
        val labels = holder.getView<LabelsView>(R.id.labels)
        labels.selectType = LabelsView.SelectType.MULTI
        labels.setLabels(item.list) { _, _, data -> data.name; }
        holder.setText(R.id.tv_city_name, item.title)


        if (item.isSelect){
            holder.setText(R.id.tv_select_all, "Unselect all")
        }else{
            holder.setText(R.id.tv_select_all, "Select all")
        }

        var selectPosition = ArrayList<Int>()

        var i = 0
        for (municipalityItemFilter in item.list) {
            if (municipalityItemFilter.isSelect) {
                selectPosition.add(i)
            }
            i++
        }

        labels.setSelects(selectPosition)
        labels.let {
            it.setOnLabelClickListener { label, data, position ->
                item.list[position].isSelect = label.isSelected
            }
        }
    }


}