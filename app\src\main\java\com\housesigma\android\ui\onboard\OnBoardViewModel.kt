package com.housesigma.android.ui.onboard

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.housesigma.android.model.MsgRes
import com.housesigma.android.network.NetClient
import com.housesigma.android.network.launch

class OnBoardViewModel : ViewModel() {

    var updateMsg: MutableLiveData<MsgRes> = MutableLiveData()

    /**
     * 1显示 0不显示
     */
    fun updateProfileShowOnBoard(isShow: Int) {
        launch({
            NetClient.apiService.updateProfileShowOnBoard(isShow)
        }, {
            updateMsg.postValue(it)
        })
    }

}