package com.housesigma.android.ui.onetap

import android.content.Intent
import android.text.TextUtils
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityGoogleOneTopBinding
import com.housesigma.android.hybrid.HybridUtils
import com.housesigma.android.model.AgentBoard
import com.housesigma.android.model.AgentBoards
import com.housesigma.android.model.Board
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.User
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.main.MainActivity
import com.housesigma.android.ui.signup.SignUpModel
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.GsonUtils
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.views.selectlistdialog.SelectListDialog
import com.lxj.xpopup.XPopup
import org.greenrobot.eventbus.EventBus

// 未注册情况
class OneTapActivity : BaseActivity() {

    private lateinit var binding: ActivityGoogleOneTopBinding
    private lateinit var idToken: String
    private lateinit var email: String
    private lateinit var signupModel: SignUpModel
    private var isAgent: String = "0"
    private var agentBoards: AgentBoards?=null
    private var board :String = ""

    private var brokerageName = ""
    private var licensedProvince = ""
    private var boardName = ""

    override fun onResume() {
        super.onResume()
        GALog.page("sign_up_onetap")
    }

    override fun getLayout(): Any {
        binding = ActivityGoogleOneTopBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        email = intent.getStringExtra("email")!!
        idToken = intent.getStringExtra("id_token")!!
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        binding.ivClose.setOnClickListener {
            finish()
        }
        binding.ivDel.setOnClickListener {
            binding.etReferralCode.setText("")
        }
        binding.etEmail.setText(email)
        binding.tvNext.setOnClickListener {
            GALog.log("third_party_auth_submit", "onetap")
            val pass = binding.etPassword.text.toString().trim()
            val referralCode = binding.etReferralCode.text.toString().trim()

            if (binding.cbIsAgent.isChecked()) {
                brokerageName = binding.etBrokerageName.text.toString().trim()
                licensedProvince = this.licensedProvince
                boardName = this.board
            }

            val crackIsAgent = MMKVUtils.getStr(LoginFragment.CRACK_IS_AGENT)
            val crackBrokerageName = MMKVUtils.getStr(LoginFragment.CRACK_BROKERAGE_NAME )
            val crackBoardName = MMKVUtils.getStr(LoginFragment.CRACK_BOARD_NAME)
            val crackLicensedProvince = MMKVUtils.getStr(LoginFragment.CRACK_LICENSED_PROVINCE)

            showLoadingDialog()
            signupModel.googleSignUp(
                credential = idToken,
                pass = pass,
                is_agent = isAgent,
                referral_code = referralCode,
                licensed_province = licensedProvince,
                board_name = boardName,
                brokerage_name = brokerageName,
                cracking_is_agent = crackIsAgent,
                cracking_board_name = crackBoardName,
                cracking_brokerage_name = crackBrokerageName,
                cracking_licensed_province = crackLicensedProvince,
            )
        }

        binding.cbIsAgent.setOnCheckedChangeListener { compoundButton, boolean ->
            if (boolean) {
                isAgent = "1"
                binding.llAgent.visibility = View.VISIBLE
            } else {
                isAgent = "0"
                binding.llAgent.visibility = View.GONE
            }
        }

        binding.llProvince.setOnClickListener {
            agentBoards?.let {
                val provinces = it.provinces
                val selectListDialog = SelectListDialog<AgentBoard>(
                    title = "Province",
                    provinces,
                    this,
                    object : SelectListDialog.SelectCallback<AgentBoard> {
                        override fun onSuccess(selectItem: AgentBoard) {
                            licensedProvince = selectItem.province
                            binding.tvSelectProvince.text = selectItem.province_text
                        }

                        override fun onCancel() {
                        }
                    })

                XPopup.Builder(this)
                    .asCustom(selectListDialog)
                    .show()
            }
        }

        binding.llBoardName.setOnClickListener {
            val provinceStr = binding.tvSelectProvince.text.toString()
            if (!TextUtils.isEmpty(provinceStr)) {
                agentBoards?.let {
                    val provinces = it.provinces
                    for (province in provinces) {
                        if (province.province_text.equals(provinceStr)) {
                            val selectListDialog = SelectListDialog<Board>(
                                title = "Board name",
                                province.boards,
                                this,
                                object : SelectListDialog.SelectCallback<Board> {
                                    override fun onSuccess(selectItem: Board) {
                                        board = selectItem.value
                                        binding.tvBoardSelectName.text = selectItem.text
                                    }

                                    override fun onCancel() {
                                    }
                                })

                            XPopup.Builder(this)
                                .asCustom(selectListDialog)
                                .show()
                        }
                    }
                }
            }
        }
    }

    override fun initData() {
        signupModel = ViewModelProvider(this).get(SignUpModel::class.java)

        signupModel.signIn.observe(this) {
            val user = HybridUtils.saveAndParseUserData(it.hybridUser)
            it.appUser = user?:User()
            saveAgentInfo()
            loginSuccess(it.appUser)
            EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_FCM_TOKEN))
            val intent = Intent(this, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
        }

        signupModel.loadingLiveData.observe(this) {
            dismissLoadingDialog()
        }

        signupModel.agentBoard.observe(this) {
            agentBoards = it
        }

        signupModel.agentboard()
    }

    private fun saveAgentInfo() {
        if (isAgent == "1") {
            val crackIsAgent = MMKVUtils.getStr(LoginFragment.CRACK_IS_AGENT)
            if (crackIsAgent != "1") {
                MMKVUtils.saveStr(LoginFragment.CRACK_IS_AGENT, isAgent)
                MMKVUtils.saveStr(LoginFragment.CRACK_BROKERAGE_NAME, brokerageName?:"")
                MMKVUtils.saveStr(LoginFragment.CRACK_BOARD_NAME, boardName?:"")
                MMKVUtils.saveStr(LoginFragment.CRACK_LICENSED_PROVINCE, licensedProvince?:"")
            }
        }
    }

    private fun loginSuccess(it: User) {
        if (!TextUtils.isEmpty(it.referral_code)){
            GALog.log("registration_success", "onetap","referral")
        } else {
            GALog.log("registration_success", "onetap")
        }
        LoginFragment.saveUserInfo(it)
    }

}