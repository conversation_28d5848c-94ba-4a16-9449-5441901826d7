package com.housesigma.android.ui.recommended

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.housesigma.android.model.*
import com.housesigma.android.network.NetClient
import com.housesigma.android.network.launch
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.HSLog
import com.housesigma.android.utils.LanguageUtils
import com.housesigma.android.utils.MMKVUtils
import retrofit2.http.Field

/**
 * Recommend、OnBoard，公用同一个model
 */
class RecommendModel : ViewModel() {

    var recommendFilter: MutableLiveData<RecommedCommunityFilter> = MutableLiveData()
    var msgRes: MutableLiveData<MsgRes> = MutableLiveData()
    var customizedFilter: MutableLiveData<CustomizedFilter> = MutableLiveData()
    var recommendStart: MutableLiveData<RecommendStart> = MutableLiveData()
    var loadingLiveData = MutableLiveData<Boolean>()
    var recommendv2Community: MutableLiveData<Recommendv2Community> = MutableLiveData()

    fun getRecommendv2Community(
        page: Int,
        price_max: String,
        price_min: String,
        id_community: String,
        community_house_type: String,
        house_type: List<String>,
        municipality_id: List<String>,
        investment: List<String>,
    ) {
        launch({
            NetClient.apiService.getRecommendv2Community(
                page = page,
                price_max = price_max,
                price_min = price_min,
                id_community = id_community,
                community_house_type = community_house_type,
                house_type = house_type,
                municipality_id = municipality_id,
                investment = investment
            )
        }, {
            recommendv2Community.postValue(it)
        })
    }


    fun getRecommendv2Start(
        page: Int,
        price_max: String,
        price_min: String,
        municipality_id: List<String>,
        investment: List<String>,
        house_type: List<String>,
    ) {
        launch({
            NetClient.apiService.getRecommendv2Start(
                page,
                price_max,
                price_min,
                municipality_id,
                investment,
                house_type
            )
        }, {
            recommendStart.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun getRecommendv2Filter() {
        launch({
            NetClient.apiService.getRecommendv2Filter()
        }, {
            recommendFilter.postValue(it)
        })
    }

    fun searchHomePageCustomize(
        municipality_id: List<Int>,
        house_type: List<String> = ArrayList(),
        price_max: Int = 6000000,
        price_min: Int = 0

    ) {
        launch({
            NetClient.apiService.searchHomePageCustomize(
                municipality_id = municipality_id,
                price_max = price_max,
                price_min = price_min,
                house_type = house_type
            )
        }, {
            msgRes.postValue(it)
            val map = HashMap<String, Any>()
            map["price_range_min"] = price_max
            map["price_range_max"] = price_min
            map["property_types"] = house_type
            map["municipalitie_ids"] = municipality_id
            HSLog.userInput(eventName = "user_input_personalize_listings",map)
        })
    }

    fun getCustomizedFilter() {
        launch({
            NetClient.apiService.getCustomizedFilter()
        }, {
            customizedFilter.postValue(it)
        })
    }

}