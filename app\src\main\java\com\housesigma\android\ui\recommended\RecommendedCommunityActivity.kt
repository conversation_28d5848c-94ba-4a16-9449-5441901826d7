package com.housesigma.android.ui.recommended

import android.os.Bundle
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.listener.OnLoadMoreListener
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.base.BaseListingsAdapterHelper
import com.housesigma.android.databinding.*
import com.housesigma.android.model.HouseDetail
import com.housesigma.android.model.Location
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.tos.TosDialog
import com.housesigma.android.ui.tos.VowTosDialog
import com.housesigma.android.ui.watched.NormalListingCollectionAdapter
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.Constants
import com.housesigma.android.utils.ConstantsHelper
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MapUtils
import com.housesigma.android.views.DisclaimerViewHelper
import com.housesigma.android.views.HSLoadMoreView
import org.maplibre.android.MapLibre
import org.maplibre.android.annotations.IconFactory
import org.maplibre.android.annotations.MarkerOptions
import org.maplibre.android.camera.CameraPosition
import org.maplibre.android.geometry.LatLng
import org.maplibre.android.maps.MapView
import org.maplibre.android.maps.MapLibreMap
import org.maplibre.android.maps.Style
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener

class RecommendedCommunityActivity : BaseActivity(), LoginFragment.LoginCallback {

    private lateinit var binding: ActivityRecommendedCommunityBinding
    private var price_min: Int = 0
    private var price_max: Int = 6000000
    private var id_community: String? = null
    private var community_plus: String? = null
    private var municipality_id: ArrayList<String> = ArrayList()
    private var community_house_type: String? = null
    private var investment: ArrayList<String> = ArrayList()
    private var house_type: ArrayList<String> = ArrayList()
    private lateinit var recommendModel: RecommendModel
    private val adapter = NormalListingCollectionAdapter()
    private var page: Int = 1
    private var mList: MutableList<HouseDetail> = ArrayList()
//    private lateinit var headerBinding: HeaderRecommendedCommunityMapBinding

    private var loginDialog: LoginFragment? = null

    private var MapLibreMap: MapLibreMap? = null
    private var mapView: MapView? = null
    private var listingSize: Int = 0

    override fun getLayout(): Any {
        MapLibre.getInstance(this)
        municipality_id = intent.getStringArrayListExtra("municipality_id") ?: ArrayList()
        investment = intent.getStringArrayListExtra("investment") ?: ArrayList()
        house_type = intent.getStringArrayListExtra("house_type") ?: ArrayList()

        community_house_type = intent.getStringExtra("community_house_type") ?: ""
        id_community = intent.getStringExtra("id_community") ?: ""
        price_max = intent.getIntExtra("price_max", 0)
        price_min = intent.getIntExtra("price_min", 0)
        community_plus = intent.getStringExtra("community_plus") ?: ""
        listingSize = intent.getIntExtra("listing_size", 0)


        binding = ActivityRecommendedCommunityBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }

        binding.tvTitle.text = community_plus

        binding.rv.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        binding.rv.adapter = adapter
        adapter.loadMoreModule.loadMoreView = HSLoadMoreView()
        adapter.loadMoreModule.setOnLoadMoreListener(object : OnLoadMoreListener {
            override fun onLoadMore() {
                page++
                loadData()
            }
        })


        binding.refreshLayout.setOnRefreshListener { refreshLayout ->
            page = 1
            loadData()
        }

        binding.ivClose.setOnClickListener {
            finish()
        }

//        headerBinding = HeaderRecommendedCommunityMapBinding.inflate(layoutInflater)
//        adapter.addHeaderView(
//            headerBinding.root,
//            orientation = LinearLayout.VERTICAL
//        )
        mapView = binding.mapView
//        mapView = headerBinding.mapView

        adapter.setOnItemChildClickListener { adapter, view, position ->
            val houseDetail = adapter.data[position] as HouseDetail

            when (view.id) {
                R.id.rl -> {
                    if (BaseListingsAdapterHelper.canJumpListingDetail(houseDetail)){
                        return@setOnItemChildClickListener
                    }
                    WebViewHelper.jumpHouseDetail(
                        this,
                        houseDetail.id_listing,
                        houseDetail.seo_suffix,
                        eventSource = "recommendcommunities"
                    )
                }

                R.id.tv_login_required -> {
                    val tvLoginRequiredStr = (view as TextView).text.toString()
                    if (Constants.TEXT_VIEW_TRREB_TIMEOUT.equals(tvLoginRequiredStr)){
                        showLoginDialog(reLogin = LoginFragment.RE_LOGIN_TRREB_TIMEOUT)
                    } else if ("Password Expired".equals(tvLoginRequiredStr)) {
                        showLoginDialog(reLogin = LoginFragment.RE_LOGIN_VALIDATE)
                    }else{
                        showLoginDialog()
                    }
                }

                R.id.tv_agreement_required -> {
                    this.let {
                        TosDialog(
                            this, it, it, houseDetail.tos_source,
                            object : TosDialog.TosCallback {
                                override fun onSuccess() {
                                    page = 1
                                    loadData()
                                }
                            }).show()
                    }
                }

                R.id.tv_not_available -> {
                    VowTosDialog(houseDetail.id_listing,this,this,this).show()
                }
            }
        }
    }


    override fun initData() {
        recommendModel = ViewModelProvider(this).get(RecommendModel::class.java)
        recommendModel.recommendv2Community.observe(this) {
            adapter.loadMoreModule.preLoadNumber = 2
            adapter.loadMoreModule.loadMoreComplete()

            if (it.total_page <= it.current_page) {
                adapter.loadMoreModule.enableLoadMoreEndClick = false
                adapter.loadMoreModule.loadMoreEnd(true)
                adapter.loadMoreModule.isAutoLoadMore = false
                adapter.loadMoreModule.isEnableLoadMore = false
                adapter.loadMoreModule.isEnableLoadMoreIfNotFullPage = false
            }else{
                adapter.loadMoreModule.isEnableLoadMore = true
                adapter.loadMoreModule.isAutoLoadMore = true
            }

            binding.refreshLayout.finishRefresh(true)
//            binding.refreshLayout.finishLoadMore(
//                0, true,
//                it.total_page <= it.current_page
//            )
//            binding.refreshLayout.setNoMoreData(it.total_page <= it.current_page)

            if (page == 1) {
                mList.clear()
            }
            mList.addAll(it.house_list)
            adapter.setList(mList)

            DisclaimerViewHelper.handleDisclaimer(adapter,this)

            val that = it.community
            if (listingSize <= 1) {
                binding.tvListingsSize.setText(listingSize.toString() + " Listing")
            } else {
                binding.tvListingsSize.setText(listingSize.toString() + " Listings")
            }
            try {
                mapView?.onCreate(null)
                mapView?.getMapAsync { map ->
                    MapLibreMap = map
                    MapLibreMap?.uiSettings?.isRotateGesturesEnabled = false
                    setUpMapboxMap(ConstantsHelper.getMapVector())
                    moveCameraToPoint(Location(that.map.lat, that.map.lon))


                    val iconFactory = IconFactory.getInstance(RecommendedCommunityActivity@ this)
                    binding.tvTitle.text = that.community_name
                    val binding = MapMarkerWindowRecommendCommunityBinding.inflate(layoutInflater)
                    binding.tvNumber.text = that.community_name
                    val bitmap = MapUtils.convertViewToNormalBitmap(binding.root)
                    val fromResource = iconFactory.fromBitmap(bitmap)
                    val options = MarkerOptions().icon(fromResource)
                        .position(LatLng(that.map.lat, that.map.lon))


                    var addMarker = MapLibreMap?.addMarker(options)
//                    if (MapLibreMap!=null&&mapView!=null){
//                        addMarker?.showInfoWindow(MapLibreMap!!,mapView!!)
//                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

            binding.ivChart.setOnClickListener {
                WebViewHelper.jumpMarket(
                    this,
                    municipality = that.id_municipality,
                    community = that.id_community,
                    house_type = community_house_type
                )
            }
        }
        loadData()
    }

    private fun setUpMapboxMap(styleUrl: String) {
        MapLibreMap?.setStyle(styleUrl, object : Style.OnStyleLoaded {
            override fun onStyleLoaded(style: Style) {
            }
        })
    }

    /**
     * 移动camera 到某个位置
     */
    private fun moveCameraToPoint(point: Location) {
        var currentMapCenter = LatLng(point.lat, point.lon)
        var currentMapZoom = 14.0
        MapLibreMap?.cameraPosition = CameraPosition.Builder()
            .target(
                currentMapCenter
            )
            .zoom(currentMapZoom)
            .build()
    }

    override fun onDestroy() {
        super.onDestroy()
        mapView?.onDestroy()
    }

    private fun loadData() {
        recommendModel.getRecommendv2Community(
            page = page,
            price_max = price_max.toString(),
            price_min = price_min.toString(),
            id_community = id_community ?: "",
            community_house_type = community_house_type ?: "",
            house_type = house_type,
            municipality_id = municipality_id,
            investment = investment,
        )
    }

    private fun showLoginDialog(reLogin: String = "") {
        GALog.log("login_button_click")
        supportFragmentManager.let {
            if (loginDialog == null) {
                loginDialog = LoginFragment()
            }
            if (loginDialog?.isAdded == true) return
            val bundle = Bundle()
            bundle.putString("reLogin", reLogin)
            loginDialog?.arguments = bundle
            loginDialog?.show(it, "")
        }
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
        page = 1
        loadData()
    }
}