package com.housesigma.android.ui.recommended

import android.content.Intent
import android.util.Log
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityRecommendedCommunityListBinding
import com.housesigma.android.model.RecommendStartItem
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.HSLog
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener

class RecommendedCommunityListActivity : BaseActivity() {

    private lateinit var binding: ActivityRecommendedCommunityListBinding
    private var price_min: Int = 0
    private var price_max: Int = 6000000
    private var municipality_id: ArrayList<String> = ArrayList()
    private var investment: ArrayList<String> = ArrayList()
    private var house_type: ArrayList<String> = ArrayList()
    private lateinit var recommendModel: RecommendModel
    private val adapter = RecommendedCommunityListAdapter()
    private var page: Int = 1
    private var mList: MutableList<RecommendStartItem> = ArrayList()

    override fun getLayout(): Any {
        municipality_id = intent.getStringArrayListExtra("municipality_id") as ArrayList<String>
        investment = intent.getStringArrayListExtra("investment") as ArrayList<String>
        house_type = intent.getStringArrayListExtra("house_type") as ArrayList<String>
        price_max = intent.getIntExtra("price_max", 0)
        price_min = intent.getIntExtra("price_min", 0)
        binding = ActivityRecommendedCommunityListBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }


        binding.rv.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        binding.rv.adapter = adapter
        binding.refreshLayout.setOnRefreshListener { refreshLayout ->
            refreshLayout.finishRefresh(2000)
            loadData()
        }

        binding.ivClose.setOnClickListener {
            finish()
        }

        binding.ivEdit.setOnClickListener {
            finish()
        }

        binding.refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                page = 1
                loadData()
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                page++
                loadData()
            }

        })


        adapter.addChildClickViewIds(
            R.id.ll_market_trends,
            R.id.ll
        )
        adapter.setOnItemChildClickListener { adapter, view, position ->
            val item = mList[position]
            when (view.id) {
                R.id.ll -> {
                    val intent = Intent(this, RecommendedCommunityActivity::class.java)
                    intent.putExtra("municipality_id", municipality_id)
                    intent.putExtra("price_max", 6000000)
                    intent.putExtra("price_min", 0)
                    intent.putExtra("house_type",house_type)//house_type是 外层array的那个
                    intent.putExtra("investment", investment)
                    intent.putExtra("community_house_type", item.house_type)//community_house_type 是string类型
                    intent.putExtra("id_community",item.id_community)
                    intent.putExtra("community_plus",item.community_plus)
                    intent.putExtra("listing_size",item.listing)
                    startActivity(intent)
                }


                R.id.ll_market_trends -> {
                    WebViewHelper.jumpMarket(this, municipality = item.id_municipality, community = item.id_community,house_type=item.house_type)
                }


            }
        }


    }

    override fun initData() {
        recommendModel = ViewModelProvider(this).get(RecommendModel::class.java)
        recommendModel.recommendStart.observe(this) {
            binding.tvSummary.text =
                "Searched " + it.total_city + " cities, " + it.total_community + " communities, " + it.total_listing + " listings"

            binding.refreshLayout.finishRefresh(true)
            binding.refreshLayout.finishLoadMore(
                0, true,
                it.total_page <= it.current_page
            )
            binding.refreshLayout.setNoMoreData(it.total_page <= it.current_page)

            if (page == 1) {
                mList.clear()
            }
            mList.addAll(it.community_list)
            adapter.setList(mList)
        }
        loadData()
    }


    private fun loadData() {
        recommendModel.getRecommendv2Start(
            page = page,
            price_max = price_max.toString(),
            price_min = price_min.toString(),
            municipality_id = municipality_id,
            investment = investment,
            house_type = house_type
        )
    }
}