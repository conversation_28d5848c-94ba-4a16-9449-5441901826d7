package com.housesigma.android.ui.recommended

import android.graphics.Bitmap
import android.text.TextUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.RecommendStartItem
import com.housesigma.android.utils.ScreenUtils
import jp.wasabeef.glide.transformations.RoundedCornersTransformation


class RecommendedCommunityListAdapter :
    BaseQuickAdapter<RecommendStartItem, BaseViewHolder>(R.layout.item_recommend_community_list) {


    override fun convert(holder: BaseViewHolder, item: RecommendStartItem) {
        val topCorner = ScreenUtils.dpToPx(8f)
        val multi: MultiTransformation<Bitmap> = MultiTransformation(
            CenterCrop(),
            RoundedCornersTransformation(
                topCorner.toInt(),
                0,
                RoundedCornersTransformation.CornerType.ALL
            )
        )
        loadImage(item, multi, holder, R.id.iv_house_pic1, 0)
        loadImage(item, multi, holder, R.id.iv_house_pic2, 1)
        loadImage(item, multi, holder, R.id.iv_house_pic3, 2)

//        // 副标题区，主要为位置信息
        var address =
            if (!TextUtils.isEmpty(item.community_plus))
                item.community_plus
            else ""
        address = address.plus(
            if (!TextUtils.isEmpty(item.municipality_plus))
                " - ${item.municipality_plus}"
            else ""
        )
        holder.setText(R.id.tv_address, address)
        holder.setText(R.id.tv_house_type_name, item.house_type_name)
        holder.setText(R.id.tv_listings_size, "View Listings (" + item.listing + ")")
        holder.setText(R.id.tv_median_price, "$ ${item.price_sold_median ?: ""}")
    }

    private fun loadImage(
        item: RecommendStartItem,
        multi: MultiTransformation<Bitmap>,
        holder: BaseViewHolder,
        iv: Int,
        position: Int
    ) {
        if ((position + 1) > item.pics.size) {
            Glide.with(context)
                .load("")
                .transform(multi)
                .error(R.drawable.shape_pic_place_holder_radius_8)
                .placeholder(R.drawable.shape_pic_place_holder_radius_8)
                .into(holder.getView(iv))
        } else {
            Glide.with(context)
                .load(item.pics[position])
                .transform(multi)
                .error(R.drawable.shape_pic_place_holder_radius_8)
                .placeholder(R.drawable.shape_pic_place_holder_radius_8)
                .into(holder.getView(iv))
        }

    }

}