package com.housesigma.android.ui.recommended

import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.donkingliang.labels.LabelsView
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityRecommendedStartBinding
import com.housesigma.android.model.CustomizedMunicipalityFilter
import com.housesigma.android.model.HouseTypeFilter
import com.housesigma.android.model.InvestmentFilter
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSLog
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.views.SaleSeekBar
import com.jaygoo.widget.OnRangeChangedListener
import com.jaygoo.widget.RangeSeekBar
import java.lang.Exception

class RecommendedStartActivity : BaseActivity() {

    private lateinit var binding: ActivityRecommendedStartBinding
    private lateinit var recommendModel: RecommendModel
    private var customizeAdapter = RecommendedStartAdapter()
    private var isSelectAllPropertyType: Boolean = false
    private var isSelectAllInvestmentRequirement: Boolean = false

    override fun onResume() {
        super.onResume()
        GALog.page("recommend_communities")
    }
    override fun getLayout(): Any {
        binding = ActivityRecommendedStartBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }

        binding.labelsHouseTypeFilter.selectType = LabelsView.SelectType.MULTI
        binding.labelsInvestmentRequirement.selectType = LabelsView.SelectType.MULTI
        binding.ivClose.setOnClickListener { finish() }
        binding.rv.adapter = customizeAdapter
        binding.rv.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)

        binding.saleSb.setOnChangeListener(object :SaleSeekBar.OnChangeListener{
            override fun onChange(showPrice: String) {
                binding.tvPrice.text = showPrice
            }

            override fun onStopTrackingTouch() {
            }
        })

        binding.tvSelectAllPropertyType.setOnClickListener {
            if (isSelectAllPropertyType) {
                binding.labelsHouseTypeFilter.clearAllSelect()
                binding.tvSelectAllPropertyType.text = "Select all"
            } else {
                try {
                    binding.labelsHouseTypeFilter.setSelects(0,1,2,3)
                }catch (e:Exception){
                    e.printStackTrace()
                }
                binding.tvSelectAllPropertyType.text = "Unselect all"
            }
            isSelectAllPropertyType = !isSelectAllPropertyType
        }

        binding.tvSelectAllInvestmentRequirement.setOnClickListener {
            if (isSelectAllInvestmentRequirement) {
                binding.labelsInvestmentRequirement.clearAllSelect()
                binding.tvSelectAllInvestmentRequirement.text = "Select all"
            } else {
                try {
                    binding.labelsInvestmentRequirement.setSelects(0,1,2,3)
                }catch (e:Exception){
                    e.printStackTrace()
                }
                binding.labelsInvestmentRequirement.setSelects(0,1,2,3)
                binding.tvSelectAllInvestmentRequirement.text = "Unselect all"
            }
            isSelectAllInvestmentRequirement = !isSelectAllInvestmentRequirement
        }

        customizeAdapter.addChildClickViewIds(
            R.id.tv_select_all
        )
        customizeAdapter.setOnItemChildClickListener { adapter, view, position ->
            when (view.id) {
                R.id.tv_select_all -> {
                    val item = adapter.getItem(position) as CustomizedMunicipalityFilter
                    item.isSelect = !item.isSelect
                    for (municipalityItemFilter in item.list) {
                        municipalityItemFilter.isSelect = item.isSelect
                    }
                    adapter.notifyItemChanged(position)
                }
            }
        }

        binding.tvStart.setOnClickListener {
            val house_type = ArrayList<String>()
            val investment = ArrayList<String>()
            val municipality_id = ArrayList<String>()
//            Logger.d(binding.labelsHouseTypeFilter.getSelectLabelDatas<HouseTypeFilter>().toString())
//            Logger.d( binding.labelsInvestmentRequirement.getSelectLabelDatas<InvestmentFilter>().toString())
//            Logger.d(customizeAdapter.data.toString())
            var houseTypeFilter =
                binding.labelsHouseTypeFilter.getSelectLabelDatas<HouseTypeFilter>()
            var investmentFilter =
                binding.labelsInvestmentRequirement.getSelectLabelDatas<InvestmentFilter>()
            var data = customizeAdapter.data

            for (datum in data) {
                for (municipalityItemFilter in datum.list) {
                    if (municipalityItemFilter.isSelect) {
                        municipality_id.add(municipalityItemFilter.id.toString())
                    }
                }
            }

            for (houseTypeFilter in houseTypeFilter) {
                house_type.add(houseTypeFilter.id)
            }

            for (investmentFilter in investmentFilter) {
                investment.add(investmentFilter.id)
            }
            if (investment.size == 0) {
                ToastUtils.showLong("Please choose an investment requirement")
                return@setOnClickListener
            }
            if (house_type.size == 0) {
                ToastUtils.showLong("Please choose a property type")
                return@setOnClickListener
            }
            if (municipality_id.size == 0) {
                ToastUtils.showLong("Please choose a city")
                return@setOnClickListener
            }

            GALog.log("recommend_submit")


            val map = HashMap<String, Any>()
            map["price_range_min"] = binding.saleSb.priceLeft
            map["price_range_max"] = binding.saleSb.priceRight
            map["investment"] = investment
            map["property_types"] = house_type
            map["municipalitie_ids"] = municipality_id
            HSLog.userInput(eventName = "user_input_community_recommendations",map)


            val intent = Intent(this, RecommendedCommunityListActivity::class.java)
            intent.putStringArrayListExtra("municipality_id", municipality_id)
            intent.putStringArrayListExtra("house_type", house_type)
            intent.putStringArrayListExtra("investment", investment)
            intent.putExtra("price_max", binding.saleSb.priceRight)
            intent.putExtra("price_min", binding.saleSb.priceLeft)
            startActivity(intent)


        }

    }

    override fun initData() {
        recommendModel = ViewModelProvider(this).get(RecommendModel::class.java)
        recommendModel.recommendFilter.observe(this) {
            customizeAdapter.addData(it.municipality_filter)

            var houseTypeFilter = it.house_type_filter
            var investmentFilter = it.investment_filter

            binding.labelsHouseTypeFilter.setLabels(it.house_type_filter) { _, _, data -> data.name }

            binding.labelsInvestmentRequirement.setLabels(it.investment_filter) { _, _, data -> data.name }
        }
        recommendModel.getRecommendv2Filter()
    }
}