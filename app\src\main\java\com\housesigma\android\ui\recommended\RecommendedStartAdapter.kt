package com.housesigma.android.ui.recommended

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.donkingliang.labels.LabelsView
import com.housesigma.android.R
import com.housesigma.android.model.CustomizedMunicipalityFilter

class RecommendedStartAdapter :
    BaseQuickAdapter<CustomizedMunicipalityFilter, BaseViewHolder>(R.layout.item_recommended_start) {

    override fun convert(holder: BaseViewHolder, item: CustomizedMunicipalityFilter) {
        val labels = holder.getView<LabelsView>(R.id.labels)
        labels.selectType = LabelsView.SelectType.MULTI
        labels.setLabels(item.list) { _, _, data -> data.name; }


        if (item.isSelect){
            holder.setText(R.id.tv_select_all, "Unselect all")
        }else{
            holder.setText(R.id.tv_select_all, "Select all")
        }

        // 从接口中拿数据
        val selectIntArray: MutableList<Int> = ArrayList()
        var i = 0
        for (municipalityItemFilter in item.list) {
            if (municipalityItemFilter.isSelect==true){
                selectIntArray.add(i)
            }
            i++
        }
        labels.setSelects(selectIntArray)

        holder.setText(R.id.tv_city_name, item.title)
        labels.let {
            it.setOnLabelClickListener { label, data, position ->
                item.list[position].isSelect = label.isSelected
            }
        }
    }


}