package com.housesigma.android.ui.search

import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.Editable
import android.text.Html
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.widget.LinearLayout
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.AbsSuperApplication.finishActivityByPosition
import com.housesigma.android.HSApp
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivitySearchBinding
import com.housesigma.android.databinding.HeaderSearchHistoryBinding
import com.housesigma.android.helper.JumpHelper
import com.housesigma.android.model.*
import com.housesigma.android.network.NetClient
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.GsonUtils
import com.housesigma.android.utils.HSHtmlImageGetter
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.views.DisclaimerViewHelper
import com.housesigma.android.views.HSDecoration
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.lang.reflect.Type


class SearchActivity : BaseActivity() {

    private lateinit var searchBinding: ActivitySearchBinding
    private lateinit var searchViewModel: SearchViewModel
    private val mHandler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
        }
    }
    private var saveSearchList: ArrayList<String> = ArrayList()
    private var name: String = ""
    private var isSale: Boolean = true //是否是卖，有两种，一种租，一种买卖
    private lateinit var searchHistoryAdapter: SearchHistoryAdapter

    override fun onResume() {
        super.onResume()
        GALog.page("search")
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        when (event.type) {
            // tos更新、ReLogin、修改密码 的情况
            MessageType.RELOAD_PAGE_TOS_UPDATED, MessageType.RELOAD_WEB_VIEW, MessageType.PASSWORD_CHANGE, -> {
                runOnUiThread {
                    if (!TextUtils.isEmpty(name)){
                        searchViewModel.getSearchAddress(name)
                    }
                }
            }
            else -> {}
        }
    }

    override fun onStart() {
        super.onStart()
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }


    override fun getLayout(): Any {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        searchBinding = ActivitySearchBinding.inflate(layoutInflater)
        return searchBinding.root
    }

    override fun initView() {
        isSale = intent.getBooleanExtra("is_sale", true)
        initViews()
    }

    override fun initData() {
        val searchList = MMKVUtils.getStr("search_list") ?: "[]"
        val type: Type = object : TypeToken<ArrayList<String?>?>() {}.type
        val list: List<String> = Gson().fromJson(searchList, type)
        saveSearchList.clear()
        saveSearchList.addAll(list)
//        Logger.d( "get cache search list : $saveSearchList")
        searchHistoryAdapter = initSearchHistoryAdapter()
        searchHistoryAdapter.data = saveSearchList
        searchHistoryAdapter.notifyDataSetChanged()
    }

    /**
     * 保存搜索历史
     */
    private fun saveSearchList() {
        if (!TextUtils.isEmpty(name)) {
            if (saveSearchList.indexOf(name) == -1) {
                saveSearchList.add(0, name)
            } else {
                if (saveSearchList.size > 1) {
                    saveSearchList.remove(name)
                    saveSearchList.add(0, name)
                }
            }
            val json = Gson().toJson(saveSearchList)
            MMKVUtils.saveStr("search_list", json)
            searchHistoryAdapter.notifyDataSetChanged()
        }
    }

    /*
    清空搜索历史
     */
    private fun clearSearchList() {
        saveSearchList.clear()
        MMKVUtils.removeData("search_list")
    }

    private fun initViews() {
        searchBinding.etSearchTerm.isFocusable = true
        searchBinding.etSearchTerm.requestFocus()
        searchBinding.ivDel.setOnClickListener {
            searchBinding.etSearchTerm.setText("")
            searchBinding.ivDel.visibility = View.GONE
            searchBinding.rlEmpty.visibility = View.VISIBLE
            searchBinding.tvListings.visibility = View.GONE
            searchBinding.tvCommunity.visibility = View.GONE
            searchBinding.tvOpenInMap.visibility = View.GONE
            searchBinding.tvPreconstruction.visibility = View.GONE
        }

        searchBinding.ivClose.setOnClickListener {
            finish()
        }

        searchBinding.etSearchTerm.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(
                text: CharSequence?,
                start: Int,
                lengthBefore: Int,
                lengthAfter: Int
            ) {
                Log.e(
                    "hkj",
                    "onTextChanged:$text lengthAfter:$lengthAfter lengthBefore:$lengthBefore"
                )
                if (text.toString().length <= 2) {
                    searchBinding.rlEmpty.visibility = View.VISIBLE
                    if (text.toString().length == 0) {
                        searchBinding.ivDel.visibility = View.GONE
                    }
                } else {
                    searchBinding.rlEmpty.visibility = View.GONE
                    searchBinding.ivDel.visibility = View.VISIBLE
                    searchBinding.tvListings.visibility = View.VISIBLE
                    searchBinding.tvCommunity.visibility = View.VISIBLE
                    searchBinding.tvOpenInMap.visibility = View.VISIBLE
//                    searchBinding.tvPreconstruction.visibility = View.VISIBLE
                    val function: () -> Unit = {
                        name = text.toString()
                        searchViewModel.getSearchAddress(name)
                    }
                    NetClient.cancelCallWithTag("suggestv3")
                    mHandler.removeCallbacksAndMessages(null)
                    mHandler.postDelayed(Runnable(function), 250)
                }

            }

            override fun afterTextChanged(p0: Editable?) {

            }

        })
        searchViewModel = ViewModelProvider(this).get(SearchViewModel::class.java)

        val searchHouseAdapter = initSearchListingsAdapter()
        val searchCommunityAdapter = initSearchCommunityAdapter()
        val searchLocationsAdapter = initSearchLocationsAdapter()
        val searchPreconAdapter = initSearchPreconAdapter()

// 无结果的时候应该显示空 rv应该有个empty状态的view
        searchViewModel.searchAddress.observe(this) {

//            searchBinding.rlEmpty.visibility = View.GONE
            handleData(searchHouseAdapter, it, searchCommunityAdapter, searchLocationsAdapter,searchPreconAdapter)
        }


        DisclaimerViewHelper.handleDisclaimer(this,searchBinding.tvDisclaimer)
    }

    private fun handleData(
        searchHouseAdapter: SearchListingsAdapter,
        it: SearchAddress,
        searchCommunityAdapter: SearchCommunityAdapter,
        searchLocationsAdapter: SearchLocationsAdapter,
        searchPreconAdapter: SearchPreconAdapter
    ) {
        searchBinding.tvListings.visibility = View.VISIBLE
        searchBinding.tvCommunity.visibility = View.VISIBLE
        searchBinding.tvOpenInMap.visibility = View.VISIBLE


        val allPreconList = it.precon_list.toMutableList()
        handleEmptyPreconView(allPreconList)

        if (allPreconList.size>1) {
            searchPreconAdapter.data = (it.precon_list as MutableList<Precon>).subList(0,1)
            searchBinding.llListViewShowMoreMunicipalities.visibility = View.VISIBLE
            searchBinding.tvListViewShowMoreMunicipalities.text = "Show More"
            searchBinding.tvListViewShowMoreMunicipalities.setCompoundDrawablesWithIntrinsicBounds(resources.getDrawable(R.drawable.ic_map_list_view_show_more_arrow_down),null, null, null)
            searchBinding.llListViewShowMoreMunicipalities.setOnClickListener {
                val showMoreStatusStr = searchBinding.tvListViewShowMoreMunicipalities.text
                if ("Show More".equals(showMoreStatusStr)){
                    searchPreconAdapter.data = allPreconList
                    searchBinding.tvListViewShowMoreMunicipalities.text = "Show Less"
                    searchBinding.tvListViewShowMoreMunicipalities.setCompoundDrawablesWithIntrinsicBounds(resources.getDrawable(R.drawable.ic_map_list_view_show_more_arrow_up),null, null, null)
                } else {
                    searchPreconAdapter.data = allPreconList.subList(0,1)
                    searchBinding.tvListViewShowMoreMunicipalities.text = "Show More"
                    searchBinding.tvListViewShowMoreMunicipalities.setCompoundDrawablesWithIntrinsicBounds(resources.getDrawable(R.drawable.ic_map_list_view_show_more_arrow_down),null, null, null)
                }
                searchPreconAdapter.notifyDataSetChanged()
            }
        }else{
            searchPreconAdapter.data = allPreconList
            searchBinding.llListViewShowMoreMunicipalities.visibility = View.GONE
        }


        searchPreconAdapter.notifyDataSetChanged()

        searchHouseAdapter.data = it.house_list as MutableList<House>
        searchHouseAdapter.notifyDataSetChanged()

        searchCommunityAdapter.data = (it.community_list) as MutableList<Community>
        searchCommunityAdapter.notifyDataSetChanged()


        val locations = ArrayList<City>()
        locations.clear()
        if (it.city_list.size > 0) {
            for (city in it.city_list) {
                val locationItem = City(
                    location_type = 0, address = city.address,
                    location = city.location, id_municipality = city.id_municipality
                )
                locations.add(locationItem)
            }
        }


        if (it.community_suggest.size > 0) {
            val community = it.community_suggest.first()
            val communityName = community.community_name ?: "-"
            val municipalityName = community.municipality_name ?: "-"
            val provinceAbbr = community.province_abbr ?: "-"
            val coordinate = community.coordinate
            val idMunicipality = community.id_municipality ?:""
            val idCommunity  = community.id_community ?:""
            if (coordinate != null) {
                locations.add(
                    City(
                        location_type = 1,
                        address = communityName + ", "
                                + municipalityName + ", " + provinceAbbr,
                        location = Location(coordinate.lat, coordinate.lon),
                        id_municipality = idMunicipality,
                        id_community = idCommunity
                    )
                )
            }
        }



        if (it.place_list.size > 0) {
            val place = it.place_list.first()
            if (place.lat != null) {
                if (place?.lat != 0.0) {
                    locations.add(
                        City(
                            location_type = 2,
                            address = place.text,
                            location = Location(place.lat, place.lng),
                            id_municipality = place.id_municipality?:""
                        )
                    )
                }
            }

        }
        searchLocationsAdapter.data = locations
        searchLocationsAdapter.notifyDataSetChanged()
    }

    /**
     * precon如果为空，则不显示precon所有的内容
     */
    private fun handleEmptyPreconView(allPreconList: MutableList<Precon>) {
        if (allPreconList.size == 0) {
            searchBinding.tvPreconstruction.visibility = View.GONE
            searchBinding.rvPreconstruction.visibility = View.GONE
            searchBinding.viewPreconLine.visibility = View.GONE
        } else {
            searchBinding.tvPreconstruction.visibility = View.VISIBLE
            searchBinding.rvPreconstruction.visibility = View.VISIBLE
            searchBinding.viewPreconLine.visibility = View.VISIBLE
        }
    }

    private fun initSearchLocationsAdapter(): SearchLocationsAdapter {
        val searchOpenInMapAdapter = SearchLocationsAdapter()
        val divider = HSDecoration(this, DividerItemDecoration.VERTICAL, false,true)
        searchBinding.rvOpenInMap.addItemDecoration(divider)
        searchBinding.rvOpenInMap.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        searchBinding.rvOpenInMap.adapter = searchOpenInMapAdapter
        searchOpenInMapAdapter.setEmptyView(R.layout.empty_view_locations)

        searchOpenInMapAdapter.addChildClickViewIds(
            R.id.ll
        )
        searchOpenInMapAdapter.setOnItemChildClickListener { adapter, view, position ->
            when (view.id) {
                R.id.ll -> {
                    GALog.log("address_result_click", "search")
                    saveSearchList()
                    val item = adapter.getItem(position) as City

//                    0是city，1是Community，2是address
                    var zoom = 14.0
                    if (item.location_type == 0) {
                        zoom = 12.0
                    } else if (item.location_type == 1) {
                        zoom = 14.0
                    } else if (item.location_type == 2) {
                        zoom = 14.0
                    }


                    try {
                        val activityNameByPosition = (application as HSApp).getActivityNameByPosition(1)
                        if (activityNameByPosition=="com.housesigma.android.ui.map.MapActivity"){
                            finishActivityByPosition(1)
                        }
                    } catch (exception: Exception) {
                        exception.printStackTrace()
                    }


                    val mapType = if(isSale) arrayListOf("for-sale") else arrayListOf("for-lease")
                    SearchActivity@ this.let {
                        JumpHelper.jumpMapActivityFromSearch(
                            it,
                            item.location.lat,
                            item.location.lon,
                            zoom = zoom,
                            map_type = mapType,
                            municipality = item.id_municipality?:"",
                            id_community = item.id_community
                        )
                    }
                }
            }
        }
        return searchOpenInMapAdapter
    }

    private fun initSearchCommunityAdapter(): SearchCommunityAdapter {
        val searchCommunityAdapter = SearchCommunityAdapter()
        val divider = HSDecoration(this, DividerItemDecoration.VERTICAL, false,true)
        searchBinding.rvCommunity.addItemDecoration(divider)
        searchBinding.rvCommunity.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        searchBinding.rvCommunity.adapter = searchCommunityAdapter

        searchCommunityAdapter.setEmptyView(R.layout.empty_view_community)

        searchCommunityAdapter.addChildClickViewIds(
            R.id.ll
        )
        searchCommunityAdapter.setOnItemChildClickListener { adapter, view, position ->
            when (view.id) {
                R.id.ll -> {
                    GALog.log("community_result_click", "search")
                    saveSearchList()
                    val item = adapter.getItem(position) as Community
                    SearchActivity@ this.let {
                        WebViewHelper.jumpMarket(
                            it,
                            municipality = item.id_municipality.toString(),
                            community = item.id_community.toString()
                        )
                    }
                }
            }
        }
        return searchCommunityAdapter
    }

    private fun initSearchPreconAdapter(): SearchPreconAdapter {
        val searchPreconAdapter = SearchPreconAdapter()
        val divider = HSDecoration(this, DividerItemDecoration.VERTICAL, false)
        searchBinding.rvPreconstruction.addItemDecoration(divider)
        searchBinding.rvPreconstruction.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        searchBinding.rvPreconstruction.adapter = searchPreconAdapter
        searchPreconAdapter.setOnItemChildClickListener { adapter, view, position ->
            when (view.id) {
                R.id.ll -> {
                    GALog.log("preview_click", "precon_search")
                    saveSearchList()
                    val item = adapter.getItem(position) as Precon
                    SearchActivity@ this.let {
                        WebViewHelper.jumpPreconDetail(
                            it,
                            item.id_project,
                        )
                    }
                }
            }
        }
        return searchPreconAdapter
    }

    private fun initSearchListingsAdapter(): SearchListingsAdapter {
        val searchHouseAdapter = SearchListingsAdapter()
        val divider = HSDecoration(this, DividerItemDecoration.VERTICAL, false,true)
        searchBinding.rvListings.addItemDecoration(divider)

        searchBinding.rvListings.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        searchBinding.rvListings.adapter = searchHouseAdapter
        searchHouseAdapter.setEmptyView(R.layout.empty_view_listings)



        searchHouseAdapter.addChildClickViewIds(
            R.id.tv_login_required,
            R.id.tv_agreement_required,
            R.id.ll
        )
        searchHouseAdapter.setOnItemChildClickListener { adapter, view, position ->

            when (view.id) {
                R.id.ll -> {
                    GALog.log("preview_click", "search")
                    saveSearchList()
                    val item = adapter.getItem(position) as House
                    SearchActivity@ this.let {
                        WebViewHelper.jumpHouseDetail(
                            it,
                            item.id_listing,
                            item.seo_suffix
                        )
                        EventBus.getDefault().postSticky(MessageEvent(MessageType.REMOVE_COMMUNITY_BOUNDARIES))
                    }
                }
            }
        }
        return searchHouseAdapter
    }

    private fun initSearchHistoryAdapter(): SearchHistoryAdapter {
        val searchHistoryAdapter = SearchHistoryAdapter()
        val divider = HSDecoration(this, DividerItemDecoration.VERTICAL, false,false)
        searchBinding.rvSearchHistory.addItemDecoration(divider)
        searchBinding.rvSearchHistory.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        searchBinding.rvSearchHistory.adapter = searchHistoryAdapter
        searchHistoryAdapter.setEmptyView(R.layout.empty_search_history)

        val headerBinding = HeaderSearchHistoryBinding.inflate(layoutInflater)
        searchHistoryAdapter.addHeaderView(
            headerBinding.root,
            orientation = LinearLayout.VERTICAL
        )

        headerBinding.tvClearSearch.setOnClickListener {
            clearSearchList()
            searchHistoryAdapter.notifyDataSetChanged()
        }

        searchHistoryAdapter.addChildClickViewIds(
            R.id.ll
        )
        searchHistoryAdapter.setOnItemChildClickListener { adapter, view, position ->
            var searchName = adapter.data[position] as String
            when (view.id) {
                R.id.ll -> {
                    searchBinding.etSearchTerm.setText(searchName)
                    searchBinding.etSearchTerm.setSelection(searchName.length)
                }
            }
        }
        return searchHistoryAdapter
    }


}