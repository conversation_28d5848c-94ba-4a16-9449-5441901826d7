package com.housesigma.android.ui.search

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.Community


class SearchCommunityAdapter() :
    BaseQuickAdapter<Community, BaseViewHolder>(R.layout.item_search_communitys) {

    override fun convert(holder: BaseViewHolder, item: Community) {
        holder.setText(R.id.tv_community_name, item.community_name)
        holder.setText(R.id.tv_municipality_name, item.municipality_name)
    }

}