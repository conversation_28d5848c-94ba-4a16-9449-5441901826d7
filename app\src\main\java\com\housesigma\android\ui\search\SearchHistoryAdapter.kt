package com.housesigma.android.ui.search

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R


class SearchHistoryAdapter() :
    BaseQuickAdapter<String, BaseViewHolder>(R.layout.item_search_history) {

    override fun convert(holder: BaseViewHolder, item: String) {
        holder.setText(R.id.tv_history_name, item)
    }

}