package com.housesigma.android.ui.search

import android.graphics.Color
import android.graphics.Paint
import android.text.TextUtils
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.House
import com.housesigma.android.model.listingOnSearchStatusTypeV2
import com.housesigma.android.utils.ScreenUtils
import jp.wasabeef.glide.transformations.RoundedCornersTransformation


class SearchListingsAdapter :
    BaseQuickAdapter<House, BaseViewHolder>(R.layout.item_search_listings) {


    override fun convert(holder: BaseViewHolder, item: House) {
        val topCorner = ScreenUtils.dpToPx(6f)
        val multi = MultiTransformation(
            CenterCrop(),
            RoundedCornersTransformation(
                topCorner.toInt(),
                0,
                RoundedCornersTransformation.CornerType.ALL
            )
        )

        holder.setText(R.id.tv_price, "$${item.getPriceText()}")
        var municipalityName = item.municipality_name
        municipalityName = municipalityName.plus(
            if (!TextUtils.isEmpty(item.community_name))
                " - ${item.community_name}"
            else ""
        )
        holder.setText(R.id.tv_address, item.address)
        holder.setText(R.id.tv_municipality_name, municipalityName)


        holder.setText(R.id.tv_type_text, item.list_status.text)

        holder.setText(
            R.id.tv_date_times,
            (if (TextUtils.isEmpty(item.date_preview)) "" else item.date_preview + ", ") + (item.ml_count_text)
        )
        holder.setText(R.id.tv_house_type_name, item.type_text)


        if (item.bedroom_string==null) {
            holder.setGone(R.id.tv_bedroom_string, true)
        } else {
            holder.setVisible(R.id.tv_bedroom_string, true)
            holder.setText(R.id.tv_bedroom_string, item.bedroom_string)
        }

        if (item.washroom==null) {
            holder.setGone(R.id.tv_washroom, true)
        } else {
            holder.setVisible(R.id.tv_washroom, true)
            holder.setText(R.id.tv_washroom, item.washroom)
        }

        if (item.garage==null) {
            holder.setGone(R.id.tv_garage, true)
        } else {
            holder.setVisible(R.id.tv_garage, true)
            holder.setText(R.id.tv_garage, item.garage)
        }


        Glide.with(context)
            .load(item.photo_url)
            .transform(multi)
            .error(R.drawable.shape_pic_place_holder)
            .placeholder(R.drawable.shape_pic_place_holder)
            .into(holder.getView(R.id.iv_house_pic))
        var tvPrice = holder.getView<TextView>(R.id.tv_price)
        setPriceTextStyle(tvPrice, item)
    }


    /**
    按照岳说的逻辑：

    现在不需要判断已售，所以基本上live 的使用场景就只有
    list_status.live === 1
    显示绿色的挂牌价
    else
    显示灰色的带中划线的挂牌价..
     */
    private fun setPriceTextStyle(tvPrice: TextView, item: House) {
        when (item.statusTypeV2()) {
            listingOnSearchStatusTypeV2.onSaleOrOnRent
            -> {
                tvPrice.setTextColor(Color.parseColor("#28A3B3"))
                tvPrice.paint.flags = 0
                tvPrice.paint.isAntiAlias = true
            }

            listingOnSearchStatusTypeV2.soldOrRend
            -> {
                tvPrice.setTextColor(Color.parseColor("#FF5F05"))
                tvPrice.paint.flags = 0
                tvPrice.paint.isAntiAlias = true
            }

            else -> {
                tvPrice.setTextColor(Color.parseColor("#BBBBBB"))
                tvPrice.paint.flags = (Paint.STRIKE_THRU_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG)//中划线
            }
        }
    }


}