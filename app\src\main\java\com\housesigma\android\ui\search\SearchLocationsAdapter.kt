package com.housesigma.android.ui.search

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.City


class SearchLocationsAdapter() :
    BaseQuickAdapter<City, BaseViewHolder>(R.layout.item_search_locations) {

    override fun convert(holder: BaseViewHolder, item: City) {
        holder.setText(R.id.tv_address, item.address)
        //0是city，1是Community，2是address
        if (item.location_type == 0) {
            holder.setText(R.id.tv_type, "City")
            holder.setBackgroundResource(R.id.iv_type,R.drawable.ic_city)
        } else if (item.location_type == 1) {
            holder.setText(R.id.tv_type, "Community")
            holder.setBackgroundResource(R.id.iv_type,R.drawable.ic_community)
        } else if (item.location_type == 2) {
            holder.setText(R.id.tv_type, "Address")
            holder.setBackgroundResource(R.id.iv_type,R.drawable.ic_address)
        }
    }

}