package com.housesigma.android.ui.search

import android.graphics.Color
import android.graphics.Paint
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.House
import com.housesigma.android.model.Precon
import com.housesigma.android.model.listingOnSearchStatusTypeV2
import com.housesigma.android.utils.ScreenUtils
import jp.wasabeef.glide.transformations.RoundedCornersTransformation


class SearchPreconAdapter :
    BaseQuickAdapter<Precon, BaseViewHolder>(R.layout.item_search_precon) {


    init {
        addChildClickViewIds(
            R.id.ll
        )
    }
    override fun convert(holder: BaseViewHolder, item: Precon) {
        val topCorner = ScreenUtils.dpToPx(6f)
        val multi = MultiTransformation(
            CenterCrop(),
            RoundedCornersTransformation(
                topCorner.toInt(),
                0,
                RoundedCornersTransformation.CornerType.ALL
            )
        )
        holder.setText(R.id.tv_project_name, item.project_name?:"")
        if (item.price == null) {
            holder.setGone(R.id.ll_price, true)
        } else {
            holder.setVisible(R.id.ll_price, true)
            holder.setText(R.id.tv_price, "$${item.price}")
        }
        holder.setText(R.id.tv_house_type_name, item.property_type_text?:"")
        holder.setText(R.id.tv_completion_date, item.completion_date?:"")
        holder.setText(R.id.tv_address, item.addr?:"")

        if (TextUtils.isEmpty(item.sqft_text)) {
            holder.setGone(R.id.tv_sqft, true)
        } else {
            holder.setVisible(R.id.tv_sqft, true)
            holder.setText(R.id.tv_sqft, item.sqft_text?:"")
        }

        if (TextUtils.isEmpty(item.bedroom_text)) {
            holder.setGone(R.id.tv_bedroom_string, true)
        } else {
            holder.setVisible(R.id.tv_bedroom_string, true)
            holder.setText(R.id.tv_bedroom_string, item.bedroom_text?:"")
        }


        Glide.with(context)
            .load(item.photo_url)
            .transform(multi)
            .error(R.drawable.shape_pic_place_holder)
            .placeholder(R.drawable.shape_pic_place_holder)
            .into(holder.getView(R.id.iv_house_pic))
    }

}