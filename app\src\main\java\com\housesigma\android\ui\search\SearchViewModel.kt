package com.housesigma.android.ui.search

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.housesigma.android.model.SearchAddress
import com.housesigma.android.network.NetClient
import com.housesigma.android.network.launch
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSLog
import org.checkerframework.checker.units.qual.A

class SearchViewModel : ViewModel() {

    var searchAddress: MutableLiveData<SearchAddress> = MutableLiveData()

    fun getSearchAddress(str: String) {
        launch({
            NetClient.apiService.getSearchAddress( str)
        }, {
            searchAddress.postValue(it)

            val map = HashMap<String, Any>()
            map["search_terms"] = str
            HSLog.userInput(eventName = "user_input_search_bar",map)
        })
    }

}