package com.housesigma.android.ui.signup

import android.content.Intent
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.tasks.Task
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.BuildConfig
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivitySignupBinding
import com.housesigma.android.hybrid.HybridUtils
import com.housesigma.android.model.CountrycodeX
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.User
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.onetap.OneTapActivity
import com.housesigma.android.utils.*
import com.housesigma.android.utils.log.Logger
import com.lxj.xpopup.XPopup
import org.greenrobot.eventbus.EventBus


class SignUpActivity : BaseActivity(), LoginFragment.LoginCallback{

    private lateinit var binding: ActivitySignupBinding
    private lateinit var signupModel: SignUpModel
    private var strList = ArrayList<String>()
    private var countrycodeXList = ArrayList<CountrycodeX>()

    private var loginDialog: LoginFragment? = null

    private var phone: String? = null
    private var password: String? = null
    private var name: String? = null
    private var countryCode: String? = null
    private var email: String? = null
    private val RC_SIGN_IN = 1
    private var idToken: String? = ""

    override fun onResume() {
        super.onResume()
        GALog.page("sign_up_step1")
    }

    override fun getLayout(): Any {
        AppManager.getManager().addActivity(this)
        binding = ActivitySignupBinding.inflate(layoutInflater)
        signupModel = ViewModelProvider(this).get(SignUpModel::class.java)
        return binding.root
    }

    override fun onDestroy() {
        super.onDestroy()
        AppManager.getManager().finishActivity(this)
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        initViews()
    }

    override fun initData() {

        loadData()
    }

    private fun loadData() {
        signupModel.searchAddress.observe(this) { list ->
            countrycodeXList.addAll(list.countrycode)
            list.countrycode.forEach {
                strList.add(it.name)
            }
            list.countrycode.getOrNull(0)?.let {
                binding.tvCountryCode.text = it.countrycode
            }
        }
        signupModel.loadingLiveData.observe(this) {
            dismissLoadingDialog()
        }

        showLoadingDialog()
        signupModel.getInitCountryCode()
    }

    private fun initViews() {
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestIdToken(BuildConfig.ONE_TAP_WEB_CLIENT_ID)
            .requestEmail()
            .build()
        // Build a GoogleSignInClient with the options specified by gso.
        val mGoogleSignInClient = GoogleSignIn.getClient(this, gso)

        binding.ivClose.setOnClickListener {
            finish()
        }

        binding.tvCountryCode.setOnClickListener {
            XPopup.Builder(this)
                .asBottomList(
                    "", strList.toTypedArray()
                ) { position, text ->
                    binding.tvCountryCode.text = countrycodeXList[position].countrycode
                }
                .show()
        }

        binding.tvSignIn.setOnClickListener {
            GALog.log("login_button_click")
            showLoginDialog()
        }
        binding.llEmail.setOnClickListener {

            binding.vLineEmail.setBackgroundResource(R.color.app_main_color)
            binding.vLinePhone.setBackgroundResource(R.color.color_cccccc)
            binding.tvEmail.setTextColor(resources.getColor(R.color.app_main_color))
            binding.tvPhone.setTextColor(resources.getColor(R.color.color_black))

            binding.etEmail.visibility = View.VISIBLE
            binding.llInputPhone.visibility = View.GONE
        }

        binding.rlSignInGoogle.setOnClickListener {
            GALog.log("third_party_auth_start","onetap")
            val signInIntent = mGoogleSignInClient.signInIntent
            startActivityForResult(signInIntent, RC_SIGN_IN)
        }

        binding.llPhone.setOnClickListener {
            binding.vLinePhone.setBackgroundResource(R.color.app_main_color)
            binding.vLineEmail.setBackgroundResource(R.color.color_cccccc)
            binding.tvEmail.setTextColor(resources.getColor(R.color.color_black))
            binding.tvPhone.setTextColor(resources.getColor(R.color.app_main_color))

            binding.etEmail.visibility = View.GONE
            binding.llInputPhone.visibility = View.VISIBLE
        }

        signupModel.msgRes.observe(this) {
            // TODO 这里可能需要自行维护一个activity栈来记录便于关闭打开的activity
            var intent = Intent(this@SignUpActivity, SignUpTermsOfUseActivity::class.java)
            intent.putExtra("phone", phone)
            intent.putExtra("password", password)
            intent.putExtra("name", name)
            intent.putExtra("countryCode", countryCode)
            intent.putExtra("email", email)
            startActivity(intent)
//            finish()
        }

        signupModel.googleSignRes.observe(this) {
            if (it.registered) {
//                loginSuccess(it.user, "onetap")

                GALog.log("login_success", "onetap")

                val user = HybridUtils.saveAndParseUserData(it.hybridUser)
                it.appUser = user?: User()

                LoginFragment.saveUserInfo(it.appUser)

                EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_MARKET))
                EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_HOME))
                EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_FCM_TOKEN))
                EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_WEB_VIEW))
                finish()
            } else {
                // 这里不用user对象传递是存在一个问题：
                // User对象Parcelize化之后，有些字段一旦为空的时候
                // Parcelize化的时候，就会报空指针异常，需要检查全局代码有没有类似的问题(已检查过)
                val intent = Intent(this, OneTapActivity::class.java)
                intent.putExtra("email", it.appUser.email)
                intent.putExtra("id_token", idToken)
                startActivity(intent)
            }
        }

        signupModel.loadingLiveData.observe(this){
            dismissLoadingDialog()
        }

        binding.tvNext.setOnClickListener {
            showLoadingDialog()
            if (binding.etEmail.visibility == View.VISIBLE) {
//                email login
                GALog.log("registration_step1_submit","email")
                email = binding.etEmail.text.toString().trim()
                password = binding.etPassword.text.toString().trim()
                name = binding.etName.text.toString().trim()

                phone = null
                countryCode = null


                signupModel.sendCode(
                    email = email!!, pass = password!!, name = name!!
                )

            } else {
                GALog.log("registration_step1_submit","phone")
//                phone login
                phone = binding.etPhone.text.toString().trim()
                password = binding.etPassword.text.toString().trim()
                countryCode = binding.tvCountryCode.text.toString().trim()
                name = binding.etName.text.toString().trim()

                email = null

                signupModel.sendCode(
                    phoneNumber = phone!!, pass = password!!, name = name!!, countryCode = countryCode!!
                )
            }


        }

    }

    private fun showLoginDialog() {
        GALog.log("login_button_click")
        if (loginDialog == null) {
            loginDialog = LoginFragment()
        }
        if (loginDialog?.isAdded == true) return
        loginDialog?.show(supportFragmentManager, "")
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            RC_SIGN_IN -> {
                val task = GoogleSignIn.getSignedInAccountFromIntent(data);
                handleSignInResult(task)
            }
        }
    }

    private fun handleSignInResult(completedTask: Task<GoogleSignInAccount>) {
        try {
            val account: GoogleSignInAccount = completedTask.getResult(ApiException::class.java)
            idToken = account.idToken
            signupModel.googleSignIn(credential = account.idToken!!)
            // Signed in successfully, show authenticated UI.
        } catch (e: ApiException) {
            // The ApiException status code indicates the detailed failure reason.
            // Please refer to the GoogleSignInStatusCodes class reference for more information.
            Logger.d( "signInResult:failed code=" + e.statusCode)
        }
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
        finish()
    }
}