package com.housesigma.android.ui.signup

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.housesigma.android.hybrid.HybridUtils
import com.housesigma.android.model.*
import com.housesigma.android.network.NetClient
import com.housesigma.android.network.launch

class SignUpModel : ViewModel() {

    var searchAddress: MutableLiveData<CountryCode> = MutableLiveData()
    var msgRes: MutableLiveData<MsgRes> = MutableLiveData()
    var tempPasswordMsgRes: MutableLiveData<MsgRes> = MutableLiveData()
    var googleSignRes: MutableLiveData<GoogleSignRes> = MutableLiveData()
    var signIn: MutableLiveData<SignIn> = MutableLiveData()
    var agentBoard: MutableLiveData<AgentBoards> = MutableLiveData()
    var tosModel: MutableLiveData<TosModel> = MutableLiveData()
    val loadingLiveData = MutableLiveData<Boolean>()
    var signInFailure = MutableLiveData<Boolean>()
    var signTos: MutableLiveData<MsgRes> = MutableLiveData()
    var refreshSignIn: MutableLiveData<MsgRes> = MutableLiveData()


    fun refreshSignIn(
    ) {
        launch({
            NetClient.apiService.refreshSignIn()
        }, {
            refreshSignIn.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }


    fun agentboard(
    ) {
        launch({
            NetClient.apiService.agentboard()
        }, {
            agentBoard.postValue(it)
        })
    }

    fun signIn(
        email: String = "",
        pass: String = "",
        phoneNumber: String = "",
        countryCode: String = "",
        reLoginType:String = "",
    ) {
        launch({
            var loginType = reLoginType
            if (TextUtils.isEmpty(reLoginType)){
                loginType ="normal"
            }
            NetClient.apiService.signIn(
                email = email, pass = pass,
                phonenumber = phoneNumber, countrycode = countryCode, login_type = loginType
            )
        }, {
            val user = HybridUtils.saveAndParseUserData(it.hybridUser)
            it.appUser = user?:User()
            signIn.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        }, onFailure = {
            signInFailure.postValue(true)
        })
    }


    fun setTos(sources: List<String>) {
        launch({
            NetClient.apiService.setTos(
                sources = sources
            )
        }, {
            signTos.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun getTos() {
        launch({
            NetClient.apiService.getTos()
        }, {
            tosModel.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun googleSignIn(credential: String = "",reLoginType:String = "",) {
        launch({
            var loginType = reLoginType
            if (TextUtils.isEmpty(reLoginType)){
                loginType ="normal"
            }
            NetClient.apiService.googleSignIn(credential = credential, login_type = loginType)
        }, {
            val user = HybridUtils.saveAndParseUserData(it.hybridUser)
            it.appUser = user?:User()
            googleSignRes.postValue(it)
        })
    }

    fun googleSignUp(
        credential: String,
        pass: String,
        is_agent: String,
        referral_code: String = "",
        licensed_province: String = "",
        board_name: String = "",
        brokerage_name: String = "",
        cracking_is_agent: String ?= null,
        cracking_licensed_province: String ?= null,
        cracking_board_name: String ?= null,
        cracking_brokerage_name: String ?= null,
    ) {
        launch({
            var agentCracking : AgentCracking ?= null
            if (cracking_is_agent == "1") {
                agentCracking = AgentCracking(is_agent=cracking_is_agent,
                    licensed_province=cracking_licensed_province?:"",
                    board_name=cracking_board_name?:"",
                    brokerage_name=cracking_brokerage_name?:"")
            }
            val requestGoogleSignup = RequestGoogleSignup(
                credential = credential,
                pass = pass,
                is_agent = is_agent,
                referral_code = referral_code,
                licensed_province = licensed_province,
                board_name = board_name,
                brokerage_name = brokerage_name, agent_cracking = agentCracking
            )
            NetClient.apiService.googleSignUp(requestGoogleSignup)
        }, {
            val user = HybridUtils.saveAndParseUserData(it.hybridUser)
            it.appUser = user?:User()
            signIn.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }


    fun sendCode(
        email: String = "",
        pass: String = "",
        phoneNumber: String = "",
        countryCode: String = "",
        name: String = "",
    ) {
        launch({
            NetClient.apiService.sendCode(
                email = email, pass = pass,
                phonenumber = phoneNumber, countrycode = countryCode,
                name = name
            )
        }, {
            msgRes.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun signup(
        code: String = "",
        email: String = "",
        pass: String = "",
        phoneNumber: String = "",
        countryCode: String = "",
        name: String = "",
        is_agent: String = "0",
        referral_code: String = "",
        licensed_province: String = "",
        board_name: String = "",
        brokerage_name: String = "",
        cracking_is_agent: String ?= null,
        cracking_licensed_province: String?= null,
        cracking_board_name: String ?= null,
        cracking_brokerage_name: String ?= null,
    ) {
        launch({
            NetClient.apiService.signup(
                code = code,
                email = email, pass = pass,
                phonenumber = phoneNumber, countrycode = countryCode,
                name = name, is_agent = is_agent,
                referral_code = referral_code,
                licensed_province = licensed_province,
                board_name = board_name,
                brokerage_name = brokerage_name,
                cracking_is_agent = cracking_is_agent,
                cracking_licensed_province = cracking_licensed_province,
                cracking_board_name = cracking_board_name,
                cracking_brokerage_name = cracking_brokerage_name,
            )
        }, {
            val user = HybridUtils.saveAndParseUserData(it.hybridUser)
            it.appUser = user?:User()
            signIn.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun getInitCountryCode() {
        launch({
            NetClient.apiService.getInitCountryCode()
        }, {
            searchAddress.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun sendTempPassword() {
        launch({
            NetClient.apiService.sendTempPassword()
        }, {
            tempPasswordMsgRes.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

}