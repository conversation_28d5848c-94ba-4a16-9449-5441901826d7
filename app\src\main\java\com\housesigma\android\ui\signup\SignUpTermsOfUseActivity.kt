package com.housesigma.android.ui.signup

import android.content.Intent
import android.text.Html
import android.text.TextUtils
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivitySignupTermsOfUseBinding
import com.housesigma.android.utils.AppManager
import com.housesigma.android.utils.GALog

class SignUpTermsOfUseActivity : BaseActivity() {

    private var phone: String? = null
    private var password: String? = null
    private var name: String? = null
    private var countryCode: String? = null
    private var email: String? = null
    private lateinit var binding: ActivitySignupTermsOfUseBinding
    private lateinit var signupModel: SignUpModel

    override fun getLayout(): Any {
        AppManager.getManager().addActivity(this)
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        phone = intent.getStringExtra("phone")
        password = intent.getStringExtra("password")
        name = intent.getStringExtra("name")
        countryCode = intent.getStringExtra("countryCode")
        email = intent.getStringExtra("email")
        binding = ActivitySignupTermsOfUseBinding.inflate(layoutInflater)
        signupModel = ViewModelProvider(this).get(SignUpModel::class.java)
        return binding.root
    }

    override fun onDestroy() {
        super.onDestroy()
        AppManager.getManager().finishActivity(this)
    }

    override fun onResume() {
        super.onResume()
        GALog.page("sign_up_step2")
    }

    override fun initView() {
        initViews()
    }

    override fun initData() {
        signupModel.getTos()
    }

    private fun initViews() {
        signupModel.tosModel.observe(this) {
            binding.tvTerms.text = Html.fromHtml(
                "<div style='text-align:center'><h5><b><font color=\"#28A3B3\">"+it.hs?.header+"</font></b></h5></div>"  +
                        it.hs?.body
            )

            var termsOthers = ""
            it.dataSource?.forEach {
                termsOthers = termsOthers +"<div style='text-align:center'><h5><b><font color=\"#28A3B3\">"+ it?.header +"</font></b></h5></div>"  + it?.body + "<br><br>"
            }
            binding.tvTerms2.text = Html.fromHtml(
                termsOthers
            )
        }

        signupModel.loadingLiveData.observe(this) {
            dismissLoadingDialog()
        }

        signupModel.signTos.observe(this) {
            if (!TextUtils.isEmpty(email)) {
                GALog.log("registration_step2_submit", "email")
            } else {
                GALog.log("registration_step2_submit", "phone")
            }
            var intent = Intent(
                this@SignUpTermsOfUseActivity,
                SignUpVerifyCodeActivity::class.java
            )
            intent.putExtra("phone", phone)
            intent.putExtra("password", password)
            intent.putExtra("name", name)
            intent.putExtra("countryCode", countryCode)
            intent.putExtra("email", email)
            startActivity(
                intent
            )
//            finish()
        }

        binding.ivClose.setOnClickListener {
            finish()
        }

        binding.tvAccept.setOnClickListener {
            showLoadingDialog()
            val ids = ArrayList<String>()
            ids.clear()
            signupModel.tosModel.value?.dataSource?.forEach {
                it?.id?.let { it1 -> ids.add(it1) }
            }
            signupModel.setTos(ids)
        }

        binding.tvReject.setOnClickListener {
            finish()
        }
    }
}