package com.housesigma.android.ui.signup

import android.text.TextUtils
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivitySignupVerifyCodeBinding
import com.housesigma.android.hybrid.HybridUtils
import com.housesigma.android.model.AgentBoard
import com.housesigma.android.model.AgentBoards
import com.housesigma.android.model.Board
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.User
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.AppManager
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.GsonUtils
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.views.selectlistdialog.SelectListDialog
import com.housesigma.android.views.verifycodelib.VerifyCodeCompleteListener
import com.lxj.xpopup.XPopup
import org.greenrobot.eventbus.EventBus


class SignUpVerifyCodeActivity : BaseActivity() {
    private lateinit var signupModel: SignUpModel
    private lateinit var signupVerifyCodeBinding: ActivitySignupVerifyCodeBinding

    private var code: String = ""
    private var phone: String? = null
    private var password: String? = null
    private var name: String? = null
    private var countryCode: String? = null
    private var email: String? = null
    private var isAgent: String = "0"
    private var agentBoards: AgentBoards? = null

    private var board = ""

    private var brokerageName = ""
    private var licensedProvince = ""
    private var boardName = ""

    override fun getLayout(): Any {
        AppManager.getManager().addActivity(this)
        phone = intent.getStringExtra("phone")
        password = intent.getStringExtra("password")
        name = intent.getStringExtra("name")
        countryCode = intent.getStringExtra("countryCode")
        email = intent.getStringExtra("email")


        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        signupModel = ViewModelProvider(this).get(SignUpModel::class.java)
        signupVerifyCodeBinding = ActivitySignupVerifyCodeBinding.inflate(layoutInflater)
        return signupVerifyCodeBinding.root
    }

    override fun onDestroy() {
        super.onDestroy()
        AppManager.getManager().finishActivity(this)
    }

    override fun onResume() {
        super.onResume()
        GALog.page("sign_up_step3")
    }

    override fun initView() {
        initViews()
    }

    override fun initData() {
        signupModel.agentBoard.observe(this) {
            agentBoards = it
        }

        signupModel.agentboard()
    }

    private fun initViews() {
        signupVerifyCodeBinding.ivClose.setOnClickListener {
            finish()
        }

        signupVerifyCodeBinding.cbIsAgent.setOnCheckedChangeListener { compoundButton, boolean ->
            if (boolean) {
                isAgent = "1"
                signupVerifyCodeBinding.llAgent.visibility = View.VISIBLE
            } else {
                isAgent = "0"
                signupVerifyCodeBinding.llAgent.visibility = View.GONE
            }
        }

        signupVerifyCodeBinding.llProvince.setOnClickListener {
            agentBoards?.let {
                val provinces = it.provinces
                val selectListDialog = SelectListDialog<AgentBoard>(
                    title = "Province",
                    provinces,
                    this,
                    object : SelectListDialog.SelectCallback<AgentBoard> {
                        override fun onSuccess(selectItem: AgentBoard) {
                            licensedProvince = selectItem.province
                            signupVerifyCodeBinding.tvSelectProvince.text = selectItem.province_text

                            board = ""
                            signupVerifyCodeBinding.tvBoardSelectName.text = ""
                        }

                        override fun onCancel() {
                        }
                    })

                XPopup.Builder(this)
                    .asCustom(selectListDialog)
                    .show()
            }
        }

        signupVerifyCodeBinding.llBoardName.setOnClickListener {
            val provinceStr = signupVerifyCodeBinding.tvSelectProvince.text.toString()
            if (!TextUtils.isEmpty(provinceStr)) {
                agentBoards?.let {
                    val provinces = it.provinces
                    for (province in provinces) {
                        if (province.province_text.equals(provinceStr)) {
                            val selectListDialog = SelectListDialog<Board>(
                                title = "Board name",
                                province.boards,
                                this,
                                object : SelectListDialog.SelectCallback<Board> {
                                    override fun onSuccess(selectItem: Board) {
                                        board = selectItem.value
                                        signupVerifyCodeBinding.tvBoardSelectName.text = selectItem.text
                                    }

                                    override fun onCancel() {
                                    }
                                })

                            XPopup.Builder(this)
                                .asCustom(selectListDialog)
                                .show()
                        }
                    }
                }
            }
        }
        if (email.isNullOrEmpty()) {
            signupVerifyCodeBinding.tvSendTo.text = "Sent to $phone"
        } else {
            signupVerifyCodeBinding.tvSendTo.text = "Sent to $email"
        }

        signupModel.signIn.observe(this) { it ->
            val user = HybridUtils.saveAndParseUserData(it.hybridUser)
            it.appUser = user?:User()
            saveAgentInfo()
            loginSuccess(it.appUser)
            EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_MARKET))
//            EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_HOME))//注册是不需要发这个事件的，会走生命周期
            EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_FCM_TOKEN))
            
            //DEV-4474  After sign up, app thinks user is still logged out
            EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_WEB_VIEW))

            AppManager.getManager().finishActivity(SignUpTermsOfUseActivity::class.java)
            AppManager.getManager().finishActivity(SignUpActivity::class.java)
            finish()
        }

        signupModel.loadingLiveData.observe(this){
            dismissLoadingDialog()
        }



        signupVerifyCodeBinding.tvRegister.setOnClickListener {
            showLoadingDialog()

            val referralCode = signupVerifyCodeBinding.etReferralCode.text.toString().trim()

            if (signupVerifyCodeBinding.cbIsAgent.isChecked()) {
                 brokerageName = signupVerifyCodeBinding.etBrokerageName.text.toString().trim()
                 licensedProvince = this.licensedProvince
                 boardName = this.board
            }

            val crackIsAgent = MMKVUtils.getStr(LoginFragment.CRACK_IS_AGENT)
            val crackBoardName = MMKVUtils.getStr(LoginFragment.CRACK_BOARD_NAME)
            val crackBrokerageName = MMKVUtils.getStr(LoginFragment.CRACK_BROKERAGE_NAME )
            val crackLicensedProvince = MMKVUtils.getStr(LoginFragment.CRACK_LICENSED_PROVINCE)

            if (TextUtils.isEmpty(email)) {
                GALog.log("registration_step3_submit", "phone")
                // phone register
                signupModel.signup(
                    code = code,
                    phoneNumber = phone!!,
                    pass = password!!,
                    name = name!!,
                    countryCode = countryCode!!,
                    is_agent = isAgent,
                    referral_code = referralCode,
                    licensed_province = licensedProvince,
                    board_name = boardName,
                    brokerage_name = brokerageName,
                    cracking_is_agent = crackIsAgent,
                    cracking_board_name = crackBoardName,
                    cracking_brokerage_name = crackBrokerageName,
                    cracking_licensed_province = crackLicensedProvince,
                )
            } else {
                GALog.log("registration_step3_submit", "email")
                // email register
                signupModel.signup(
                    code = code,
                    email = email!!, pass = password!!, name = name!!,
                    is_agent = isAgent,
                    referral_code = referralCode,
                    licensed_province = licensedProvince,
                    board_name = boardName,
                    brokerage_name = brokerageName,
                    cracking_is_agent = crackIsAgent,
                    cracking_board_name = crackBoardName,
                    cracking_brokerage_name = crackBrokerageName,
                    cracking_licensed_province = crackLicensedProvince,
                )
            }
        }
        signupVerifyCodeBinding.verifyCodeView.setCompleteListener(object :
            VerifyCodeCompleteListener {
            override fun verifyCodeComplete() {
                code = signupVerifyCodeBinding.verifyCodeView.getText()
            }

        })

        signupVerifyCodeBinding.ivDel.setOnClickListener {
            signupVerifyCodeBinding.etReferralCode.setText("")
        }

    }

    private fun saveAgentInfo() {
        if (isAgent == "1") {
            MMKVUtils.saveStr(LoginFragment.CRACK_IS_AGENT, "1")
            MMKVUtils.saveStr(LoginFragment.CRACK_BROKERAGE_NAME, brokerageName?:"")
            MMKVUtils.saveStr(LoginFragment.CRACK_BOARD_NAME, boardName?:"")
            MMKVUtils.saveStr(LoginFragment.CRACK_LICENSED_PROVINCE, licensedProvince?:"")
        }
    }

    private fun loginSuccess(it: User) {
        if (!TextUtils.isEmpty(email)) {
            if (!TextUtils.isEmpty(it.referral_code)) {
                GALog.log("registration_success", "email","referral")
            } else {
                GALog.log("registration_success", "email")
            }
            MMKVUtils.saveStr(LoginFragment.SIGN_IN_VIA, "email")
            MMKVUtils.saveStr(LoginFragment.SIGN_IN_EMAIL, email)
        }else{
            if (!TextUtils.isEmpty(it.referral_code)) {
                GALog.log("registration_success", "phone","referral")
            } else {
                GALog.log("registration_success", "phone")
            }
            MMKVUtils.saveStr(LoginFragment.SIGN_IN_VIA, "phone")
            MMKVUtils.saveStr(LoginFragment.SIGN_IN_PHONE, phone)
        }

        LoginFragment.saveUserInfo(it)
    }

}