package com.housesigma.android.ui.splash

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.text.TextUtils
import android.util.Log
import androidx.lifecycle.ViewModelProvider
import com.google.firebase.analytics.FirebaseAnalytics
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.BuildConfig
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.ui.home.HomeViewModel
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.main.MainActivity
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.LanguageUtils
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.log.Logger
import me.jessyan.autosize.AutoSizeConfig


@SuppressLint("CustomSplashScreen")
class SplashActivity : BaseActivity() {

    private lateinit var countDownTimer: CountDownTimer
    private lateinit var homeViewModel: HomeViewModel
    private var appLinkData: Uri?= null
    private var countDownInterval: Long = 1000

    override fun getLayout(): Any {
        if (HSUtil.isPad(this)) {
            AutoSizeConfig.getInstance().stop(this)
        }
        return R.layout.activity_splash
    }

    override fun initView() {
        exitAppIfCpuABIContainsX86()
        LanguageUtils().setAppLanguage(this)
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
            statusBarColor(R.color.color_white)
            statusBarDarkFont(true)
        }

        FirebaseAnalytics.getInstance(this).setAnalyticsCollectionEnabled(true)
    }


    private fun exitAppIfCpuABIContainsX86() {
        try {
            // 某些阉割过的设备此处可能有异常，所以要捕获下异常
            if (Build.CPU_ABI.contains("x86")) {
                finish()
            }
        } catch (e:Exception){
            e.printStackTrace()
        }
    }

    override fun initData() {
        handleAppLinks()
        homeViewModel = ViewModelProvider(this).get(HomeViewModel::class.java)
        homeViewModel.token.observe(this) {
            Logger.d("token : " + it.access_token)
            MMKVUtils.saveStr(LoginFragment.LOGIN_TOKEN, it.access_token)
            countDownTimer.start()
        }
        countDownTimer = object : CountDownTimer(countDownInterval, countDownInterval) {
            override fun onFinish() {
                val intentSplash = Intent(this@SplashActivity, MainActivity::class.java)
                appLinkData?.let {
                    val bundle = Bundle()
                    bundle.putString("type", "applinks")
                    bundle.putString("uri", appLinkData.toString())
                    intentSplash.putExtra("data", bundle)
                }
                intentSplash.putExtras(intent)
                startActivity(intentSplash)
                finish()
            }

            override fun onTick(millisUntilFinished: Long) {
            }
        }
        if (TextUtils.isEmpty(MMKVUtils.getStr(LoginFragment.LOGIN_TOKEN))) {
            homeViewModel.getAccessToken()
        } else {
            countDownTimer.start()
        }
    }

    private fun handleAppLinks() {
        // ATTENTION: This was auto-generated to handle app links.
        val appLinkIntent = intent
        val appLinkAction = appLinkIntent.action
        appLinkData = appLinkIntent.data
        Logger.e("[applinks] $appLinkAction $appLinkData")
//        if ("android.intent.action.VIEW" != appLinkAction || appLinkData == null) {
//            return
//        }
        if (appLinkData!=null) {
            countDownInterval = 1
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownTimer.cancel()
    }


}