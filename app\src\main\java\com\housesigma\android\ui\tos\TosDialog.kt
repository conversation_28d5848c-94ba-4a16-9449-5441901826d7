package com.housesigma.android.ui.tos

import android.app.Dialog
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Html
import android.view.Window
import android.view.WindowManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import com.google.gson.Gson
import com.housesigma.android.databinding.DialogTosBinding
import com.housesigma.android.model.InitApp
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.MMKVUtils
import org.greenrobot.eventbus.EventBus


class TosDialog(
    viewModelStoreOwner: ViewModelStoreOwner,
    lifecycle: LifecycleOwner,
    context: Context,
    tosSource: String,
    cb: TosCallback
) : Dialog(context) {

    interface TosCallback {
        fun onSuccess()
    }

    private var mCallback: TosCallback = cb
    private var tosSource = tosSource

    private var mContext: ViewModelStoreOwner = viewModelStoreOwner
    private var mLifecycle: LifecycleOwner = lifecycle
    private lateinit var tosViewModel: TosViewModel
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        GALog.log("agreement_start")
        this.window?.requestFeature(Window.FEATURE_NO_TITLE)
        val binding = DialogTosBinding.inflate(layoutInflater)
        setContentView(binding.root)
        tosViewModel = ViewModelProvider(mContext).get(TosViewModel::class.java)
        initViews(binding)
        tosViewModel.tosMsg.observe(mLifecycle) {
            EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_PAGE_TOS_UPDATED))
            mCallback.onSuccess()
            dismiss()
        }
        this.window?.setBackgroundDrawable(ColorDrawable(0x00000000))
        this.window?.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT)
    }

    private fun initViews(binding: DialogTosBinding) {
        val initApp = HSUtil.getInitApp()

        try {
            initApp?.let {
                // 这里是从服务端拿的header和body，不确定是否真正返回了tos内容所以需要try catch
                binding.tvTitle.text = initApp.tos[tosSource]?.term_text?.header
                binding.tvContent.text =
                    Html.fromHtml(initApp.tos[tosSource]?.term_text?.body)
            }
        } catch (exception: Exception) {
            exception.printStackTrace()
        }

        binding.tvReject.setOnClickListener {
            GALog.log("agreement_reject")
            dismiss()
        }
        binding.tvAccept.setOnClickListener {
            GALog.log("agreement_accept")
            tosViewModel.authTos(tosSource)
        }

    }


}