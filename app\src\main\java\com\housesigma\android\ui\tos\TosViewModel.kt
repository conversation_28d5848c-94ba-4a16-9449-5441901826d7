package com.housesigma.android.ui.tos

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.housesigma.android.model.MsgRes
import com.housesigma.android.model.VowTosTermText
import com.housesigma.android.network.NetClient
import com.housesigma.android.network.launch

class TosViewModel : ViewModel() {

    var tosMsg: MutableLiveData<MsgRes> = MutableLiveData()
    var vowTosTermText: MutableLiveData<VowTosTermText> = MutableLiveData()

    fun authTos(str: String) {
        launch({
            NetClient.apiService.authTos( str)
        }, {
            tosMsg.postValue(it)
        })
    }


    fun getVowTosInfo(idListing: String) {
        launch({
            NetClient.apiService.getVowTosInfo(idListing)
        }, {
            vowTosTermText.postValue(it)
        })
    }

}