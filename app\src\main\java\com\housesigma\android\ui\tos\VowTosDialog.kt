package com.housesigma.android.ui.tos

import android.app.Dialog
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import com.housesigma.android.databinding.DialogVowTosBinding
import com.housesigma.android.model.VowTosButtonModel
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.LiveChatUtil
import com.housesigma.android.views.HSHtmlTextView

class VowTosDialog(
    idListing:String,
    viewModelStoreOwner: ViewModelStoreOwner,
    lifecycle: LifecycleOwner,
    context: Context,
) : Dialog(context) {

    private var idListing = idListing
    private var mContext: ViewModelStoreOwner = viewModelStoreOwner
    private var mLifecycle: LifecycleOwner = lifecycle
    private lateinit var tosViewModel: TosViewModel
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.window?.requestFeature(Window.FEATURE_NO_TITLE)
        val binding = DialogVowTosBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initViews(binding)
        tosViewModel = ViewModelProvider(mContext).get(TosViewModel::class.java)
        this.window?.setBackgroundDrawable(ColorDrawable(0x00000000))
        this.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT
        )
        tosViewModel.vowTosTermText.observe(mLifecycle) { vowTos ->
            HSHtmlTextView.setHtml(context, vowTos?.header ?: "", binding.tvTitle)
            HSHtmlTextView.setHtml(context, vowTos?.body ?: "", binding.tvContent)

            val btnBottomLeft = vowTos.buttons?.bottom_left
            val btnBottomRight = vowTos.buttons?.bottom_right
            val btnCenterBottom = vowTos.buttons?.center_bottom
            val btnCenterUp = vowTos.buttons?.center_up

            bindButton(binding.tvBottomLeftButton, btnBottomLeft)
            bindButton(binding.tvBottomRightButton, btnBottomRight)
            bindButton(binding.tvCenterBottom, btnCenterBottom)
            bindButton(binding.tvCenterUp, btnCenterUp)

            if (btnCenterBottom?.sub_title == null) {
                binding.tvCenterBottomSubtitle.visibility = View.GONE
            } else {
                binding.tvCenterBottomSubtitle.visibility = View.VISIBLE
                binding.tvCenterBottomSubtitle.text = btnCenterBottom.sub_title
            }

            if (btnCenterUp?.sub_title == null) {
                binding.tvCenterUpSubtitle.visibility = View.GONE
            } else {
                binding.tvCenterUpSubtitle.visibility = View.VISIBLE
                binding.tvCenterUpSubtitle.text = btnCenterUp.sub_title
            }
        }

        tosViewModel.getVowTosInfo(idListing)
    }

    private fun bindButton(
        tosButton: TextView,
        vowTosButtonModel: VowTosButtonModel?
    ) {
        tosButton.let { textView ->
            if (vowTosButtonModel == null) {
                textView.visibility = View.GONE
            } else {
                textView.visibility = View.VISIBLE
                textView.text = vowTosButtonModel.text
                val url = vowTosButtonModel.url ?: ""
                textView.setOnClickListener {
                    val action = vowTosButtonModel.action
                    if (action == "live_chat") {
                        dismiss()
                        LiveChatUtil.startActivity(context)
                    } else if (action == "close_popup") {
                        dismiss()
                    } else if (action == "outside_browser") {
                        dismiss()
                        WebViewHelper.jumpOuterWebView(
                            context,
                            url
                        )
                    } else if (action == "inside_webview") {
                        dismiss()
                        WebViewHelper.jumpInnerWebView(
                            context,
                            url,
                            false
                        )
                    } else if (action == "inside_browser") {
                        dismiss()
                        WebViewHelper.jumpInnerWebView(
                            context,
                            url,
                            true
                        )
                    }
                }
            }
        }
    }

    private fun startHSChatActivity() {
        LiveChatUtil.startActivity(context)
    }

    private fun initViews(binding: DialogVowTosBinding) {
    }


}