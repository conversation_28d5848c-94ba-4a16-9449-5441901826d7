package com.housesigma.android.ui.watcharea

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.util.Log
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.databinding.ActivityWatchedAreaBinding
import com.housesigma.android.model.*
import com.housesigma.android.ui.account.AddEmailLaterDialog
import com.housesigma.android.ui.account.ChangeContactActivity
import com.housesigma.android.ui.account.PromptUserAddEmailDialog
import com.housesigma.android.ui.main.MainActivity
import com.housesigma.android.ui.map.helper.MapHelper
import com.housesigma.android.ui.watched.WatchedViewModel
import com.housesigma.android.utils.ConstantsHelper
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.MapUtils
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.utils.log.Logger
import org.maplibre.android.annotations.PolygonOptions
import org.maplibre.android.annotations.PolylineOptions
import org.maplibre.android.camera.CameraPosition
import org.maplibre.android.camera.CameraUpdateFactory
import org.maplibre.android.geometry.LatLng
import org.maplibre.android.geometry.LatLngBounds
import org.maplibre.android.maps.MapView
import org.maplibre.android.maps.Style
import org.maplibre.android.style.layers.FillLayer
import org.maplibre.android.style.layers.Property
import org.maplibre.android.style.layers.PropertyFactory
import org.maplibre.android.style.sources.GeoJsonSource
import org.maplibre.android.utils.BitmapUtils
import org.greenrobot.eventbus.EventBus
import org.maplibre.android.MapLibre
import org.maplibre.android.maps.MapLibreMap
import org.maplibre.android.plugins.annotation.OnSymbolClickListener
import org.maplibre.android.plugins.annotation.OnSymbolDragListener
import org.maplibre.android.plugins.annotation.Symbol
import org.maplibre.android.plugins.annotation.SymbolManager
import org.maplibre.android.plugins.annotation.SymbolOptions
import org.maplibre.geojson.FeatureCollection


class WatchedAreaActivity : BaseActivity() {

    private lateinit var watchedViewModel: WatchedViewModel
    private lateinit var binding: ActivityWatchedAreaBinding

    private var mapView: MapView? = null
    private lateinit var mapLibreMap: MapLibreMap
    private lateinit var mapboxStyle: Style
    private lateinit var mapHelper: MapHelper

    private var lon1: Double = MapHelper.lon1
    private var lon2: Double = MapHelper.lon2
    private var lat1: Double = MapHelper.lat1
    private var lat2: Double = MapHelper.lat2
    private var zoom: Double = MapHelper.zoom

    private var fillSource: GeoJsonSource? = null
    private var polyHashList: HashMap<String, Pair<LinkedHashMap<String, LatLng>, Boolean>> =
        HashMap()
    private var rootSymbolId: String? = null
    private var newPolygon: Boolean = false
    private var startDrag: Boolean = false
    private var symbolManager: SymbolManager? = null

    private var isEdit = false
    private var editWatchPolygon: WatchPolygon? = null

    private var editPolygon: org.maplibre.android.annotations.Polygon? = null
    private var editPolyLine: org.maplibre.android.annotations.Polyline? = null

    private var watchedAreaName: String? = ""

    private var mShowAddEmail:Int = 0
    private val REQUEST_CODE_ADD_EMAIL:Int = 1001

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isEdit = intent.getBooleanExtra("edit", false)
        editWatchPolygon = intent.getParcelableExtra<WatchPolygon>("watchPolygon")
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }

        mapView = binding.mapView
        mapView?.onCreate(savedInstanceState)
        mapView?.getMapAsync { map ->
            mapLibreMap = map
            mapLibreMap.uiSettings.isRotateGesturesEnabled = false
            mapHelper = MapHelper(WatchedAreaActivity@ this, layoutInflater, mapLibreMap)
            setUpMapboxMap(ConstantsHelper.getMapVector())
        }
    }


    override fun getLayout(): Any {
        MapLibre.getInstance(this)
        binding = ActivityWatchedAreaBinding.inflate(layoutInflater)


        return binding.root
    }

    private fun setUpMapboxMap(styleUrl: String) {
        mapLibreMap.setMaxZoomPreference(20.0)
        mapLibreMap.setMinZoomPreference(4.0)



        mapLibreMap.setStyle(styleUrl, object : Style.OnStyleLoaded {
            override fun onStyleLoaded(style: Style) {
                mapboxStyle = style
                addMarkerIconStyle(style)
                symbolManager = SymbolManager(mapView!!, mapLibreMap!!, mapLibreMap?.style!!)


                symbolManager?.addClickListener(object : OnSymbolClickListener {
                    override fun onAnnotationClick(t: Symbol?): Boolean {
                        highlightSymbol(t)
                        Log.d("click click", t?.id.toString());
                        if (polyHashList.keys.first().equals(t?.id.toString())) {
                            val polykey = polyHashList.keys.first()
                            polyHashList.put(polykey, Pair(polyHashList.get(polykey)!!.first, true))
                        }
                        drawPolygon()
                        return true
                    }
                })

                symbolManager?.addDragListener(object : OnSymbolDragListener {
                    override fun onAnnotationDragStarted(annotation: Symbol?) {
                        startDrag = true
                        Log.d("drag", "drag started")
                        highlightSymbol(annotation)
                        return
                    }

                    override fun onAnnotationDrag(annotation: Symbol?) {
//                        Log.d(TAG, "symbol layer id" + symbolManager.layerId)

                        val polyKey = polyHashList.filter {
                            it.value.first.filterKeys { it.equals(annotation!!.id.toString()) }.keys.size > 0
                        }.keys.first().toString()
                        Log.d(TAG, "drag poly key " + polyKey)
                        polyHashList.get(polyKey)?.first?.put(
                            annotation!!.id.toString(),
                            annotation!!.latLng
                        )


                        drawPolygon()
                        return
                    }

                    override fun onAnnotationDragFinished(annotation: Symbol?) {
                        if (startDrag) {
                            startDrag = !startDrag
                            lowlightSymbol(annotation)
                        }

                        return
                    }


                })

                fillSource = initFillSource(style)
                initFillLayer(style)


                if (isEdit) {
                    GALog.page("edit_watched_area")
                    createEditArea()
                } else {
                    GALog.page("add_watched_area")
                    moveCameraToCenter()
                    Handler().postDelayed({ createArea() }, 200)
                }
                addOnCameraMoveListener()


            }
        })
    }

    private fun createEditArea() {
        val polygon = editWatchPolygon?.polygon
        if (polygon != null) {

            var latLngBounds2 = LatLngBounds.Builder()

            polygon.forEach {
                latLngBounds2.include(MapUtils.polygon2LatLng(it))
                addMarker(MapUtils.polygon2LatLng(it))
            }
            mapLibreMap.animateCamera(
                CameraUpdateFactory.newLatLngBounds(
                    latLngBounds2.build(), 100
                )
            )
        }


    }


    private fun initFillSource(loadedMapStyle: Style): GeoJsonSource {
        val fillFeatureCollection =
            FeatureCollection.fromFeatures(arrayOf())
        val fillGeoJsonSource = GeoJsonSource(FILL_SOURCE_ID, fillFeatureCollection)
        loadedMapStyle.addSource(fillGeoJsonSource)
        return fillGeoJsonSource
    }

    private fun initFillLayer(loadedMapStyle: Style) {
        val fillLayer = FillLayer(
            FILL_LAYER_ID,
            FILL_SOURCE_ID
        )
        fillLayer.setProperties(
            PropertyFactory.fillAntialias(true),
            PropertyFactory.fillColor(Color.parseColor("#0999FF")),
            PropertyFactory.fillOpacity(0.5f),
            PropertyFactory.fillOutlineColor(Color.parseColor("#A4C0F1")),
            PropertyFactory.visibility(Property.VISIBLE),
            PropertyFactory.fillTranslateAnchor(Property.FILL_TRANSLATE_ANCHOR_MAP),
        )
        symbolManager?.layerId?.let { loadedMapStyle.addLayerBelow(fillLayer, it) }
    }

    private fun addMarker(point: LatLng) {
        Log.d(TAG, "Map clicked")
        var symbolOptions: SymbolOptions =
            SymbolOptions().withLatLng(point).withIconImage(ID_ICON_LOCATION).withDraggable(true)
                .withIconSize(1f)
        val symbol = symbolManager?.create(symbolOptions)

        symbol?.let {
            if (polyHashList.values.isEmpty()) {
                rootSymbolId = symbol.id.toString()
            } else if (newPolygon) {
                newPolygon = false
                rootSymbolId = symbol.id.toString()
            }

            symbolManager?.iconAllowOverlap = true


            if (polyHashList.get(rootSymbolId) == null) {
                polyHashList.put(
                    rootSymbolId!!,
                    Pair(linkedMapOf(symbol.id.toString() to point), false)
                )
            } else {
                polyHashList.get(rootSymbolId)?.first?.put(symbol.id.toString(), point)
            }


            Log.d(TAG, "layer id " + symbol.id.toString())
            Log.d(TAG, polyHashList.toString())
            drawPolygon()
        }

    }

    fun highlightSymbol(t: Symbol?) {
        t?.iconImage = ID_ICON_LOCATION_SELECTED
        symbolManager?.update(t)
    }


    fun lowlightSymbol(t: Symbol?) {
        t?.iconImage = ID_ICON_LOCATION
        symbolManager?.update(t)
    }

    private fun drawPolygon() {
        val points = polyHashList.values
        val polygonPoints: List<LatLng> = points.map {
            val items =
                it.first.values.toList().map { it -> LatLng(it.latitude, it.longitude) }
                    .toMutableList()
            if (it.second) {
                items.add(items.get(0))
            }
            items
        }.first()
        // 最少有三个点
        if (polygonPoints.size < 3) {
            return
        }

        val polylinePoints: MutableList<LatLng> = points.map {
            val items =
                it.first.values.toList().map { it -> LatLng(it.latitude, it.longitude) }
                    .toMutableList()
            if (it.second) {
                items.add(items.get(0))
            }
            items
        }.first().toMutableList()
        polylinePoints.add(LatLng(polylinePoints[0].latitude, polylinePoints[0].longitude))

//        Logger.d( "drawPolygon..." + polygonPoints.toString())
//        Logger.d( "drawPolygonLine..." + polylinePoints.toString())
        if (editPolygon == null && editPolyLine == null) {
            editPolygon = mapLibreMap.addPolygon(
                PolygonOptions()
                    .addAll(polygonPoints)
                    .fillColor(Color.parseColor("#1092F0"))
                    .alpha(0.3f)
            )

            editPolyLine = mapLibreMap.addPolyline(
                PolylineOptions()
                    .addAll(polylinePoints)
                    .color(Color.parseColor("#1E91FB"))
                    .width(3.0f)
                    .alpha(1f)
            )
        } else {
            editPolygon?.let {
                it.points = polygonPoints
                mapLibreMap.updatePolygon(it)
            }

            editPolyLine?.let {
                it.points = polylinePoints
                mapLibreMap.updatePolyline(it)
            }
        }
    }


    companion object {
        private val TAG = "hkj"
        private val FILL_LAYER_ID = "fill-layer-id"
        private val FILL_SOURCE_ID = "fill-source-id"
        private val ID_ICON_LOCATION = "location"
        private val ID_ICON_LOCATION_SELECTED = "location_selected"

    }

    private fun addMarkerIconStyle(style: Style) {
        style.addImage(
            ID_ICON_LOCATION,
            BitmapUtils.getBitmapFromDrawable(
                ContextCompat.getDrawable(
                    this,
                    R.drawable.ic_marker_darg_point
                )
            )!!,
            false
        )

        style.addImage(
            ID_ICON_LOCATION_SELECTED, BitmapUtils.getBitmapFromDrawable(
                ContextCompat.getDrawable(
                    this,
                    R.drawable.ic_marker_darg_point4x
                )
            )!!,
            false
        )
    }

    private fun addOnCameraMoveListener() {
        mapLibreMap.addOnCameraIdleListener {

            Logger.d( "move camera idle.........")

            // 可见区域
            val visibleRegion = mapLibreMap.projection.visibleRegion
            val farLeft = visibleRegion.farLeft //可视区域的左上角。
            val nearRight = visibleRegion.nearRight //可视区域的右下角。
            val bounds = visibleRegion.latLngBounds


            bounds.let {
                val west = it.longitudeWest
                val south = it.latitudeSouth
                val east = it.longitudeEast
                val north = it.latitudeNorth

                val extraLat = Math.abs(south - north) / 2
                val extraLon = Math.abs(west - east) / 2

                lat1 = it.latitudeNorth + (if (north > south) extraLat else -extraLat)
                lon1 = it.longitudeEast + (if (east > west) extraLon else -extraLon)
                lat2 = it.latitudeSouth + (if (south > north) extraLat else -extraLat)
                lon2 = it.longitudeWest + (if (west > east) extraLon else -extraLon)
            }


            val cameraZoom = mapLibreMap.cameraPosition.zoom
            zoom = cameraZoom
        }

    }

    /**
     * 第一次进来的时候创建watchedArea 五边形
     */
    private fun createArea() {
        var cLatitude = mapLibreMap.cameraPosition.target?.latitude?:0.0
        var cLongitude = mapLibreMap.cameraPosition.target?.longitude?:0.0


        var pointLat1 = cLatitude + 0.15 * (lat1 - lat2)
        var pointLon1 = cLongitude

        var pointLat2 = cLatitude + 0.06 * (lat1 - lat2)
        var pointLon2 = cLongitude - (0.2 * (lon1 - lon2))

        var pointLat3 = cLatitude - (0.09 * (lat1 - lat2))
        var pointLon3 = cLongitude - (0.14 * (lon1 - lon2))

        var pointLat4 = cLatitude - (0.09 * (lat1 - lat2))
        var pointLon4 = cLongitude + (0.14 * (lon1 - lon2))

        var pointLat5 = cLatitude + 0.06 * (lat1 - lat2)
        var pointLon5 = cLongitude + (0.2 * (lon1 - lon2))

        addMarker(LatLng(pointLat1, pointLon1))
        addMarker(LatLng(pointLat2, pointLon2))
        addMarker(LatLng(pointLat3, pointLon3))
        addMarker(LatLng(pointLat4, pointLon4))
        addMarker(LatLng(pointLat5, pointLon5))

        val polykey = polyHashList.keys.first()
        polyHashList.put(polykey, Pair(polyHashList.get(polykey)!!.first, true))
        drawPolygon()
    }

    /**
     * 移动camera 到中心点位置，在这儿的位置是固定的几个点
     */
    private fun moveCameraToCenter() {
        //  这里的位置和选择的location相关
        var currentMapCenter = MapUtils.getCurrentMapCenter()
        var currentMapZoom = MapUtils.getCurrentMapZoom()

        mapLibreMap.cameraPosition = CameraPosition.Builder()
            .target(
                currentMapCenter
            )
            .zoom(currentMapZoom)
            .build()
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            statusBarDarkFont(true)
            statusBarColor(R.color.color_white)
            fitsSystemWindows(true)
        }
        binding.ivClose.setOnClickListener {
            finish()
        }

        binding.ivToolZoomPlus.setOnClickListener {
            mapLibreMap.animateCamera(CameraUpdateFactory.zoomIn());
        }

        binding.ivToolZoomMin.setOnClickListener {
            mapLibreMap.animateCamera(CameraUpdateFactory.zoomOut());
        }


        binding.tvCancel.setOnClickListener {
            GALog.log("drawing_cancel")
            finish()
        }
        binding.tvDone.setOnClickListener {
            GALog.log("drawing_done")

            binding.llTip.visibility = View.GONE
            WatchedAreaInputFragment(
                object : WatchedAreaInputFragment.WatchedAreaInputCallback {
                    override fun onSuccess(
                        saveWatchPolygon: DataSaveWatchPolygon,
                        showAddEmail: Int
                    ) {
                        mShowAddEmail = showAddEmail
                        watchedAreaName = saveWatchPolygon.description
                        ToastUtils.showLong("Saved")
                        //第一次保存成功后，弹出修改条件页
                        val watchedAreaFilterFragment = WatchedAreaFilterFragment.newInstance(saveWatchPolygon)
                        watchedAreaFilterFragment.setWatchedAreaFilterFragment(
                            object : WatchedAreaFilterFragment.Callback {
                                override fun onSave(saveWatchPolygon: DataSaveWatchPolygon) {
                                    watchedAreaName = saveWatchPolygon.description
                                    val data = saveWatchPolygon
                                    watchedViewModel.updateWatchPolygon(data)
                                }

                                override fun onSkip() {
                                    // DEV-4539 根据WatchedAreaFilterFragment.Callback onSave里 mShowAddEmail字段显示不同的弹窗
                                    if (mShowAddEmail==1) {
                                        showPromptUserToAddEmailDialog()
                                    } else {
                                        showSuccessDialog(watchedAreaName)
                                    }
                                }
                            })
                        watchedAreaFilterFragment.show(supportFragmentManager, "")
                    }
                }, polygon1 = polyHashList, editWatchPolygon
            ).show(supportFragmentManager, "")
        }
    }

    private fun showSuccessDialog(watchedAreaName: String? = "") {
        val visibleRegion = mapLibreMap.projection.visibleRegion
        val center = visibleRegion.latLngBounds.center
        val cameraZoom = mapHelper.getCameraPositionZoom(mapLibreMap)
        
        // 生成多边形路径字符串
        var polygonPath = ""
        val polyHashValue = polyHashList.values.firstOrNull()
        if (polyHashValue != null) {
            val points = polyHashValue.first.values.map { "${it.longitude},${it.latitude}" }
            // 如果是闭合多边形，需要添加第一个点作为最后一个点
            if (polyHashValue.second && points.isNotEmpty()) {
                polygonPath = points.joinToString("|") + "|" + points.first()
            } else {
                polygonPath = points.joinToString("|")
            }
        }



        // Set result intent and finish the activity
        val resultIntent = Intent().apply {
            putExtra("watchedAreaName", watchedAreaName)
            putExtra("centerLat", center.latitude)
            putExtra("centerLng", center.longitude)
            putExtra("cameraZoom", cameraZoom)
            putExtra("polygonPath", polygonPath)
        }
        setResult(RESULT_OK, resultIntent)
        finish()
    }

    override fun initData() {
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        watchedViewModel.updateWatchPolygon.observe(this) {
        // 根据WatchedAreaFilterFragment.Callback onSave里 mShowAddEmail字段显示不同的弹窗
            if (mShowAddEmail==1) {
                showPromptUserToAddEmailDialog()
            } else {
                showSuccessDialog(watchedAreaName)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode==REQUEST_CODE_ADD_EMAIL) {
            showSuccessDialog(watchedAreaName)
        }
    }

    private fun showPromptUserToAddEmailDialog() {
        this.let {
            watchedViewModel.stopPrompt()
            val promptUserAddEmailDialog = PromptUserAddEmailDialog(
                "add_watched_area",
                WatchedAreasFragment@ this,
                it,
                it,
                object : PromptUserAddEmailDialog.PromptUserAddEmailDialogCallback {
                    override fun onSendCode(email: String) {
                        val intent = Intent(it, ChangeContactActivity::class.java)
                        intent.putExtra("is_email", true)
                        intent.putExtra("email", email)
                        startActivityForResult(
                            intent,
                            REQUEST_CODE_ADD_EMAIL
                        )// forResult?返回后弹出showSuccessDialog(watchedAreaName)
                    }

                    override fun onNotNow() {
                        AddEmailLaterDialog(
                            it,
                            object : AddEmailLaterDialog.AddEmailLaterDialogCallback {
                                override fun onOkay() {
                                    showSuccessDialog(watchedAreaName)
                                }

                                override fun onGoBack() {
                                    showPromptUserToAddEmailDialog()
                                }
                            }).show()
                    }
                })
            promptUserAddEmailDialog.show()
        }
    }


    override fun onStart() {
        super.onStart()
        mapView?.onStart()
    }

    override fun onResume() {
        super.onResume()
        mapView?.onResume()
        GALog.page("add_watched_area")
    }

    override fun onPause() {
        super.onPause()
        mapView?.onPause()
    }

    override fun onStop() {
        super.onStop()
        mapView?.onStop()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mapView?.onSaveInstanceState(outState)
    }

    override fun onLowMemory() {
        super.onLowMemory()
        mapView?.onLowMemory()
    }

    override fun onDestroy() {
        super.onDestroy()
        mapView?.onDestroy()
        symbolManager?.onDestroy()
    }

}