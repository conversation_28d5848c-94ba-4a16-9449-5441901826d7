package com.housesigma.android.ui.watcharea

import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import com.donkingliang.labels.LabelsView
import com.google.gson.Gson
import com.gyf.immersionbar.ktx.destroyImmersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseDialogFragment
import com.housesigma.android.databinding.DialogWatchedAreaFilterBinding
import com.housesigma.android.model.*
import com.housesigma.android.ui.map.MapViewModel
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.GsonUtils
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.LoadingDialog
import com.housesigma.android.views.SaleSeekBar
import com.jaygoo.widget.OnRangeChangedListener
import com.jaygoo.widget.RangeSeekBar

class WatchedAreaFilterFragment() :
    BaseDialogFragment() {

    interface Callback {
        fun onSave(saveWatchPolygon: DataSaveWatchPolygon)

        fun onSkip()
    }

    private lateinit var mapViewModel: MapViewModel
    private var loadingDialog: LoadingDialog? = null
    private lateinit var binding: DialogWatchedAreaFilterBinding

    private lateinit var saveWatchPolygon :DataSaveWatchPolygon

    private var bedroomRange: MutableList<Int> = ArrayList()
    private var basement: MutableList<String> = ArrayList()
    private var bathroomMin: String = "0"
    private var garageMin: String = "0"
    private var openHouseDate: String = "0"
    private var description: String = ""
    private var fee: String = "0"

//    private var priceLeft: Int = 0
//    private var priceRight: Int = 6000000


    private var feetLeft: Int = 0
    private var feetRight: Int = 100

    private var squareFootageLeft: Int = 0
    private var squareFootageRight: Int = 4000

    private var lotSizeMaxValue: Int = 15
    private var lotSizeLeft: Int = 0
    private var lotSizeRight: Int = lotSizeMaxValue

    private var buildingAgeMaxValue: Int = 24
    private var buildingAgeLeft: Int = 0
    private var buildingAgeRight: Int = buildingAgeMaxValue

    private var lotSizeFilter:List<LotSizeFilter>?=null
    private var buildingAgeFilter:List<BuildingAgeFilter>?=null

    private var mcb: Callback?=null
    private var mapFilter: MapFilter? = null

    private var listType: ArrayList<Int> = ArrayList()
    private var houseType: ArrayList<String> = ArrayList()

    companion object {
        fun newInstance(saveWatchPolygon: DataSaveWatchPolygon): WatchedAreaFilterFragment {
            val args = Bundle()
            val saveWatchPolygonJson = GsonUtils.parseToStr(saveWatchPolygon)
            args.putString("saveWatchPolygonJson", saveWatchPolygonJson)
            val fragment = WatchedAreaFilterFragment()
            fragment.arguments = args
            return fragment
        }
    }

    fun setWatchedAreaFilterFragment(cb: Callback) {
        mcb = cb
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreen)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        mapViewModel = ViewModelProvider(this).get(MapViewModel::class.java)
        binding = DialogWatchedAreaFilterBinding.inflate(inflater, container, false)
        val saveWatchPolygonJson = arguments?.getString("saveWatchPolygonJson")
        saveWatchPolygon = Gson().fromJson(saveWatchPolygonJson, DataSaveWatchPolygon::class.java)

        initViews()
        initData()
        dialog?.setCanceledOnTouchOutside(true)
        return binding.root
    }

    private fun initData() {
        Logger.d("saveWatchPolygon "+ saveWatchPolygon)
        if (saveWatchPolygon==null) return
        mapViewModel.getMapFilter()

        mapViewModel.mapFilter.observe(this) {
            mapFilter = it
            setData(mapFilter!!)
            setOnLabelClickListener()
        }
    }


    fun setData(mapFilter: MapFilter) {
        lotSizeFilter = mapFilter.lot_size_filter
        if (mapFilter.lot_size_filter == null) {
            binding.rsbLotSize.visibility = View.GONE
            binding.tvLotSize.visibility = View.GONE
        } else {
            binding.rsbLotSize.visibility = View.VISIBLE
            binding.tvLotSize.visibility = View.VISIBLE
        }

        buildingAgeFilter = mapFilter.building_age_filter
        if (mapFilter.building_age_filter == null) {
            binding.rsbBuildingAge.visibility = View.GONE
            binding.tvBuildingAge.visibility = View.GONE
        } else {
            binding.rsbBuildingAge.visibility = View.VISIBLE
            binding.tvBuildingAge.visibility = View.VISIBLE
        }

        val labelsBasement = binding.labelsBasement
        val labelsBathroom = binding.labelsBathroom
        val labelsBedroom = binding.labelsBeadroom
        val labelsGarageParking = binding.labelsGarageParking
        val labelsOpenHouse = binding.labelsOpenHouse
        val labelsPropertyType = binding.labelsPropertyType
        val labelsType = binding.labelsType

        val list = ArrayList<HouseType>()
        list.add(HouseType(typeName = "For Sale", position = 1, isSelect = saveWatchPolygon.filter.list_type.contains(1)))
        list.add(HouseType(typeName = "Sold", position = 3, isSelect = saveWatchPolygon.filter.list_type.contains(3)))
        list.add(HouseType(typeName = "De-listed", position = 5, isSelect = saveWatchPolygon.filter.list_type.contains(5)))

        labelsType.selectType = LabelsView.SelectType.MULTI
        labelsType.setLabels(list) { _, _, data -> data.typeName }



        // list_type
        setListTypeSelect(list, labelsType)

        labelsPropertyType.selectType = LabelsView.SelectType.MULTI
        labelsPropertyType.setLabels(mapFilter?.house_type_filter) { _, _, data -> data.name }

        for (house_type in saveWatchPolygon.filter.house_type) {
            for (mapFilterHouseTypeFilter in mapFilter.house_type_filter) {
                if (mapFilterHouseTypeFilter.id == house_type) {
                    mapFilterHouseTypeFilter.isSelect = true
                }
            }
        }
        setHouseTypeSelect(mapFilter.house_type_filter, labelsPropertyType)


        labelsBasement.selectType = LabelsView.SelectType.MULTI
        labelsBasement.setLabels(mapFilter?.basement_filter) { _, _, data -> data.name }

        // 交叉对比basement
        for (basement in saveWatchPolygon.filter.basement) {
            for (basementItem in mapFilter.basement_filter) {
                if (basementItem.id == basement) {
                    basementItem.isSelect = true
                }
            }
        }

        var selectList: MutableList<Int> = ArrayList()
        var i = 0
        for (item in mapFilter.basement_filter) {
            if (item.isSelect) {
                selectList.add(i)
            }
            i++
        }
        labelsBasement.setSelects(selectList)



        labelsBedroom.selectType = LabelsView.SelectType.MULTI
        labelsBedroom.setLabels(mapFilter?.bedroom_filter) { _, _, data -> data.name }


        // 交叉对比Bedroom
        for (bedroom in saveWatchPolygon.filter.bedroom_range) {
            for (bedroomItem in mapFilter.bedroom_filter) {
                if (bedroomItem.id == bedroom.toString()) {
                    bedroomItem.isSelect = true
                }
            }
        }

        selectList.clear()
        i = 0
        for (item in mapFilter.bedroom_filter) {
            if (item.isSelect) {
                selectList.add(i)
            }
            i++
        }
        labelsBedroom.setSelects(selectList)


        labelsBathroom.selectType = LabelsView.SelectType.SINGLE_IRREVOCABLY
        labelsBathroom.setLabels(mapFilter?.bathroom_filter) { _, _, data -> data.name }
        // 交叉对比Bathroom
        for (bathRoomItem in mapFilter.bathroom_filter) {
            if (bathRoomItem.id == saveWatchPolygon.filter.bathroom_min.toString()) {
                bathRoomItem.isSelect = true
            }
        }
        selectList.clear()
        i = 0
        for (item in mapFilter.bathroom_filter) {
            if (item.isSelect) {
                selectList.add(i)
            }
            i++
        }
        labelsBathroom.setSelects(selectList)




        labelsGarageParking.selectType = LabelsView.SelectType.SINGLE_IRREVOCABLY
        labelsGarageParking.setLabels(mapFilter?.garage_filter) { _, _, data -> data.name }

        // 交叉对比Garage
        for (garageItem in mapFilter.garage_filter) {
            if (garageItem.id == saveWatchPolygon.filter.garage_min.toString()) {
                garageItem.isSelect = true
            }
        }
        selectList.clear()
        i = 0
        for (item in mapFilter.garage_filter) {
            if (item.isSelect) {
                selectList.add(i)
            }
            i++
        }
        labelsGarageParking.setSelects(selectList)




        labelsOpenHouse.selectType = LabelsView.SelectType.SINGLE_IRREVOCABLY
        labelsOpenHouse.setLabels(mapFilter?.open_house_filter) { _, _, data -> data.name }



        // 交叉对比openhouse
        for (item in mapFilter.open_house_filter) {
            if (item.id == saveWatchPolygon.filter.open_house_date.toString()) {
                item.isSelect = true
            }
        }
        selectList.clear()
        i = 0
        for (item in mapFilter.open_house_filter) {
            if (item.isSelect) {
                selectList.add(i)
            }
            i++
        }
        labelsOpenHouse.setSelects(selectList)


        binding.etDescription.setText(saveWatchPolygon.filter.description)

        binding.etFee.setText(saveWatchPolygon.filter.max_maintenance_fee)


        binding.rsbFeet.setProgress(saveWatchPolygon.filter.front_feet[0].toFloat(),
            saveWatchPolygon.filter.front_feet[1].toFloat()
        )


        binding.saleSb.setProgress(HSUtil.calRevertSquareFootageCurve(saveWatchPolygon.filter.listing_price[0]).toFloat(),
            HSUtil.calRevertSquareFootageCurve(saveWatchPolygon.filter.listing_price[1]).toFloat()
        )

        binding.rsbSquare.setProgress(saveWatchPolygon.filter.square_footage[0].toFloat(),
            saveWatchPolygon.filter.square_footage[1].toFloat()
        )


        saveWatchPolygon.filter.lot_size?.get(0)?.let {
            saveWatchPolygon.filter.lot_size!![1]?.let { it1 ->
                val lotSizeLeftPosition = HSUtil.getPositionByLotSizeValue(it)
                val lotSizeRightPosition = HSUtil.getPositionByLotSizeValue(it1)

                if (lotSizeLeftPosition==-1||lotSizeRightPosition==-1){
                    return
                }

                binding.rsbLotSize.setProgress(
                    lotSizeLeftPosition.toFloat(),
                    lotSizeRightPosition.toFloat()
                )

            }
        }


        saveWatchPolygon.filter.building_age?.get(0)?.let {
            saveWatchPolygon.filter.building_age!![1]?.let { it1 ->
                val buildingAgeLeftPosition = HSUtil.getPositionByBuildingAgeValue(it)
                val buildingAgeRightPosition = HSUtil.getPositionByBuildingAgeValue(it1)

                if (buildingAgeLeftPosition==-1||buildingAgeRightPosition==-1){
                    return
                }

                binding.rsbBuildingAge.setProgress(
                    buildingAgeLeftPosition.toFloat(),
                    buildingAgeRightPosition.toFloat()
                )

            }
        }
    }


    private fun setHouseTypeSelect(
        list: List<MapFilterHouseTypeFilter>,
        labelsView: LabelsView
    ) {
        val selectList: MutableList<Int> = ArrayList()
        var i = 0
        for (item in list) {
            if (item.isSelect) {
                selectList.add(i)
            }
            i++
        }
        labelsView.setSelects(selectList)
    }

    private fun setListTypeSelect(
        list: ArrayList<HouseType>,
        labelsType: LabelsView
    ) {
        val selectList: MutableList<Int> = ArrayList()
        var i = 0
        for (item in list) {
            if (item.isSelect) {
                selectList.add(i)
            }
            i++
        }
        labelsType.setSelects(selectList)
    }


    fun setOnLabelClickListener() {
        val labelsBasement = binding.labelsBasement
        val labelsBathroom = binding.labelsBathroom
        val labelsBedroom = binding.labelsBeadroom
        val labelsGarageParking = binding.labelsGarageParking
        val labelsOpenHouse = binding.labelsOpenHouse
        val labelsPropertyType = binding.labelsPropertyType
        val labelsType = binding.labelsType


//        labelsType.setSelects(0)
        labelsType.let {
            it.setOnLabelClickListener { label, data, position ->
                // 对选项进行处理 选了all就不能选择其他的，选择其他的就要去掉all
//                if (position == 0) {
//                    it.setSelects(0)
//                } else {
//                    val selectLabels = it.selectLabels
//                    selectLabels.remove(0)
//                    it.setSelects(selectLabels)
//                }
                var selectLabelDatas =
                    it.getSelectLabelDatas<HouseType>()

                if (selectLabelDatas.size == 0) {
                    it.setSelects(0)
                }

            }
        }



//        labelsPropertyType.setSelects(0)
        labelsPropertyType.let {
            it.setOnLabelClickListener { label, data, position ->
                // 对选项进行处理 选了all就不能选择其他的，选择其他的就要去掉all
                if (position == 0) {
                    it.setSelects(0)
                } else {
                    val selectLabels = it.selectLabels
                    selectLabels.remove(0)
                    it.setSelects(selectLabels)
                }
                var selectLabelDatas =
                    it.getSelectLabelDatas<MapFilterHouseTypeFilter>()
                if (selectLabelDatas.size == 0) {
                    it.setSelects(0)
                }

                val allLabel = it.getLabels<MapFilterIdName>()
                if (selectLabelDatas.size - 1 == allLabel.size - 2) {
                    it.setSelects(0)
                }
            }
        }


//        labelsBeadroom.setSelects(0)
        labelsBathroom.let {
            it.setOnLabelClickListener { label, data, position ->
//                bathroomMin = position.toString()
            }
        }

        labelsGarageParking.let {
            it.setOnLabelClickListener { label, data, position ->
                garageMin = position.toString()
            }
        }

        labelsOpenHouse.let {
            it.setOnLabelClickListener { label, data, position ->
                openHouseDate = position.toString()
            }
        }

        labelsBasement.let {
            it.setOnLabelClickListener { label, data, position ->
            }
        }

        labelsBedroom.let {
            it.setOnLabelClickListener { label, data, position ->
                // 对选项进行处理 选了all就不能选择其他的，选择其他的就要去掉all
                if (position == 0) {
                    it.setSelects(0)
                } else {
                    val selectLabels = it.selectLabels
                    selectLabels.remove(0)
                    it.setSelects(selectLabels)
                }
                var selectLabelDatas =
                    it.getSelectLabelDatas<MapFilterIdName>()

                if (selectLabelDatas.size == 0) {
                    it.setSelects(0)
                }
                val allLabel = it.getLabels<MapFilterIdName>()
                if (selectLabelDatas.size - 1 == allLabel.size - 2) {
                    it.setSelects(0)
                }
            }
        }


    }


    private fun initViews() {
        if (saveWatchPolygon==null) return
        binding.tvWatchedAreaName.text = saveWatchPolygon.description

        binding.saleSb.setOnChangeListener(object :SaleSeekBar.OnChangeListener{
            override fun onChange(showPrice: String) {
                binding.tvPrice.text = showPrice
            }

            override fun onStopTrackingTouch() {
            }
        })
        binding.saleSb.setDefaultValue()
        binding.rsbFeet.setOnRangeChangedListener(object : OnRangeChangedListener {
            override fun onRangeChanged(
                view: RangeSeekBar?,
                leftValue: Float,
                rightValue: Float,
                isFromUser: Boolean
            ) {
                feetLeft = leftValue.toInt()
                feetRight = rightValue.toInt()

                if (leftValue.toInt() == 0 && rightValue.toInt() == 100) {
                    binding.tvFeet.text =
                        "Lot Front (feet): Unspecified" + " - 100+"
                } else if (rightValue.toInt() == 100) {
                    binding.tvFeet.text =
                        "Lot Front (feet): ".plus(leftValue.toInt()) + " - 100+"
                } else if (leftValue.toInt() == 0) {
                    binding.tvFeet.text =
                        "Lot Front (feet): Unspecified" + " - " + rightValue.toInt()
                } else {
                    binding.tvFeet.text =
                        "Lot Front (feet): ".plus(leftValue.toInt()) + " - " + rightValue.toInt()
                }
            }

            override fun onStartTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
            }

            override fun onStopTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
                GALog.log("map_filters_click","lot_front")
            }
        })

        binding.rsbFeet.setProgress(0f, 100f)

        binding.rsbSquare.setOnRangeChangedListener(object : OnRangeChangedListener {
            override fun onRangeChanged(
                view: RangeSeekBar?,
                leftValue: Float,
                rightValue: Float,
                isFromUser: Boolean
            ) {

                squareFootageLeft = leftValue.toInt()
                squareFootageRight = rightValue.toInt()
                if (leftValue.toInt() == 0 && rightValue.toInt() == 4000) {
                    binding.tvSquare.text =
                        "Square Footage: Unspecified" + " - Max"
                } else if (rightValue.toInt() == 100) {
                    binding.tvSquare.text =
                        "Square Footage: ".plus(leftValue.toInt()) + " - Max"
                } else if (leftValue.toInt() == 0) {
                    binding.tvSquare.text =
                        "Square Footage: Unspecified" + " - " + rightValue.toInt()
                } else {
                    binding.tvSquare.text =
                        "Square Footage: ".plus(leftValue.toInt()) + " - " + rightValue.toInt()
                }
            }

            override fun onStartTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
            }

            override fun onStopTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
                GALog.log("map_filters_click","square_footage")
            }
        })
        binding.rsbSquare.setProgress(0f, 4000f)

        binding.rsbLotSize.setProgress(lotSizeLeft.toFloat(), lotSizeRight.toFloat())
        binding.rsbLotSize.setOnRangeChangedListener(object : OnRangeChangedListener {
            override fun onRangeChanged(
                view: RangeSeekBar?,
                leftValue: Float,
                rightValue: Float,
                isFromUser: Boolean
            ) {
                lotSizeLeft = Math.round(leftValue.toDouble()).toInt()
                lotSizeRight = Math.round(rightValue.toDouble()).toInt()

                val lotSizeFilterLeft = HSUtil.getLotSize(lotSizeLeft)
                val lotSizeFilterRight = HSUtil.getLotSize(lotSizeRight)

                binding.tvLotSize.text =  "Lot Size: "+lotSizeFilterLeft?.text + " - "+lotSizeFilterRight?.text
            }

            override fun onStartTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
            }

            override fun onStopTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
                GALog.log("map_filters_click","lot_size")
            }
        })


        binding.rsbBuildingAge.setProgress(buildingAgeLeft.toFloat(), buildingAgeRight.toFloat())
        binding.rsbBuildingAge.setOnRangeChangedListener(object : OnRangeChangedListener {
            override fun onRangeChanged(
                view: RangeSeekBar?,
                leftValue: Float,
                rightValue: Float,
                isFromUser: Boolean
            ) {
                buildingAgeLeft = Math.round(leftValue.toDouble()).toInt()
                buildingAgeRight = Math.round(rightValue.toDouble()).toInt()

                val buildingFilterLeft = HSUtil.getBuildingAge(buildingAgeLeft)
                val buildingFilterRight = HSUtil.getBuildingAge(buildingAgeRight)

                binding.tvBuildingAge.text =  "Building Age (years): "+buildingFilterLeft?.text + " - "+buildingFilterRight?.text
            }

            override fun onStartTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
            }

            override fun onStopTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
                GALog.log("map_filters_click","building_age")
            }
        })


        binding.tvSave.setOnClickListener {
            GALog.log("save_filter_submit")
            updateSaveWatchPolygon()
            mcb?.onSave(saveWatchPolygon)
            dismiss()
        }

        binding.tvSkip.setOnClickListener {
            mcb?.onSkip()
            dismiss()
        }

    }

    private fun updateSaveWatchPolygon() {
        basement?.clear()
        for (selectLabelData in binding.labelsBasement.getSelectLabelDatas<MapFilterIdName>()) {
            basement?.add(selectLabelData.id)
        }

        listType?.clear()
        for (selectLabelData in binding.labelsType.getSelectLabelDatas<HouseType>()) {
            listType?.add(selectLabelData.position)
        }

        houseType?.clear()
        for (selectLabelData in binding.labelsPropertyType.getSelectLabelDatas<MapFilterHouseTypeFilter>()) {
            houseType?.add(selectLabelData.id)
        }

        bedroomRange?.clear()
        for (selectLabelData in binding.labelsBeadroom.getSelectLabelDatas<MapFilterIdName>()) {
            bedroomRange?.add(selectLabelData.id.toInt())
        }

        bathroomMin = binding.labelsBathroom.getSelectLabelDatas<MapFilterIdName>().getOrNull(0)?.id.toString()
        openHouseDate = binding.labelsOpenHouse.getSelectLabelDatas<MapFilterIdName>().getOrNull(0)?.id.toString()
        garageMin = binding.labelsGarageParking.getSelectLabelDatas<MapFilterIdName>().getOrNull(0)?.id.toString()

        description = binding.etDescription.text.toString().trim()
        fee = binding.etFee.text.toString().trim()


        val data = saveWatchPolygon
        val saveFilter = data.filter
        saveFilter.basement = basement.toMutableList()          //检查过
        saveFilter.bathroom_min = bathroomMin.toInt()           //检查过
        saveFilter.bedroom_range = bedroomRange.toMutableList()//检查过
        saveFilter.description = description                   //检查过
        saveFilter.front_feet = mutableListOf(feetLeft, feetRight)//检查过
        saveFilter.garage_min = garageMin.toInt()             //检查过
        saveFilter.house_type = houseType                     //检查过
        saveFilter.list_type = listType                       //检查过
        saveFilter.max_maintenance_fee = fee                  //检查过
        saveFilter.open_house_date = openHouseDate.toInt()   //检查过
        saveFilter.listing_price = mutableListOf(binding.saleSb.priceLeft, binding.saleSb.priceRight)//检查过
//        saveFilter.show_comparision = 0
//        saveFilter.show_history = 0
        saveFilter.square_footage = mutableListOf(squareFootageLeft, squareFootageRight)//检查过
        if (lotSizeFilter != null) {
            val lotSizeLeftModel = HSUtil.getLotSize(lotSizeLeft)
            val lotSizeRightModel = HSUtil.getLotSize(lotSizeRight)
            saveFilter.lot_size = mutableListOf(lotSizeLeftModel?.value?:0, lotSizeRightModel?.value?:0)//检查过
        }

        if (buildingAgeFilter != null) {
            val buildingAgeLeftModel = HSUtil.getBuildingAge(buildingAgeLeft)
            val buildingAgeRightModel = HSUtil.getBuildingAge(buildingAgeRight)
            saveFilter.building_age = mutableListOf(buildingAgeLeftModel?.value?:0, buildingAgeRightModel?.value?:0)
        }
    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        val windowParams = window?.attributes
        windowParams?.dimAmount = 0.3f // 遮罩效果消失
        window?.setLayout(-1, -2)
        windowParams?.gravity = Gravity.BOTTOM
        window?.attributes = windowParams
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        getDialog()?.let { destroyImmersionBar(it) }
        activity?.let {
            loadingDialog?.dismiss()
        }
    }

}


