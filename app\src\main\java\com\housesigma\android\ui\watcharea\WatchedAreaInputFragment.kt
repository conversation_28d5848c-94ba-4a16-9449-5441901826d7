package com.housesigma.android.ui.watcharea

import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ktx.destroyImmersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseDialogFragment
import com.housesigma.android.databinding.DialogWathedAreaInputBinding
import com.housesigma.android.model.DataSaveWatchPolygon
import com.housesigma.android.model.Polygon
import com.housesigma.android.model.WatchPolygon
import com.housesigma.android.ui.watched.WatchedViewModel
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.LoadingDialog
import org.maplibre.android.geometry.LatLng

class WatchedAreaInputFragment(
    cb: WatchedAreaInputCallback,
    polygon1: HashMap<String, Pair<LinkedHashMap<String, LatLng>, Boolean>> =
        HashMap(),
//    polygon2: Polygon,
//    polygon3: Polygon,
//    polygon4: Polygon,
//    polygon5: Polygon,
    editWatchedPolygon: WatchPolygon?
) : BaseDialogFragment() {

    private val imm: InputMethodManager? by lazy { activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager? }

    private var mCallback: WatchedAreaInputCallback = cb
    private lateinit var watchedViewModel: WatchedViewModel
    private var loadingDialog: LoadingDialog? = null
    private var polygon1: HashMap<String, Pair<LinkedHashMap<String, LatLng>, Boolean>> = polygon1
//    private var polygon2: Polygon = polygon2
//    private var polygon3: Polygon = polygon3
//    private var polygon4: Polygon = polygon4
//    private var polygon5: Polygon = polygon5

    private var editWatchedPolygon: WatchPolygon? = editWatchedPolygon
    private var dataSaveWatchPolygon: DataSaveWatchPolygon? = null

    private lateinit var binding: DialogWathedAreaInputBinding

    interface WatchedAreaInputCallback {
        fun onSuccess(dataSaveWatchPolygon: DataSaveWatchPolygon, showAddEmail:Int)

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreen)
    }

    private fun hideSoftInput() {
        imm?.hideSoftInputFromWindow(binding.etAreaName.windowToken, 0)
    }

    override fun onPause() {
        super.onPause()


    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        binding = DialogWathedAreaInputBinding.inflate(inflater, container, false)
        initViews(binding)
        initData()
        dialog?.setCanceledOnTouchOutside(false)
        return binding.root
    }

    private fun initData() {
        watchedViewModel.saveWatchPolygon.observe(this) {
            mCallback.onSuccess(it.data,it.show_add_email?:0)
            hideSoftInput()
            dismiss()

        }


        watchedViewModel.updateWatchPolygon.observe(this) {
            mCallback.onSuccess(dataSaveWatchPolygon!!,0)
            hideSoftInput()
            dismiss()

        }

        watchedViewModel.loadingLiveData.observe(this) {
            loadingDialog?.let {
                if (it.isShowing){
                    it.dismiss()
                }
            }
        }
    }

    private fun initViews(binding: DialogWathedAreaInputBinding) {
        if (editWatchedPolygon != null) {
            binding.etAreaName.setText(editWatchedPolygon?.description!!)
        }


        binding.tvSave.setOnClickListener {
            GALog.log("save_name_submit")
            val areaName = binding.etAreaName.text.toString().trim()
            if (!TextUtils.isEmpty(areaName)) {
                val points = polygon1.values
                if (points.size == 0) {
                    ToastUtils.showLong("Map is not ready yet.")
                    return@setOnClickListener
                }
                val latLngs: List<List<Polygon>> = points.map {
                    val items =
                        it.first.values.toList().map { it -> Polygon(it.latitude, it.longitude) }
                            .toMutableList()
                    items
                }
                activity?.let {
                    loadingDialog = LoadingDialog(it)
                    loadingDialog?.show()
                }

                val list = latLngs.get(0)
                Logger.d("lat lngs : "+latLngs)
                if (editWatchedPolygon == null) {
                    watchedViewModel.saveWatchPolygon(
                        areaName,
                        polygon = list,
                    )
                } else {

                    dataSaveWatchPolygon = DataSaveWatchPolygon(
                        id = editWatchedPolygon!!.id,
                        polygon = list,
                        description = areaName,
                        filter = editWatchedPolygon!!.filter
                    )



                    watchedViewModel.updateWatchPolygon(dataSaveWatchPolygon!!)
                }

            }
        }
    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        val windowParams = window?.attributes
//        windowParams?.dimAmount = 0.0f // 遮罩效果消失
        window?.setLayout(-1, -2)
        windowParams?.gravity = Gravity.BOTTOM
        window?.attributes = windowParams
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        getDialog()?.let { destroyImmersionBar(it) }
        activity?.let {
            loadingDialog?.dismiss()
        }
    }

}


