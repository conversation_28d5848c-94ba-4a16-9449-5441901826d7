package com.housesigma.android.ui.watcharea

import android.content.DialogInterface
import android.graphics.Bitmap
import android.os.Bundle
import android.view.*
import androidx.lifecycle.ViewModelProvider
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.gyf.immersionbar.ktx.destroyImmersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseDialogFragment
import com.housesigma.android.databinding.DialogWathedAreaSuccessBinding
import com.housesigma.android.ui.map.propertype.PreconMapSettingDialog
import com.housesigma.android.ui.watched.WatchedViewModel
import com.housesigma.android.utils.ConstantsHelper
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.ScreenUtils
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.LoadingDialog
import org.maplibre.android.geometry.LatLng
import jp.wasabeef.glide.transformations.RoundedCornersTransformation

class WatchedAreaSuccessFragment : BaseDialogFragment() {

    private var mCallback: WatchedAreaSuccessCallback ?= null
    private lateinit var watchedViewModel: WatchedViewModel
    private var loadingDialog: LoadingDialog? = null
    private var watchedAreaName: String?=null
    private var centerLatLng: LatLng?=null
    private var cameraZoom: Double?=null
    private var polygonPath: String?=null

    interface WatchedAreaSuccessCallback {
        fun onSuccess()
    }

    companion object {
        fun newInstance(watchedAreaName: String? = "",
                        centerLatLng: LatLng,
                        cameraZoom: Double,
                        polygonPath: String? = null): WatchedAreaSuccessFragment {
            val args = Bundle()
            args.putString("watchedAreaName", watchedAreaName)
            args.putParcelable("centerLatLng", centerLatLng)
            args.putDouble("cameraZoom", cameraZoom)
            args.putString("polygonPath", polygonPath)
            val fragment = WatchedAreaSuccessFragment()
            fragment.arguments = args
            return fragment
        }
    }

    fun setWatchedAreaSuccessCallback(cb: WatchedAreaSuccessCallback){
        mCallback = cb
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreen)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        val binding = DialogWathedAreaSuccessBinding.inflate(inflater, container, false)
        centerLatLng = arguments?.getParcelable("centerLatLng")
        watchedAreaName = arguments?.getString("watchedAreaName")
        cameraZoom = arguments?.getDouble("cameraZoom")
        polygonPath = arguments?.getString("polygonPath")
        initViews(binding)
        initData()
        dialog?.setCanceledOnTouchOutside(false)
        return binding.root
    }

    private fun initData() {
        watchedViewModel.saveWatchPolygon.observe(this) {
            mCallback?.onSuccess()
            dismiss()
        }
    }

    private fun initViews(binding: DialogWathedAreaSuccessBinding) {
        binding.tvSaveName.text = "Saved as “$watchedAreaName”"
        binding.tvManageWatchArea.setOnClickListener {
            GALog.log("manage_watch_area_click")
            mCallback?.onSuccess()
            dismiss()
//            val areaName = binding.etAreaName.text.toString().trim()
//            if (!TextUtils.isEmpty(areaName)) {
//                activity?.let {
//                    loadingDialog = LoadingDialog(it)
//                    loadingDialog?.show()
//                }
//                watchedViewModel.saveWatchPolygon(areaName)
//            }
        }

        val topCorner = ScreenUtils.dpToPx(16f)
        val multi: MultiTransformation<Bitmap> = MultiTransformation(
            CenterCrop(),
            RoundedCornersTransformation(
                topCorner.toInt(),
                0,
                RoundedCornersTransformation.CornerType.ALL
            )
        )
        
        // 优先使用多边形路径构建URL
        val url = if (polygonPath != null && polygonPath!!.isNotEmpty()) {
            ConstantsHelper.getMapStatic() + "auto/<EMAIL>?path=$polygonPath&fill=rgba(40,163,179,0.4)&stroke=rgba(40,163,179,1)&width=1.5"
        } else {
            // 如果没有多边形路径，则使用旧的方式
            centerLatLng?.let { 
                ConstantsHelper.getMapStatic() + it.longitude + "," + it.latitude + "," + cameraZoom + "/<EMAIL>?ver=20211012" 
            } ?: ""
        }
        
        Logger.d(url)
        activity?.let {
            Glide.with(this)
                .load(url)
                .transform(multi)
                .error(R.drawable.shape_pic_place_holder)
                .placeholder(R.drawable.shape_pic_place_holder)
                .into(binding.ivPic)
        }
    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        val windowParams = window?.attributes
        windowParams?.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE //穿透事件
        windowParams?.dimAmount = 0.0f // 遮罩效果消失
        window?.setLayout(-1, -2)
        windowParams?.gravity = Gravity.BOTTOM
        window?.attributes = windowParams
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        getDialog()?.let { destroyImmersionBar(it) }
        activity?.let {
            loadingDialog?.dismiss()
        }
    }

}


