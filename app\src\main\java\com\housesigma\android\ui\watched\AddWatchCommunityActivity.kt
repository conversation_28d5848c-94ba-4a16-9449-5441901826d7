package com.housesigma.android.ui.watched

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.databinding.ActivityAddWatchCommunityBinding
import com.housesigma.android.databinding.PopWatchCommunityFilterBinding
import com.housesigma.android.helper.ProvinceHelper
import com.housesigma.android.model.*
import com.housesigma.android.network.NetClient
import com.housesigma.android.ui.map.MapViewModel
import com.housesigma.android.ui.map.helper.MapHelper
import com.housesigma.android.utils.*
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.HSAlertDialog
import com.housesigma.android.views.HSPopWindow
import com.housesigma.android.views.LoadingDialog
import com.housesigma.android.views.viewpagerindicator.view.indicator.Indicator
import com.housesigma.android.views.viewpagerindicator.view.indicator.slidebar.ColorBar
import com.housesigma.android.views.viewpagerindicator.view.indicator.transition.OnTransitionTextListener
import org.maplibre.android.MapLibre
import org.maplibre.android.annotations.Marker
import org.maplibre.android.annotations.Polygon
import org.maplibre.android.annotations.PolygonOptions
import org.maplibre.android.annotations.Polyline
import org.maplibre.android.annotations.PolylineOptions
import org.maplibre.android.camera.CameraPosition
import org.maplibre.android.camera.CameraUpdateFactory
import org.maplibre.android.geometry.LatLng
import org.maplibre.android.geometry.LatLngBounds
import org.maplibre.android.maps.MapView
import org.maplibre.android.maps.MapLibreMap
import org.maplibre.android.maps.Style
import org.maplibre.android.style.layers.RasterLayer
import org.maplibre.android.style.sources.RasterSource
import org.maplibre.android.style.sources.TileSet
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import permissions.dispatcher.*

@RuntimePermissions
class AddWatchCommunityActivity : AppCompatActivity() {

    private lateinit var mapView: MapView
    private lateinit var mapLibreMap: MapLibreMap
    private lateinit var mapboxStyle: Style
    private lateinit var mapViewModel: MapViewModel
    private var isSatellite: Boolean = false


    private lateinit var binding: ActivityAddWatchCommunityBinding
    private lateinit var watchedViewModel: WatchedViewModel
    private var myAdapter = MyAdapter(this)
    private lateinit var popWindow: HSPopWindow
    private var regionFilter: List<RegionFilter>? = null
    private var municipalityFilter: List<MunicipalityFilter>? = null
    private var communityFilter: List<CommunityFilterX>? = null
    private var communityPolygon: Polygon? = null
    private var communityPolyline: Polyline? = null

    // 第二级
    private var municipalityList: List<Municipality> = ArrayList()

    // 第三级
    private var communityFilterItem: List<CommunityFilterItem> = ArrayList()

    //默认最后一级选择的id 默认14
    private var id: Int = 14
    private var houseType: String = "D."


    private lateinit var mapHelper: MapHelper
    private var lon1: Double = 0.0
    private var lon2: Double = 0.0
    private var lat1: Double = 0.0
    private var lat2: Double = 0.0
    private var cameraZoom: Double = 0.0

    // 各个api 同等类型的marker单独储存，去重逻辑单独处理 优势 更清晰好维护
    private var hashMapMapInfo = HashMap<Long, MapMarkerInfo>() //listing2

    override fun onResume() {
        super.onResume()
        GALog.page("add_watched_community")
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        MapLibre.getInstance(this)
        super.onCreate(savedInstanceState)
        mapViewModel = ViewModelProvider(this).get(MapViewModel::class.java)
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        binding = ActivityAddWatchCommunityBinding.inflate(layoutInflater)
        setContentView(binding.root)

        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.color_white)
            statusBarDarkFont(true)
        }


        initMap(savedInstanceState)
        initViews()
        initData()
    }

    private fun initMap(savedInstanceState: Bundle?) {
        val styleUrl = "https://map.housesigma.com/styles/maptiler-street-v3/style.json"
        mapView = binding.mapView
        mapView.onCreate(savedInstanceState)
        mapView.getMapAsync { map ->
            mapLibreMap = map
            mapLibreMap.uiSettings.isRotateGesturesEnabled = false
            mapLibreMap.setMaxPitchPreference(0.0)//关闭仰角
            mapLibreMap.setMinPitchPreference(0.0)
            mapHelper = MapHelper(AddWatchCommunityActivity@this, layoutInflater, mapLibreMap)
            setUpMapboxMap(styleUrl)
        }

        binding.icMapToolLocation.setOnClickListener {
            // 申请定位
            requestLocationWithPermissionCheck()

        }
        binding.icMapToolSatellite.setOnClickListener {
            if (isSatellite) {
                binding.icMapToolSatellite.setBackgroundResource(R.drawable.ic_map_tool_satellite)
                mapLibreMap.style?.removeLayer(RasterLayer("satellite", "satellite-source"))
            } else {
                binding.icMapToolSatellite.setBackgroundResource(R.drawable.ic_map_map)
                val size = mapLibreMap.style?.layers?.size
                if (size != null) {
                    mapLibreMap.style?.addLayerAt(
                        RasterLayer("satellite", "satellite-source"),
                        size - 1
                    )
                }
            }
            isSatellite = !isSatellite
        }
    }

    private fun setUpMapboxMap(styleUrl: String) {
        mapLibreMap.setMaxZoomPreference(20.0)
        mapLibreMap.setMinZoomPreference(4.0)

        mapLibreMap.setStyle(styleUrl, object : Style.OnStyleLoaded {
            override fun onStyleLoaded(style: Style) {
                mapboxStyle = style
                style.addSource(
                    RasterSource(
                        "satellite-source",
                        TileSet(
                            "tileset",
                            "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
                        ), 256
                    )
                )

                addOnCameraMoveListener()
            }
        })
    }


    private fun moveCamera(lat: Double, lon: Double) {
        mapLibreMap.cameraPosition = CameraPosition.Builder()
            .target(LatLng(lat, lon))
            .zoom(15.0)
            .build()
    }

    private fun initData() {
        val loadingDialog = LoadingDialog(this)
        watchedViewModel.addWatchCommunityMsg.observe(this) {
            ToastUtils.showLong(it.message)
            EventBus.getDefault().postSticky(MessageEvent(MessageType.WATCHED_COMMUNITIES_CHANGED))
            finish()
        }

        watchedViewModel.initCommunityFilterList.observe(this) {
            loadingDialog.dismiss()
            handleFilterData(it)
        }

        mapViewModel.communityDetail.observe(this) {
            showCommunityBoundaries(it)
        }

        mapViewModel.mapList.observe(this) { that ->
            CoroutineScope(Dispatchers.Main).launch {
                handleMapListing(that)
            }
        }

        loadingDialog.show()
        watchedViewModel.initCommunityFilter()
    }

    private fun showCommunityBoundaries(communityDetail: CommunityDetail){
        try {
            communityPolygon?.let { mapLibreMap.removePolygon(it) }
            communityPolyline?.let { mapLibreMap.removePolyline(it) }
            communityDetail.boundary?.let {
                val polygon = ArrayList<LatLng>()
                val polyline = ArrayList<LatLng>()
                for (point in it.coordinates[0]) {
                    val latLng = LatLng(point[1], point[0])
                    polyline.add(latLng)
                    polygon.add(latLng)
                }
                communityPolygon = mapLibreMap.addPolygon(
                    PolygonOptions()
                        .addAll(polygon)
                        .fillColor(Color.parseColor("#28a3b3"))
                        .alpha(0.2f)
                )
                communityPolyline = mapLibreMap.addPolyline(
                    PolylineOptions()
                        .addAll(polyline)
                        .color(Color.parseColor("#28a3b3"))
                        .width(3.0f)
                        .alpha(1f)
                )

                // 移动到能显示下communityPolygon的zoom
                val bounds = LatLngBounds.Builder()
                communityPolygon?.points?.forEach {
                    bounds.include(it)
                }
                if (communityPolygon != null) {
                    val latLngBounds = bounds.build()
                    val cameraUpdate = CameraUpdateFactory.newLatLngBounds(latLngBounds, 50)
                    mapLibreMap.moveCamera(cameraUpdate)
                }
            }
        } catch (e:Exception) {
            e.printStackTrace()
        }
    }


    private suspend fun handleMapListing(that: MapList) {
        val notSameMapList = ArrayList<Marker>()
        // api返回的marker，判断现有marker列表中有无，
        // 如果marker列表中没有，需要添加上
        // 如果marker列表中有，则不需要添加
        // 只需要保留比较本地和网络重叠的部分，去掉不同的。
        for (entry in hashMapMapInfo) {
            var find = false
            for (mapMarkerInfo in that.list) {
                if (entry.value.equals(mapMarkerInfo)) {
                    find = true
                    break
                }
            }
            if (!find) {
                entry.value.localMarker?.let {
                    notSameMapList.add(it)
                }
            }
        }

        notSameMapList.forEach {
            hashMapMapInfo.remove(it.id)
        }


        mapLibreMap.removeAnnotations(notSameMapList)


        val start = System.currentTimeMillis()

        that.list.forEach {
            var find = false
            for (mutableEntry in hashMapMapInfo) {
                if (mutableEntry.value.equals(it)) {
                    find = true
                    Logger.e( "find same element in hashMapMapInfo")
                    break
                }
            }

            if (!find) {
                var marker: Marker? = null
                if ("sold".equals(it.marker)) {
                    marker = mapHelper.addSoldMarker(it)
                } else if ("de-listed".equals(it.marker)) {
                    marker = mapHelper.addDeListedMarker(it)
                } else if ("active".equals(it.marker)) {
                    marker = mapHelper.addSaleMarker(it)
                } else if ("normal".equals(it.marker)) {
                    marker = mapHelper.addGroupMarker(it)
                }

                marker?.let { markerThat ->
                    it.localMarker = markerThat
                    hashMapMapInfo.put(
                        markerThat.id, it
                    )
                }
            }
        }
        val end = System.currentTimeMillis()
        Logger.d( "function cast time is " + (end - start));



    }

    /**
     * 监听camera move，如在60ms内有变化，就可以获取屏幕可见经纬度范围，请求api接口
     */
    private fun addOnCameraMoveListener() {
        getMapRequestRegion()

//        https://stackoverflow.com/questions/38727517/oncamerachangelistener-is-deprecated
//        用addOnCameraIdleListener监听代替OnCameraChangeListener
        mapLibreMap.addOnCameraIdleListener {
//            if (!ready) return@addOnCameraIdleListener
            Logger.e( "move camera idle.........")
            // 可见区域
            getMapRequestRegion()
            reloadMapData()
        }
    }

    private val mHandler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
        }
    }

    private fun reloadMapData(){
        val function: () -> Unit = {
            debounceReloadMapData()
        }
        NetClient.cancelCallWithTag("map_listing2")
        mHandler.removeCallbacksAndMessages(null)
        mHandler.postDelayed(kotlinx.coroutines.Runnable(function), 250)
    }

    private fun debounceReloadMapData() {
//        Logger.e( "debounceReloadMapData2: "+System.currentTimeMillis())
        if (lat1 == 0.0) return
        val cameraZoom = mapHelper.getCameraPositionZoom(mapLibreMap)
        //// 固定字段
//            listing_price: [0, 0],
//            rent_price: [0, 0],
//            open_house_date: 0,
//            max_maintenance_fee: 0,
//// 动态字段
//            zoom:
//            house_type:
//            lat1:
//            lat2:
//            lon1:
//            lon2:
        mapViewModel.getMapListing2(
            houseType = arrayListOf(houseType),
            lon1 = lon1,
            lon2 = lon2,
            lat1 = lat1,
            lat2 = lat2,
            zoom = cameraZoom-1,
            listing_days  = "0",
            de_list_days = "90",
            list_type = arrayListOf("1","3"),
            basement = arrayListOf(),
            bedroom_range =arrayListOf("0"),
            open_house_date = "0",
            bathroom_min = "0",
            garage_min = "0",
            description = "",
            max_maintenance_fee = "0",
            price =  arrayListOf(),
            front_feet = arrayListOf("0","100"),
            square_footage = arrayListOf("0","4000"),
            listing_type = arrayListOf(),
            lot_size = null,
            building_age = null
        )
    }

    private fun getMapRequestRegion() {
        val visibleRegion = mapLibreMap.projection.visibleRegion
//        val farLeft = visibleRegion.farLeft //可视区域的左上角。
//        val nearRight = visibleRegion.nearRight //可视区域的右下角。
        val bounds = visibleRegion.latLngBounds
        cameraZoom = mapHelper.getCameraPositionZoom(mapLibreMap)


        bounds.let {
            val west = it.longitudeWest
            val south = it.latitudeSouth
            val east = it.longitudeEast
            val north = it.latitudeNorth

            val extraLat = Math.abs(south - north) / 2
            val extraLon = Math.abs(west - east) / 2

            //扩大后的经纬度，linting接口需要
            lat1 = it.latitudeNorth + (if (north > south) extraLat else -extraLat)
            lon1 = it.longitudeEast + (if (east > west) extraLon else -extraLon)
            lat2 = it.latitudeSouth + (if (south > north) extraLat else -extraLat)
            lon2 = it.longitudeWest + (if (west > east) extraLon else -extraLon)
        }

    }

    private fun handleFilterData(it: CommunityFilter) {
        communityFilter = it.community_filter
        var houseTypeFilter = it.housetype_filter
        municipalityFilter = it.municipality_filter
        regionFilter = it.region_filter

        var defaultRegionId = it.default_region_id
        var defaultMunicipalityId = it.default_municipality_id

        binding.tvFilterRegion.text = defaultRegionId

        regionFilter?.forEach {
            if (it.id == defaultRegionId) {
                it.checked = true
                municipalityFilter?.forEach {
                    if (it.region.id == defaultRegionId) {
                        municipalityList = it.municipality_list
                        municipalityList?.forEach {
                            if (it.id == defaultMunicipalityId) {
                                binding.tvFilter2.text = it.name
                                it.checked = true
                            }
                        }
                        communityFilter?.forEach {
                            if ((it as CommunityFilterX).id_municipality == defaultMunicipalityId) {
                                communityFilterItem = it.list
                            }
                        }
                    }
                }
            }
        }

        if (communityFilterItem?.size != 0) {
            val communityItem = communityFilterItem[0]
            communityItem.checked = true
            binding.tvFilter3.text = communityItem.name
            id = communityItem.id

            // 请求api/community/detail接口，然后根据boundary字段绘制一个多边形
            mapViewModel.getCommunityDetail(id.toString())
            moveCamera(communityItem.map.lat, communityItem.map.lon)
        } else {
            id = -1
        }

        houseType = houseTypeFilter[0].id
        myAdapter.tabNames = houseTypeFilter
        binding.tabMainIndicator.onTransitionListener = OnTransitionTextListener().setColor(
            resources.getColor(R.color.app_main_color),
            resources.getColor(R.color.color_black)
        ).setSize(15f, 15f)

        binding.tabMainIndicator.setScrollBar(
            ColorBar(
                this,
                resources.getColor(R.color.app_main_color),
                10
            )
        )
        binding.tabMainIndicator.setAdapter(myAdapter)

        binding.tabMainIndicator.onIndicatorItemClickListener = object :
            Indicator.OnIndicatorItemClickListener {
            override fun onItemClick(clickItemView: View?, position: Int): Boolean {
//                Logger.d("onItemClick..."+position)
                houseType = houseTypeFilter[position].id

                reloadMapData()
                return false
            }


        }
    }

    private fun initViews() {
        binding.ivClose.setOnClickListener {
            finish()
        }

        binding.tvAddWatchCommunity.setOnClickListener {
            // TODO add filter
            if (id != -1) {
                watchedViewModel.addWatchCommunity(houseType, id)
            }
        }

        binding.llFilterRegion.setOnClickListener {
            if (regionFilter == null) return@setOnClickListener
            val inflate = PopWatchCommunityFilterBinding.inflate(layoutInflater)
            var adapter = WatchCommunityFilter1Adapter()
            adapter.addChildClickViewIds(R.id.cb_filter)
            adapter.setOnItemChildClickListener { adapter, view, position ->

                val item = adapter.getItem(position) as RegionFilter
                when (view.id) {
                    R.id.cb_filter -> {
                        popWindow.dissmiss()
                        //clearCheck
                        adapter.data.forEach {
                            (it as RegionFilter).checked = false
                        }
                        item.checked = true
                        binding.tvFilterRegion.text = item.name

                        var findLevel2 = false
                        // 找到和selectRegionFilter相匹配的下一级list
                        municipalityFilter?.forEach {
                            if ((it as MunicipalityFilter).region.id == item.id) {
                                municipalityList = it.municipality_list
                                findLevel2 = true
                            }
                        }

                        if (!findLevel2) {
                            municipalityList = emptyList()
                        }
                        var municipalityItem: Municipality? = null
                        if (municipalityList.size != 0) {
                            municipalityItem = municipalityList[0]
                            municipalityItem.checked = true
                            binding.tvFilter2.text = municipalityItem.name
                        } else {
                            binding.tvFilter2.text = ""
                        }

                        if (municipalityItem == null) {
                            return@setOnItemChildClickListener
                        }


                        // 找到相匹配的下一级list
                        var findLevel3 = false
                        communityFilter?.forEach {
                            if ((it as CommunityFilterX).id_municipality == municipalityItem.id) {
                                communityFilterItem = it.list
                                findLevel3 = true
                            }
                        }

                        if (!findLevel3) {
                            communityFilterItem = emptyList()
                        }

                        if (communityFilterItem.size != 0) {
                            var communityItem = communityFilterItem[0]
                            communityItem.checked = true
                            binding.tvFilter3.text = communityItem.name

                            id = communityItem.id
                            setAddWatchCommunityStatus(true)

                            // 请求api/community/detail接口，然后根据boundary字段绘制一个多边形
                            mapViewModel.getCommunityDetail(id.toString())
                            moveCamera(communityItem.map.lat, communityItem.map.lon)
                        } else {
                            binding.tvFilter3.text = ""

                            id = -1

                            setAddWatchCommunityStatus(false)
                        }


                    }
                }
            }
            inflate.rv.adapter = adapter
            inflate.rv.layoutManager =
                LinearLayoutManager(AddWatchCommunityActivity@ this, RecyclerView.VERTICAL, false)
            adapter.addData(regionFilter!!)
            popWindow = HSPopWindow.PopupWindowBuilder(this)
                .setView(inflate.root)
                .size(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                .enableBackgroundDark(false) //弹出popWindow时，背景是否变暗
                .create()
                .showAsDropDown(binding.vLine, 0, 0)
        }





        binding.llFilter2.setOnClickListener {
            if (municipalityList == null) return@setOnClickListener
            val inflate = PopWatchCommunityFilterBinding.inflate(layoutInflater)
            var adapter = WatchCommunityFilter2Adapter()
            adapter.addChildClickViewIds(R.id.cb_filter)
            adapter.setOnItemChildClickListener { adapter, view, position ->

                val item = adapter.getItem(position) as Municipality
                when (view.id) {
                    R.id.cb_filter -> {
                        popWindow.dissmiss()
                        //clearCheck
                        adapter.data.forEach {
                            (it as Municipality).checked = false
                        }
                        item.checked = true
                        binding.tvFilter2.text = item.name


                        // 找到相匹配的下一级list
                        var findLevel3 = false
                        communityFilter?.forEach {
                            if ((it as CommunityFilterX).id_municipality == item.id) {
                                communityFilterItem = it.list
                                findLevel3 = true
                            }
                        }

                        if (!findLevel3) {
                            communityFilterItem = emptyList()
                        }

                        if (communityFilterItem.size != 0) {
                            var communityItem = communityFilterItem[0]
                            communityItem.checked = true
                            binding.tvFilter3.text = communityItem.name
                            id = communityItem.id
                            setAddWatchCommunityStatus(true)

                            // 请求api/community/detail接口，然后根据boundary字段绘制一个多边形
                            mapViewModel.getCommunityDetail(id.toString())
                            moveCamera(communityItem.map.lat, communityItem.map.lon)
                        } else {
                            binding.tvFilter3.text = ""
                            id = -1
                            setAddWatchCommunityStatus(false)
                        }
                    }
                }
            }
            inflate.rv.adapter = adapter
            inflate.rv.layoutManager =
                LinearLayoutManager(AddWatchCommunityActivity@ this, RecyclerView.VERTICAL, false)
            adapter.addData(municipalityList)
            popWindow = HSPopWindow.PopupWindowBuilder(this)
                .setView(inflate.root)
                .size(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                .enableBackgroundDark(false) //弹出popWindow时，背景是否变暗
                .create()
                .showAsDropDown(binding.vLine, 0, 0)
        }




        binding.llFilter3.setOnClickListener {
            if (communityFilterItem.isEmpty()) return@setOnClickListener
            val inflate = PopWatchCommunityFilterBinding.inflate(layoutInflater)
            var adapter = WatchCommunityFilter3Adapter()
            adapter.addChildClickViewIds(R.id.cb_filter)
            adapter.setOnItemChildClickListener { adapter, view, position ->

                val item = adapter.getItem(position) as CommunityFilterItem
                when (view.id) {
                    R.id.cb_filter -> {
                        popWindow.dissmiss()
                        //clearCheck
                        adapter.data.forEach {
                            (it as CommunityFilterItem).checked = false
                        }
                        item.checked = true
                        binding.tvFilter3.text = item.name
                        id = item.id

                        // 请求api/community/detail接口，然后根据boundary字段绘制一个多边形
                        mapViewModel.getCommunityDetail(id.toString())
                        moveCamera(item.map.lat, item.map.lon)
                        setAddWatchCommunityStatus(true)
                    }
                }
            }
            inflate.rv.adapter = adapter
            inflate.rv.layoutManager =
                LinearLayoutManager(AddWatchCommunityActivity@ this, RecyclerView.VERTICAL, false)
            adapter.addData(communityFilterItem)
            popWindow = HSPopWindow.PopupWindowBuilder(this)
                .setView(inflate.root)
                .size(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                .enableBackgroundDark(false) //弹出popWindow时，背景是否变暗
                .create()
                .showAsDropDown(binding.vLine, 0, 0)
        }
    }

    private fun setAddWatchCommunityStatus(status: Boolean) {
        if (status) {
            binding.vNoData.visibility = View.GONE
            binding.tvNoData.visibility = View.GONE
            binding.tvAddWatchCommunity.setBackgroundResource(R.drawable.shape_10radius_main_color_fill)
            binding.tvAddWatchCommunity.setTextColor(resources.getColor(R.color.color_white))
        } else {
            binding.vNoData.visibility = View.VISIBLE
            binding.tvNoData.visibility = View.VISIBLE
            binding.tvAddWatchCommunity.setBackgroundResource(R.drawable.shape_10radius_gray_fill)
            binding.tvAddWatchCommunity.setTextColor(resources.getColor(R.color.color_gray))
        }
    }


    inner class MyAdapter(context: Context) :
        Indicator.IndicatorAdapter() {
        lateinit var tabNames: List<CommunityFilterHouseTypeFilter>

        private var context: Context = context

        override fun getCount(): Int {
            return tabNames.size
        }

        override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
            var convertView = convertView
            if (convertView == null) {
                convertView = LayoutInflater.from(context)
                    .inflate(R.layout.tab_main, parent, false)
            }
            val textView = convertView as TextView
            textView.text = "  ".plus(tabNames[position].name).plus("  ")
            return textView
        }

    }


    @SuppressLint("MissingPermission")
    @NeedsPermission(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    fun requestLocation() {
        if (!LocationUtils.isLocServiceEnable(this)) {
            HSAlertDialog(
                this, "Location Permission Required", "Please enable location permissions in settings.",
                "Cancel", "Settings",
                object : HSAlertDialog.HSAlertCallback {
                    override fun onSuccess() {
                        val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                        startActivityForResult(intent,0)
                    }
                }).show()
            return
        }

        getHSLocation()
    }

    /**
     * 先使用android自带定位api简单判断经纬度是否在指定范围内
     * 如果在范围内，则开启mapbox我的位置功能，mapbox我的位置功能使用的是play框架的混合定位
     * 反复测试过，以上这种效果最好
     */
    private fun getHSLocation() {
        val netWorkLocation = LocationUtils.getNetWorkLocation(this)
        if (netWorkLocation!=null) {
            moveRightRegion(netWorkLocation)
        }
        LocationUtils.addLocationListener(
            this,
            LocationManager.NETWORK_PROVIDER,
            object : LocationListener,
                LocationUtils.ILocationListener {
                override fun onSuccess(location: Location?) {
                    LocationUtils.unRegisterListener(this@AddWatchCommunityActivity)
                    moveRightRegion(location)
                }

                override fun onFail() {
                    moveRightRegion(null)
                }

                override fun onLocationChanged(location: Location) {
                }
            })
    }


    /**
     * 移动到正确位置
     * @param resultLocation 位置信息
     */
    private fun moveRightRegion(resultLocation: Location?) {
        if (resultLocation != null) {
            // 跳转到我的位置 但是有限制，不会跳出省
            // 超出界限时，将定位到默认地址 ，目前是多伦多
            val neLatLng = LatLng(65.0, -50.0)
            val swLatLng = LatLng(40.0, -137.0)
            //           BC @49.246292,-123.116226
            //           ON @43.955259, -79.346008
            var FAILBACK_LATLON = LatLng(43.955259, -79.346008)

            val abbreviationFromCache = ProvinceHelper.getAbbreviationFromCache("ON")
            if ("ON".equals(abbreviationFromCache)) {
                FAILBACK_LATLON = LatLng(43.955259, -79.346008)
            } else if ("BC".equals(abbreviationFromCache)) {
                FAILBACK_LATLON = LatLng(49.246292, -123.116226)
            } else if ("AB".equals(abbreviationFromCache)) {
                FAILBACK_LATLON = LatLng(51.045005, -114.072129)
            }

            val FAILBACK_ZOOM = 7.0

            if (resultLocation != null) {
                var latitude = resultLocation.latitude
                var longitude = resultLocation.longitude
                var zoom = MapHelper.zoom - 1
                if (!MapUtils.inBounds(
                        LatLng(
                            latitude,
                            longitude
                        ), neLatLng, swLatLng
                    )
                ) {
                    latitude = FAILBACK_LATLON.latitude
                    longitude = FAILBACK_LATLON.longitude
                    zoom = FAILBACK_ZOOM
                    mapLibreMap.cameraPosition = CameraPosition.Builder()
                        .target(
                            LatLng(
                                latitude,
                                longitude
                            )
                        )
                        .zoom(zoom)
                        .build()
                } else {
                    zoom = 14.0
                    mapLibreMap.cameraPosition = CameraPosition.Builder()
                        .zoom(zoom)
                        .build()
                    mapHelper.showMapboxLocationComponent(mapLibreMap)
                }
            }
        }
    }


    @OnShowRationale(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    fun showRationaleForRequestLocation(request: PermissionRequest) {
    }

    @OnPermissionDenied(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    fun requestLocationDenied() {
    }

    @OnNeverAskAgain(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    fun requestLocationNeverAskAgain() {
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        onRequestPermissionsResult(requestCode, grantResults)
    }


}