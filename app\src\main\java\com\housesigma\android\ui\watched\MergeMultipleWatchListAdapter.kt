package com.housesigma.android.ui.watched

import android.app.Activity
import android.graphics.Bitmap
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.MultipleWatchItem
import com.housesigma.android.model.NetResponse
import com.housesigma.android.model.PolygonPhoto
import com.housesigma.android.network.NetClient
import com.housesigma.android.utils.ScreenUtils
import jp.wasabeef.glide.transformations.RoundedCornersTransformation
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response


class MergeMultipleWatchListAdapter :
    BaseMultiItemQuickAdapter<MultiItemEntity, BaseViewHolder>() {

    init {
        addItemType(0, R.layout.item_multiple_watch_list)
        addItemType(1, R.layout.item_share_watch_list)
    }

    override fun convert(holder: BaseViewHolder, item: MultiItemEntity) {
        when (item?.itemType) {
            0 -> {
                item as MultipleWatchItem
                val multiTransformation: MultiTransformation<Bitmap> = MultiTransformation(
                    CenterCrop(),
                    RoundedCornersTransformation(
                        ScreenUtils.dpToPx(8f).toInt(),
                        0,
                        RoundedCornersTransformation.CornerType.ALL
                    )
                )

                if (item.isPublic()) {
                    holder.setText(R.id.tv_privacy, "Shareable")
                    holder.setImageResource(R.id.iv_privacy, R.drawable.ic_watched_public)
                } else if (item.isPrivate()) {
                    holder.setText(R.id.tv_privacy, "Private")
                    holder.setImageResource(R.id.iv_privacy, R.drawable.ic_watched_private)
                }

                if (item.is_default == 1) {
                    holder.setText(R.id.tv_watched_name, "Default")
                    holder.setGone(R.id.iv_watchlist_menu, true)
                } else {
                    holder.setText(R.id.tv_watched_name, item.name ?: "")
                    holder.setVisible(R.id.iv_watchlist_menu, true)
                }

                if (item.watched_count == 1) {
                    holder.setText(
                        R.id.tv_watched_count,
                        item.watched_count.toString() + " Property"
                    )
                } else {
                    holder.setText(
                        R.id.tv_watched_count,
                        item.watched_count.toString() + " Properties"
                    )
                }
                if (!item.is_load_photo) {
                    loadHousePic("", multiTransformation, holder)
                    NetClient.apiService.getWatchListPhoto(item.id).enqueue(object :
                        Callback<NetResponse<PolygonPhoto>> {
                        override fun onResponse(
                            call: Call<NetResponse<PolygonPhoto>>,
                            response: Response<NetResponse<PolygonPhoto>>
                        ) {
                            try {
                                // DEV-4455 server端返回的response body在一定情况下（原因不明），有可能是null，所以这里不能强转，会发生crashed
                                val photoUrl =
                                    (response.body() as NetResponse<PolygonPhoto>)?.data?.photo_url
                                item.photo_url = photoUrl ?: ""
                                item.is_load_photo = true
                                loadHousePic(item.photo_url, multiTransformation, holder)
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                        }

                        override fun onFailure(
                            call: Call<NetResponse<PolygonPhoto>>,
                            t: Throwable
                        ) {
                            item.photo_url = ""
                            item.is_load_photo = true
                            loadHousePic(item.photo_url!!, multiTransformation, holder)
                        }
                    })
                } else {
                    loadHousePic(item.photo_url, multiTransformation, holder)
                }
            }

            1 -> {
                item as MultipleWatchItem

                if (item.is_head == true) {
                    holder.setVisible(R.id.tv_watchlist_share_title, true)
                } else {
                    holder.setGone(R.id.tv_watchlist_share_title, true)
                }
                val multiTransformation: MultiTransformation<Bitmap> = MultiTransformation(
                    CenterCrop(),
                    RoundedCornersTransformation(
                        ScreenUtils.dpToPx(8f).toInt(),
                        0,
                        RoundedCornersTransformation.CornerType.ALL
                    )
                )

                if (item.isPrivate()) {
                    holder.setVisible(R.id.tv_listing_owner_name, true)
                    holder.setText(R.id.tv_listing_owner_name, "This list has been deleted or set to private")

                    holder.setGone(R.id.tv_watchlist_name, true)
                    holder.setGone(R.id.tv_updated_time, true)
                    holder.setGone(R.id.ll_privacy, true)

                } else if (item.isPublic()) {
                    holder.setVisible(R.id.tv_watchlist_name, true)
                    holder.setText(R.id.tv_watchlist_name, item.name ?: "")

                    holder.setVisible(R.id.tv_listing_owner_name, true)
                    holder.setVisible(R.id.tv_updated_time, true)
                    holder.setGone(R.id.ll_privacy, true)

                    val watchedCount = item.watched_count ?: 0
                    val ownerName = item.owner_name ?: ""
                    holder.setVisible(R.id.tv_listing_owner_name, true)
                    if (item.watched_count <= 1) {
                        holder.setText(
                            R.id.tv_listing_owner_name,
                            "$watchedCount Property - $ownerName"
                        )
                    } else {
                        holder.setText(
                            R.id.tv_listing_owner_name,
                            "$watchedCount Properties - $ownerName"
                        )
                    }

                    val statusChgDateText = item.status_chg_date_text ?: ""
                    holder.setText(R.id.tv_updated_time, statusChgDateText)
                }



                if (!item.is_load_photo) {
                    loadHousePic("", multiTransformation, holder)
                    NetClient.apiService.getWatchListPhoto(item.id).enqueue(object :
                        Callback<NetResponse<PolygonPhoto>> {
                        override fun onResponse(
                            call: Call<NetResponse<PolygonPhoto>>,
                            response: Response<NetResponse<PolygonPhoto>>
                        ) {
                            try {
                                // DEV-4455 server端返回的response body在一定情况下（原因不明），有可能是null，所以这里不能强转，会发生crashed
                                val photoUrl =
                                    (response.body() as NetResponse<PolygonPhoto>)?.data?.photo_url
                                item.photo_url = photoUrl ?: ""
                                item.is_load_photo = true
                                loadHousePic(item.photo_url, multiTransformation, holder)
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                        }

                        override fun onFailure(
                            call: Call<NetResponse<PolygonPhoto>>,
                            t: Throwable
                        ) {
                            item.photo_url = ""
                            item.is_load_photo = true
                            loadHousePic(item.photo_url!!, multiTransformation, holder)
                        }
                    })
                } else {
                    loadHousePic(item.photo_url, multiTransformation, holder)
                }
            }

        }
    }


    private fun loadHousePic(
        picUrl: String?,
        multi: MultiTransformation<Bitmap>,
        holder: BaseViewHolder
    ) {
        try {
            if (context == null) return
            if (context is Activity) {
                val activity = context as Activity
                if (!activity.isDestroyed && !activity.isFinishing) {
//                    Logger.d("url "+picUrl)
                    Glide.with(context)
                        .load(picUrl)
                        .transform(multi)
                        .error(R.drawable.ic_watchlist_listing_placeholder)
                        .placeholder(R.drawable.ic_watchlist_listing_placeholder)
                        .into(holder.getView(R.id.iv_house_pic))
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}