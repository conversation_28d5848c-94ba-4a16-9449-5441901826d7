package com.housesigma.android.ui.watched

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.housesigma.android.R
import com.housesigma.android.base.BaseFragment
import com.housesigma.android.databinding.FooterMultipleWatchListBinding
import com.housesigma.android.databinding.FragmentMultipleWatchListiBinding
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.MultipleWatchItem
import com.housesigma.android.model.MultipleWatchList
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.views.HSAlertDialog
import com.housesigma.android.views.MultipleWatchListMenuDialog
import com.housesigma.android.views.NewWatchListDialog
import com.housesigma.android.views.RenameWatchListDialog
import com.housesigma.android.views.ShareWatchListMenuDialog
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.enums.PopupPosition
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


class MultipleWatchListFragment : BaseFragment() {

    private lateinit var binding: FragmentMultipleWatchListiBinding
    private lateinit var watchedViewModel: WatchedViewModel
    private val mergeAdapter = MergeMultipleWatchListAdapter()
    private var mList: MutableList<MultiItemEntity> = java.util.ArrayList()
    private var mSelectItem: MultipleWatchItem? = null
    override fun createView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        binding = FragmentMultipleWatchListiBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView(root: View?) {
        initViews()
        initData()
    }

    override fun initListener() {
    }

    override fun tag(): String {
        return "watched_watchlists"
    }


    override fun onResume() {
        super.onResume()
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun onVisibilityChanged(visible: Boolean) {
        super.onFragmentVisibilityChanged(visible)
        if (visible) {
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        when (event.type) {
            MessageType.WATCHED_MULTIPLE_WATCHLIST_CHANGED -> {
                reloadData()
            }
            else -> {}
        }
    }
    override fun refreshLoad() {
        super.refreshLoad()
        reloadData()
    }

    override fun lazyLoad() {
        reloadData()
    }

    private fun initData() {
        watchedViewModel.delWatchListMsg.observe(viewLifecycleOwner){
            ToastUtils.showDel(it.message)
        }

        watchedViewModel.unsubscribeWatchListMsg.observe(viewLifecycleOwner){
            ToastUtils.showDel(it.message)
        }


        watchedViewModel.multipleWatchList.observe(viewLifecycleOwner) {
            binding.refreshLayout.finishRefresh(true)
            bindViews(mergeAdapter, it)
        }
        watchedViewModel.watchListSubscribeList.observe(viewLifecycleOwner) {
            bindShareViews(mergeAdapter,it)
        }
        watchedViewModel.multipleWatchListLoadingLiveData.observe(viewLifecycleOwner){
            watchedViewModel.getWatchListSubscribeList()
        }

        watchedViewModel.updateWatchlistPrivacyAndShowShareMsg.observe(viewLifecycleOwner){
            mSelectItem?.meta?.let { meta ->
                activity?.let {
                    HSUtil.share(
                        it,
                        meta.getShareContent()
                    )
                }
            }
        }
    }


    private fun reloadData() {
        watchedViewModel.getMultipleWatchList("")
    }

    private fun bindShareViews(adapter: MergeMultipleWatchListAdapter, list: MultipleWatchList) {
        val iterator = mList.iterator()
        while (iterator.hasNext()) {
            val item = iterator.next()
            if (item.itemType==1) {
                iterator.remove()
            }
        }

        for ((positionItem, item) in list.withIndex()) {
            if (positionItem==0) {
                item.is_head = true
            }
            item.itemType = 1
        }
        mList.addAll(mList.size,list)
        adapter.setList(mList)
    }

    private fun showUnsubscribeConfirmationDialog(id: String) {
        activity?.let {
            HSAlertDialog(
                it, "Confirm Removal", "Are you sure you want to remove this watchlist? You will no longer get any updates for this watchlist.",
                "Cancel", "remove",
                object : HSAlertDialog.HSAlertCallback {
                    override fun onSuccess() {
                        watchedViewModel.unsubscribeWatchlist(id)
                    }
                }).show()
        }
    }

    private fun bindViews(adapter: MergeMultipleWatchListAdapter, list: MultipleWatchList) {
        mList.clear()
        mList.addAll(0,list)
        adapter.addChildClickViewIds(
            R.id.ll_root,
            R.id.iv_watchlist_menu
        )
        adapter.setOnItemChildClickListener { adapter, view, position ->
            val itemType = adapter.getItemViewType(position)
            when (itemType) {
                0 -> {
                    val item = mList[position] as MultipleWatchItem
                    when (view.id) {
                        R.id.ll_root -> {
                            GALog.log("watchlists_actions","view_watchlist")

                            activity?.let {
                                val intent = Intent(activity, WatchedListingActivity::class.java)
                                intent.putExtra("id_user_watchlist",item.id)
                                intent.putExtra("is_watch_list",true)
                                startActivity(intent)
                            }
                        }

                        R.id.iv_watchlist_menu -> {
//                            item.privacy ?: 0,
                            activity?.let {
                                val multipleWatchListMenuDialog = MultipleWatchListMenuDialog(
                                    item.id,
                                    it,
                                    object :
                                        MultipleWatchListMenuDialog.MultipleWatchListMenuCallback {
                                        override fun onDelete() {
                                            GALog.log("watchlists_actions","delete_watchlist")
                                            context?.let {
                                                HSAlertDialog(
                                                    it, "Confirm Removal", "Are you sure you want to Delete this watchlist? You will no longer get any updates for all the listings.",
                                                    "Cancel", "Delete",
                                                    object : HSAlertDialog.HSAlertCallback {
                                                        override fun onSuccess() {
                                                            watchedViewModel.delWatchlist(item.id)
                                                        }
                                                    }).show()
                                            }
                                        }

                                        override fun onEdit() {
                                            GALog.log("watchlists_actions","edit_watchlist")
                                            val name = item.name
                                            val watchlistId = item.id
                                            val newWatchListDialog = RenameWatchListDialog(it, it, object :
                                                RenameWatchListDialog.RenameWatchListCallback {
                                                override fun onSuccess() {
                                                }
                                            }, watchlistId, name,item.privacy?:0)
                                            XPopup.Builder(it)
                                                .popupPosition(PopupPosition.Bottom)
                                                .asCustom(newWatchListDialog)
                                                .show()
                                        }

                                        override fun onShare() {
                                            if (item.privacy==1) {

                                                GALog.log("watchlists_actions","share_watchlist")
                                                item.meta?.let { meta ->
                                                    activity?.let {
                                                        HSUtil.share(
                                                            it,
                                                            meta.getShareContent()
                                                        )
                                                    }
                                                }

                                            } else {

                                                GALog.log("watchlists_actions","private_to_share")
                                                HSAlertDialog(
                                                    it, "Share watchlist", "This watchlist is private,do you want to change it to public and share?",
                                                    "Cancel", "OK",
                                                    object : HSAlertDialog.HSAlertCallback {
                                                        override fun onSuccess() {
                                                            GALog.log("watchlists_actions","share_watchlist")
                                                            mSelectItem = item
                                                            watchedViewModel.updateWatchlistPrivacyAndShowShare(item.id,item.name?:"")
                                                        }
                                                    }).show()

                                            }

                                        }
                                    })
                                XPopup.Builder(it)
                                    .asCustom(multipleWatchListMenuDialog)
                                    .show()
                            }


                        }
                    }
                }
                1 -> {
                    val item = mList[position] as MultipleWatchItem
                    when (view.id) {
                        R.id.ll_root -> {
                            GALog.log("shared_watchlist_actions_click","view_shared_watchlist")
                            activity?.let {
                                val intent = Intent(activity, WatchedListingActivity::class.java)
                                intent.putExtra("id_user_watchlist",item.id)
                                intent.putExtra("is_watch_list",false)
                                startActivity(intent)
                            }
                        }

                        R.id.iv_watchlist_menu -> {
                            GALog.log("shared_watchlist_actions_click","shared_watchlist_more_vert")
//                    watchlist被owner设置为private的话，则只能remove from my library，不能share
                            val isShowShare = item.isPublic()
                            activity?.let {
                                val shareWatchListMenuDialog = ShareWatchListMenuDialog(
                                    it,
                                    isShowRemove = true,
                                    isShowSave = false,
                                    isShowShare = isShowShare,
                                    cb = object :
                                        ShareWatchListMenuDialog.ShareWatchListMenuCallback {
                                        override fun onShare() {
                                            GALog.log("shared_watchlist_actions_click","share_watchlist")
                                            item.meta?.let { meta ->
                                                activity?.let {
                                                    HSUtil.share(
                                                        it,
                                                        meta.getShareContent()
                                                    )
                                                }
                                            }
                                        }

                                        override fun onSave() {}

                                        override fun onRemove() {
                                            showUnsubscribeConfirmationDialog(item.id)
                                        }

                                    })
                                XPopup.Builder(it)
                                    .asCustom(shareWatchListMenuDialog)
                                    .show()
                            }


                        }
                    }
                }
            }

        }
    }

    private fun initViews() {
        binding.rv.layoutManager =
            LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
        binding.rv.adapter = mergeAdapter

//        binding.rvShare.layoutManager =
//            LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
//        binding.rvShare.adapter = shareWatchListAdapter

        val footerBinding = FooterMultipleWatchListBinding.inflate(layoutInflater)
        mergeAdapter.addFooterView(
            footerBinding.root,
            orientation = LinearLayout.VERTICAL
        )

        footerBinding.tvNewWatchlist.setOnClickListener {
            context?.let {
                GALog.log("watchlists_actions","new_watchlist")
                val newWatchListDialog = NewWatchListDialog()
                newWatchListDialog.show(childFragmentManager,"")
            }
        }

        binding.refreshLayout.setOnRefreshListener { refreshLayout ->
            refreshLayout.finishRefresh(2000)
            reloadData()
        }

    }


}