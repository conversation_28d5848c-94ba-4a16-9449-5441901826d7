package com.housesigma.android.ui.watched

import android.widget.CompoundButton
import androidx.appcompat.widget.AppCompatCheckBox
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.MultipleWatchItem

class SelectWatchListAdapter :
    BaseQuickAdapter<MultipleWatchItem, BaseViewHolder>(R.layout.item_select_watch_list) {

    override fun convert(holder: BaseViewHolder, item: MultipleWatchItem) {
        val cbWatchlistName = holder.getView<AppCompatCheckBox>(R.id.cb_watchlist_name)
        cbWatchlistName.text = item.name ?: ""
        holder.getView<AppCompatCheckBox>(R.id.cb_watchlist_name).isChecked = (item.is_watched == 1)
        cbWatchlistName.setOnCheckedChangeListener(object :
            CompoundButton.OnCheckedChangeListener {
            override fun onCheckedChanged(compoundButton: CompoundButton?, boolean: <PERSON><PERSON>an) {
                if (boolean) {
                    item.is_watched = 1
                } else {
                    item.is_watched = 0
                }
            }
        })
    }

}