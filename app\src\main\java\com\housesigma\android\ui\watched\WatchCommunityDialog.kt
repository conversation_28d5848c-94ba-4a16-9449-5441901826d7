package com.housesigma.android.ui.watched

import android.app.Application
import android.app.Dialog
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Window
import android.view.WindowManager
import android.widget.CompoundButton
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider.AndroidViewModelFactory
import com.housesigma.android.databinding.DialogWatchCommunityBinding
import com.housesigma.android.model.Community
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.ToastUtils


class WatchCommunityDialog(context: FragmentActivity, watchCommunity: Community,cb:SelectCallback) :
    Dialog(context) {

    private var mContext: FragmentActivity = context
    private lateinit var viewModel: WatchedViewModel
    private var watchCommunity: Community = watchCommunity
    private var watchTypes = watchCommunity.watch_types.toMutableList()
    private var mCallback: SelectCallback = cb
    interface SelectCallback {
        fun onSuccess(watchTypes: List<String>)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.window?.requestFeature(Window.FEATURE_NO_TITLE)
        val androidFactory = AndroidViewModelFactory((context.applicationContext as Application))
        viewModel = androidFactory.create(WatchedViewModel::class.java)
        viewModel.updateWatchCommunityMsg.observe(mContext) {
            if (mCallback!=null) mCallback.onSuccess(watchTypes)
            ToastUtils.showLong(it.message)
            this.dismiss()
        }


        val binding = DialogWatchCommunityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        this.window?.setBackgroundDrawable(ColorDrawable(0x00000000))
        this.window?.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT)

        watchCommunity.watch_types.let {
            if (it.size != 0) {
                binding.cbNew.isChecked = it.contains("new")
                binding.cbSold.isChecked = it.contains("sold")
                binding.cbDelisted.isChecked = it.contains("delisted")
            }
        }


        binding.tvAddWatchCommunity.setOnClickListener {
            GALog.log("popup_submit",
                watchTypes.joinToString(",")
                    .replace("new","watch_new")
                    .replace("sold","watch_sold")
                    .replace("delisted","watch_delisted"))
            viewModel.updateWatchCommunity(
                watchCommunity.house_type,
                watchCommunity.id,
                watchTypes
            )
        }

        binding.cbNew.setOnCheckedChangeListener(object :
            CompoundButton.OnCheckedChangeListener {
            override fun onCheckedChanged(compoundButton: CompoundButton?, boolean: Boolean) {
                if (compoundButton?.isPressed == false) {
                    return;
                }
                if (boolean) {
                    if (!watchTypes.contains("new")) {
                        watchTypes.add("new")
                    }
                } else {
                    if (watchTypes.contains("new")) {
                        watchTypes.remove("new")
                    }
                }
            }
        })


        binding.cbSold.setOnCheckedChangeListener(object :
            CompoundButton.OnCheckedChangeListener {
            override fun onCheckedChanged(compoundButton: CompoundButton?, boolean: Boolean) {
                if (compoundButton?.isPressed == false) {
                    return;
                }
                if (boolean) {
                    if (!watchTypes.contains("sold")) {
                        watchTypes.add("sold")
                    }
                } else {
                    if (watchTypes.contains("sold")) {
                        watchTypes.remove("sold")
                    }
                }
            }
        })

        binding.cbDelisted.setOnCheckedChangeListener(object :
            CompoundButton.OnCheckedChangeListener {
            override fun onCheckedChanged(compoundButton: CompoundButton?, boolean: Boolean) {
                if (compoundButton?.isPressed == false) {
                    return;
                }
                if (boolean) {
                    if (!watchTypes.contains("delisted")) {
                        watchTypes.add("delisted")
                    }
                } else {
                    if (watchTypes.contains("delisted")) {
                        watchTypes.remove("delisted")
                    }
                }
            }
        })

    }


}