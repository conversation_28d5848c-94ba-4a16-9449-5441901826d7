package com.housesigma.android.ui.watched

import androidx.appcompat.widget.AppCompatCheckBox
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.CommunityFilterItem


class WatchCommunityFilter3Adapter() :
    BaseQuickAdapter<CommunityFilterItem, BaseViewHolder>(R.layout.item_watch_community_filter) {

    override fun convert(holder: BaseViewHolder, item: CommunityFilterItem) {
        holder.setText(R.id.cb_filter,item.name)
        holder.getView<AppCompatCheckBox>(R.id.cb_filter).isChecked = item.checked
    }
}