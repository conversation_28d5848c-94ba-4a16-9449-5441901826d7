package com.housesigma.android.ui.watched

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.housesigma.android.R
import com.housesigma.android.base.BaseFragment
import com.housesigma.android.base.BaseListingsAdapterHelper
import com.housesigma.android.databinding.EmptyShareWatchListNotExistBinding
import com.housesigma.android.databinding.EmptyWatchNotesBinding
import com.housesigma.android.databinding.FragmentNotesBinding
import com.housesigma.android.model.HouseDetail
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.MultipleWatchItem
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.tos.TosDialog
import com.housesigma.android.ui.tos.VowTosDialog
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.Constants
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.ToastUtils
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


class WatchNotesFragment : BaseFragment(), LoginFragment.LoginCallback {

    private lateinit var binding: FragmentNotesBinding
    private lateinit var watchedViewModel: WatchedViewModel

    private var mCurrentPageNumber = 1
    private val mAdapter = WatchedNotesAdapter()
    private var mListType = arrayListOf("sale", "sold", "other")
    private var mIsSelectSale: Boolean = true
    private var mIsSelectSold: Boolean = true
    private var mIsSelectDelisted: Boolean = true
    private var mNeedRefreshToPageOne: Boolean = false
    private var mList: MutableList<HouseDetail> = java.util.ArrayList()
    private var mLoginDialog: LoginFragment? = null

    override fun createView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        binding = FragmentNotesBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView(root: View?) {
        initViews()
        initData()
    }

    override fun initListener() {
    }

    override fun tag(): String {
        return "watched_notes"
    }


    override fun onResume() {
        super.onResume()
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun onVisibilityChanged(visible: Boolean) {
        super.onFragmentVisibilityChanged(visible)
        if (visible) {
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        when (event.type) {
            MessageType.REFRESH_NOTES -> {
                reloadData()
            }
            else -> {}
        }
    }

    override fun refreshLoad() {
        super.refreshLoad()
        reloadData()
    }

    override fun lazyLoad() {
        reloadData()
    }

    private fun initData() {
        watchedViewModel.removeWatchedMsg.observe(this) {
            GALog.log("watched_notes_actions_click", "delete")
            ToastUtils.showDel(it.message)
            reloadData()
        }

        watchedViewModel.addWatchNoteMsg.observe(this){
            GALog.log("watched_notes_actions_click", "save_note")
        }

        watchedViewModel.watchNoteList.observe(this) {
            it.message?.let { msg ->
                val view = EmptyWatchNotesBinding.inflate(layoutInflater)
                view.tvEmptyWatchNotes.text = msg
                mAdapter.setEmptyView(view.root)
            }

            mNeedRefreshToPageOne = false
            binding.refreshLayout.finishLoadMore(
                0, true,
                it.houselist.size < 20
            )
            binding.refreshLayout.setNoMoreData(it.houselist.size < 20)
            bindViews(mAdapter, it.houselist)
        }

        watchedViewModel.loadingLiveData.observe(viewLifecycleOwner) {
            binding.refreshLayout.finishLoadMore(true)
            binding.refreshLayout.finishRefresh(true)
        }
    }

    private fun bindViews(adapter: WatchedNotesAdapter, list: List<HouseDetail>) {
        adapter.setWatchlistType(true)
        adapter.setWatchNoteListType(true)
        if (mCurrentPageNumber == 1) {
            mList.clear()
        }

        mList.addAll(list)
        adapter.setList(mList)

        adapter.setOnItemChildClickListener { adapter, view, position ->
            val etNote = adapter.getViewByPosition(position, R.id.et_note) as EditText
            val note = etNote.text.toString().trim()
            val houseDetail = adapter.data[position] as HouseDetail

            when (view.id) {
                R.id.rl -> {
                    val item = mList[position]
                    if (BaseListingsAdapterHelper.canJumpListingDetail(item)) {
                        return@setOnItemChildClickListener
                    }
                    GALog.log("preview_click", "watched_notes")

                    activity?.let { it1 ->
                        WebViewHelper.jumpHouseDetail(
                            it1,
                            houseDetail.id_listing,
                            houseDetail.seo_suffix
                        )
                    }
                }

                R.id.tv_login_required -> {
                    val tvLoginRequiredStr = (view as TextView).text.toString()
                    if (Constants.TEXT_VIEW_TRREB_TIMEOUT.equals(tvLoginRequiredStr)) {
                        showLoginDialog(reLogin = LoginFragment.RE_LOGIN_TRREB_TIMEOUT)
                    } else if ("Password Expired".equals(tvLoginRequiredStr)) {
                        showLoginDialog(reLogin = LoginFragment.RE_LOGIN_VALIDATE)
                    } else {
                        showLoginDialog()
                    }
                }


                R.id.tv_agreement_required -> {
                    GALog.log("agreement_button_click")
                    activity?.let {
                        TosDialog(WatchNotesFragment@ this,
                            it,
                            it,
                            houseDetail.tos_source,
                            object : TosDialog.TosCallback {
                                override fun onSuccess() {
                                    mCurrentPageNumber = 1
                                    reloadData()
                                }
                            }).show()
                    }

                }

                R.id.tv_not_available -> {
                    context?.let { VowTosDialog(houseDetail.id_listing, this, this, it).show() }
                }

                R.id.ll_pin_show -> {//有内容的
                    GALog.log("watched_notes_actions_click", "edit_note")
                    houseDetail.isEdit = true
                    adapter.notifyItemChanged(position)
                }

                R.id.ll_pin_a_note -> {//无内容的
                    GALog.log("watched_notes_actions_click", "add_note")
                    houseDetail.isEdit = true
                    adapter.notifyItemChanged(position)
                }

                R.id.tv_cancel -> {//编辑的取消
                    houseDetail.isEdit = false
                    adapter.notifyItemChanged(position)
                }

                R.id.tv_save_note -> {//编辑的保存
                    houseDetail.isEdit = false
                    houseDetail.note = note
                    watchedViewModel.addWatchNote(houseDetail.id_listing, note)
                    adapter.notifyItemChanged(position)
                }

            }
        }
    }

    private fun showLoginDialog(reLogin: String = "") {
        GALog.log("login_button_click")
        childFragmentManager.let {
            if (mLoginDialog == null) {
                mLoginDialog = LoginFragment()
            }
            if (mLoginDialog?.isAdded == true) return
            val bundle = Bundle()
            bundle.putString("reLogin", reLogin)
            mLoginDialog?.arguments = bundle
            mLoginDialog?.show(it, "")
        }
    }

    private fun reloadData() {
        mCurrentPageNumber = 1
        watchedViewModel.getNoteList(page = 1, list_type = mListType)
    }


    private fun initViews() {
        binding.tvSold.setOnClickListener {
            GALog.log("watched_notes_actions_click", "sold")
            if (mIsSelectSold) {
                val isDeSelect = deSelectType("sold")
                if (!isDeSelect) return@setOnClickListener
                binding.tvSold.setBackgroundResource(R.drawable.shape_watch_center_normal)
                binding.tvSold.setTextColor(resources.getColor(R.color.app_main_color))
                binding.rv.scrollToPosition(0)
            } else {
                binding.tvSold.setBackgroundResource(R.drawable.shape_watch_center_selected)
                binding.tvSold.setTextColor(resources.getColor(R.color.color_white))

                if (!mListType.contains("sold")) {
                    mListType.add("sold")
                }
                binding.rv.scrollToPosition(0)
            }
            mIsSelectSold = !mIsSelectSold
            MMKVUtils.saveBoolean("watch_unselect_sold", !mIsSelectSold)

            reloadData()
        }

        binding.tvForSale.setOnClickListener {
            GALog.log("watched_notes_actions_click", "sale")
            if (mIsSelectSale) {
                val isDeSelect = deSelectType("sale")
                if (!isDeSelect) return@setOnClickListener
                binding.tvForSale.setBackgroundResource(R.drawable.shape_watch_left_normal)
                binding.tvForSale.setTextColor(resources.getColor(R.color.app_main_color))
                binding.rv.scrollToPosition(0)
            } else {
                binding.tvForSale.setBackgroundResource(R.drawable.shape_watch_left_selected)
                binding.tvForSale.setTextColor(resources.getColor(R.color.color_white))

                if (!mListType.contains("sale")) {
                    mListType.add("sale")
                }
                binding.rv.scrollToPosition(0)
            }
            mIsSelectSale = !mIsSelectSale
            MMKVUtils.saveBoolean("watch_unselect_sale", !mIsSelectSale)

            reloadData()
        }

        binding.tvDeListed.setOnClickListener {
            GALog.log("watched_notes_actions_click", "delisted")
            if (mIsSelectDelisted) {

                val isDeSelect = deSelectType("other")
                if (!isDeSelect) return@setOnClickListener
                binding.tvDeListed.setBackgroundResource(R.drawable.shape_watch_right_normal)
                binding.tvDeListed.setTextColor(resources.getColor(R.color.app_main_color))
                binding.rv.scrollToPosition(0)
            } else {
                binding.tvDeListed.setBackgroundResource(R.drawable.shape_watch_right_selected)
                binding.tvDeListed.setTextColor(resources.getColor(R.color.color_white))

                if (!mListType.contains("other")) {
                    mListType.add("other")
                }
                binding.rv.scrollToPosition(0)
            }
            mIsSelectDelisted = !mIsSelectDelisted
            MMKVUtils.saveBoolean("watch_unselect_de_listed", !mIsSelectDelisted)

            reloadData()
        }

        binding.rv.setHasFixedSize(true)
        context?.let {
            binding.rv.layoutManager = LinearLayoutManager(it, RecyclerView.VERTICAL, false)
        }
        binding.rv.adapter = mAdapter


        binding.refreshLayout.setOnRefreshListener { refreshLayout ->
            refreshLayout.finishRefresh(2000)
            reloadData()
        }
        binding.refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                reloadData()
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                mCurrentPageNumber++
                watchedViewModel.getNoteList(
                    mCurrentPageNumber,
                    list_type = mListType
                )
            }
        })
    }

    private fun deSelectType(type: String): Boolean {
        if ((mListType.size == 1)) {
            return false
        }
        if (mListType.contains(type)) {
            mListType.remove(type)
        }
        return true
    }

    override fun onLoginSuccess() {
        mLoginDialog?.dismiss()
        mLoginDialog = null
        reloadData()
    }

}