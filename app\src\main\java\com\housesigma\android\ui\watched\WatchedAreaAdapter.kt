package com.housesigma.android.ui.watched

import android.app.Activity
import android.graphics.Bitmap
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.NetResponse
import com.housesigma.android.model.PolygonPhoto
import com.housesigma.android.model.WatchedArea
import com.housesigma.android.network.NetClient
import com.housesigma.android.utils.ConstantsHelper
import com.housesigma.android.utils.ScreenUtils
import jp.wasabeef.glide.transformations.RoundedCornersTransformation
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response


class WatchedAreaAdapter :
    BaseQuickAdapter<WatchedArea, BaseViewHolder>(R.layout.item_watched_area) {


    override fun convert(holder: BaseViewHolder, item: WatchedArea) {
        val topCorner = ScreenUtils.dpToPx(16f)
        val multi: MultiTransformation<Bitmap> = MultiTransformation(
            CenterCrop(),
            RoundedCornersTransformation(
                topCorner.toInt(),
                0,
                RoundedCornersTransformation.CornerType.ALL
            )
        )

        if (item.polygon.size > 0) {
            val path = item.polygon.joinToString("|")
            val joinPath = path + "|" + item.polygon.getOrNull(0)
            Glide.with(context)
                .load(
                    ConstantsHelper.getMapStatic()+"auto/<EMAIL>?path=$joinPath&fill=rgba(198,213,239,0.5)&stroke=rgba(164,192,241,1)&width=2"
                )
                .transform(multi)
                .error(R.drawable.shape_pic_place_holder)
                .placeholder(R.drawable.shape_pic_place_holder)
                .into(holder.getView(R.id.iv_house_pic1))
        }





        if (!item.is_load_photo) {
            NetClient.apiService.getWatchPolygonPhoto(item.id.toString()).enqueue(object :
                Callback<NetResponse<PolygonPhoto>> {
                override fun onResponse(
                    call: Call<NetResponse<PolygonPhoto>>,
                    response: Response<NetResponse<PolygonPhoto>>
                ) {
                    try {
                        // DEV-4455 server端返回的response body在一定情况下（原因不明），有可能是null，所以这里不能强转，会发生crashed
                        val photoUrl = (response.body() as NetResponse<PolygonPhoto>)?.data?.photo_url
                        item.photo_url = photoUrl ?: ""
                        item.is_load_photo = true
                        loadHousePic(item.photo_url, multi, holder)
                    } catch (e:Exception) {
                        e.printStackTrace()
                    }
                }

                override fun onFailure(call: Call<NetResponse<PolygonPhoto>>, t: Throwable) {
                    item.photo_url = ""
                    item.is_load_photo = true
                    loadHousePic(item.photo_url!!, multi, holder)
                }
            })
        } else {
            loadHousePic(item.photo_url, multi, holder)
        }
        holder.setText(R.id.tv_description, item.description)
        holder.setText(R.id.tv_notification_text, item.notification_text)
    }

    private fun loadHousePic(
        picUrl: String?,
        multi: MultiTransformation<Bitmap>,
        holder: BaseViewHolder
    ) {

        if (TextUtils.isEmpty(picUrl)) {
            holder.getView<TextView>(R.id.tv_view_all).visibility = View.GONE
        } else {
            holder.getView<TextView>(R.id.tv_view_all).visibility = View.VISIBLE
        }
        try {
            if (context == null) return
            if (context is Activity) {
                val activity = context as Activity
                if (!activity.isDestroyed && !activity.isFinishing) {
//                    Logger.d("url "+picUrl)
                    if (TextUtils.isEmpty(picUrl)) {
                        holder.setVisible(R.id.ll_no_pic,true)
                        holder.setGone(R.id.iv_house_pic2,true)
                    } else {
                        holder.setVisible(R.id.iv_house_pic2,true)
                        holder.setGone(R.id.ll_no_pic,true)
                        Glide.with(context)
                            .load(picUrl)
                            .transform(multi)
                            .into(holder.getView(R.id.iv_house_pic2))
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}