package com.housesigma.android.ui.watched

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.housesigma.android.R
import com.housesigma.android.base.BaseFragment
import com.housesigma.android.databinding.FooterWatchedAreaBinding
import com.housesigma.android.databinding.FragmentWatchedAreaBinding
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.WatchedArea
import com.housesigma.android.ui.account.AddEmailLaterDialog
import com.housesigma.android.ui.account.ChangeContactActivity
import com.housesigma.android.ui.account.PromptUserAddEmailDialog
import com.housesigma.android.ui.map.MapActivity
import com.housesigma.android.ui.watcharea.WatchedAreaActivity
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.views.HSAlertDialog
import com.housesigma.android.views.MultipleWatchListMenuDialog
import com.housesigma.android.views.RenameWatchListDialog
import com.housesigma.android.views.WatchedAreaMenuDialog
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.enums.PopupPosition
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


class WatchedAreasFragment : BaseFragment() {

    private lateinit var binding: FragmentWatchedAreaBinding
    private lateinit var watchedViewModel: WatchedViewModel
    private val adapter = WatchedAreaAdapter()
    private var watchedAreaId: Int? = null
    private var isFirstTimeShow: Boolean = true
    private var needRefreshToPageOne: Boolean = false
    private var promptUserAddEmailDialog : PromptUserAddEmailDialog? = null

    override fun createView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        binding = FragmentWatchedAreaBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initView(root: View?) {
        initViews()
        initData()
    }

    override fun initListener() {
    }

    override fun tag(): String {
        return "watched_areas"
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        when (event.type) {
            MessageType.WATCHED_AREAS_CHANGED -> {
                needRefreshToPageOne = true
            }
            else -> {}
        }
    }

    override fun onResume() {
        super.onResume()
        if (needRefreshToPageOne) {
            loadData()
        }
    }
    override fun onVisibilityChanged(visible: Boolean) {
        super.onFragmentVisibilityChanged(visible)
        if (visible&&isFirstTimeShow) {
            isFirstTimeShow = false
            loadData()
        }
    }

    private fun showPromptUserToAddEmailDialog() {
        context?.let {
            watchedViewModel.stopPrompt()
            if (promptUserAddEmailDialog == null) {
                promptUserAddEmailDialog = PromptUserAddEmailDialog(
                    tag(),
                    WatchedAreasFragment@ this,
                    viewLifecycleOwner,
                    it,
                    object : PromptUserAddEmailDialog.PromptUserAddEmailDialogCallback {
                        override fun onSendCode(email: String) {
                            val intent = Intent(context, ChangeContactActivity::class.java)
                            intent.putExtra("is_email", true)
                            intent.putExtra("email", email)
                            it.startActivity(intent)
                        }

                        override fun onNotNow() {
                            AddEmailLaterDialog(
                                it,
                                object : AddEmailLaterDialog.AddEmailLaterDialogCallback {
                                    override fun onOkay() {
                                    }

                                    override fun onGoBack() {
                                        showPromptUserToAddEmailDialog()
                                    }
                                }).show()
                        }
                    })
            }
            promptUserAddEmailDialog?.let { promptUserAddEmailDialog ->
                if (promptUserAddEmailDialog.isShowing) {
                    return
                }
                promptUserAddEmailDialog.show()
            }
        }
    }

    override fun lazyLoad() {
    }

    override fun refreshLoad() {
        super.refreshLoad()
        loadData()
    }

    private fun initData() {
        watchedViewModel.loadingLiveData.observe(viewLifecycleOwner) {
            dismissLoading()
        }
        watchedViewModel.watchedArea.observe(viewLifecycleOwner) {
            needRefreshToPageOne = false
            if (it.show_add_email==1) {
//              进入添加 contact email 流程
                showPromptUserToAddEmailDialog()
            }
            bindViews(adapter, it.list)
        }
        watchedViewModel.delAreasMsg.observe(viewLifecycleOwner) {
            ToastUtils.showLong(it.message)
            watchedViewModel.getWatchPolygonList()
        }

        watchedViewModel.getWatchPolygon.observe(viewLifecycleOwner) { watchPolygon ->
            watchedAreaId?.let {
                val intent = Intent(activity, WatchedAreaActivity::class.java)
                intent.putExtra("edit", true)
                watchPolygon.id = watchedAreaId as Int
                intent.putExtra("watchPolygon", watchPolygon)
                activity?.startActivity(intent)
            }
        }
    }

    private fun loadData() {


        watchedViewModel.getWatchPolygonList()
    }

    private fun bindViews(adapter: WatchedAreaAdapter, list: List<WatchedArea>) {
        adapter.data = list.toMutableList()
        adapter.notifyDataSetChanged()
        adapter.addChildClickViewIds(
            R.id.iv_house_pic1,
            R.id.iv_del_watched_area,
            R.id.rl_view_all,
            R.id.tv_view,
        )
        adapter.setOnItemChildClickListener { adapter, view, position ->
            val watchedArea = adapter.getItem(position) as WatchedArea
            when (view.id) {
                R.id.iv_del_watched_area -> {
                    activity?.let {
                        val watchedAreaMenuDialog = WatchedAreaMenuDialog(
                            it,
                            object :
                                WatchedAreaMenuDialog.MultipleWatchListMenuCallback {
                                override fun onDelete() {
                                    context?.let {
                                        GALog.log("watched_areas_actions", "delete")
                                        showDelConfirmDialog(watchedArea)
                                    }
                                }

                                override fun onEdit() {
                                    showLoading()
                                    GALog.log("watched_areas_actions", "edit")
                                    watchedAreaId = watchedArea.id
                                    watchedViewModel.getWatchPolygon(watchedArea.id.toString())
                                }
                            })
                        XPopup.Builder(it)
                            .asCustom(watchedAreaMenuDialog)
                            .show()
                    }
                }

                R.id.rl_view_all -> {
                    GALog.log("watched_areas_actions", "view_listing")
                    if (TextUtils.isEmpty(watchedArea.photo_url)) return@setOnItemChildClickListener
                    activity?.let {
                        val intent = Intent(it, WatchedPropertiesActivity::class.java)
                        intent.putExtra("id", watchedArea.id.toString())
                        intent.putExtra("description", watchedArea.description)
                        it.startActivity(intent)
                    }
                }

                R.id.iv_house_pic1, R.id.tv_view -> {
                    GALog.log("watched_areas_actions", "view")
                    // 跳到map页面，显示area
                    activity?.let {
                        val intent = Intent(it, MapActivity::class.java)
                        intent.putExtra("map_type", arrayListOf("for-sale"))
                        intent.putParcelableArrayListExtra(
                            "watch_area_polygon",
                            watchedArea.polygon
                        )
                        it.startActivity(intent)
                    }

                }

            }
        }
    }

    private fun showDelConfirmDialog(watchedArea: WatchedArea) {
        activity?.let {
            HSAlertDialog(
                it, "Delete ${watchedArea.description}?", "",
                "Cancel", "Delete",
                object : HSAlertDialog.HSAlertCallback {
                    override fun onSuccess() {
                        watchedViewModel.deletePolygon(watchedArea.id)
                    }
                }).show()
        }

    }

    private fun initViews() {
        binding.rv.layoutManager =
            LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
        binding.rv.adapter = adapter

        binding.refreshLayout.setOnRefreshListener { refreshLayout ->
            refreshLayout.finishRefresh(2000)
            loadData()
        }

        val footerBinding = FooterWatchedAreaBinding.inflate(layoutInflater)
        adapter.addFooterView(
            footerBinding.root,
            orientation = LinearLayout.VERTICAL
        )

        footerBinding.tvAddWatchedArea.setOnClickListener {
            GALog.log("watched_areas_actions", "add")

            val intent = Intent(activity, MapActivity::class.java)
            intent.putExtra("is_sale", true)
            intent.putExtra("is_show_watch_hint", true)
            intent.putExtra("map_type", arrayListOf("for-sale", "sold", "de-listed-5"))
            startActivity(intent)


        }
    }

}