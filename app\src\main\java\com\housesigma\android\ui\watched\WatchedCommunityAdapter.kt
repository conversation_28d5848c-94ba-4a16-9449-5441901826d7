package com.housesigma.android.ui.watched

import android.graphics.Bitmap
import android.text.TextUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.Community
import com.housesigma.android.model.HSWatchTypeComparator
import com.housesigma.android.utils.ScreenUtils
import jp.wasabeef.glide.transformations.RoundedCornersTransformation


class WatchedCommunityAdapter :
    BaseQuickAdapter<Community, BaseViewHolder>(R.layout.item_watched_community) {


    override fun convert(holder: BaseViewHolder, item: Community) {
        val topCorner = ScreenUtils.dpToPx(8f)
        val multi: MultiTransformation<Bitmap> = MultiTransformation(
            CenterCrop(),
            RoundedCornersTransformation(
                topCorner.toInt(),
                0,
                RoundedCornersTransformation.CornerType.ALL
            )
        )

        if (item.pics.size > 0) {
            Glide.with(context)
                .load(item.pics[0])
                .transform(multi)
                .error(R.drawable.shape_pic_place_holder_radius_8)
                .placeholder(R.drawable.shape_pic_place_holder_radius_8)
                .into(holder.getView(R.id.iv_house_pic1))
            holder.setVisible(R.id.iv_house_pic1, true)
        } else {
            holder.setVisible(R.id.iv_house_pic1, false)
        }


        if (item.pics.size > 1) {
            Glide.with(context)
                .load(item.pics[1])
                .transform(multi)
                .error(R.drawable.shape_pic_place_holder_radius_8)
                .placeholder(R.drawable.shape_pic_place_holder_radius_8)
                .into(holder.getView(R.id.iv_house_pic2))
            holder.setVisible(R.id.iv_house_pic2, true)
        } else {
            holder.setVisible(R.id.iv_house_pic2, false)
        }

        if (item.pics.size > 2) {
            Glide.with(context)
                .load(item.pics[2])
                .transform(multi)
                .error(R.drawable.shape_pic_place_holder_radius_8)
                .placeholder(R.drawable.shape_pic_place_holder_radius_8)
                .into(holder.getView(R.id.iv_house_pic3))
            holder.setVisible(R.id.iv_house_pic3, true)
        } else {
            holder.setVisible(R.id.iv_house_pic3, false)
        }

//        // 副标题区，主要为位置信息
        var address =
            if (!TextUtils.isEmpty(item.community_plus))
                item.community_plus
            else ""
        address = address.plus(
            if (!TextUtils.isEmpty(item.municipality_plus))
                " - ${item.municipality_plus}"
            else ""
        )
        holder.setText(R.id.tv_address, address)
        holder.setText(R.id.tv_house_type_name, item.house_type_name)
        holder.setText(R.id.tv_date, item.period)

        holder.setText(R.id.tv_median_price, "$  ${item.price_sold_median ?: ""}")

        if (item.watch_types.size != 0) {
            holder.setText(R.id.tv_type,item.watch_types.sortedWith(HSWatchTypeComparator()).joinToString(", ")
                .replace("new","New")
                .replace("sold","Sold")
                .replace("delisted","Delisted"))
            holder.setVisible(R.id.tv_type,true)
        } else {
            holder.setGone(R.id.tv_type,true)
        }
    }

}