package com.housesigma.android.ui.watched

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.housesigma.android.R
import com.housesigma.android.base.BaseFragment
import com.housesigma.android.databinding.FooterWatchedCommunityBinding
import com.housesigma.android.databinding.FragmentWatchedCommunityBinding
import com.housesigma.android.model.Community
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.WatchedArea
import com.housesigma.android.ui.account.AddEmailLaterDialog
import com.housesigma.android.ui.account.ChangeContactActivity
import com.housesigma.android.ui.account.PromptUserAddEmailDialog
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.HSAlertDialog
import com.housesigma.android.views.WatchedAreaMenuDialog
import com.lxj.xpopup.XPopup
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


class WatchedCommunityFragment : BaseFragment() {

    private lateinit var binding: FragmentWatchedCommunityBinding
    private lateinit var watchedViewModel: WatchedViewModel
    private val adapter = WatchedCommunityAdapter()
    private var needRefreshToPageOne: Boolean = false
    private var isFirstTimeShow: Boolean = true
    private var promptUserAddEmailDialog : PromptUserAddEmailDialog? = null

    override fun createView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        binding = FragmentWatchedCommunityBinding.inflate(inflater, container, false)
        return binding.root
    }


    private fun initData() {
        watchedViewModel.watchCommunity.observe(viewLifecycleOwner) {
            if (it.show_add_email==1) {
//              进入添加 contact email 流程
                showPromptUserToAddEmailDialog()
            }
            needRefreshToPageOne = false
            bindViews(adapter, it.community)
        }
        watchedViewModel.removeWatchedCommunityMsg.observe(viewLifecycleOwner) {
            ToastUtils.showLong(it.message)
            watchedViewModel.getWatchCommunityList()
        }
    }

    override fun initView(root: View?) {
        initViews()
        initData()
    }

    override fun initListener() {
    }

    override fun tag(): String {
        return "watched_communities"
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        when (event.type) {
            MessageType.WATCHED_COMMUNITIES_CHANGED -> {
                needRefreshToPageOne = true
            }
            else -> {}
        }
    }

    override fun onResume() {
        super.onResume()
        if (needRefreshToPageOne) {
            loadData()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun onVisibilityChanged(visible: Boolean) {
        super.onFragmentVisibilityChanged(visible)
        if (visible&&isFirstTimeShow) {
            isFirstTimeShow = false
            loadData()
        }
    }

    private fun showPromptUserToAddEmailDialog() {
        context?.let {
            watchedViewModel.stopPrompt()
            if (promptUserAddEmailDialog == null) {
                promptUserAddEmailDialog = PromptUserAddEmailDialog(
                    tag(),
                    WatchedAreasFragment@ this,
                    viewLifecycleOwner,
                    it,
                    object : PromptUserAddEmailDialog.PromptUserAddEmailDialogCallback {
                        override fun onSendCode(email: String) {
                            val intent = Intent(context, ChangeContactActivity::class.java)
                            intent.putExtra("is_email", true)
                            intent.putExtra("email", email)
                            it.startActivity(intent)
                        }

                        override fun onNotNow() {
                            AddEmailLaterDialog(
                                it,
                                object : AddEmailLaterDialog.AddEmailLaterDialogCallback {
                                    override fun onOkay() {
                                    }

                                    override fun onGoBack() {
                                        showPromptUserToAddEmailDialog()
                                    }
                                }).show()
                        }
                    })
            }
            promptUserAddEmailDialog?.let { promptUserAddEmailDialog ->
                if (promptUserAddEmailDialog.isShowing) {
                    return
                }
                promptUserAddEmailDialog.show()
            }

        }
    }

    override fun refreshLoad() {
        super.refreshLoad()
        loadData()
    }

    override fun lazyLoad() {
    }

    private fun loadData() {
        watchedViewModel.getWatchCommunityList()
    }

    private fun bindViews(adapter: WatchedCommunityAdapter, list: List<Community>) {
        adapter.data = list.toMutableList()
        adapter.notifyDataSetChanged()
        adapter.addChildClickViewIds(
            R.id.iv_watched_community_menu,
            R.id.tv_watched_community_market_trend,
            R.id.tv_watched_community_view_listing,
            R.id.ll_pics,
        )
        adapter.setOnItemChildClickListener { adapter, view, position ->
            val community = adapter.getItem(position) as Community
            when (view.id) {
                R.id.ll_pics, R.id.tv_watched_community_view_listing -> {
                    GALog.log("watched_communities_actions", "view_listing")
                    GALog.page("newly_listed_listings_of_community")
                    activity?.let {
                        WebViewHelper.jumpWatchedTrendListing(
                            it,
                            community = community.id_community,
                            house_type = community.house_type,
                            municipality = community.id_municipality,
                            type = "2"
                        )
                    }
                }


                R.id.iv_watched_community_menu -> {

                    activity?.let {
                        val watchedAreaMenuDialog = WatchedAreaMenuDialog(
                            it,
                            object :
                                WatchedAreaMenuDialog.MultipleWatchListMenuCallback {
                                override fun onDelete() {
                                    context?.let {
                                        GALog.log("watched_communities_actions", "delete")
                                        showDelConfirmDialog(community)
                                    }
                                }

                                override fun onEdit() {
                                    GALog.log("watched_communities_actions", "edit")
                                    val dialog = activity?.let {
                                        WatchCommunityDialog(it, community, object :
                                            WatchCommunityDialog.SelectCallback {
                                            override fun onSuccess(watchTypes: List<String>) {
                                                Logger.d( "watchTypes " + watchTypes + " position " + position)
                                                community.watch_types = watchTypes
                                                adapter.notifyItemChanged(position)
                                            }
                                        })
                                    }
                                    dialog?.show()
                                }
                            })
                        XPopup.Builder(it)
                            .asCustom(watchedAreaMenuDialog)
                            .show()
                    }
                }

                R.id.tv_watched_community_market_trend -> {
                    GALog.log("watched_communities_actions", "market")
                    activity?.let {
                        WebViewHelper.jumpMarket(
                            it,
                            community.id_municipality.toString(),
                            community.house_type,
                            community.id_community.toString(),
                        )
                    }
                }
            }
        }
    }

    private fun showDelConfirmDialog(community: Community) {
        activity?.let {
            HSAlertDialog(
                it, "Confirm Removal?", "",
                "Cancel", "Confirm",
                object : HSAlertDialog.HSAlertCallback {
                    override fun onSuccess() {
                        watchedViewModel.removeCommunityWatch(community.house_type, community.id)
                    }
                }).show()
        }

    }

    private fun initViews() {
        binding.rv.layoutManager =
            LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
        binding.rv.adapter = adapter

        binding.refreshLayout.setOnRefreshListener { refreshLayout ->
            refreshLayout.finishRefresh(2000)
            loadData()
        }

        val footerBinding = FooterWatchedCommunityBinding.inflate(layoutInflater)
        adapter.addFooterView(
            footerBinding.root,
            orientation = LinearLayout.VERTICAL
        )

        footerBinding.tvAddWatchCommunity.setOnClickListener {
            GALog.log("watched_communities_actions", "add")
            startActivity(Intent(activity, AddWatchCommunityActivity::class.java))
        }
    }

}