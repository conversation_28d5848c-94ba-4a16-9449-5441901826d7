package com.housesigma.android.ui.watched

import android.content.Intent
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.viewpager.widget.ViewPager
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseHomeFragment
import com.housesigma.android.databinding.FragmentWatchedBinding
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.ui.account.NotificationActivity
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.ScreenUtils
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.HSOnTransitionTextListener
import com.housesigma.android.views.viewpagerindicator.view.indicator.IndicatorViewPager
import com.housesigma.android.views.viewpagerindicator.view.indicator.slidebar.ColorBar
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


class WatchedFragment : BaseHomeFragment(), ViewPager.OnPageChangeListener {

    private lateinit var binding: FragmentWatchedBinding
    private var currentPosition = 0

    override fun initImmersionBar() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
            titleBar(binding.vTop)
        }
    }

    override fun createView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        binding = FragmentWatchedBinding.inflate(inflater, container, false)
        return binding.root
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        try {
            when (event.type) {
                MessageType.JUMP_WATCHED_LISTINGS -> {
                    if (binding.tabMainViewPager != null) {
                        binding.tabMainViewPager.currentItem = 0
                    }
                }
                MessageType.JUMP_WATCHED_NOTES -> {
                    if (binding.tabMainViewPager != null) {
                        binding.tabMainViewPager.currentItem = 1
                    }
                }
                MessageType.JUMP_WATCHED_AREA -> {
                    if (binding.tabMainViewPager != null) {
                        binding.tabMainViewPager.currentItem = 2
                    }
                }
                MessageType.JUMP_WATCHED_COMMUNITIES -> {
                    if (binding.tabMainViewPager != null) {
                        binding.tabMainViewPager.currentItem = 3
                    }
                }
                MessageType.JUMP_WATCHED_RECENTLY_VIEWED -> {
                    if (binding.tabMainViewPager != null) {
                        binding.tabMainViewPager.currentItem = 4
                    }
                }
                else -> {}
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    override fun initView(root: View?) {
        binding.tabMainViewPager.addOnPageChangeListener(object :ViewPager.OnPageChangeListener{
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }
            override fun onPageSelected(position: Int) {
                sendScreenViewByPosition(position)
            }

            override fun onPageScrollStateChanged(state: Int) {
            }
        })
    }

    override fun initListener() {
    }

    override fun tag(): String {
        return "watched"
    }

    override fun refreshLoad() {
        super.refreshLoad()
    }

    override fun onVisibilityChanged(visible: Boolean) {
        super.onFragmentVisibilityChanged(visible)
        if (visible) {
            sendScreenViewByPosition(binding.tabMainViewPager.currentItem)
        }
    }

    private fun sendScreenViewByPosition(position: Int) {
        when (position) {
            0 -> {
                GALog.page("listings")
            }

            1 -> {
                GALog.page("watched_notes")
            }

            2 -> {
                GALog.page("watched_areas")
            }

            3 -> {
                GALog.page("watched_communities")
            }

            4 -> {
                GALog.page("recently_viewed")
            }
        }
    }

    override fun lazyLoad() {
        initViews()
        loadData()
    }

    private fun loadData() {
    }

    private fun initViews() {
        binding.ivNotification.setOnClickListener {
            GALog.log("notification_setting_click")
            startActivity(Intent(activity, NotificationActivity::class.java))
        }

        val viewPager = binding.tabMainViewPager
        binding.tabMainIndicator.onTransitionListener = HSOnTransitionTextListener().setColor(
            Color.parseColor("#FFFFFF"),
            Color.parseColor("#FFFFFF")
        ).setSize(15f, 15f)
        binding.tabMainIndicator.setScrollBar(ColorBar(activity, 0xFFFFFFFF.toInt(), 10))
        val indicatorViewPager = IndicatorViewPager(binding.tabMainIndicator, viewPager)
        indicatorViewPager.adapter = MyAdapter(fragmentManager = childFragmentManager)
        viewPager.isCanScroll = true
        viewPager.offscreenPageLimit = 2
        viewPager.currentItem = currentPosition
        viewPager.setOnPageChangeListener(this)
    }

    inner class MyAdapter(fragmentManager: FragmentManager?) :
        IndicatorViewPager.IndicatorFragmentPagerAdapter(fragmentManager) {
        private val tabNames =
            arrayOf(
                resources.getString(R.string.watched_tab_listings),
                resources.getString(R.string.watched_tab_notes),
                resources.getString(R.string.watched_tab_areas),
                resources.getString(R.string.watched_tab_communities),
                resources.getString(R.string.watched_tab_recently_viewed)
            )

        override fun getCount(): Int {
            return tabNames.size
        }

        override fun getViewForTab(position: Int, convertView: View?, container: ViewGroup?): View {
            var convertView = convertView
            if (convertView == null) {
                convertView = LayoutInflater.from(activity)
                    .inflate(R.layout.tab_main, container, false)
            }
            val textView = convertView as TextView
            textView.text = tabNames[position]
            return textView
        }

        override fun getFragmentForPage(position: Int): Fragment {
            return if (position == 0) {
                MultipleWatchListFragment()
            } else if (position == 1) {
                WatchNotesFragment()
            } else if (position == 2) {
                WatchedAreasFragment()
            } else if (position == 3) {
                WatchedCommunityFragment()
            } else {
                WatchedRecentFragment()
            }
        }

    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
    }

    override fun onPageSelected(position: Int) {
        if (position == 0) {
            GALog.log("watched_tab_click", "listings")
        } else if (position == 1) {
            GALog.log("watched_tab_click", "notes")
        } else if (position == 2) {
            GALog.log("watched_tab_click", "areas")
        } else if (position == 3) {
            GALog.log("watched_tab_click", "communities")
        } else {
            GALog.log("watched_tab_click", "recent_viewed")
        }
    }


    override fun onPageScrollStateChanged(state: Int) {
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

}