package com.housesigma.android.ui.watched

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.log.Logger
import java.lang.reflect.Type

class WatchedHelper {

    companion object {
        private const val WATCHED_LIST_KEY = "watched_list"
        private val gson = Gson()
        private val watchedSet: HashSet<String> by lazy {
            loadWatchedListFromStorage()
        }

        private fun loadWatchedListFromStorage(): HashSet<String> {
            val cacheListingId = MMKVUtils.getStr(WATCHED_LIST_KEY) ?: "[]"
            val type: Type = object : TypeToken<ArrayList<String?>?>() {}.type
            return try {
                val list: List<String> = gson.fromJson(cacheListingId, type)
                HashSet(list.filter { !it.isNullOrEmpty() })
            } catch (e: Exception) {
                Logger.e("Error parsing watched list: ${e.message}")
                HashSet()
            }
        }

        private fun saveWatchedListToStorage() {
            val json = gson.toJson(watchedSet.toList())
            MMKVUtils.saveStr(WATCHED_LIST_KEY, json)
            Logger.i("Saved watched list: $watchedSet")
        }

        /**
         * Batch insert listing IDs, clear local and disk cache first
         */
        fun saveWatchedBatch(listingIds: List<String>) {
            watchedSet.clear()
            watchedSet.addAll(listingIds.filter { !it.isNullOrEmpty() })
            saveWatchedListToStorage()
        }

        /**
         * Check if a listing ID exists in the watched cache
         */
        fun findWatchedByListingId(listingId: String): Boolean {
            return listingId.isNotEmpty() && watchedSet.contains(listingId)
        }

        fun clearAllData() {
            watchedSet.clear()
            MMKVUtils.removeData(WATCHED_LIST_KEY)
        }
        
    }
}