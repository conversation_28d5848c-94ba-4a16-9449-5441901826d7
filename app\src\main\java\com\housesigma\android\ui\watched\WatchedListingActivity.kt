package com.housesigma.android.ui.watched

import android.os.Bundle
import android.view.View
import android.os.Build
import android.text.Html
import android.text.TextUtils
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.base.BaseListingsAdapterHelper
import com.housesigma.android.databinding.ActivityWatchedListingBinding
import com.housesigma.android.databinding.EmptyShareWatchListNotExistBinding
import com.housesigma.android.model.HouseDetail
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.MultipleWatchList
import com.housesigma.android.model.WatchList
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.tos.TosDialog
import com.housesigma.android.ui.tos.VowTosDialog
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.Constants
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.MMKVUtils
import com.housesigma.android.utils.ToastUtils
import com.housesigma.android.views.HSAlertDialog
import com.housesigma.android.views.ListingMenuDialog
import com.housesigma.android.views.SelectWatchListDialog
import com.lxj.xpopup.XPopup
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * watchlist分享详情和watchlist详情公用一个activity
 */
class WatchedListingActivity : BaseActivity(), LoginFragment.LoginCallback {

    private lateinit var binding: ActivityWatchedListingBinding
    private lateinit var watchedViewModel: WatchedViewModel
    private var pageNumber = 1
    private val adapter = WatchedListingAdapter()

    private var isWatchList : Boolean = true //true为普通watchlist页面，false为sharewatchlist页面

    //0 "sale"
    //1: "sold"
    //2: "other"
    private var listType = arrayListOf("sale", "sold", "other")

    private var isSelectSale: Boolean = true
    private var isSelectSold: Boolean = true
    private var isSelectDelisted: Boolean = true
    private var needRefreshToPageOne: Boolean = false

    private var mWatchList:WatchList? = null

    private var idUserWatchlist: String = ""

    private var mList: MutableList<HouseDetail> = java.util.ArrayList()
    private var selectWatchlists: MultipleWatchList? = null

    private var idListing: String? = null
    private var mlNum: String? = null

    private var selectWatchListDialog : SelectWatchListDialog?=null
    private var loginDialog: LoginFragment? = null


    override fun getLayout(): Any {
        binding = ActivityWatchedListingBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        idUserWatchlist = intent.getStringExtra("id_user_watchlist") ?: ""
        isWatchList = intent.getBooleanExtra("is_watch_list",true)

        initViews()
    }

    fun tag(): String {
        if (isWatchList){
            return "watched_listings"
        } else {
            return "shared_watchlist_detail"
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        when (event.type) {
            MessageType.WATCHED_MULTIPLE_WATCHLIST_CHANGED -> {
                needRefreshToPageOne = true
            }

            MessageType.WATCHED_MULTIPLE_WATCHLIST_NEWED -> {
                idListing?.let { watchedViewModel.getMultipleWatchList(it) }
            }

            else -> {}
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun onResume() {
        super.onResume()
        GALog.page(tag())
        if (needRefreshToPageOne) {
            if (isWatchList){
                reloadData()
            }
        }
    }

    override fun initData() {
        watchedViewModel.removeWatchedMsg.observe(this) {
            GALog.log("watched_listing_actions", "delete")
            ToastUtils.showDel(it.message)
            reloadData()
        }

        watchedViewModel.addWatchNoteMsg.observe(this){
            GALog.log("watched_listing_actions", "save_note")
        }

        watchedViewModel.unsubscribeWatchListMsg.observe(this){
            ToastUtils.showDel(it.message)
            reloadData()
        }

        watchedViewModel.subscribeWatchListMsg.observe(this){
            ToastUtils.showSuccess(it.message)
            reloadData()
        }


        watchedViewModel.watchList.observe(this) {
            mWatchList = it
            binding.tvWatchlistName.text = it.watchlist_name ?: ""

            needRefreshToPageOne = false
            binding.refreshLayout.finishRefresh(true)
            binding.refreshLayout.finishLoadMore(
                0, true,
                it.houselist.size < 20
            )
            binding.refreshLayout.setNoMoreData(it.houselist.size < 20)

            if (it.is_owner == null || it.is_owner == 1) {
                isWatchList = true
            } else {
                isWatchList = false
            }

            bindViews(adapter, it.houselist)
            binding.ivShare.setOnClickListener {
                share()
            }
            if (isWatchList) {
                adapter.setEmptyView(R.layout.empty_watch_list)
                binding.llListingType.visibility = View.VISIBLE
                binding.tvUpdatedTime.visibility = View.GONE
                binding.tvOwnerName.visibility = View.GONE
                binding.tvFollow.visibility = View.GONE
                if (mWatchList?.is_default == 1) {
                    binding.ivShare.visibility = View.GONE
                } else {
                    binding.ivShare.visibility = View.VISIBLE
                }
            } else {
                binding.llListingType.visibility = View.GONE
                binding.tvUpdatedTime.visibility = View.VISIBLE
                binding.tvOwnerName.visibility = View.VISIBLE


                if (it.owner_name == null) {
                    binding.tvOwnerName.visibility = View.GONE
                } else {
                    binding.tvOwnerName.visibility = View.VISIBLE
                    binding.tvOwnerName.text = "Created by: " + it.owner_name
                }

                if (it.updated_on == null) {
                    binding.tvUpdatedTime.visibility = View.GONE
                } else {
                    binding.tvUpdatedTime.visibility = View.VISIBLE
                    binding.tvUpdatedTime.text = "Updated: " + it.updated_on
                }

                if (it.is_disabled == 1) {
                    binding.tvOwnerName.visibility = View.GONE
                    binding.tvUpdatedTime.visibility = View.GONE
                    binding.tvFollow.visibility = View.GONE
                    binding.ivShare.visibility = View.GONE
                    val view = EmptyShareWatchListNotExistBinding.inflate(layoutInflater)
                    view.tvVisitHomepage.setOnClickListener {
                        EventBus.getDefault().postSticky(MessageEvent(MessageType.JUMP_HOME))
                        GALog.log("shared_watchlist_actions_click","visit_homepage")
                        finish()
                    }
                    adapter.setEmptyView(view.root)
                } else {
                    binding.tvFollow.visibility = View.VISIBLE
                    binding.ivShare.visibility = View.VISIBLE
                    adapter.setEmptyView(R.layout.empty_share_watch_list)



                    val isSubscribed = mWatchList?.is_subscribed == 1
                    if (isSubscribed) {
                        binding.tvFollow.text = "UnFollow"
                    } else {
                        binding.tvFollow.text = "Follow"
                    }

                    binding.tvFollow.setOnClickListener {
                        val followStr = binding.tvFollow.text.toString()
                        if ("Follow".equals(followStr)){
                            if (LoginFragment.isLogin()) {
                                watchedViewModel.subscribeWatchlist(idUserWatchlist)
                            } else {
                                showLoginDialog()
                            }
                        } else {
                            showUnsubscribeConfirmationDialog(idUserWatchlist)
                        }
                    }
                }
            }

        }

        watchedViewModel.multipleWatchList.observe(this) {
            selectWatchlists = it
            idListing?.let { _idListing->
                mlNum?.let { _mlNum ->
                    selectWatchlists?.let { _selectWatchlists->
                        showSaveToWatchDialog(_selectWatchlists, _idListing, _mlNum)
                    }
                }
            }
        }


        watchedViewModel.updateWatchlistUpdateListingsMsg.observe(this){
            ToastUtils.showLong(it.message)
            selectWatchListDialog?.dismiss()
        }

        watchedViewModel.loadingLiveData.observe(this){
            dismissLoadingDialog()
        }

        watchedViewModel.multipleWatchListLoadingLiveData.observe(this) {
            dismissLoadingDialog()
        }

        watchedViewModel.updateWatchlistPrivacyAndShowShareMsg.observe(this){
            ToastUtils.showLong(it.message)
            mWatchList?.privacy = 1
            share()
        }


        reloadData()
    }

    private fun share(){

        if (isWatchList){
            if (mWatchList?.privacy==1) {
                GALog.log("watchlists_actions","share_watchlist")
                mWatchList?.meta?.let { meta ->
                    <EMAIL> {
                        HSUtil.share(
                            it,
                            meta.getShareContent()
                        )
                    }
                }
            } else {
                GALog.log("watchlists_actions","private_to_share")
                HSAlertDialog(
                    this@WatchedListingActivity, "Share watchlist", "This watchlist is private,do you want to change it to public and share?",
                    "Cancel", "OK",
                    object : HSAlertDialog.HSAlertCallback {
                        override fun onSuccess() {
                            watchedViewModel.updateWatchlistPrivacyAndShowShare(idUserWatchlist,mWatchList?.watchlist_name?:"")
                        }
                    }).show()
            }
        } else {
            GALog.log("shared_watchlist_actions_click","share_watchlist")
            mWatchList?.meta?.let { meta ->
                <EMAIL> {
                    HSUtil.share(
                        it,
                        meta.getShareContent()
                    )
                }
            }
        }



    }

    private fun showUnsubscribeConfirmationDialog(id: String) {
        HSAlertDialog(
            this@WatchedListingActivity, "Confirm Removal", "Are you sure you want to remove this watchlist? You will no longer get any updates for this watchlist.",
            "Cancel", "remove",
            object : HSAlertDialog.HSAlertCallback {
                override fun onSuccess() {
                    watchedViewModel.unsubscribeWatchlist(id)
                }
            }).show()
    }


    private fun reloadData() {
        showLoadingDialog()
        pageNumber = 1
        if (isWatchList) {
            watchedViewModel.watchListListings(page = 1, list_type = listType, idUserWatchlist)
        } else {
            watchedViewModel.watchlistSubscribeListings(page = 1, idUserWatchlist)
        }
    }


    private fun bindViews(adapter: WatchedListingAdapter, list: List<HouseDetail>) {
        adapter.setWatchlistType(isWatchList)
        if (pageNumber == 1) {
            mList.clear()
        }

        mList.addAll(list)
        adapter.setList(mList)

        adapter.setOnItemChildClickListener { adapter, view, position ->
            val houseDetail = adapter.data[position] as HouseDetail

            when (view.id) {
                R.id.rl -> {
                    val item = mList[position]
                    if (BaseListingsAdapterHelper.canJumpListingDetail(item)){
                        return@setOnItemChildClickListener
                    }
                    if (isWatchList) {
                        GALog.log("preview_click", "watch_lists")
                    } else {
                        GALog.log("preview_click", "shared_watchlist_listing")
                    }

                    WatchedListingActivity@ this?.let {
                        WebViewHelper.jumpHouseDetail(
                            it,
                            houseDetail.id_listing,
                            houseDetail.seo_suffix
                        )
                    }
                }

                R.id.tv_login_required -> {
                    val tvLoginRequiredStr = (view as TextView).text.toString()
                    if (Constants.TEXT_VIEW_TRREB_TIMEOUT.equals(tvLoginRequiredStr)){
                        showLoginDialog(reLogin = LoginFragment.RE_LOGIN_TRREB_TIMEOUT)
                    } else if ("Password Expired".equals(tvLoginRequiredStr)) {
                        showLoginDialog(reLogin = LoginFragment.RE_LOGIN_VALIDATE)
                    }else{
                        showLoginDialog()
                    }
                }


                R.id.tv_agreement_required -> {
                    GALog.log("agreement_button_click")
                    val houseDetail = mList[position]
                    WatchedListingActivity@ this?.let {
                        TosDialog(HomeFragment@ this,
                            it,
                            it,
                            houseDetail.tos_source,
                            object : TosDialog.TosCallback {
                                override fun onSuccess() {
                                    pageNumber = 1
                                    reloadData()
                                }
                            }).show()
                    }

                }

                R.id.tv_not_available -> {
                    VowTosDialog(houseDetail.id_listing,this,this,this).show()
                }

                R.id.iv_watched_menu -> {//操作

                    if (isWatchList) {
                    } else {
                        GALog.log("shared_watchlist_actions_click", "shared_watchlist_listing_more_vert")
                    }
                    idListing = houseDetail.id_listing
                    mlNum = houseDetail.ml_num


                    idListing?.let { _idListing->
                        mlNum?.let {
                            showListingMenuDialog(houseDetail)
                        }
                    }


                }
            }
        }
    }
    private fun showLoginDialog(reLogin: String = "") {
        GALog.log("login_button_click")
        supportFragmentManager?.let {
            if (loginDialog == null) {
                loginDialog = LoginFragment()
            }
            if (loginDialog?.isAdded == true) return
            val bundle = Bundle()
            bundle.putString("reLogin", reLogin)
            loginDialog?.arguments = bundle
            loginDialog?.show(it, "")
        }
    }

    private fun showListingMenuDialog(houseDetail:HouseDetail) {
//        selectWatchlists?.let {
        val tvWatchlistName = binding.tvWatchlistName.text.toString().trim()?:""
        val multipleWatchListMenuDialog = ListingMenuDialog(
            this,
            isWatchList,
            tvWatchlistName,
            object :
                ListingMenuDialog.ListingsMenuCallback {
                override fun onDelete() {
                    showDeleteConfirmDialog(houseDetail.id_listing)
                }

                override fun onSaveToWatchlist() {
                    if (BaseListingsAdapterHelper.canJumpListingDetail(houseDetail)) {
                        showLoginDialog()
                        return
                    }
                    //请求api 显示loading
                    showLoadingDialog()
                    watchedViewModel.getMultipleWatchList(houseDetail.id_listing)

                }
            })
        XPopup.Builder(this@WatchedListingActivity)
            .enableDrag(false)
            .asCustom(multipleWatchListMenuDialog)
            .show()
//        }
    }

    private fun showSaveToWatchDialog(
        it: MultipleWatchList,
        idListing: String,
        mlNum: String
    ) {
        if (selectWatchListDialog?.isShow==true) {
            selectWatchListDialog?.notifyDataSetChangedAndAutoSelectTop1(selectWatchlists)
            return
        }
        selectWatchListDialog = SelectWatchListDialog(
            idListing,
            mlNum,
            selectWatchlists,
            this@WatchedListingActivity,
            object :
                SelectWatchListDialog.NewWatchListCallback {
                override fun onSuccess(
                    id_listing: String,
                    ml_num: String,
                    ids_user_watchlist: List<String>
                ) {
                    showLoadingDialog()
                    var gaEventTypeName = ""
                    if (isWatchList){
                        gaEventTypeName = "watched_listing_actions"
                    }else{
                        gaEventTypeName = "shared_watchlist_actions_click"
                    }
                    watchedViewModel.updateWatchlistUpdateListings(
                        id_listing,
                        ml_num,
                        ids_user_watchlist,
                        gaEventTypeName
                    )
                }

                override fun onCancel() {
                }
            })
        XPopup.Builder(this@WatchedListingActivity)
            .enableDrag(false)
            .asCustom(selectWatchListDialog)
            .show()
    }

    private fun showDeleteConfirmDialog(idListing: String) {
        HSAlertDialog(
            this@WatchedListingActivity,
            "Confirm Removal",
            "Are you sure you want to remove this listing? You will no longer get any updates.",
            "Cancel",
            "Remove",
            object : HSAlertDialog.HSAlertCallback {
                override fun onSuccess() {
                    watchedViewModel.removeWatched(idUserWatchlist,idListing)
                }
            }).show()
    }

    private fun initViews() {
        binding.ivClose.setOnClickListener { finish() }
        binding.tvSold.setOnClickListener {
            GALog.log("watched_listing_actions", "sold")
            if (isSelectSold) {
                val isDeSelect = deSelectType("sold")
                if (!isDeSelect) return@setOnClickListener
                binding.tvSold.setBackgroundResource(R.drawable.shape_watch_center_normal)
                binding.tvSold.setTextColor(resources.getColor(R.color.app_main_color))
            } else {
                binding.tvSold.setBackgroundResource(R.drawable.shape_watch_center_selected)
                binding.tvSold.setTextColor(resources.getColor(R.color.color_white))

                if (!listType.contains("sold")) {
                    listType.add("sold")
                }
            }
            isSelectSold = !isSelectSold
            MMKVUtils.saveBoolean("watch_unselect_sold", !isSelectSold)

            reloadData()
        }

        binding.tvForSale.setOnClickListener {
            GALog.log("watched_listing_actions", "sale")
            if (isSelectSale) {
                val isDeSelect = deSelectType("sale")
                if (!isDeSelect) return@setOnClickListener
                binding.tvForSale.setBackgroundResource(R.drawable.shape_watch_left_normal)
                binding.tvForSale.setTextColor(resources.getColor(R.color.app_main_color))

            } else {
                binding.tvForSale.setBackgroundResource(R.drawable.shape_watch_left_selected)
                binding.tvForSale.setTextColor(resources.getColor(R.color.color_white))

                if (!listType.contains("sale")) {
                    listType.add("sale")
                }
            }
            isSelectSale = !isSelectSale
            MMKVUtils.saveBoolean("watch_unselect_sale", !isSelectSale)

            reloadData()
        }

        binding.tvDeListed.setOnClickListener {
            GALog.log("watched_listing_actions", "delisted")
            if (isSelectDelisted) {

                val isDeSelect = deSelectType("other")
                if (!isDeSelect) return@setOnClickListener
                binding.tvDeListed.setBackgroundResource(R.drawable.shape_watch_right_normal)
                binding.tvDeListed.setTextColor(resources.getColor(R.color.app_main_color))

            } else {
                binding.tvDeListed.setBackgroundResource(R.drawable.shape_watch_right_selected)
                binding.tvDeListed.setTextColor(resources.getColor(R.color.color_white))

                if (!listType.contains("other")) {
                    listType.add("other")
                }
            }
            isSelectDelisted = !isSelectDelisted
            MMKVUtils.saveBoolean("watch_unselect_de_listed", !isSelectDelisted)

            reloadData()
        }



        binding.rv.setHasFixedSize(true)
        binding.rv.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        binding.rv.adapter = adapter

        binding.refreshLayout.setOnRefreshListener { refreshLayout ->
            refreshLayout.finishRefresh(2000)
            reloadData()
        }

        binding.refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                reloadData()
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                pageNumber++
                if (isWatchList) {
                    watchedViewModel.watchListListings(pageNumber, list_type = listType, idUserWatchlist)
                } else {
                    watchedViewModel.watchlistSubscribeListings(pageNumber, idUserWatchlist)
                }
            }
        })

        if (isWatchList) {
            adapter.setEmptyView(R.layout.empty_watch_list)
            binding.llListingType.visibility = View.VISIBLE
            binding.tvUpdatedTime.visibility = View.GONE
            binding.tvOwnerName.visibility = View.GONE
        } else {
            adapter.setEmptyView(R.layout.empty_share_watch_list)
            binding.llListingType.visibility = View.GONE
            binding.tvUpdatedTime.visibility = View.VISIBLE
            binding.tvOwnerName.visibility = View.VISIBLE
        }

    }

    private fun deSelectType(type: String): Boolean {
        if ((listType.size == 1)) {
            return false
        }
        if (listType.contains(type)) {
            listType.remove(type)
        }
        return true
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
        pageNumber = 1
        reloadData()
    }

}