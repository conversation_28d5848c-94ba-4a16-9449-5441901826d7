package com.housesigma.android.ui.watched

import android.graphics.Bitmap
import android.graphics.Paint
import android.text.TextUtils
import android.util.TypedValue
import android.widget.LinearLayout
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.base.BaseListingsAdapterHelper
import com.housesigma.android.model.HouseDetail
import com.housesigma.android.model.SortScore
import com.housesigma.android.utils.ScreenUtils
import jp.wasabeef.glide.transformations.BlurTransformation
import jp.wasabeef.glide.transformations.RoundedCornersTransformation


class WatchedListingAdapter() :
    BaseQuickAdapter<HouseDetail, BaseViewHolder>(R.layout.item_watched_listing) {

    private var mIsWatchList : Boolean = false
    private var mIsWatchNoteList : Boolean = false

    init {
        addChildClickViewIds(
            R.id.iv_watched_menu,
            R.id.rl,
            R.id.tv_agreement_required,
            R.id.tv_login_required
        )
    }

    override fun convert(holder: BaseViewHolder, item: HouseDetail) {

        if (!TextUtils.isEmpty(item?.list_status?.text)) {
            holder.setVisible(R.id.tv_status, true)
            holder.setText(R.id.tv_status, item?.list_status?.text?:"")
        } else {
            holder.setGone(R.id.tv_status, true)
        }

        // 副标题区，主要为位置信息
        var address =
            if (!TextUtils.isEmpty(item.municipality_name))
                "${item.address},  ${item.municipality_name}"
            else item.address
        address = address.plus(
            if (!TextUtils.isEmpty(item.community_name))
                " - ${item.community_name}"
            else ""
        )

        holder.setText(R.id.tv_address, address)

//        // 底部房源信息，包含：房屋类型，卧、卫、车库数量
        holder.setText(R.id.tv_house_type_name, item.house_type_name)
        if (item.bedroom_string==null) {
            holder.setGone(R.id.tv_bedroom_string, true)
        } else {
            holder.setVisible(R.id.tv_bedroom_string, true)
            holder.setText(R.id.tv_bedroom_string, item.bedroom_string)
        }

        if (item.washroom==null) {
            holder.setGone(R.id.tv_washroom, true)
        } else {
            holder.setVisible(R.id.tv_washroom, true)
            holder.setText(R.id.tv_washroom, item.washroom)
        }

        if (item.parking.total==null) {
            holder.setGone(R.id.tv_garage, true)
        } else {
            holder.setVisible(R.id.tv_garage, true)
            holder.setText(R.id.tv_garage, item.parking.total)
        }

        // 主标题区
        handleTitleAreaText(holder, item)

        // 最下方scores school growth 标签
        handleTag(item, holder)

        // brokerage_text
        handleBrokerageText(holder, item)

        // 楼花tag
        handleAssignmentTag(holder, item)

        // with hs tag
        handleWithHsTag(holder, item)

        // 房源图片以及遮挡逻辑
        BaseListingsAdapterHelper.handleImageMaskView(context,item, holder)

        // 如果是watch note列表，就隐藏watched menu
        if (mIsWatchNoteList){
            holder.setGone(R.id.iv_watched_menu,true)
        } else {
            holder.setVisible(R.id.iv_watched_menu,true)
        }
    }

    private fun handleAssignmentTag(holder: BaseViewHolder, item: HouseDetail) {
        if (TextUtils.isEmpty(item.text.hs_exclusive_tag)) {
            holder.setGone(R.id.tv_assignment, true)
        } else {
            holder.setText(R.id.tv_assignment,item.text.hs_exclusive_tag)
            holder.setVisible(R.id.tv_assignment, true)
        }
    }

    private fun handleWithHsTag(holder: BaseViewHolder, item: HouseDetail) {
        if (!TextUtils.isEmpty(item.text.transaction_via_label)) {
            holder.setText(R.id.tv_with_hs, item.text.transaction_via_label)
            holder.setVisible(R.id.tv_with_hs, true)
        } else {
            holder.setGone(R.id.tv_with_hs, true)
        }
    }

    private fun handleBrokerageText(holder: BaseViewHolder, item: HouseDetail) {
        if (TextUtils.isEmpty(item.brokerage_text)) {
            holder.setGone(R.id.tv_bc_brokerage_text, true)
            holder.setGone(R.id.line2, true)
            holder.setVisible(R.id.line, true)
        } else {
            holder.setVisible(R.id.tv_bc_brokerage_text, true)
            holder.setText(R.id.tv_bc_brokerage_text, item.brokerage_text)
            holder.setVisible(R.id.line2, true)
            holder.setGone(R.id.line, true)
        }
    }

    private fun handleTag(
        item: HouseDetail,
        holder: BaseViewHolder
    ) {
        if (item.scores.rent != null) {
            holder.setText(R.id.tv_rental, "Rental " + item.scores.rent + "/10")
            holder.setVisible(R.id.tv_rental, true)
        } else {
            holder.setGone(R.id.tv_rental, true)
        }

        if (item.scores.school != null) {
            holder.setText(R.id.tv_school, "School " + item.scores.school + "/10")
            holder.setVisible(R.id.tv_school, true)
        } else {
            holder.setGone(R.id.tv_school, true)
        }

        if (item.scores.growth != null) {
            holder.setText(R.id.tv_growth, "Growth " + item.scores.growth + "/10")
            holder.setVisible(R.id.tv_growth, true)
        } else {
            holder.setGone(R.id.tv_growth, true)
        }
        adjustScoreViewOrder(holder, item)
    }

    /**
     * 调整分数显示顺序，第一顺序是分数，第二顺序分数相同，按School、Rental、Growth再排序
     */
    private fun adjustScoreViewOrder(
        holder: BaseViewHolder,
        item: HouseDetail
    ) {
        val tvSchool = holder.getView<TextView>(R.id.tv_school)
        val tvRental = holder.getView<TextView>(R.id.tv_rental)
        val tvGrowth = holder.getView<TextView>(R.id.tv_growth)
        val llScoreView = holder.getView<LinearLayout>(R.id.ll_score)

        llScoreView.removeAllViews()

        val rentScore = if (item.scores.rent != null) item.scores.rent.toInt() else 0
        val schoolScore = if (item.scores.school != null) item.scores.school.toInt() else 0
        val growthScore = if (item.scores.growth != null) item.scores.growth.toInt() else 0

        val scoreList = listOf(
            SortScore(schoolScore, "school"),
            SortScore(rentScore, "rent"),
            SortScore(growthScore, "growth")
        ).sortedByDescending { it.score }

        scoreList.forEach {
            when (it.scoreType) {
                "rent" -> {
                    llScoreView.addView(tvRental)
                }
                "school" -> {
                    llScoreView.addView(tvSchool)
                }
                "growth" -> {
                    llScoreView.addView(tvGrowth)
                }
            }
        }
    }

    private fun handleTitleAreaText(holder: BaseViewHolder, item: HouseDetail) {
        holder.setVisible(R.id.tv_what_for, true)

        val tvPrice = holder.getView<TextView>(R.id.tv_price)

        if (!TextUtils.isEmpty(item.price)) {
            holder.setVisible(R.id.ll_sell,true)
            holder.setText(R.id.tv_price, " $" + item.price)
        } else {
            holder.setGone(R.id.ll_sell,true)
        }

//        list_status.live === 1 (for sale, for lease)
//        list_status.sold === 1 (Sold, Leased)
//        list_status.live === 0, list_status.sold === 0 (Terminated, Suspeneded, Expired ....)
        if (item.list_status.live==1) {
            tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP,18.0f)
            tvPrice.setTextAppearance(context, R.style.H1Header)

            holder.setText(R.id.tv_days_ago, item.text.date_preview)
            holder.setText(R.id.tv_watched_days_ago, "")
        }

        if (!TextUtils.isEmpty(item.price_sold)&&item.list_status.sold==1) {
            tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP,14.0f)
            tvPrice.setTextAppearance(context, R.style.Subtitles2)

            holder.setText(R.id.tv_days_ago, "")
            holder.setText(R.id.tv_watched_days_ago, item.text.date_preview)

            holder.setVisible(R.id.ll_sold,true)
            
            // Set the sold for label text based on listing status
            holder.setText(R.id.tv_sold_for_label_watched, item.list_status.getSoldForDisplayText())
            
            holder.setText(R.id.tv_sold_price, " $" + item.price_sold)
            tvPrice.paint.flags = (Paint.STRIKE_THRU_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG)//中划线
        } else {
            tvPrice.paint.flags = 0
            tvPrice.paint.isAntiAlias = true
            holder.setGone(R.id.ll_sold,true)
        }

        if (item.list_status.live == 0 && item.list_status.sold == 0) {
            tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP,18.0f)
            tvPrice.setTextAppearance(context, R.style.H1Header)

            holder.setText(R.id.tv_days_ago, item.text.date_preview)
            holder.setText(R.id.tv_watched_days_ago, "")

            tvPrice.paint.flags = (Paint.STRIKE_THRU_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG)//中划线
        }
    }

    fun setWatchlistType(isWatchList: Boolean) {
        mIsWatchList = isWatchList
    }

    fun setWatchNoteListType(isWatchNoteList: Boolean) {
        mIsWatchNoteList = isWatchNoteList
    }

}