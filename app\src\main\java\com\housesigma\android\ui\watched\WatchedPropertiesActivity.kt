package com.housesigma.android.ui.watched

import android.os.Bundle
import android.widget.LinearLayout
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.base.BaseListingsAdapterHelper
import com.housesigma.android.databinding.ActivityWatchedPropertiesListBinding
import com.housesigma.android.databinding.HeaderWatchedPropertiesDescriptionBinding
import com.housesigma.android.model.HouseDetail
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.tos.TosDialog
import com.housesigma.android.ui.tos.VowTosDialog
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.Constants
import com.housesigma.android.utils.GALog
import com.housesigma.android.views.DisclaimerViewHelper
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener

class WatchedPropertiesActivity : BaseActivity(), LoginFragment.LoginCallback {

    private lateinit var binding: ActivityWatchedPropertiesListBinding
    private lateinit var watchedViewModel: WatchedViewModel
    private val adapter = NormalListingCollectionAdapter()
    private var mList: MutableList<HouseDetail> = java.util.ArrayList()
    private var page = 1
    private var id: String = ""
    private var description: String = ""
    private var loginDialog: LoginFragment? = null

    override fun onResume() {
        super.onResume()
        GALog.page("listings_of_watched_area")
    }

    override fun getLayout(): Any {
        binding = ActivityWatchedPropertiesListBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {
        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }

        binding.rv.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        binding.rv.adapter = adapter

        binding.refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                page = 1
                loadData()
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                page++
                loadData()
            }
        })

        binding.ivClose.setOnClickListener {
            finish()
        }
    }

    override fun initData() {
        id = intent.getStringExtra("id").toString()
        description = intent.getStringExtra("description").toString()
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        watchedViewModel.watchPolygonArea.observe(this) {
            binding.refreshLayout.finishRefresh(true)
            binding.refreshLayout.finishLoadMore(
                0, true,
                it.size < 20
            )
            binding.refreshLayout.setNoMoreData(it.size < 20)
            bindViews(adapter, it)
        }
        loadData()
    }

    fun loadData() {
        watchedViewModel.getWatchPolygonArea(id = id, page = page)
    }

    private fun bindViews(adapter: NormalListingCollectionAdapter, list: List<HouseDetail>) {
        if (page == 1) {
            mList.clear()
        }

        mList.addAll(list)
        adapter.setList(mList)
        adapter.removeAllHeaderView()
        val binding = HeaderWatchedPropertiesDescriptionBinding.inflate(layoutInflater)
        adapter.addHeaderView(
            binding.root,
            orientation = LinearLayout.VERTICAL
        )
        binding.tvDescription.text = description

        adapter.addChildClickViewIds(
            R.id.rl, R.id.tv_agreement_required,R.id.tv_not_available
        )

        adapter.setOnItemChildClickListener { adapter, view, position ->
            val houseDetail = adapter.data[position] as HouseDetail

            when (view.id) {
                R.id.rl -> {
                    if (BaseListingsAdapterHelper.canJumpListingDetail(houseDetail)) {
                        return@setOnItemChildClickListener
                    }
                    GALog.log("preview_click", "areas")
                    WebViewHelper.jumpHouseDetail(
                        this,
                        houseDetail.id_listing,
                        houseDetail.seo_suffix
                    )
                }

                R.id.tv_login_required -> {
                    val tvLoginRequiredStr = (view as TextView).text.toString()
                    if (Constants.TEXT_VIEW_TRREB_TIMEOUT.equals(tvLoginRequiredStr)){
                        showLoginDialog(reLogin = LoginFragment.RE_LOGIN_TRREB_TIMEOUT)
                    } else if ("Password Expired".equals(tvLoginRequiredStr)) {
                        showLoginDialog(reLogin = LoginFragment.RE_LOGIN_VALIDATE)
                    }else{
                        showLoginDialog()
                    }
                }

                R.id.tv_agreement_required -> {
                    this.let {
                        TosDialog(
                            this, it, it, houseDetail.tos_source,
                            object : TosDialog.TosCallback {
                                override fun onSuccess() {
                                    page = 1
                                    loadData()
                                }
                            }).show()
                    }
                }

                R.id.tv_not_available -> {
                    VowTosDialog(houseDetail.id_listing,this,this,this).show()
                }
            }
        }
    }

    private fun showLoginDialog(reLogin: String = "") {
        GALog.log("login_button_click")
        supportFragmentManager?.let {
            if (loginDialog == null) {
                loginDialog = LoginFragment()
            }
            if (loginDialog?.isAdded == true) return
            val bundle = Bundle()
            bundle.putString("reLogin", reLogin)
            loginDialog?.arguments = bundle
            loginDialog?.show(it, "")
        }
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
        page = 1
        loadData()
    }

}