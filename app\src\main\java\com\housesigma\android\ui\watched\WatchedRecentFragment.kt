package com.housesigma.android.ui.watched

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.housesigma.android.R
import com.housesigma.android.base.BaseFragment
import com.housesigma.android.base.BaseListingsAdapterHelper
import com.housesigma.android.databinding.FragmentWatchedRecentBinding
import com.housesigma.android.model.HouseDetail
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.tos.TosDialog
import com.housesigma.android.ui.tos.VowTosDialog
import com.housesigma.android.ui.webview.WebViewHelper
import com.housesigma.android.utils.Constants
import com.housesigma.android.utils.GALog


class WatchedRecentFragment : BaseFragment(), LoginFragment.LoginCallback {

    private lateinit var binding: FragmentWatchedRecentBinding
    private lateinit var watchedViewModel: WatchedViewModel
    private val adapter = NormalListingCollectionAdapter()
    private var isFirstShow: Boolean = true
    private var loginDialog: LoginFragment? = null

    override fun createView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        binding = FragmentWatchedRecentBinding.inflate(inflater, container, false)
        return binding.root
    }


    override fun initView(root: View?) {
        initViews()
    }

    override fun initListener() {
    }

    override fun tag(): String {
        return "recently_viewed"
    }

    override fun onResume() {
        super.onResume()
        if (!isFirstShow) {
            loadData()
        } else {
            isFirstShow = false
        }
    }
    override fun onVisibilityChanged(visible: Boolean) {
        super.onFragmentVisibilityChanged(visible)
        if (visible) {
        }
    }
    override fun refreshLoad() {
        super.refreshLoad()
        loadData()
    }

    override fun lazyLoad() {
        loadData()
    }

    private fun loadData() {
        watchedViewModel.recentHouseList.observe(viewLifecycleOwner){
            bindViews(adapter, it)
        }
        watchedViewModel.recentViewed()
    }

    private fun bindViews(
        adapter: NormalListingCollectionAdapter,
        list: List<HouseDetail>,
    ) {
        adapter.data = list.toMutableList()
        adapter.notifyDataSetChanged()
        if (list.size == 0) {
            adapter.recyclerView.visibility = View.GONE
        } else {
            adapter.recyclerView.visibility = View.VISIBLE
            adapter.setOnItemChildClickListener { adapter, view, position ->

                val item = adapter.getItem(position) as HouseDetail



                when (view.id) {
                    R.id.rl -> {
                        if (BaseListingsAdapterHelper.canJumpListingDetail(item)) return@setOnItemChildClickListener
                        GALog.log("preview_click","recent_view")
                        activity?.let { WebViewHelper.jumpHouseDetail(it,item.id_listing,item.seo_suffix) }
                    }


                    R.id.tv_login_required -> {
                        val tvLoginRequiredStr = (view as TextView).text.toString()
                        if (Constants.TEXT_VIEW_TRREB_TIMEOUT.equals(tvLoginRequiredStr)){
                            showLoginDialog(reLogin = LoginFragment.RE_LOGIN_TRREB_TIMEOUT)
                        } else if ("Password Expired".equals(tvLoginRequiredStr)) {
                            showLoginDialog(reLogin = LoginFragment.RE_LOGIN_VALIDATE)
                        } else {
                            showLoginDialog()
                        }
                    }


                    R.id.tv_agreement_required -> {
                        val houseDetail = list[position]
                        activity?.let {
                            TosDialog(HomeFragment@ this, viewLifecycleOwner, it, houseDetail.tos_source,
                                object : TosDialog.TosCallback {
                                    override fun onSuccess() {
                                        loadData()
                                    }
                                }).show()
                        }

                    }

                    R.id.tv_not_available -> {
                        val houseDetail = list[position]
                        context?.let { VowTosDialog(houseDetail.id_listing,this,this,it).show() }
                    }

                }
            }


        }

    }
    private fun showLoginDialog(reLogin: String = "") {
        GALog.log("login_button_click")
        childFragmentManager?.let {
            if (loginDialog == null) {
                loginDialog = LoginFragment()
            }
            if (loginDialog?.isAdded == true) return
            val bundle = Bundle()
            bundle.putString("reLogin", reLogin)
            loginDialog?.arguments = bundle
            loginDialog?.show(it, "")
        }
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
        loadData()
    }

    private fun initViews() {
        binding.rv.layoutManager =
            LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
        binding.rv.adapter = adapter

        binding.refreshLayout.setOnRefreshListener { refreshLayout ->
            refreshLayout.finishRefresh(2000)
            loadData()
        }

    }

}