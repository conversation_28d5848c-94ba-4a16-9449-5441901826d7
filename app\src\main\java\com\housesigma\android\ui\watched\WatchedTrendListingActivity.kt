package com.housesigma.android.ui.watched

import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.base.BaseListingsAdapterHelper
import com.housesigma.android.databinding.ActivityWatchedTrendListBinding
import com.housesigma.android.model.HouseDetail
import com.housesigma.android.ui.tos.TosDialog
import com.housesigma.android.ui.tos.VowTosDialog
import com.housesigma.android.ui.webview.WebViewHelper
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener

class WatchedTrendListingActivity : BaseActivity() {

    private lateinit var binding: ActivityWatchedTrendListBinding
    private lateinit var watchedViewModel: WatchedViewModel
    private val adapter = NormalListingCollectionAdapter()
    private var mList: MutableList<HouseDetail> = java.util.ArrayList()
    private var pageNumber = 1
    private var community: String = ""
    private var house_type: String = ""
    private var municipality: String = ""
    private var type: String = ""
    override fun getLayout(): Any {
        community = intent.getStringExtra("community").toString()
        house_type = intent.getStringExtra("house_type").toString()
        municipality = intent.getStringExtra("municipality").toString()
        type = intent.getStringExtra("type").toString()
        binding = ActivityWatchedTrendListBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun initView() {

        immersionBar {
            navigationBarColor(R.color.navigation_bar_color)
    
            fitsSystemWindows(true)
            statusBarColor(R.color.app_main_color)
            statusBarDarkFont(false)
        }

        binding.rv.layoutManager =
            LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        binding.rv.adapter = adapter

        binding.refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                pageNumber = 1
                loadData()
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                pageNumber++
                loadData()
            }
        })

        binding.ivClose.setOnClickListener {
            finish()
        }

    }

    override fun initData() {

        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        watchedViewModel.trendHouseList.observe(this) {
            binding.refreshLayout.finishRefresh(true)
            binding.refreshLayout.finishLoadMore(
                0, true,
                it.list.size < 10
            )
            binding.refreshLayout.setNoMoreData(it.list.size < 10)
            binding.tvTitle.text = it.head
            bindViews(adapter, it.list)
        }
        loadData()
    }

    fun loadData() {
//        community: "14"
//        house_type: "D."
//        municipality: "10343"
//        type: "2"
        watchedViewModel.getTrendHouseList(community = community, house_type =house_type,municipality = municipality,page = pageNumber,type = type)
    }

    private fun bindViews(adapter: NormalListingCollectionAdapter, list: List<HouseDetail>) {
        if (pageNumber == 1) {
            mList.clear()
        }

        mList.addAll(list)
        adapter.setList(mList)
//        community=14&house_type=D.&municipality=10343&page=0&type=2&province=ON&lang=zh_CN
//        community=14&house_type=D.&municipality=10343&page=1&type=2&province=ON&lang=zh_CN


        adapter.setOnItemChildClickListener { adapter, view, position ->
            val houseDetail = adapter.data[position] as HouseDetail

            when (view.id) {
                R.id.rl -> {
                    if (BaseListingsAdapterHelper.canJumpListingDetail(houseDetail)) {
                        return@setOnItemChildClickListener
                    }
                    WebViewHelper.jumpHouseDetail(
                        this,
                        houseDetail.id_listing,
                        houseDetail.seo_suffix
                    )
                }

                R.id.tv_agreement_required -> {
                    this.let {
                        TosDialog(
                            this, it, it, houseDetail.tos_source,
                            object : TosDialog.TosCallback {
                                override fun onSuccess() {
                                    loadData()
                                }
                            }).show()
                    }
                }

                R.id.tv_not_available -> {
                    VowTosDialog(houseDetail.id_listing,this,this,this).show()
                }
            }
        }
    }

}