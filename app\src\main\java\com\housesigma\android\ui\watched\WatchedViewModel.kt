package com.housesigma.android.ui.watched

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.housesigma.android.base.SingleLiveEvent
import com.housesigma.android.model.*
import com.housesigma.android.network.NetClient
import com.housesigma.android.network.launch
import com.housesigma.android.utils.AndroidHapticFeedback
import com.housesigma.android.utils.GALog
import org.greenrobot.eventbus.EventBus

class WatchedViewModel : ViewModel() {

    var recentHouseList: MutableLiveData<List<HouseDetail>> = MutableLiveData()
    var watchList: MutableLiveData<WatchList> = MutableLiveData()
    var watchNoteList: MutableLiveData<NoteWatchList> = MutableLiveData()
    var multipleWatchList: MutableLiveData<MultipleWatchList> = MutableLiveData()
    var watchListSubscribeList: MutableLiveData<MultipleWatchList> = MutableLiveData()
    var addWatchNoteMsg: MutableLiveData<MsgRes> = MutableLiveData()
    var removeWatchedMsg: MutableLiveData<MsgRes> = MutableLiveData()
    var watchCommunity: MutableLiveData<WatchCommunity> = MutableLiveData()
    var removeWatchedCommunityMsg: MutableLiveData<MsgRes> = MutableLiveData()
    var updateWatchCommunityMsg: MutableLiveData<MsgRes> = MutableLiveData()
    var addWatchCommunityMsg: MutableLiveData<MsgRes> = MutableLiveData()
    var initCommunityFilterList: MutableLiveData<CommunityFilter> = MutableLiveData()
    var watchedArea: MutableLiveData<WatchedAreaV2> = MutableLiveData()
    var delAreasMsg: MutableLiveData<MsgRes> = MutableLiveData()
    var watchPolygonArea: MutableLiveData<List<HouseDetail>> = MutableLiveData()
    var saveWatchPolygon: MutableLiveData<SaveWatchPolygon> = MutableLiveData()
    var updateWatchPolygon: MutableLiveData<MsgRes> = MutableLiveData()
    var getWatchPolygon: MutableLiveData<WatchPolygon> = MutableLiveData()
    var trendHouseList: MutableLiveData<TrendHouseList> = MutableLiveData()
    val loadingLiveData = MutableLiveData<Boolean>()
    val multipleWatchListLoadingLiveData = MutableLiveData<Boolean>()
    val watchListSubscribeListLoadingLiveData = MutableLiveData<Boolean>()
    var stopPromptMsg: MutableLiveData<MsgRes> = MutableLiveData()
    var addWatchListMsg: SingleLiveEvent<MsgRes> = SingleLiveEvent()
    var delWatchListMsg: MutableLiveData<MsgRes> = MutableLiveData()
    var subscribeWatchListMsg: MutableLiveData<MsgRes> = MutableLiveData()
    var unsubscribeWatchListMsg: MutableLiveData<MsgRes> = MutableLiveData()
    var updateWatchlistUpdateListingsMsg: MutableLiveData<MsgRes> = MutableLiveData()
    var updateWatchlistUpdateMsg: SingleLiveEvent<MsgRes> = SingleLiveEvent()
    var updateWatchlistPrivacyAndShowShareMsg: SingleLiveEvent<MsgRes> = SingleLiveEvent()

    fun saveWatchPolygon(
        description: String,
        polygon: List<Polygon>,
    ) {
        launch({
            val filter = WatchPolygonFilter(
                basement = mutableListOf(),
                bathroom_min = 0,
                bedroom_range = mutableListOf(0),
                description = "",
                front_feet = mutableListOf(0, 100),
                garage_min = 0,
                house_type = mutableListOf("all"),
                list_type = mutableListOf(1, 3, 5),
                listing_price = mutableListOf(0, 6000000),
                max_maintenance_fee = "0",
                open_house_date = 0,
//                show_comparision = 0,
//                show_history = 0,
                square_footage = mutableListOf(0, 4000)
            )
            val watchedAreaSave = WatchedAreaSave(
                description = description,
                filter = filter,
                lang = "en_US",
                polygon = polygon,
                province = "ON"
            )
            NetClient.apiService.saveWatchPolygon(watchedAreaSave)
        }, {
            saveWatchPolygon.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }


    fun updateWatchPolygon(
        dataSaveWatchPolygon: DataSaveWatchPolygon
    ) {
        launch({
            val watchedAreaSave = WatchedAreaSave(
                id = dataSaveWatchPolygon.id.toString(),
                description = dataSaveWatchPolygon.description,
                filter = dataSaveWatchPolygon.filter,
                polygon = dataSaveWatchPolygon.polygon,
                lang = "en_US",
                province = "ON"
            )
            NetClient.apiService.updateWatchPolygon(watchedAreaSave)
        }, {
            updateWatchPolygon.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun getTrendHouseList(
        community: String, house_type: String,
        municipality: String, page: Int,
        type: String
    ) {
        launch({
            NetClient.apiService.getTrendHouseList(community, house_type, municipality, page, type)
        }, {
            trendHouseList.postValue(it)
        })
    }

    fun getWatchPolygonArea(id: String, page: Int) {
        launch({
            NetClient.apiService.getWatchPolygonArea(id, page)
        }, {
            watchPolygonArea.postValue(it)
        })
    }


    fun getWatchPolygon(id: String) {
        launch({
            NetClient.apiService.getWatchPolygon(id)
        }, {
            getWatchPolygon.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun stopPrompt() {
        launch({
            NetClient.apiService.stopPrompt()
        }, {
            stopPromptMsg.postValue(it)
        })
    }


    fun initCommunityFilter() {
        launch({
            NetClient.apiService.initCommunityFilter()
        }, {
            initCommunityFilterList.postValue(it)
        })
    }

    fun addWatchCommunity(
        house_type: String,
        id: Int
    ) {
        launch({
            NetClient.apiService.addWatchCommunity(house_type, id)
        }, {
            addWatchCommunityMsg.postValue(it)
        })
    }

    fun updateWatchCommunity(
        house_type: String,
        id: Int,
        watch_type: List<String>
    ) {
        launch({
            NetClient.apiService.updateWatchCommunity(house_type, id, watch_type)
        }, {
            updateWatchCommunityMsg.postValue(it)
        })
    }

    fun recentViewed() {
        launch({
            NetClient.apiService.recentViewed()
        }, {
            recentHouseList.postValue(it)
        })
    }

    fun getWatchPolygonList() {
        launch({
            NetClient.apiService.watchPolygonList()
        }, {
            watchedArea.postValue(it)
        })
    }

    fun deletePolygon(id: Int) {
        launch({
            NetClient.apiService.deletePolygon(id)
        }, {
            delAreasMsg.postValue(it)
        })
    }


    fun getNoteList(page: Int = 1, list_type: List<String>) {
        launch({
            NetClient.apiService.getNoteList(page, list_type)
        }, {
            watchNoteList.postValue(it)
        },{
            loadingLiveData.postValue(true)
        })
    }


    fun updateWatchlistUpdateListings(
        id_listing: String,
        ml_num: String,
        ids_user_watchlist: List<String>,
        gaEventName:String //DEV-5689 这里需要区分ga埋点的事件名称 现在有 watchlist_actions 和watched_listing_actions 和 watch_listing_click 三种
    ) {
        launch({
            NetClient.apiService.updateWatchlistUpdateListings(
                id_listing,
                ml_num,
                ids_user_watchlist
            )
        }, {
            EventBus.getDefault().postSticky(MessageEvent(MessageType.WATCHED_MULTIPLE_WATCHLIST_CHANGED))
            when (gaEventName) {
                "shared_watchlist_actions_click"->{
                    if (ids_user_watchlist.size==0) {
                        GALog.log("shared_watchlist_actions_click","remove_from_my_watchlist")
                    } else {
                        GALog.log("shared_watchlist_actions_click","save_to_my_watchlist")
                    }
                }
                "watch_listing_click" -> {
                    if (ids_user_watchlist.size==0) {
                        GALog.log("watch_listing_click","remove_from_watchlist")
                    } else {
                        GALog.log("watch_listing_click","add_to_watchlist")
                    }
                }
                "watchlists_actions" -> {
                    if (ids_user_watchlist.size==0) {
                        GALog.log("watchlists_actions","remove_from_watchlist")
                    } else {
                        GALog.log("watchlists_actions","save_listing")
                    }
                }
                "watched_listing_actions" -> {
                    if (ids_user_watchlist.size==0) {
                        GALog.log("watched_listing_actions","delete")
                    } else {
                        GALog.log("watched_listing_actions","save_to_watchlist")
                    }
                }
            }

            if (ids_user_watchlist.size!=0) {
                AndroidHapticFeedback().heavyClick()
            }

            updateWatchlistUpdateListingsMsg.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun updateWatchlistUpdate(id_user_watchlist: String, name: String, privacy: Int) {
        launch({
            NetClient.apiService.updateWatchlistUpdate(id_user_watchlist, name, privacy)
        }, {
            EventBus.getDefault().postSticky(MessageEvent(MessageType.WATCHED_MULTIPLE_WATCHLIST_CHANGED))
            updateWatchlistUpdateMsg.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }


    fun updateWatchlistPrivacyAndShowShare(id_user_watchlist: String, name: String) {
        launch({
            NetClient.apiService.updateWatchlistUpdate(id_user_watchlist, name, 1)
        }, {
            EventBus.getDefault().postSticky(MessageEvent(MessageType.WATCHED_MULTIPLE_WATCHLIST_CHANGED))
            updateWatchlistPrivacyAndShowShareMsg.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun watchlistSubscribeListings(page: Int = 1, id_user_watchlist: String) {
        launch({
            NetClient.apiService.watchlistSubscribeListings(page, id_user_watchlist)
        }, {
            watchList.postValue(it)
        },{
            loadingLiveData.postValue(true)
        })
    }

    fun watchListListings(page: Int = 1, list_type: List<String>, id_user_watchlist: String) {
        launch({
            NetClient.apiService.watchListListings(page, list_type, id_user_watchlist)
        }, {
            watchList.postValue(it)
        },{
            loadingLiveData.postValue(true)
        })
    }

    fun getMultipleWatchList(id_listing: String) {
        launch({
            NetClient.apiService.getMultipleWatchList(id_listing)
        }, {
            multipleWatchList.postValue(it)
        },{
            multipleWatchListLoadingLiveData.postValue(true)
        })
    }

    fun getWatchListSubscribeList() {
        launch({
            NetClient.apiService.getWatchListSubscribeList()
        }, {
            watchListSubscribeList.postValue(it)
        },{
            watchListSubscribeListLoadingLiveData.postValue(true)
        })
    }

    fun subscribeWatchlist(id_user_watchlist:String) {
        launch({
            NetClient.apiService.subscribeWatchlist(id_user_watchlist)
        }, {
            GALog.log("shared_watchlist_actions_click","follow_watchlist")
            EventBus.getDefault().postSticky(MessageEvent(MessageType.WATCHED_MULTIPLE_WATCHLIST_CHANGED))
            subscribeWatchListMsg.postValue(it)
        })
    }

    fun unsubscribeWatchlist(id_user_watchlist:String) {
        launch({
            NetClient.apiService.unsubscribeWatchlist(id_user_watchlist)
        }, {
            GALog.log("shared_watchlist_actions_click","unfollow_watchlist")
            EventBus.getDefault().postSticky(MessageEvent(MessageType.WATCHED_MULTIPLE_WATCHLIST_CHANGED))
            unsubscribeWatchListMsg.postValue(it)
        })
    }

    fun addWatchlist(name: String,privacy :Int) {
        launch({
            NetClient.apiService.addWatchlist(name,privacy)
        }, {
            EventBus.getDefault().postSticky(MessageEvent(MessageType.WATCHED_MULTIPLE_WATCHLIST_CHANGED))
            addWatchListMsg.postValue(it)
        }, {
            loadingLiveData.postValue(true)
        })
    }

    fun delWatchlist(id_user_watchlist: String) {
        launch({
            NetClient.apiService.delWatchlist(id_user_watchlist)
        }, {
            EventBus.getDefault().postSticky(MessageEvent(MessageType.WATCHED_MULTIPLE_WATCHLIST_CHANGED))
            delWatchListMsg.postValue(it)
        })
    }


    fun addWatchNote(id_listing: String, note: String) {
        launch({
            NetClient.apiService.addWatchNote(id_listing, note)
        }, {
            addWatchNoteMsg.postValue(it)
        })
    }

    fun removeWatched(id_user_watchlist: String,id_listing: String) {
        launch({
            EventBus.getDefault().postSticky(MessageEvent(MessageType.WATCHED_MULTIPLE_WATCHLIST_CHANGED))
            NetClient.apiService.removeWatched(id_user_watchlist,id_listing)
        }, {
            removeWatchedMsg.postValue(it)
        })
    }

    fun getWatchCommunityList() {
        launch({
            NetClient.apiService.getWatchCommunityList()
        }, {
            watchCommunity.postValue(it)
        })
    }

    fun removeCommunityWatch(house_type: String, id: Int) {
        launch({
            NetClient.apiService.removeCommunityWatch(house_type, id)
        }, {
            removeWatchedCommunityMsg.postValue(it)
        })
    }

    fun watchlistListingIds() {
        launch({
            NetClient.apiService.watchlistListingIds()
        }, {
            it.watched_listings?.let { watchedListings -> WatchedHelper.saveWatchedBatch(watchedListings) }
            EventBus.getDefault().postSticky(MessageEvent(MessageType.RELOAD_WATCHED_STATUS))
        })
    }

}