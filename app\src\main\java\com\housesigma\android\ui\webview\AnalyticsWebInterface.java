package com.housesigma.android.ui.webview;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.webkit.JavascriptInterface;

import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.gson.Gson;
import com.housesigma.android.BuildConfig;
import com.housesigma.android.model.PageViewModel;
import com.housesigma.android.utils.GALog;
import com.housesigma.android.utils.GsonUtils;
import com.housesigma.android.utils.log.Logger;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Iterator;

@Deprecated
public class AnalyticsWebInterface {

    public static final String TAG = "AnalyticsWebInterface";
    private FirebaseAnalytics mAnalytics;

    public AnalyticsWebInterface(Context context) {
        mAnalytics = FirebaseAnalytics.getInstance(context);
    }

    @JavascriptInterface
    public void logEvent(String name, String jsonParams) {
        LOGD("GALog eventName:" + name);
        LOGD("logEvent jsonParams:" +jsonParams);
        if ("page_view".equals(name)){
            Gson gson = new Gson();
            PageViewModel pageViewModel = gson.fromJson(jsonParams, PageViewModel.class);
            GALog.Companion.page(pageViewModel.getPagePath());
        } else {
            mAnalytics.logEvent(name, bundleFromJson(jsonParams));
        }
    }

    @JavascriptInterface
    public void setUserProperty(String name, String value) {
        LOGD("setUserProperty:" + name);
        mAnalytics.setUserProperty(name, value);
    }

    private void LOGD(String message) {
        // Only log on debug builds, for privacy
        if (BuildConfig.DEBUG) {
            Logger.d(message);
        }
    }

    private Bundle bundleFromJson(String json) {
        // [START_EXCLUDE]
        if (TextUtils.isEmpty(json)) {
            return new Bundle();
        }

        Bundle result = new Bundle();
        try {
            JSONObject jsonObject = new JSONObject(json);
            Iterator<String> keys = jsonObject.keys();

            while (keys.hasNext()) {
                String key = keys.next();
                Object value = jsonObject.get(key);

                if (value instanceof String) {
                    result.putString(key, (String) value);
                } else if (value instanceof Integer) {
                    result.putInt(key, (Integer) value);
                } else if (value instanceof Double) {
                    result.putDouble(key, (Double) value);
                } else {
                    Logger.w( "Value for key " + key + " not one of [String, Integer, Double]");
                }
            }
        } catch (JSONException e) {
            Logger.e("Failed to parse JSON, returning empty Bundle.", e);
            return new Bundle();
        }

        return result;
        // [END_EXCLUDE]
    }

}