package com.housesigma.android.ui.webview

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import android.webkit.*
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.ViewModelProvider
import com.google.android.play.core.review.ReviewManagerFactory
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.gson.Gson
import com.gyf.immersionbar.ktx.immersionBar
import com.housesigma.android.AbsSuperApplication
import com.housesigma.android.BuildConfig
import com.housesigma.android.HSApp
import com.housesigma.android.R
import com.housesigma.android.base.BaseActivity
import com.housesigma.android.base.BaseAlertDialogBuilder
import com.housesigma.android.databinding.ActivityWebViewAcivityBinding
import com.housesigma.android.helper.JumpHelper
import com.housesigma.android.helper.ProvinceHelper
import com.housesigma.android.hybrid.HybridUtils
import com.housesigma.android.model.*
import com.housesigma.android.ui.account.FeedbackActivity
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.map.MapActivity
import com.housesigma.android.ui.map.housephoto.PreconHousePhotoListActivity
import com.housesigma.android.ui.map.precon.PreconMapActivity
import com.housesigma.android.ui.map.precon.PreconProjectStatus
import com.housesigma.android.ui.notinterested.NotInterestedHelper
import com.housesigma.android.ui.search.SearchActivity
import com.housesigma.android.utils.*
import com.housesigma.android.utils.log.Logger
import com.housesigma.android.views.AndroidBug5497Workaround
import com.housesigma.android.views.HSReviewDialog
import com.housesigma.android.views.HSWebView
import kotlinx.coroutines.runBlocking
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONException
import org.json.JSONObject

class WebViewActivity : BaseActivity(), LoginFragment.LoginCallback {
    private var webView: HSWebView? = null
    private lateinit var binding: ActivityWebViewAcivityBinding
    private var loginDialog: LoginFragment? = null
    private lateinit var webViewViewModel: WebViewViewModel
    private var gaShare: String = ""
    private var currentRouteName:String = ""
    private var screenViewName:String = ""
    private var isOnPause:Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        screenViewName = ""
        AndroidBug5497Workaround.assistActivity(this)
    }
    override fun onResume() {
        super.onResume()
        if (!TextUtils.isEmpty(screenViewName)){
            GALog.page(screenViewName)
        }
        isOnPause = false
    }
    override fun onPause() {
        super.onPause()
        isOnPause = true
    }
    inner class AnalyticsWebInterface(context: Context?) {

        @JavascriptInterface
        fun logEvent(name: String, jsonParams: String) {
            getActivity()?.let {
                if (it.isFinishing) return
                LOGD("GALog eventName:$name")
                LOGD("logEvent jsonParams:$jsonParams")
                if ("page_view" == name) {
                    val gson = Gson()
                    val (pagePath) = gson.fromJson(
                        jsonParams,
                        PageViewModel::class.java
                    )
                    if (!TextUtils.isEmpty(pagePath)) {
                        screenViewName = pagePath
                    }
                    if (!isOnPause) {
                        GALog.page(pagePath)
                    }
                } else {
                    if (!isOnPause) {
                        Firebase.analytics.logEvent(name, bundleFromJson(jsonParams))
                    }
                }
            }

        }

        @JavascriptInterface
        fun setUserProperty(name: String, value: String?) {
            LOGD("setUserProperty:$name")
            Firebase.analytics.setUserProperty(name, value)
        }

        private fun LOGD(message: String) {
            // Only log on debug builds, for privacy
            if (BuildConfig.DEBUG) {
                Logger.d(message)
            }
        }

        private fun bundleFromJson(json: String): Bundle {
            // [START_EXCLUDE]
            if (TextUtils.isEmpty(json)) {
                return Bundle()
            }
            val result = Bundle()
            try {
                val jsonObject = JSONObject(json)
                val keys = jsonObject.keys()
                while (keys.hasNext()) {
                    val key = keys.next()
                    val value = jsonObject[key]
                    if (value is String) {
                        result.putString(key, value)
                    } else if (value is Int) {
                        result.putInt(key, value)
                    } else if (value is Double) {
                        result.putDouble(key, value)
                    } else {
                        Logger.w("Value for key $key not one of [String, Integer, Double]")
                    }
                }
            } catch (e: JSONException) {
                Logger.e("Failed to parse JSON, returning empty Bundle.", e)
                return Bundle()
            }
            return result
            // [END_EXCLUDE]
        }

    }

    fun generateString(length: Int): String {
        val stringBuilder = StringBuilder(length)
        repeat(length) {
            stringBuilder.append('a') // 你可以根据需要更改字符
        }
        return stringBuilder.toString()
    }
    inner class JsObject {

        /**
         * 1.索取初始化参数
         */
        @JavascriptInterface
        fun requireInitialParameters(msg: String): String? {
            Logger.d( "webview requireInitialParameters msg " + msg)
            val token = MMKVUtils.getStr(LoginFragment.LOGIN_TOKEN)
            val userStr =  HybridUtils.getUserForHybrid()
            val initAppStr = HybridUtils.getInitAppForHybrid()
            val province = ProvinceHelper.getAbbreviationFromCache("ON")

            val lang: String = LanguageUtils().getLANG()

            val isSignIn: Int
            if (TextUtils.isEmpty(userStr)) {
                isSignIn = 0
            } else {
                isSignIn = 1
            }


            val webUserInfo = HybridUserInfo(
                access_token = token!!,
                is_hide_header = 1,
                is_sign_in = isSignIn,//0未登录，1已登陆
                lang = lang,
                province = province,
                user = userStr,
                app = initAppStr,
                secret = HSApp.secret
            )
            return GsonUtils.parseToStr(webUserInfo)
        }

        /**
         * 2.房源详情，点击地图按钮后，跳转到原生地图界面
         */
        @JavascriptInterface
        fun showListingOnNativeMap(id_listing: String) {
            Logger.d( "webview showListingOnNativeMap id_listing $id_listing")
            runOnUiThread {
                val intent = Intent(getActivity(), MapActivity::class.java)
                intent.putExtra("is_sale", true)
                intent.putExtra("map_type", arrayListOf("for-sale"))
                intent.putExtra("id_listing", id_listing)
                startActivity(intent)
            }
        }

        /**
         * Precon房源详情，点击地图按钮后，跳转到原生Precon地图界面
         */
        @JavascriptInterface
        fun showPreconOnNativeMap(id_listing: String) {
            Logger.d( "webview showPreconOnNativeMap id_listing $id_listing")
            runOnUiThread {
                val intent = Intent(getActivity(), PreconMapActivity::class.java)
                intent.putExtra("is_sale", true)
                intent.putExtra("map_type", arrayListOf(PreconProjectStatus.SELLING_NOW))
                intent.putExtra("id_listing", id_listing)
                startActivity(intent)
            }
        }

        /**
         * getSecretKey()
         *
         * // 回调
         * callbackGetSecretKey({
         *   secret_key: string
         *   expired_at: number
         * })
         */
        @JavascriptInterface
        fun getSecretKey(jsonStr: String) {
            Logger.d( "webview getSecretKey $jsonStr")
            val secretKeyModel = Gson().fromJson(jsonStr, SecretKeyModel::class.java)
            if (secretKeyModel.refreshDirectly) {
                webViewViewModel.refreshSecretKey()
                return
            }

            val secret = HSApp.secret
            val isEncrypted = secret?.isEncrypted
            val secretKey = secret?.secretKey
            val expiredAt = secret?.expiredAt
            if (secret == null || secretKey == null || expiredAt == null || isEncrypted == null) {
                webViewViewModel.refreshSecretKey()
                return
            }

            val expireTime = expiredAt - (60 *1) // 提前1分钟刷新
            val currentTime = System.currentTimeMillis()/1000
            if (currentTime >= expireTime) {
                webViewViewModel.refreshSecretKey()
            }else{
                callbackGetSecretKey(secretKey, expiredAt, secret?.isEncrypted?:0)
            }
        }


//        有4种登录成功的场景：
//        1.普通注册
//        2.普通登录
//        3.google  登录注册
//        4.Google 登录成功
        //  这四种情况应该统一处理，保证一致性
        /**
         * 3. 需要登录时，H5 告知 原生，唤起登录；登录成功之后，原生再告知 H5 登录成功
         */
        @JavascriptInterface
        fun callNativeSignin(jsonStr: String) {
            Logger.d( "webview callNativeSignin msg $jsonStr")
            val request = Gson().fromJson(jsonStr, CallNativeSignInRequest::class.java)
            runOnUiThread {
                getActivity()?.let {
                    loginDialog = LoginFragment()
                    if (loginDialog?.isAdded == true) return@let
                    val bundle = Bundle()
                    val loginType: String
                    if (request.reLogin == true) {
                        loginType = LoginFragment.RE_LOGIN_TRREB_TIMEOUT
                    } else if (request.reValidate == true) {
                        loginType = LoginFragment.RE_LOGIN_VALIDATE
                    } else{
                        loginType = ""
                    }
                    bundle.putString("reLogin", loginType)
                    loginDialog?.arguments = bundle
                    loginDialog?.show(
                        supportFragmentManager,
                        ""
                    )
                }

            }

        }

        /**
         * 4. h5 tos 更新后，告知 native
         */
        @JavascriptInterface
        fun tosUpdated(msg: String) {
            Logger.d( "webview tosUpdated msg $msg")
            // android现在用不到
            //这个又需要原生协助了。 在tos_update 之后，reload掉market页面。
            runOnUiThread {
                EventBus.getDefault()
                    .postSticky(MessageEvent(MessageType.RELOAD_PAGE_TOS_UPDATED))
            }
        }

        /**
         * 4. 这部分是壳中就存在的，按原有的约定，继续使用
         */
        @JavascriptInterface
        fun openUrl(urlString: String) {
            Logger.d( "webview openUrl msg $urlString")
            runOnUiThread {
                if (!TextUtils.isEmpty(urlString)) {

                    try {
                        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(urlString))
                        getActivity()?.startActivity(intent)
                    } catch (e:Exception) {
                        e.printStackTrace()
                    }

                }
            }
        }


        /**
         * 4. 这部分是壳中就存在的，按原有的约定，继续使用
         */
        @JavascriptInterface
        fun share(title: String) {
            Logger.d( "webview share title $title")
            runOnUiThread {
                if (!TextUtils.isEmpty(title)) {
                    HSUtil.share(this@WebViewActivity, title)
                }
            }
        }

        /**
         * 4. 这部分是壳中就存在的，按原有的约定，继续使用
         */
        @JavascriptInterface
        fun ready(msg: String) {
            Logger.d( "webview ready")
        }

        //壳中函数
        @JavascriptInterface
        fun getMeta(): String {
            val json = JSONObject()
            json.put("applicationId", BuildConfig.APPLICATION_ID)
            json.put("version", BuildConfig.VERSION_NAME)
            return json.toString()
        }

        //壳中函数
        @JavascriptInterface
        fun getToken() {
            Logger.d( "getToken")
        }

        @JavascriptInterface
        fun collectUserData(json :String) {
            Logger.d( "webview collectUserData : $json")
            val hsUserData = Gson().fromJson(json, HSUserData::class.java)
            hsUserData?.let {
                val ui = hsUserData.ep?.get("ui")
                if (ui != null) { //含有userInput的情况
                    HSLog.sendUserInputHybrid(hsUserData)
                    return
                } else if ((hsUserData.en == "page_view")) {
                    //屏蔽掉hybrid的page_view，由native HSLog.page处理，否则有生命周期计算问题
                    return
                } else {
                    HSLog.sendToHS(hsUserData)
                }
            }
        }

        @JavascriptInterface
        fun triggerVibration(json:String) {
            // impact: light/medium/heavy
            Logger.d( "webview triggerVibration : $json")
            val jsTriggerVibrationModel = Gson().fromJson(json, JsTriggerVibrationModel::class.java)
            runOnUiThread {
                when (jsTriggerVibrationModel?.impact) {
                    "light" -> AndroidHapticFeedback().tick()
                    "medium" -> AndroidHapticFeedback().click()
                    "heavy" -> AndroidHapticFeedback().heavyClick()
                    else -> {}
                }
            }
        }

        /**
         * 房源详情，图片列表，跳转到原生图片列表
         */
        @JavascriptInterface
        fun showListingPhotos(id_listing: String) {
            Logger.d( "webview showListingPhotos $id_listing")

            runOnUiThread {
                getActivity()?.let { JumpHelper.jumpHousePhotoListActivity(it, id_listing) }
            }
        }

        /**
         * Precon房源详情，图片列表，跳转到原生图片列表
         */
        @JavascriptInterface
        fun showPreconPhotos(id_project: String) {
            Logger.d( "webview showPreconPhotos $id_project")

            runOnUiThread {
                val intent = Intent(getActivity(), PreconHousePhotoListActivity::class.java)
                intent.putExtra("id_project", id_project)
                startActivity(intent)
            }
        }



        /**
         * 新开页面，在新的webview中打开
         */
        @JavascriptInterface
        fun pushToWebView(url: String) {
            Logger.d( "webview pushToWebView $url")

            runOnUiThread {
                if (TextUtils.isEmpty(url)) return@runOnUiThread
                getActivity()?.let {
                    if (url.contains("openHsNativeType=2")) {
                        WebViewHelper.jumpInnerWebView(it, url, true)
                    } else if (url.contains("openHsNativeType=3")) {
                        WebViewHelper.jumpOuterWebView(it, url.replace("?openHsNativeType=3","").replace("&openHsNativeType=3",""))
                    } else {
                        WebViewHelper.jumpInnerWebView(it, url, false)
                    }
                }
            }
        }


        /**
         * 隐藏显示分享按钮
         */
        @JavascriptInterface
        fun showShareButton(isShow: String) {
            Logger.d( "webview showShareButton $isShow")

            runOnUiThread {
                if ("true".equals(isShow)) {
                    binding.ivShare.visibility = View.VISIBLE
                } else {
                    binding.ivShare.visibility = View.GONE
                }

            }

        }



        /**
         * 隐藏显示搜索按钮
         */
        @JavascriptInterface
        fun showSearchButton(isShow: String) {
            Logger.d( "webview showSearchButton $isShow")

            runOnUiThread {
                if ("true".equals(isShow)) {
                    binding.ivSearch.visibility = View.VISIBLE
                } else {
                    binding.ivSearch.visibility = View.GONE
                }

            }

        }



        /**
         * h5发现过期之后(900)，调桥的接口，由原生这边来继续处理token过期的情况
         * 请求api中的error.code告诉native
         */
        @JavascriptInterface
        fun sendErrorCode(code: String) {
            Logger.d( "webview sendErrorCode $code")
            runOnUiThread {
                if ("900".equals(code)) {
                    EventBus.getDefault()
                        .postSticky(MessageEvent(MessageType.ReLogin).put(code.toInt()))
                }
            }
        }

        @JavascriptInterface
        fun watchedListingsChanged() {
            Logger.d( "webview watchedListingsChanged")
            runOnUiThread {
                EventBus.getDefault()
                    .postSticky(MessageEvent(MessageType.WATCHED_MULTIPLE_WATCHLIST_CHANGED))
            }
        }

        @JavascriptInterface
        fun watchedCommunitiesChanged() {
            Logger.d( "webview watchedListingsChanged")
            runOnUiThread {
                EventBus.getDefault()
                    .postSticky(MessageEvent(MessageType.WATCHED_COMMUNITIES_CHANGED))
            }
        }


        @JavascriptInterface
        fun getFirebaseRemoteConfigParams(): String? {
            Logger.d( "webview getFirebaseRemoteConfigParams")
            val remoteConfig: FirebaseRemoteConfig = Firebase.remoteConfig
            return GsonUtils.parseToStr(remoteConfig.all)
        }

        @JavascriptInterface
        fun triggerMoreBtn(json :String) {
            Logger.d( "webview triggerMoreBtn : $json")
            val triggerMoreBtnModel = Gson().fromJson(json, TriggerMoreBtn::class.java)
            runOnUiThread {
                if (triggerMoreBtnModel.showed) {
                    binding.ivMenu.visibility = View.VISIBLE
                }else {
                    binding.ivMenu.visibility = View.GONE
                }
            }
        }

        @JavascriptInterface
        fun showLiveChat() {
            Logger.d( "webview showLiveChat")
            runOnUiThread {
                getActivity()?.let {
                    LiveChatUtil.startActivity(it)
                }
            }
        }

        @JavascriptInterface
        fun findNotInterestedByListingId(json :String): Boolean {
            Logger.d( "webview findNotInterestedByListingId: $json")
            val notInterestedListing = Gson().fromJson(json, NotInterestedListing::class.java)
            if (notInterestedListing.listing_id==null) {
                return false
            } else {
                return NotInterestedHelper.findNotInterestedByListingId(notInterestedListing.listing_id)
            }
        }


        @JavascriptInterface
        fun triggerNotInterestedListing(json :String) {
            Logger.d( "webview triggerNotInterestedListing : $json")
            val triggerNotInterestedListingModel = Gson().fromJson(json, TriggerNotInterestedListing::class.java)
            runOnUiThread {
                if (triggerNotInterestedListingModel.is_del) {
                    NotInterestedHelper.delNotInterested(triggerNotInterestedListingModel.listing_id)
                } else {
                    NotInterestedHelper.saveNotInterested(triggerNotInterestedListingModel.listing_id)
                }
                EventBus.getDefault().postSticky(MessageEvent(MessageType.TRIGGER_NOT_INTERESTED_LISTING))
            }
        }

        @JavascriptInterface
        fun navToAgentMap(json :String) {
            Logger.d( "webview navToAgentMap : $json")
            val bridgeAgentMap = Gson().fromJson(json, BridgeAgentMap::class.java)
            runOnUiThread {
                getActivity()?.let {
                    JumpHelper.jumpAgentMapActivity(it, bridgeAgentMap.status,id = bridgeAgentMap.id,slug = bridgeAgentMap.slug)
                }
            }
        }

        @JavascriptInterface
        fun refreshNotes() {
            Logger.d( "webview refreshNotes")
            runOnUiThread {
                EventBus.getDefault().postSticky(MessageEvent(MessageType.REFRESH_NOTES))
            }
        }

        @JavascriptInterface
        fun getLastLocation(): String? {
            Logger.d( "webview getLastLocation")
            val lat = MMKVUtils.getDouble("user_location_latitude")
            val lon = MMKVUtils.getDouble("user_location_longitude")
            val timestamp = MMKVUtils.getLong("user_location_timestamp")
            return Gson().toJson(GetLastLocationResponse(lat, lon, timestamp))
        }

        @JavascriptInterface
        fun showCurrentRoute(json: String) {
            val showCurrentRouteModel = Gson().fromJson(json, ShowCurrentRoute::class.java)
            currentRouteName = showCurrentRouteModel.name
            Logger.d( "webview showCurrentRoute $json")
            runOnUiThread {

                when (currentRouteName) {
                    "ListingSub", "Listing" -> { //房源详情
                        gaShare = "listing"
                        binding.tvTitle.text = "HouseSigma"


                        // 参考 DEV-3892 本地存储的stop_review为空的时候才调用接口，否则都不需要调接口
                        val stopReview = !TextUtils.isEmpty(MMKVUtils.getStr("stop_review"))
                        if (!stopReview) {
                            webViewViewModel.reviewCheck()
                        }
                    }

                    "Precon" -> { //Precon房源详情
                        gaShare = "precon"
                        binding.tvTitle.text = "HouseSigma"


                        // 参考 DEV-3892 本地存储的stop_review为空的时候才调用接口，否则都不需要调接口
                        val stopReview = !TextUtils.isEmpty(MMKVUtils.getStr("stop_review"))
                        if (!stopReview) {
                            webViewViewModel.reviewCheck()
                        }
                    }

                    "ListingNearbySold" -> {
                        gaShare = "similar_sold"
                        binding.tvTitle.text = "Similar Sold"
                    }

                    "ListingNearbyRent" -> {
                        gaShare = "similar_rented"
                        binding.tvTitle.text = "Similar Rented"
                    }

                    "Sell", "SellNext" -> {
                        binding.tvTitle.text = "Sell with HouseSigma"
                    }

                    "Market" -> {
                        gaShare = "market"
                        binding.tvTitle.text = "Market Trends"
                    }

                    "Report" -> {
                        binding.tvTitle.text = "Blog"
                    }

                    "Estimate" -> {
                        gaShare = "estimate"
                        binding.tvTitle.text = "Home Valuation"
                    }
                    "EstimateResult" -> {
                        gaShare = "estimate"
                        binding.tvTitle.text = "Home Valuation"
                    }

                    "newly_listed_listings_of_community" -> {
                        binding.tvTitle.text = "Just Sold"
                    }

                    "just_sold_listings_of_community" -> {
                        binding.tvTitle.text = "Newly Listed"
                    }



                    else -> {
                        binding.tvTitle.text = "HouseSigma"
                    }
                }

                if (!TextUtils.isEmpty(showCurrentRouteModel.title)) {
                    binding.tvTitle.text = showCurrentRouteModel.title
                }
            }
        }

    }

    private fun evaluateJs(js: String) {
        Logger.d( "javascript:$js")
        webView?.evaluateJavascript("javascript:$js") { value ->
            Logger.d(
                "evaluateJavascript onReceiveValue: $value"
            )
        }
    }

    override fun getLayout(): Any {
        webViewViewModel = ViewModelProvider(this).get(WebViewViewModel::class.java)
        binding = ActivityWebViewAcivityBinding.inflate(layoutInflater)


        return binding.root
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: MessageEvent) {
        when (event.type) {
            MessageType.RELOAD_WEB_VIEW, MessageType.PASSWORD_CHANGE -> {
                val token = MMKVUtils.getStr(LoginFragment.LOGIN_TOKEN)
                val userJsonStr = HybridUtils.getUserForHybrid()
                val afterNativeSignIn = AfterNativeSignIn(token = token!!, user = userJsonStr)
                val js = "afterNativeSignin(" + GsonUtils.parseToStr(afterNativeSignIn) + ")"
                runOnUiThread {
                    evaluateJs(js)
                }
            }

            MessageType.RELOAD_PAGE_TOS_UPDATED -> {
                runOnUiThread {
                    // 这个页面不需要刷新 DEV-2743
                    if ("EstimateResult".equals(currentRouteName)
                        || "Estimate".equals(currentRouteName)) {
                        return@runOnUiThread
                    }
                    webView?.reload()
                }
            }

            MessageType.UPDATE_LAST_POSITION -> {
                runOnUiThread {
                    val lat = MMKVUtils.getDouble("user_location_latitude")
                    val lon = MMKVUtils.getDouble("user_location_longitude")
                    val timestamp = MMKVUtils.getLong("user_location_timestamp")
                    val js = "updateLastPosition(" + GsonUtils.parseToStr(GetLastLocationResponse(lat, lon, timestamp)) + ")"
                    Logger.d( "updateLastPosition js $js")
                    evaluateJs(js)
                }
            }
            else -> {}
        }
    }

    override fun initView() {
        var webViewUrl = intent.getStringExtra(WebViewHelper.WEB_VIEW_URL)
        // webview工具条，默认有工具条浏览器打开
        val webViewTool = intent.getBooleanExtra(WebViewHelper.WEB_VIEW_TOOL, false)
        Logger.d( "webViewUrl is $webViewUrl")
        if (webViewUrl == null) {
            ToastUtils.showLong("URL is null")
            finish()
            return
        }

        if ("zh_CN".equals(LanguageUtils().getLANG())) {
            webViewUrl = webViewUrl.replace("/en/", "/zh/")
        } else if ("en_US".equals(LanguageUtils().getLANG())) {
            webViewUrl = webViewUrl.replace("/zh/", "/en/")
        }
        createWebView()
        webView?.loadUrl(webViewUrl)


        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        immersionBar {
            navigationBarColor(R.color.color_white)
            statusBarColor(R.color.app_main_color)
            fitsSystemWindows(true)
        }


        if (webViewTool) {
            binding.ivOpen.visibility = View.VISIBLE
            binding.ivOpen.setOnClickListener {
                WebViewHelper.jumpOuterWebView(this, webViewUrl)
            }
        } else {
            binding.ivOpen.visibility = View.GONE
        }

        binding.ivClose.setOnClickListener {
            if (webView==null) {
                finish()
            }
            webView?.let {
                if (it.canGoBackOrForward(-1)) {
                    it.goBackOrForward(-1)
                } else {
                    finish()
                }
            }
        }
        binding.tvTitle.setOnClickListener {
            finish()
        }
        binding.ivSearch.setOnClickListener {
            GALog.log("search_start")
            AbsSuperApplication.finishActivity(SearchActivity::class.java)
            startActivity(Intent(this, SearchActivity::class.java))
        }

        binding.ivShare.setOnClickListener {
            getResourceForShare()
        }

        binding.ivMenu.setOnClickListener {
            clickMoreBtn()
        }
    }

    private fun callbackGetSecretKey(secretKey: String, expiredAt: Long, isEncrypted: Int) {
        val js = "callbackGetSecretKey({\"secret_key\":\"$secretKey\",\"expired_at\":$expiredAt,\"is_encrypted\":$isEncrypted})"
        runOnUiThread {
            evaluateJs(js)
        }
    }

    private fun clickMoreBtn() {
        webView?.evaluateJavascript("javascript:clickMoreBtn()") { value ->
            Logger.d(
                "evaluateJavascript onReceiveValue: $value"
            )
        }
    }

    private fun getResourceForShare() {
        webView?.evaluateJavascript("javascript:getResourceForShare()",
            object : ValueCallback<String> {
                override fun onReceiveValue(shareStr: String?) {
                    if (shareStr != null) {
                        val shareBean = Gson().fromJson(shareStr, ShareModel::class.java)
                        if (shareBean != null) {
                            GALog.log("page_share_click", gaShare)
                            HSUtil.share(
                                this@WebViewActivity,
                                UrlUtil.getURLDecoderString(shareBean.title) + " " + shareBean.url
                            )
                        }
                    }
                }
            })
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun createWebView() {
        try {
            webView = HSWebView(this)
        } catch (e:Exception) {
            ToastUtils.showLong("Chromium WebView package does not exist")
            e.printStackTrace()
        }
        webView?.let {
            it.setInitialScale(1)
            it.addJavascriptInterface(JsObject(), "bridge")
            // Only add the JavaScriptInterface on API version JELLY_BEAN_MR1 and above, due to
            // security concerns, see link below for more information:
            // https://developer.android.com/reference/android/webkit/WebView.html#addJavascriptInterface(java.lang.Object,%20java.lang.String)

            // Only add the JavaScriptInterface on API version JELLY_BEAN_MR1 and above, due to
            // security concerns, see link below for more information:
            // https://developer.android.com/reference/android/webkit/WebView.html#addJavascriptInterface(java.lang.Object,%20java.lang.String)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                it.addJavascriptInterface(
                    AnalyticsWebInterface(this),"AnalyticsWebInterface"
                )
            } else {
                Logger.w("Not adding JavaScriptInterface, API Version: " + Build.VERSION.SDK_INT)
            }

            if (!"prod".equals(BuildConfig.FLAVOR)) {
                WebView.setWebContentsDebuggingEnabled(true)
            }
            binding.webviewContainer.addView(webView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            it.settings.userAgentString =
                it.settings.userAgentString + "_HouseSigma_Android_" + BuildConfig.VERSION_NAME
            it.settings.javaScriptEnabled = true
            it.settings.useWideViewPort = true
            it.settings.loadWithOverviewMode = true
            it.settings.builtInZoomControls = true
            it.settings.setSupportZoom(true)
            it.settings.displayZoomControls = false
            it.settings.layoutAlgorithm =
                WebSettings.LayoutAlgorithm.TEXT_AUTOSIZING
            it.settings.javaScriptCanOpenWindowsAutomatically = true
            it.settings.domStorageEnabled = true

            it.settings.setTextSize(WebSettings.TextSize.NORMAL);
            it.settings.cacheMode = WebSettings.LOAD_DEFAULT
            it.settings.databaseEnabled = true
            it.settings.textZoom = 100

            it.webViewClient = object : WebViewClient() {
                override fun onLoadResource(view: WebView?, url: String?) {

                }

                override fun doUpdateVisitedHistory(view: WebView?, url: String?, isReload: Boolean) {
                    super.doUpdateVisitedHistory(view, url, isReload)
                }

                override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                    super.onPageStarted(view, url, favicon)
                }

                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                }

                @RequiresApi(api = Build.VERSION_CODES.O)
                override fun onRenderProcessGone(
                    view: WebView?,
                    detail: RenderProcessGoneDetail?
                ): Boolean {
//                    原生webView在8.0系统后默认是多进程，Render进程和主进程隔离
                    try {
                        if (webView == null) {
                            return false
                        }
                        destroyWebView()
                        createWebView()
                        detail?.let { renderGoneDetail->
                            if (renderGoneDetail.didCrash()){
                                Firebase.crashlytics.log("The WebView rendering process crashed!")
                            } else {
                                Firebase.crashlytics.log("System killed the WebView rendering process to reclaim memory. Recreating...")
                            }
                        }
                        Firebase.crashlytics.log("The app continues executing")
                        // # DEV-1988 hybird 假死后杀掉 webview 返回之前的页面并上报Crashlytics
//                     # DEV-2968 已经做兼容处理，只需要打LOG到crashlytics就可以，崩溃后再追踪
                        Firebase.crashlytics.log("onRenderProcessGone")
                        return true
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    return true
                }
            }


        }
    }

    override fun initData() {
        webViewViewModel.refreshSecretKey.observe(this) {
            if (it.secret?.secretKey != null && it.secret?.expiredAt!=null) {
                callbackGetSecretKey(it.secret?.secretKey?:"", it.secret?.expiredAt?:0,it.secret?.isEncrypted?:0)
            }
        }

        webViewViewModel.reviewCheck.observe(this) {
            if (it.stopreview!=null) {
                MMKVUtils.saveStr("stop_review", it.stopreview)
            } else {
                MMKVUtils.removeData("stop_review")
            }
            if (it.review==false) return@observe
            val hsReviewDialog = HSReviewDialog(
                this,
                object : HSReviewDialog.HSAlertCallback {
//              review/stop 接口 review_rating 参数进行扩容
//                        Tap Later  == -1
//                        Tap Not Like == 10
//                        Tap Like == 15
                    override fun onLike() {
                        webViewViewModel.stopReview("15")
                        showPlayReviewFlow()
                    }

                    override fun onNotLike() {
                        webViewViewModel.stopReview("10")
                        showImproveAlert()
                    }

                    override fun onLater() {
                        webViewViewModel.stopReview("-1")
                    }
                })
            hsReviewDialog.show()
        }
    }

    private fun showImproveAlert() {
        val builder = BaseAlertDialogBuilder(this)
        builder.setTitle("Can you tell us how we could improve?")
        builder.setMessage("Your feedback is very important. Help make HouseSigma better.")
        builder.setPositiveButton(
            "OK"
        ) { _, _ ->

            GALog.log("app_review_prompt","feedback_yes")
            startActivity(Intent(this, FeedbackActivity::class.java))
        }
        builder.setNegativeButton(
            "Cancel"
        ) { _, _ ->

            GALog.log("app_review_prompt","feedback_no")
        }
        val dialog: AlertDialog = builder.create()
        dialog.show()
    }

    private fun showPlayReviewFlow() {
        // 有些用户手机没有装谷歌框架，也没办法调起来打分功能，只能try catch一下防止闪退
        try {
            val manager = ReviewManagerFactory.create(this)
            val request = manager.requestReviewFlow()
            request.addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    val reviewInfo = task.result
                    if (reviewInfo != null) {
                        val flow = manager.launchReviewFlow(WebViewActivity@this, reviewInfo)
                        flow.addOnCompleteListener { _ ->
                        }
                    }
                }
            }
        } catch (e:Exception) {
            e.printStackTrace()
        }
    }

    private fun showThanksAlert(url: String) {
        val builder = BaseAlertDialogBuilder(this)
        builder.setTitle("Thanks! We like you too!")
        builder.setMessage("There is no better way to share your love for Housesigma than giving us a nice review")
        builder.setPositiveButton(
            "Review"
        ) { _, _ ->
            try {
                WebViewHelper.jumpOuterWebView(this, url)
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }

        }
        builder.setNegativeButton(
            "Not Now"
        ) { _, _ ->


        }
        val dialog: AlertDialog = builder.create()
        dialog.show()
    }


    override fun onDestroy() {
        super.onDestroy()
        destroyWebView()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        webView?.let {
            if (it.canGoBackOrForward(-1) && keyCode == KeyEvent.KEYCODE_BACK) {
                it.goBackOrForward(-1)
                return true
            }
            if (it.canGoBackOrForward(1) && keyCode == KeyEvent.KEYCODE_FORWARD) {
                it.goBackOrForward(1)
                return true
            }
        }

        return super.onKeyDown(keyCode, event)
    }

    private fun destroyWebView(){
        if (webView == null) {
            return
        }
        try {
            val webViewContainer = binding.webviewContainer
            webViewContainer.removeView(webView)
        } catch (e:Exception) {
            e.printStackTrace()
        }
        try {
            webView?.destroy()
            webView = null
        } catch (e:Exception) {
            e.printStackTrace()
        }
    }

    override fun onLoginSuccess() {
        loginDialog?.dismiss()
        loginDialog = null
    }

}