package com.housesigma.android.ui.webview

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import com.housesigma.android.BuildConfig
import com.housesigma.android.R
import com.housesigma.android.helper.ProvinceHelper
import com.housesigma.android.model.RecommendListingType
import com.housesigma.android.utils.ConstantsHelper
import com.housesigma.android.utils.MapUrlParamsUtils
import com.housesigma.android.utils.log.Logger


class WebViewHelper {

    companion object {
        const val WEB_VIEW_URL = "web_view_url"
        const val WEB_VIEW_TOOL = "web_view_tool"
        const val MARKET_URL =
            BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/market?municipality=&community=&house_type=&ign=&from=tabbar"

        fun jumpSimilarSold(
            context: Context,
            listingId: String
        ) {
            val intent = Intent(context, WebViewActivity::class.java)
            intent.putExtra(
                WEB_VIEW_URL,
                BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/listing/" + listingId + "/nearby-sold"
            )
            context.startActivity(intent)
        }


        fun jumpSimilarRented(
            context: Context,
            listingId: String
        ) {
            val intent = Intent(context, WebViewActivity::class.java)
            intent.putExtra(
                WEB_VIEW_URL,
                BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/listing/" + listingId + "/nearby-rent"
            )
            context.startActivity(intent)
        }


        fun jumpWatchedTrendListing(
            context: Context,
            community: String,
            house_type: String,
            municipality: String,
            type: String
        ) {
//            val intent = Intent(context, WatchedTrendListingActivity::class.java)
//            intent.putExtra("community",community)
//            intent.putExtra("house_type",house_type)
//            intent.putExtra("municipality",municipality)
//            intent.putExtra("type",type)
//            context.startActivity(intent)
            val intent = Intent(context, WebViewActivity::class.java)
            intent.putExtra(
                WEB_VIEW_URL,
                BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/trend/" + type + "?municipality=" + municipality + "&community=" + community + "&property=" + house_type + "&from=community_watched"
            )
            context.startActivity(intent)
        }

        /**
         * 单独开启一个market页
         */
        fun jumpMarket(
            context: Context,
            municipality: String? = "10343",
            house_type: String? = "all",
            community: String? = "all",
        ) {
            val intent = Intent(context, WebViewActivity::class.java)
            intent.putExtra(
                WEB_VIEW_URL,
                BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/market?municipality=" +
                        municipality + "&community=" + community + "&house_type=" + house_type + "&ign="
            )
            context.startActivity(intent)
        }

        fun jumpPreconDetail(context: Context, id_project: String,eventSource:String? = "") {
            val intent = Intent(context, WebViewActivity::class.java)
            if (!TextUtils.isEmpty(eventSource)) {
                intent.putExtra(
                    WEB_VIEW_URL,
                    BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/precon/" + id_project + "?event_source=" + eventSource
                )
            } else {
                intent.putExtra(
                    WEB_VIEW_URL,
                    BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/precon/" + id_project
                )
            }

            context.startActivity(intent)
            if (context is Activity){
                context.overridePendingTransition(R.anim.anim_open_enter, R.anim.anim_open_exit)
            }
        }


        /**
         * 通过RecommendListingType获取eventSource
         */
        fun getEventSourceByType(type: Int?=0): String {
            when (type) {
                RecommendListingType.watchedCommunityUpdates -> {
                    return "watchedareacommunity"
                }
                RecommendListingType.soldBelowBought -> {
                    return "soldbelowbought"
                }
                RecommendListingType.featuredListings -> {
                    return "featuredlistings"
                }
                RecommendListingType.justSold -> {
                    return "justsold"
                }
                RecommendListingType.highReurnsType -> {
                    return "highreturns"
                }
                RecommendListingType.bestForRentalInvestment -> {
                    return "bestforrental"
                }
                RecommendListingType.bestForSchool -> {
                    return "bestforschool"
                }
                RecommendListingType.oneYearPriceGrowth -> {
                    return "1yearpricegrowth"
                }
                RecommendListingType.newlyListed -> {
                    return "newlylisted"
                }
                RecommendListingType.recommendForYou -> {
                    return "recommendforyou"
                }
                RecommendListingType.exclusivePreconAssignment -> {
                    return "preconassignment"
                }
                else -> {
                    return ""
                }
            }

        }

        fun jumpHouseDetail(context: Context, id_listing: String, seo_suffix: String? = "",type: Int?=0,eventSource:String? = "") {
            val intent = Intent(context, WebViewActivity::class.java)
            if (type != 0) {
                // 如果是由首页推荐 section 跳转到了房源详情页，则通过getEventSourceByType 添加 event_source 参数
                intent.putExtra(
                    WEB_VIEW_URL,
                    BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/listing/" + id_listing + "/" + seo_suffix+"?event_source=" + getEventSourceByType(type)
                )
            } else {

                // 也可以自行携带 event_source 参数，目前只有路由和地图卡片额外携带event_source参数。
                intent.putExtra(
                    WEB_VIEW_URL,
                    BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/listing/" + id_listing + "/" + seo_suffix + "?event_source=" + (eventSource ?: "")
                )
            }

            context.startActivity(intent)
            if (context is Activity){
                context.overridePendingTransition(R.anim.anim_open_enter, R.anim.anim_open_exit)
            }
        }

        fun jumpSell(context: Context) {
            val intent = Intent(context, WebViewActivity::class.java)
            intent.putExtra(
                WEB_VIEW_URL,
                BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/sell"
            )
            context.startActivity(intent)
        }

        fun jumpAgentExperienceListings(context: Context,province: String? = null,slug: String, status: String) {
            var currentProvince = province
            if (TextUtils.isEmpty(province)) {
                currentProvince = ProvinceHelper.getAbbreviationFromCache("ON")
            }
            val intent = Intent(context, WebViewActivity::class.java)
            // /h5/:lang/:province/agents/:slug/listings/:status/
            intent.putExtra(
                WEB_VIEW_URL,
                BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/" + currentProvince?.lowercase() + "/agents/" + slug + "/listings/" + status
            )
            context.startActivity(intent)
        }



        fun jumpHouseContact(context: Context, id_listing: String) {
            val intent = Intent(context, WebViewActivity::class.java)
            intent.putExtra(
                WEB_VIEW_URL,
                BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/listing/" + id_listing + "/contact"
            )
            context.startActivity(intent)
        }

        fun jumpEstimate(context: Context, params: HashMap<String, Any>?) {
            val intent = Intent(context, WebViewActivity::class.java)
            val urlParamsByMap = MapUrlParamsUtils.getUrlParamsByMap(params)
            var url = BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/estimate"
            Logger.i("Estimate urlParams " + urlParamsByMap)
            if (!TextUtils.isEmpty(urlParamsByMap)) {
                url = url + "?" + urlParamsByMap
            }
            intent.putExtra(
                WEB_VIEW_URL,
                url
            )
            intent.putExtra(WebViewHelper.WEB_VIEW_TOOL, false)
            context.startActivity(intent)
        }

        fun jumpReports(context: Context) {
            val intent = Intent(context, WebViewActivity::class.java)
            intent.putExtra(
                WEB_VIEW_URL,
                BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/reports"
            )
            context.startActivity(intent)
        }

        fun jumpAgentDetail(context: Context, slug: String,province:String? = null) {
            var currentProvince = province
            if (TextUtils.isEmpty(province)) {
                currentProvince = ProvinceHelper.getAbbreviationFromCache("ON")
            }
            val intent = Intent(context, WebViewActivity::class.java)
            intent.putExtra(
                WEB_VIEW_URL,
                BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/"+ currentProvince?.lowercase() + "/agents/" + slug
            )
            context.startActivity(intent)
        }

        fun jumpAgents(context: Context,province:String? = null) {
            var currentProvince = province
            if (TextUtils.isEmpty(province)) {
                currentProvince = ProvinceHelper.getAbbreviationFromCache("ON")
            }
            val intent = Intent(context, WebViewActivity::class.java)
            intent.putExtra(
                WEB_VIEW_URL,
                BuildConfig.WEBVIEW_URL_PREFIX + "/h5/en/" + currentProvince?.lowercase() + "/agents"
            )
            context.startActivity(intent)
        }

        fun jumpJoinUs(context: Context) {
            val intent = Intent(context, WebViewActivity::class.java)
            intent.putExtra(
                WEB_VIEW_URL,
                ConstantsHelper.getUrlJoinUs()
            )
            context.startActivity(intent)
        }

        fun jumpInnerWebView(context: Context, url: String, hasTool: Boolean? = false) {
            val intent = Intent(context, WebViewActivity::class.java)
            intent.putExtra(
                WEB_VIEW_URL,
                url
            )
            intent.putExtra(
                WEB_VIEW_TOOL,
                hasTool
            )
            context.startActivity(intent)
        }

        fun jumpOuterWebView(context: Context, url: String) {
            try {
                if (TextUtils.isEmpty(url)) return
                val intent = Intent()
                intent.action = Intent.ACTION_VIEW
                intent.data = Uri.parse(url)
                context.startActivity(intent)
            } catch (e:Exception) {
                e.printStackTrace()
                // 跳转外部浏览器有异常的，做兜底跳转应用内部浏览器处理
                jumpInnerWebView(context,url)
            }
        }
    }
}