package com.housesigma.android.ui.webview

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.housesigma.android.HSApp
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.RefreshSecretKey
import com.housesigma.android.model.ReviewCheck
import com.housesigma.android.model.Secret
import com.housesigma.android.network.NetClient
import com.housesigma.android.network.launch
import com.housesigma.android.ui.watched.WatchedHelper
import org.greenrobot.eventbus.EventBus

class WebViewViewModel : ViewModel() {

    var reviewCheck: MutableLiveData<ReviewCheck> = MutableLiveData()
    var refreshSecretKey: MutableLiveData<RefreshSecretKey> = MutableLiveData()


    fun reviewCheck() {
        launch({
            NetClient.apiService.reviewCheck()
        }, {
            reviewCheck.postValue(it)
        })
    }
    fun stopReview(review_rating:String) {
        launch({
            NetClient.apiService.stopReview(review_rating)
        }, {
        })
    }

    fun refreshSecretKey() {
        launch({
            NetClient.apiService.refreshSecretKey()
        }, {
            HSApp.secret = it.secret

            refreshSecretKey.postValue(it)
        })
    }



}