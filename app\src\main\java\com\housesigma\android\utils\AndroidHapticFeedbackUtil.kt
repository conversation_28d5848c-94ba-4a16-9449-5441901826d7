package com.housesigma.android.utils

import android.content.Context
import android.os.*
import com.housesigma.android.HSApp

class AndroidHapticFeedback() {
    fun normal() {
        val durationMillis = 100L
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibrationEffect = VibrationEffect.createOneShot(durationMillis, VibrationEffect.DEFAULT_AMPLITUDE)
            vibrate(durationMillis, vibrationEffect)
        } else vibrate(durationMillis)
    }
    fun tick() {
        val durationMillis = 100L
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibrationEffect = VibrationEffect.createPredefined(VibrationEffect.EFFECT_TICK)
            vibrate(durationMillis, vibrationEffect)
        } else vibrate(durationMillis)
    }

    fun click() {
        val durationMillis = 100L
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibrationEffect = VibrationEffect.createPredefined(VibrationEffect.EFFECT_CLICK)
            vibrate(durationMillis, vibrationEffect)
        } else vibrate(durationMillis)
    }

    fun doubleClick() {
        val durationMillis = 100L
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibrationEffect = VibrationEffect
                .createPredefined(VibrationEffect.EFFECT_DOUBLE_CLICK)
            vibrate(durationMillis, vibrationEffect)
        } else vibrate(durationMillis)
    }

    fun heavyClick() {
        val durationMillis = 100L
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibrationEffect = VibrationEffect
                .createPredefined(VibrationEffect.EFFECT_HEAVY_CLICK)
            vibrate(durationMillis, vibrationEffect)
        } else vibrate(durationMillis)
    }


    fun customDuration(durationMillis: Long) {
        vibrate(durationMillis)
    }

    /**
     *  [vibrationEffect] is only used from [Build.VERSION_CODES.O] or API 26 and above
     *  Some effects are also limited to Android 12+ or [Build.VERSION_CODES.S]
     *
     *  If a [VibrationEffect] is not provided then [VibrationEffect.createOneShot] will be used
     *  with provided [durationMillis] and [VibrationEffect.DEFAULT_AMPLITUDE]
     *
     *  Below Android 8 (less than [Build.VERSION_CODES.O]), only the duration will be used.
     *  **/
    private fun vibrate(durationMillis: Long, vibrationEffect: VibrationEffect? = null) {
        val context = HSApp.appContext ?: return
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibrator =
                context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
            if (vibrationEffect == null) {
                val effect = VibrationEffect
                    .createOneShot(durationMillis, VibrationEffect.DEFAULT_AMPLITUDE)
                vibrator.vibrate(CombinedVibration.createParallel(effect))
            } else {
                vibrator.vibrate(CombinedVibration.createParallel(vibrationEffect))
            }
        } else {
            @Suppress("DEPRECATION")
            val vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                if (vibrationEffect == null) {
                    val effect = VibrationEffect
                        .createOneShot(durationMillis, VibrationEffect.DEFAULT_AMPLITUDE)
                    vibrator.vibrate(effect)
                } else vibrator.vibrate(vibrationEffect)
            } else {
                @Suppress("DEPRECATION")
                vibrator.vibrate(durationMillis)
            }
        }
    }
}