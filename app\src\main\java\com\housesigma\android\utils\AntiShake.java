package com.housesigma.android.utils;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * ================================================
 * Description:
 * <p>
 * 页面内容介绍: 使用方法: 在任何需要拦截的地方(任何对象/点击/执行方法等) 加入 if (AntiShake.check(position)) return; 或 if (AntiShake.check(position, 1500)) return;
 * <p>
 * ================================================
 */
public class AntiShake {

//    需要注意的是,默认使用场景是同一个Activity里有不超过200个可以在一秒内同时点击的View,
//    或者会同时读取/处理超过200个不同的数据,才需要扩大200这个参数.正常页面中,size() > 50应该就够用了,合理修改这个参数可以减少内存支出.
    private static Map<String, Long> map = new LinkedHashMap<String, Long>() {
        @Override
        protected boolean removeEldestEntry(Entry<String, Long> pEldest) {
            return size() > 200;
        }
    };

    public static boolean check(Object obj) {
        return check(obj, 1000);
    }


    /**
     * 防抖检查
     * @param obj 防抖对象
     * @param delayTime 防抖时间，默认1000秒
     * @return false是未命中防抖规则，可以继续执行，true是已命中防抖规则
     */
    public static boolean check(Object obj, int delayTime) {
        Long time = map.get(obj.toString());
        if (time == null) {
            map.put(obj.toString(), System.currentTimeMillis());
            return false;
        } else {
            boolean b = System.currentTimeMillis() - time <= delayTime;
            if (!b) map.put(obj.toString(), System.currentTimeMillis());
            return b;
        }
    }
}