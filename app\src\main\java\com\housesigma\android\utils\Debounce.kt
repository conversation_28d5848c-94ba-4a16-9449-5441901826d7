package com.housesigma.android.utils

import android.os.Handler
import android.os.Looper
import android.view.View
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 创建一个防抖动函数，该函数在给定时间内只执行一次操作。
 *
 * @param waitMillis 等待时间（以毫秒为单位），在此时间内多次触发操作时，只执行最后一次操作。
 * @param action 要执行的操作，表示为一个无参数的函数。
 * @return 一个防抖动函数，可以在需要防抖动的地方调用。
 */
fun debounce(
    waitMillis: Long,
    action: () -> Unit
): () -> Unit {
    // 创建一个处理器，用于在主线程中执行操作
    val handler = Handler(Looper.getMainLooper())
    // 存储最后一个提交给处理器的可运行操作
    var lastRunnable: Runnable? = null

    // 返回一个防抖动函数
    return {
        // 如果存在上一个可运行操作，则从处理器中删除它
        lastRunnable?.let { handler.removeCallbacks(it) }
        // 创建一个新的可运行操作，包装要执行的操作
        lastRunnable = kotlinx.coroutines.Runnable {
            action()
        }
        // 将新的可运行操作提交给处理器，延迟指定的等待时间后执行
        lastRunnable?.let { handler.postDelayed(it, waitMillis) }
    }
}

fun View.setDebounceClickListener(listener: View.OnClickListener, time: Long = 50L) {
    var job: Job? = null
    this.setOnClickListener {
        job?.cancel()
        job = CoroutineScope(Dispatchers.Main).launch {
            delay(time)
            listener.onClick(it)
        }
    }
}