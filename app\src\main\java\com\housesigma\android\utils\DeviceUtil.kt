package com.housesigma.android.utils

import android.content.Context
import android.content.Context.VIBRATOR_SERVICE
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator


object DeviceUtil {
    /**
     * 获取机型
     */
    val phoneModel: String
        get() {
            val brand = Build.BRAND //手机品牌
            val model = Build.MODEL //手机型号
            return "$brand $model"
        }

    /**
     * 获取操作系统
     */
    val oS: String
        get() {
            return "Android" + Build.VERSION.RELEASE
        }
}