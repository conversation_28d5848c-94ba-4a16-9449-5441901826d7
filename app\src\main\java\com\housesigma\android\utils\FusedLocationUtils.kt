package com.housesigma.android.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.os.Looper
import androidx.core.app.ActivityCompat
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationAvailability
import com.google.android.gms.location.LocationCallback
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationResult
import com.google.android.gms.location.LocationServices
import com.housesigma.android.utils.log.Logger

class FusedLocationUtils(private val context: Context) {

    private val fusedLocationClient: FusedLocationProviderClient = LocationServices.getFusedLocationProviderClient(context)
    private var locationCallback: LocationCallback? = null

    interface LocationResultListener {
        fun onLocationResult(location: Location?)
        fun onLocationError(error: String)
    }

    /**
     * Get the last known location. If the location is not available or the user wants to forcefully
     * get a new location, a fresh location request is made.
     * @param listener Callback to handle the location result or error.
     */
    fun getCurrentLocation(listener: LocationResultListener) {
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
            ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            listener.onLocationError("Location permissions are not granted")
            return
        }

        requestNewLocationData(listener)
    }

    private fun requestNewLocationData(listener: LocationResultListener) {
        Logger.d("Location requestNewLocationData")
        val locationRequest = LocationRequest.create().apply {
            interval = 10000 // 10 seconds
            fastestInterval = 5000 // 5 seconds
            priority = LocationRequest.PRIORITY_BALANCED_POWER_ACCURACY
            numUpdates = 3 // Request location 3 times
        }

        locationCallback = object : LocationCallback() {
            override fun onLocationAvailability(p0: LocationAvailability) {
                super.onLocationAvailability(p0)
                Logger.d("Location availability received $p0")
            }
            override fun onLocationResult(locationResult: LocationResult) {
                Logger.d("Location result received")
                locationResult ?: return
                for (location in locationResult.locations) {
                    listener.onLocationResult(location)
                }
                fusedLocationClient.removeLocationUpdates(this)
            }
        }

        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
            ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            listener.onLocationError("Location permissions are not granted")
            return
        }

        fusedLocationClient.requestLocationUpdates(locationRequest,
            locationCallback as LocationCallback, Looper.getMainLooper())
    }
}