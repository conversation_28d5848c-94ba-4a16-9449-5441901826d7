package com.housesigma.android.utils

import android.os.Bundle
import androidx.annotation.NonNull
import androidx.annotation.Size
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.housesigma.android.utils.log.Logger

class GALog {

    companion object {
        private const val HS_LABEL = "hs_label"
        private const val HS_USER = "hs_user"
        private var mScreenName: String? = null


        fun log(eventName: String, label: String? = null,hsUser: String? = null) {
            try {
                Logger.i("GA Log eventName [" + eventName + "] ,hs_label [" + label + "] ,screenName ["+ mScreenName+"]")
                val firebaseAnalytics = Firebase.analytics
                val bundle = Bundle()
                if (label != null) {
                    bundle.putString(HS_LABEL, label)
                }
                if (hsUser != null) {
                    bundle.putString(HS_USER, hsUser)
                }
                firebaseAnalytics.logEvent(eventName,bundle)
                HSLog.log(eventName,bundle)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        fun page(screenName: String) {
            try {
                mScreenName = screenName
                Logger.i("GA Log page SCREEN_NAME [" + mScreenName + "]")
                val firebaseAnalytics = Firebase.analytics
                val bundle = Bundle()
                bundle.putString(FirebaseAnalytics.Param.SCREEN_NAME, mScreenName)
                firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW,bundle)

                HSLog.page(screenName)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

    }

}