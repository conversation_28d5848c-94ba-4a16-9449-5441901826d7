package com.housesigma.android.utils

import android.content.res.AssetManager
import com.google.gson.Gson
import com.housesigma.android.HSApp
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.lang.reflect.Type


object GsonUtils {

    private val gson: Gson = Gson()

    fun parseToStr(obj: Any):String?{
        return gson.toJson(obj)
    }

    fun <T>parseAssets(fileName:String,type: Type):T?{
        val stringBuilder = StringBuilder()
        val assetManager: AssetManager = HSApp.appContext?.assets ?:return null
        try {
            val bufferedReader = BufferedReader(
                InputStreamReader(
                    assetManager.open(fileName), "utf-8"
                )
            )
            var line: String?
            while (bufferedReader.readLine().also { line = it } != null) {
                stringBuilder.append(line)
            }
            bufferedReader.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }

        return  gson.fromJson(stringBuilder.toString(),type)
    }

}