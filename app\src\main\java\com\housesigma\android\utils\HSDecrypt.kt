package com.housesigma.android.utils

import android.util.Base64
import com.housesigma.android.utils.log.Logger
import javax.crypto.Cipher
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

class HSDecrypt {
    companion object {
        private fun generateSecretKeyFromToken(token: String): SecretKey {
            var paddedToken = token
            if (token.length < 16) {
                paddedToken = token.padEnd(16, '*')
            } else if (token.length > 16) {
                paddedToken = token.substring(0, 16)
            }

            val keyBytes = paddedToken.toByteArray()
            return SecretKeySpec(keyBytes, "AES")
        }

        private fun decryptUserInput(encryptedUserInput: String, key: SecretKey, counter: ByteArray): ByteArray? {
            val cipher = Cipher.getInstance("AES/CTR/NoPadding")
            val ivSpec = IvParameterSpec(counter)
            cipher.init(Cipher.DECRYPT_MODE, key, ivSpec)
            val decodedInput = Base64.decode(encryptedUserInput, Base64.NO_WRAP)
            val original = cipher.doFinal(decodedInput)
            return original
        }

        /**
         * 解密字符串
         * @param encryptedString 加密的内容
         * @param decryptedCounter 明文的计数器
         * @param secretKey 服务器下发的 secretKey
         */
        fun getDecodedString(encryptedString: String, decryptedCounter: ByteArray, secretKey: String): ByteArray? {
            // 使用 AES 密钥和解密后的计数器解密加密字符串
            val decryptedUserInput = decryptUserInput(encryptedString, generateSecretKeyFromToken(secretKey), decryptedCounter)
            Logger.i("HSDecrypt decrypted user input: $decryptedUserInput")
            return decryptedUserInput
        }

    }
}