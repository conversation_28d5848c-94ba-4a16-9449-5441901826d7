package com.housesigma.android.utils

import android.util.Base64
import com.housesigma.android.BuildConfig
import com.housesigma.android.model.EncryptPayloadModel
import com.housesigma.android.utils.log.Logger
import java.nio.charset.Charset
import java.security.KeyFactory
import java.security.PublicKey
import java.security.SecureRandom
import java.security.spec.X509EncodedKeySpec
import javax.crypto.Cipher
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

class HSEncrypt {
    companion object {
        private fun generateSecretKeyFromToken(token: String): SecretKey {
            var paddedToken = token
            if (token.length < 16) {
                paddedToken = token.padEnd(16, '*')
            } else if (token.length > 16) {
                paddedToken = token.substring(0, 16)
            }

            val keyBytes = paddedToken.toByteArray()
            val secretKeySpec = SecretKeySpec(keyBytes, "AES")
            return secretKeySpec
        }

        private fun generateRandomCounter(): ByteArray {
            val counter = ByteArray(16)
            SecureRandom().nextBytes(counter)
            return counter
        }

        private fun encryptUserInput(
            userInput: String,
            key: SecretKey,
            counter: ByteArray
        ): String? {
            val cipher = Cipher.getInstance("AES/CTR/NoPadding")
            val ivSpec = IvParameterSpec(counter)
            cipher.init(Cipher.ENCRYPT_MODE, key, ivSpec)
            val newBuffer = cipher.doFinal(userInput.toByteArray(Charset.forName("UTF-8")))
            val base64String = Base64.encodeToString(newBuffer, Base64.NO_WRAP)
            return base64String
        }

        private fun encryptCounterWithPublicKey(
            counter: ByteArray,
            publicKeyBytes: ByteArray
        ): String? {
            val keyFactory = KeyFactory.getInstance("RSA")
            val publicKeySpec = X509EncodedKeySpec(publicKeyBytes)
            val publicKey: PublicKey = keyFactory.generatePublic(publicKeySpec)
            val cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-1AndMGF1Padding")
            cipher.init(Cipher.ENCRYPT_MODE, publicKey)
            val newBuffer = cipher.doFinal(counter)
            val base64String = Base64.encodeToString(newBuffer, Base64.NO_WRAP)
            return base64String
        }


        fun getEncryptModel(userInput: String, secretKey: String): EncryptPayloadModel? {
            try {
                val pemKey = BuildConfig.PUBLIC_KEY

                // 去除 PEM 文件的头和尾，并移除所有的空白字符
                val publicKeyPEM = pemKey
                    .replace("-----BEGIN PUBLIC KEY-----", "")
                    .replace("-----END PUBLIC KEY-----", "")
                    .replace("\\s".toRegex(), "")

                // Base64 解码
                val encoded = Base64.decode(publicKeyPEM, Base64.NO_WRAP)

                //  使用用户令牌准备 AES CTR 密钥
                //  生成随机 16 字节计数器
                val generateRandomCounter = generateRandomCounter()

                //使用计数器和密钥加密用户输入
                val encryptUserInput = encryptUserInput(
                    userInput,
                    generateSecretKeyFromToken(secretKey),
                    generateRandomCounter
                )
                //使用公钥加密计数器
                val encryptCounter = encryptCounterWithPublicKey(
                    generateRandomCounter,
                    encoded
                )
                return EncryptPayloadModel(
                    encryptUserInput,
                    encryptCounter,
                    generateRandomCounter,
                    secretKey
                )
            } catch (e: Exception) {
                e.printStackTrace()
                return null
            }
        }

    }


}