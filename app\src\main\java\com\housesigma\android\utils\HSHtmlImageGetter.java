package com.housesigma.android.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LevelListDrawable;
import android.text.Html;
import android.util.DisplayMetrics;
import android.util.Log;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.target.Target;
import com.bumptech.glide.request.transition.Transition;
import com.housesigma.android.utils.log.Logger;

public class HSHtmlImageGetter implements Html.ImageGetter {

    private Context context;
    private TextView tvContent;
    private int actX;//实际的宽  放大缩小基于textview的宽度
    private int actY;
    private LevelListDrawable mDrawable = new LevelListDrawable();

    public HSHtmlImageGetter(Context context, TextView tvContent) {
        this.context = context;
        this.tvContent = tvContent;
        //获取全屏大小
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        //我的textview有左右留边  margin
//        actX = metrics.widthPixels - DensityUtil.dp2px(MainApplication.getContext(), 40);
//        actY = metrics.heightPixels;
    }
    @Override
    public Drawable getDrawable(String source) {
        Glide.with(context)
                .asBitmap()
                .load(source)
                .into(new CustomTarget<Bitmap>() {
                    @Override
                    public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                        // 在这里处理加载成功后的Bitmap对象
                        int x = resource.getWidth();
                        int y = resource.getHeight();
                        if (x > actX || y > actY) {
                            //进行等比例缩放程序
                            Matrix matrix = new Matrix();
                            matrix.postScale(1.5f,1.5f);
                            //长和宽放大缩小的比例
                            resource = Bitmap.createBitmap(resource, 0, 0, x, y, matrix, true);
                        }
                        BitmapDrawable drawable = new BitmapDrawable(null, resource);
                        mDrawable.addLevel(1, 1, drawable);
                        mDrawable.setBounds(0, 0, resource.getWidth(), resource.getHeight());
                        mDrawable.setLevel(1);
                        tvContent.invalidate();
                        tvContent.setText(tvContent.getText()); // 解决图文重叠
                    }

                    @Override
                    public void onLoadCleared(@Nullable Drawable placeholder) {
                        // 在这里处理加载被清除时的操作
                    }
                });

        return mDrawable;
    }

    public class URLDrawable extends BitmapDrawable {
        protected Bitmap bitmap;

        @Override
        public void draw(Canvas canvas) {
            if (bitmap != null) {
                canvas.drawBitmap(bitmap, 0, 0, getPaint());
            }
        }
    }
}