package com.housesigma.android.utils

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import com.google.gson.Gson
import com.housesigma.android.HSApp
import com.housesigma.android.model.HSCollectData
import com.housesigma.android.model.HSUserData
import com.housesigma.android.model.PostHSUserData
import com.housesigma.android.network.NetClient
import com.housesigma.android.utils.log.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class HSLog {

    companion object {
        private var mScreenName: String? = null

        private var mPvStartTime: Long = 0
        private var mPvEndTime: Long = 0

        private var mSR: String? = null
        private var mVP: String? = null
        private var mLanguage: String? = null

        private var mUserProperty: HashMap<String,String>? = null

        private val mHandler = Handler(Looper.getMainLooper())
        private var mRunnable: Runnable? = null

        init {
            try {
                mLanguage = LanguageUtils().getSystemLanguage()
                HSApp.appContext?.let { context ->
                    val width = ScreenUtils.getDeviceWidth(context).toString()
                    val height = ScreenUtils.getDeviceHeight(context).toString()
                    mSR = "${width}x${height}"
                    //获取物理分辨率
                    val displayMetrics = context.resources.displayMetrics
                    val widthPixels = (displayMetrics.widthPixels/displayMetrics.density).toInt()
                    val heightPixels = (displayMetrics.heightPixels/displayMetrics.density).toInt()
                    mVP = "${widthPixels}x${heightPixels}"
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

        }

        fun setUserProperty(userProperty: HashMap<String,String>?) {
            mUserProperty = userProperty
        }

        fun getUserProperty(): HashMap<String,String>? {
            return mUserProperty
        }

        fun log(eventName: String, bundle: Bundle?=null) {
            sendToHS(mScreenName,eventName, bundle)
        }

        fun page(screenName: String?=null) {
            try {
                mScreenName = screenName
                mPvStartTime = System.currentTimeMillis()//开始时间等于发送pv的时间
                sendToHS(mScreenName,"page_view", null)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        fun sendUserEngagement() {
            try {
                mPvEndTime =  System.currentTimeMillis()//结束时间等于任意页面生命周期暂停时间
                sendToHS(mScreenName,"user_engagement", null)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        fun sendToHS(userData:HSUserData) {
            try {
                // 0. 对数据预处理
                userData.sr = mSR
                userData.vp = mVP
                userData.up = mUserProperty
                userData.lang = mLanguage

                // 1.插入数据
                HSCollectData(data = userData.toString()).let { data ->
                    DbManager.db?.let {
                        it.hSCollectDao().insert(data)
                    }
                }


                // 3. 发送并删除数据
                // 取消之前的任务（如果存在）
                mRunnable?.let {
                    mHandler.removeCallbacks(it)
//                    Logger.i("HS Log userData send is too fast")
                }

                // 创建一个新的Runnable任务
                mRunnable = Runnable {
                    CoroutineScope(Dispatchers.IO).launch {
                        // 2. 查询数据
//                Logger.i("HS Log userData [" + userData + "]")
                        val prepareSendData = DbManager.db?.hSCollectDao()?.selectAll()
//                Logger.i("HS Log userData selectAll [" + prepareSendData + "]")
                        try {
                            prepareSendData?.let {
                                // prepareSendData转成Array<HSUserData> ，用gson
                                val collectDataList = prepareSendData.map { (it.data?:"").let { data -> Gson().fromJson(data,
                                    HSUserData::class.java) } }


                                val collectRes = NetClient.collectApiService.collect(PostHSUserData(collectDataList))
                                if (collectRes.isSuccessful) {
                                    Logger.i("HS Log userData collect.isSuccessful")
                                }

                                DbManager.db?.hSCollectDao()?.deleteList(prepareSendData)
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }

                    }
                }

                // 安排新的任务在5秒后执行
                mRunnable?.let {
                    mHandler.postDelayed(it, 5000)
                }

            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        private fun sendToHS(screenName:String?=null, eventName: String, bundle: Bundle?=null) {
            if (eventName == "user_engagement" && TextUtils.isEmpty(screenName)) {
                // 错误数据
                return
            }
            val eventParams = bundleToStringMap(bundle)
            val userData = HSUserData(
                en = eventName,
                dt = mScreenName,
                et = if (eventName == "user_engagement") (mPvEndTime - mPvStartTime) else null,
                ep = eventParams,
            )

            sendToHS(userData)
        }

        private fun bundleToStringMap(bundle: Bundle?): HashMap<String, Any>? {
            if (bundle == null) {
                return null
            }
            val map = hashMapOf<String, Any>()
            for (key in bundle.keySet()) {
                // Get the value and convert it to a string
                val value = bundle.get(key)?.toString() ?: continue
                map[key] = value
            }

            return map
        }

        fun userInput(eventName: String, uiHashMap:HashMap<String, Any>) {
            val ui = Gson().toJson(uiHashMap)
            val ep = hashMapOf<String, Any>()
            if (ui == null) {
                return
            }
            ep["ui"] = ui

            sendToHS(HSUserData(
                en = eventName,
                dt = mScreenName,
                ep = ep
            ))

        }

        fun sendUserInputHybrid(userData:HSUserData) {
            userData.ep?.let { ep ->
                val ui = (userData.ep?.get("ui")?:"") as String
                    userData.ep?.put("ui",ui)
                    sendToHS(userData)
                }
            }
        }

}