package com.housesigma.android.utils;

import android.content.Context;
import android.graphics.Color;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.View;

import com.housesigma.android.R;
import com.housesigma.android.ui.webview.WebViewHelper;

public class HSURLSpan extends ClickableSpan {
    private String mUrl;
    private Context mContext;

    public HSURLSpan(Context context, String url) {
        mUrl = url;
        mContext = context;
    }

    @Override
    public void updateDrawState(TextPaint textPaint) {
        super.updateDrawState(textPaint);
        textPaint.setColor(mContext.getColor(R.color.app_main_color));
        textPaint.setUnderlineText(true);
    }

    @Override
    public void onClick(View widget) {
        if (mContext != null) {
            WebViewHelper.Companion.jumpInnerWebView(mContext, mUrl, true);
        }
    }
}
