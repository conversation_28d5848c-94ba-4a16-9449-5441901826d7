package com.housesigma.android.utils

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.content.res.Resources
import android.os.Build
import android.text.Html
import android.text.Spanned
import android.text.TextUtils
import android.util.DisplayMetrics
import android.view.Display
import android.view.WindowManager
import android.widget.TextView
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.google.gson.Gson
import com.housesigma.android.model.BuildingAgeFilter
import com.housesigma.android.model.InitApp
import com.housesigma.android.model.LotSizeFilter
import java.text.DecimalFormat
import java.util.Locale


open class HSUtil {

    companion object {
        /**
         * 切换语言
         * 设置应用语言类型
         *
         * @param context
         * @param language
         */
        @Suppress("AppBundleLocaleChanges")
        fun switchLanguage(context: Context, language: String = "") {
            val resources: Resources = context.resources
            val config: Configuration = resources.getConfiguration()
            val dm: DisplayMetrics = resources.getDisplayMetrics()
            if (language == "zh_simple") {
                config.setLocale(Locale.SIMPLIFIED_CHINESE)
            } else if (language == "en") {
                config.setLocale(Locale.ENGLISH)
            } else {
                config.setLocale(Locale.getDefault())
            }
            resources.updateConfiguration(config, dm)
        }


        /**
         * 是否是平板
         * 7寸以上屏幕为平板
         * @param context 上下文
         * @return 是平板则返回true，反之返回false
         */
        fun isPad(context: Context): Boolean {
            try {
                val isPad = (context.resources.configuration.screenLayout
                        and Configuration.SCREENLAYOUT_SIZE_MASK) >= Configuration.SCREENLAYOUT_SIZE_LARGE
                val wm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
                val display: Display = wm.defaultDisplay
                val dm = DisplayMetrics()
                display.getMetrics(dm)
                val x = Math.pow((dm.widthPixels / dm.xdpi).toDouble(), 2.0)
                val y = Math.pow((dm.heightPixels / dm.ydpi).toDouble(), 2.0)
                val screenInches = Math.sqrt(x + y) // 屏幕尺寸
                return isPad || screenInches >= 7.0
            } catch (e:Exception) {
                e.printStackTrace()
                return false
            }
        }


        /**
         * 分享
         *
         * @param context
         */
        fun share(context: Context, title: String) {
            if (TextUtils.isEmpty(title)) return
            val intent = Intent()
            intent.action = Intent.ACTION_SEND
            intent.putExtra(
                Intent.EXTRA_TEXT,
                title
            )
            intent.type = "text/plain"
            context.startActivity(Intent.createChooser(intent, "choose to share"))
        }


        fun formatNumberThousandsSeparators(number: Int): String? {
            val decimalFormat = DecimalFormat("#,##0")
            return decimalFormat.format(number)
        }


        /**
         * 用于租房的曲线
         *
         * 0-5000,一格100        50个
         * 5000-10000,一格250    20个
         */
        fun calSquareFootageCurveRenal(number: Int): Int {
            var price = 0
            if (number <= 50) {
                price = number * 100
            } else {
                price = 5000 + (number - 50) * 250
            }
            return price
        }

        /**
         * 根据租房价格判断曲线的位置
         */
        fun calRevertSquareFootageCurveRenal(price: Int): Int {
            var position = 0
            if (price >= 5000) {
                position = (price - 5000) / 250 + 50
            } else {
                position = price / 100
            }
            return position
        }

        /**
         * 用于房源售价的曲线
         *
         * #1499 [app, desktop] 地图页价格拖动条，统一步长，最大值
         * for sale
         * 100w以下，一格5w        20个
         * 100 ~ 150w，一格10w    5个
         * 150w+，一格25w         18个
         * 600w = MAX
         */
        fun calSquareFootageCurve(number: Int): Int {
            var price = 0
            if (number <= 20) {
                price = number * 50000
            } else if (number <= 25) {
                price = 1000000 + (number - 20) * 100000
            } else if (number <= 43) {
                price = 1500000 + (number - 25) * 250000
            }
            return price
        }

        /**
         * 根据售价判断曲线的位置
         */
        fun calRevertSquareFootageCurve(price: Int): Int {
            var position = 0
            if (price >= 1500000) {
                position = (price - 1500000) / 250000 + 25
            } else if (price >= 1000000) {
                position = (price - 1000000) / 100000 + 20
            } else if (price >= 0) {
                position = price / 50000
            }
            return position
        }

        /**
         * 从init/app接口中获取InitApp对象，可空
         */
        fun getInitApp(): InitApp? {
            val initAppStr = MMKVUtils.getStr("init_app")
            return Gson().fromJson(initAppStr, InitApp::class.java)
        }

        fun isGooglePlayServicesAvailable(context: Context): Boolean {
            val googleApiAvailability = GoogleApiAvailability.getInstance()
            return googleApiAvailability.isGooglePlayServicesAvailable(context) == ConnectionResult.SUCCESS
        }

        fun explainSourceToHtmlWithImage(context: Context,source: String,targetTextView:TextView): Spanned {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N){
                Html.fromHtml(
                    source,
                    Html.FROM_HTML_MODE_COMPACT,
                    HSHtmlImageGetter(context, targetTextView),
                    null,
                )
            } else {
                Html.fromHtml(
                    source,
                    HSHtmlImageGetter(context, targetTextView),
                    null,
                )
            }
        }

        fun getBuildingAge(position: Int): BuildingAgeFilter? {
            val buildingAges = listOf(
                BuildingAgeFilter(999, "100+", "Unspecified"),
                BuildingAgeFilter(100, null, "100"),
                BuildingAgeFilter(90, null, "90"),
                BuildingAgeFilter(80, null, "80"),
                BuildingAgeFilter(70, null, "70"),
                BuildingAgeFilter(60, null, "60"),
                BuildingAgeFilter(50, null, "50"),
                BuildingAgeFilter(45, null, "45"),
                BuildingAgeFilter(40, null, "40"),
                BuildingAgeFilter(35, null, "35"),
                BuildingAgeFilter(30, null, "30"),
                BuildingAgeFilter(25, null, "25"),
                BuildingAgeFilter(20, null, "20"),
                BuildingAgeFilter(15, null, "15"),
                BuildingAgeFilter(10, null, "10"),
                BuildingAgeFilter(9, null, "9"),
                BuildingAgeFilter(7, null, "8"),
                BuildingAgeFilter(6, null, "7"),
                BuildingAgeFilter(6, null, "6"),
                BuildingAgeFilter(5, null, "5"),
                BuildingAgeFilter(4, null, "4"),
                BuildingAgeFilter(3, null, "3"),
                BuildingAgeFilter(2, null, "2"),
                BuildingAgeFilter(1, null, "1"),
                BuildingAgeFilter(0, null, "0")
            )
            val buildingAge = buildingAges.getOrNull(position)
            return buildingAge
        }


        /**
         * 函数 getPositionByValue 将遍历 BuildingAge 列表，
         * 并返回第一个匹配的 BuildingAgeFilter 对象的索引。
         * 如果没有找到匹配的 value，函数将返回 -1。
         */
        fun getPositionByBuildingAgeValue(value: Int): Int {
            val buildingAges = listOf(
                BuildingAgeFilter(999, "100+", "Unspecified"),
                BuildingAgeFilter(100, null, "100"),
                BuildingAgeFilter(90, null, "90"),
                BuildingAgeFilter(80, null, "80"),
                BuildingAgeFilter(70, null, "70"),
                BuildingAgeFilter(60, null, "60"),
                BuildingAgeFilter(50, null, "50"),
                BuildingAgeFilter(45, null, "45"),
                BuildingAgeFilter(40, null, "40"),
                BuildingAgeFilter(35, null, "35"),
                BuildingAgeFilter(30, null, "30"),
                BuildingAgeFilter(25, null, "25"),
                BuildingAgeFilter(20, null, "20"),
                BuildingAgeFilter(15, null, "15"),
                BuildingAgeFilter(10, null, "10"),
                BuildingAgeFilter(9, null, "9"),
                BuildingAgeFilter(7, null, "8"),
                BuildingAgeFilter(6, null, "7"),
                BuildingAgeFilter(6, null, "6"),
                BuildingAgeFilter(5, null, "5"),
                BuildingAgeFilter(4, null, "4"),
                BuildingAgeFilter(3, null, "3"),
                BuildingAgeFilter(2, null, "2"),
                BuildingAgeFilter(1, null, "1"),
                BuildingAgeFilter(0, null, "0")
            )
            return buildingAges.indexOfFirst { it.value == value }
        }


        fun getLotSize(position: Int): LotSizeFilter? {
            val lotSizes = listOf(
                LotSizeFilter(0, "0", "Unspecified"),
                LotSizeFilter(1000, null, "1000 sqft"),
                LotSizeFilter(2000, null, "2000 sqft"),
                LotSizeFilter(3000, "3000 sqft", "3000 sqft"),
                LotSizeFilter(4000, null, "4000 sqft"),
                LotSizeFilter(5000, null, "5000 sqft"),
                LotSizeFilter(7500, null, "7500 sqft"),
                LotSizeFilter(10890, null, "1/4 acre"),
                LotSizeFilter(21780, null, "1/2 acre"),
                LotSizeFilter(43560, "1 acre", "1 acre"),
                LotSizeFilter(87120, null, "2 acres"),
                LotSizeFilter(217800, null, "5 acres"),
                LotSizeFilter(435600, null, "10 acres"),
                LotSizeFilter(871200, "20 acres", "20 acres"),
                LotSizeFilter(2178000, null, "50 acres"),
                LotSizeFilter(10000000, "Max", "Max")
            )
            val lotSize = lotSizes.getOrNull(position)
            return lotSize
        }


        /**
         * 函数 getPositionByValue 将遍历 lotSizes 列表，
         * 并返回第一个匹配的 LotSizeFilter 对象的索引。
         * 如果没有找到匹配的 value，函数将返回 -1。
         */
        fun getPositionByLotSizeValue(value: Int): Int {
            val lotSizes = listOf(
                LotSizeFilter(0, "0", "Unspecified"),
                LotSizeFilter(1000, null, "1000 sqft"),
                LotSizeFilter(2000, null, "2000 sqft"),
                LotSizeFilter(3000, "3000 sqft", "3000 sqft"),
                LotSizeFilter(4000, null, "4000 sqft"),
                LotSizeFilter(5000, null, "5000 sqft"),
                LotSizeFilter(7500, null, "7500 sqft"),
                LotSizeFilter(10890, null, "1/4 acre"),
                LotSizeFilter(21780, null, "1/2 acre"),
                LotSizeFilter(43560, "1 acre", "1 acre"),
                LotSizeFilter(87120, null, "2 acres"),
                LotSizeFilter(217800, null, "5 acres"),
                LotSizeFilter(435600, null, "10 acres"),
                LotSizeFilter(871200, "20 acres", "20 acres"),
                LotSizeFilter(2178000, null, "50 acres"),
                LotSizeFilter(10000000, "Max", "Max")
            )
            return lotSizes.indexOfFirst { it.value == value }
        }

    }


}