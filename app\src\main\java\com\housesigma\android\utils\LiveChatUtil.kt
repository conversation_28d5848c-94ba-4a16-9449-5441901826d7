package com.housesigma.android.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.housesigma.android.BuildConfig
import com.housesigma.android.HSApp
import com.housesigma.android.ui.chat.HSChatWindowActivity
import com.housesigma.android.utils.log.Logger
import com.livechatinc.inappchat.ChatWindowConfiguration

class LiveChatUtil {
    companion object{
        fun startActivity(context: Context) {
            val province = HSApp.user?.province?:""

            val userId = HSApp.user?.user_id?:""
            val userName = HSApp.user?.name?:""
            val userEmail = HSApp.user?.email?:""

            val vowAgentId = HSApp.user?.vow_agent?.id?:""
            val vowAgentName = HSApp.user?.vow_agent?.name?:""
            val vowAgentRegion = HSApp.user?.vow_agent?.region?:""

            val intent = Intent(context, HSChatWindowActivity::class.java)
            intent.putExtra(ChatWindowConfiguration.KEY_GROUP_ID, "0")
            intent.putExtra(ChatWindowConfiguration.KEY_LICENCE_NUMBER, BuildConfig.LiveChat_License)
            intent.putExtra(ChatWindowConfiguration.KEY_VISITOR_NAME, userName)
            intent.putExtra(ChatWindowConfiguration.KEY_VISITOR_EMAIL, userEmail)
            intent.putExtra(ChatWindowConfiguration.CUSTOM_PARAM_PREFIX + "user_id", userId)
            intent.putExtra(ChatWindowConfiguration.CUSTOM_PARAM_PREFIX + "province", province)
            intent.putExtra(ChatWindowConfiguration.CUSTOM_PARAM_PREFIX + "agent_id", vowAgentId)
            intent.putExtra(ChatWindowConfiguration.CUSTOM_PARAM_PREFIX + "agent_name", vowAgentName)
            intent.putExtra(ChatWindowConfiguration.CUSTOM_PARAM_PREFIX + "agent_region", vowAgentRegion)
            context.startActivity(intent)
        }
    }
}