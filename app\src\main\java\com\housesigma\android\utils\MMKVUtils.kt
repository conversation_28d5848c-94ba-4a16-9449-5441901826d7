package com.housesigma.android.utils

import android.content.Context
import android.os.Build
import com.getkeepsafe.relinker.ReLinker
import com.housesigma.android.HSApp
import com.tencent.mmkv.MMKV

/**
 * https://github.com/Tencent/MMKV
 */
object MMKVUtils {

    fun initMMKV(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            MMKV.initialize(context)
        } else {
            MMKV.initialize(context) { libName ->
                ReLinker.loadLibrary(HSApp.appContext, libName)
            }
        }
    }

    fun saveStr(key: String, value: String?) {
        MMKV.defaultMMKV()?.encode(key, value)
    }

    fun getStr(key: String): String? {
        return MMKV.defaultMMKV()?.decodeString(key)
    }

    fun saveBoolean(key: String, value: Boolean) {
        MMKV.defaultMMKV()?.encode(key, value)
    }

    fun saveDouble(key: String, value: Double) {
        MMKV.defaultMMKV()?.encode(key, value)
    }

    fun getDouble(key: String): Double {
        return MMKV.defaultMMKV()?.decodeDouble(key) ?: 0.0
    }

    fun saveLong(key: String, value: Long) {
        MMKV.defaultMMKV()?.encode(key, value)
    }

    fun getLong(key: String): Long {
        return MMKV.defaultMMKV()?.decodeLong(key) ?: 0
    }


    fun getBoolean(key: String): Boolean {
        return MMKV.defaultMMKV()?.decodeBool(key) ?: false
    }


    fun getBoolean(key: String, default: Boolean): Boolean {
        if (isContain(key)) {
            return MMKV.defaultMMKV()?.decodeBool(key) ?: default
        } else {
            return default
        }
    }

    fun saveInt(key: String, value: Int) {
        MMKV.defaultMMKV()?.encode(key, value)
    }

    fun getInt(key: String): Int {
        return MMKV.defaultMMKV()?.decodeInt(key) ?: 0
    }

    fun getInt(key: String, default: Int): Int {
        return MMKV.defaultMMKV()?.decodeInt(key, default) ?: 0
    }

    fun isContain(key: String): Boolean {
        return MMKV.defaultMMKV()?.containsKey(key) == true
    }

    fun removeData(key: String) {
        MMKV.defaultMMKV()?.remove(key)
    }
}