package com.housesigma.android.utils

import android.R.id
import android.graphics.Bitmap
import android.graphics.Canvas
import android.text.Html
import android.util.Log
import android.view.View
import androidx.core.content.res.ResourcesCompat
import com.google.gson.Gson
import com.housesigma.android.helper.ProvinceHelper
import com.housesigma.android.model.InitApp
import com.housesigma.android.model.Polygon
import com.housesigma.android.ui.login.LoginFragment
import com.housesigma.android.ui.map.helper.MapHelper
import com.housesigma.android.utils.log.Logger
import org.maplibre.android.geometry.LatLng
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.maplibre.android.utils.BitmapUtils


class MapUtils {
    companion object {

         fun inBounds(latLng: LatLng, neLatLng: LatLng, swLatLng: LatLng): Boolean {
            val eastBound = latLng.longitude < neLatLng.longitude
            val westBound = latLng.longitude > swLatLng.longitude
            val inLong: Boolean
            if (neLatLng.longitude < swLatLng.longitude) {
                inLong = eastBound || westBound;
            } else {
                inLong = eastBound && westBound;
            }
            val inLat = latLng.latitude > swLatLng.latitude && latLng.longitude < neLatLng.latitude
            return inLat && inLong
        }

        fun convertViewToNormalBitmap(view: View): Bitmap {
            if (view.measuredWidth <= 0 || view.measuredHeight <= 0) {
                view.measure(
                    View.MeasureSpec.makeMeasureSpec(
                        0,
                        View.MeasureSpec.UNSPECIFIED
                    ),
                    View.MeasureSpec.makeMeasureSpec(
                        0,
                        View.MeasureSpec.UNSPECIFIED
                    )
                )
                view.layout(0, 0, view.measuredWidth, view.measuredHeight)
            }
            view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            val bitmap = Bitmap.createBitmap(view.measuredWidth, view.measuredHeight, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)
            view.draw(canvas)
            return bitmap
        }

        fun convertViewToBitmap(view: View): Bitmap {
            view.measure(
                View.MeasureSpec.makeMeasureSpec(
                    0,
                    View.MeasureSpec.UNSPECIFIED
                ),
                View.MeasureSpec.makeMeasureSpec(
                    0,
                    View.MeasureSpec.UNSPECIFIED
                )
            )
            // 放大缩小地图marker会偏移，所以这里把高度测量算成2倍高度，这样默认的锚点中心点就是箭头指向的点
            view.layout(0, 0, view.measuredWidth, view.measuredHeight*2)
            view.buildDrawingCache()
            return view.drawingCache
        }
        
        fun latLng2Polygon(latLng: LatLng): Polygon {
            var polygon = Polygon(latLng.latitude, latLng.longitude)
            return polygon
        }


        fun polygon2LatLng(polygon: Polygon): LatLng {
            return LatLng(polygon.lat, polygon.lon)
        }

        fun setCurrentMapZoom(zoom:Double) {
            MMKVUtils.saveDouble("last_zoom",zoom)
        }

        fun getCurrentMapZoom(): Double {
            val lastZoom = MMKVUtils.getDouble("last_zoom")
            if (lastZoom!=0.0 ) {
                return lastZoom
            }
            return MapHelper.zoom - 1
        }

        fun setCurrentMapCenter(lastLatLon: LatLng) {
            MMKVUtils.saveDouble("last_lat", lastLatLon.latitude)
            MMKVUtils.saveDouble("last_lon", lastLatLon.longitude)
        }

        fun isShowRaeMapTips(): Boolean {
            val initApp = HSUtil.getInitApp()
            initApp?.let {
                if (initApp.show_rae_map_tips==1) {
                    return true
                }
            }
            return false
        }

        fun isCollectLocation (): Boolean {
            val initApp = HSUtil.getInitApp()
            initApp?.let {
                if (initApp.collectLocation==1) {
                    return true
                }
            }
            return false
        }

        fun getCurrentMapCenter(): LatLng? {
            val lastLat = MMKVUtils.getDouble("last_lat")
            val lastLon = MMKVUtils.getDouble("last_lon")
            if (lastLat!=0.0 || lastLon!=0.0) {
                var latLng = LatLng(lastLat, lastLon)
                return latLng
            } else {
                val initApp = HSUtil.getInitApp()
                val provinceStr = ProvinceHelper.getAbbreviationFromCache("ON")
                initApp?.let {
                    for (province in initApp.provinces) {
                        if (provinceStr.equals(province.id)) {
                            var lat = province?.location?.lat ?: MapHelper.lat1
                            var lon = province?.location?.lon ?: MapHelper.lon2
                            var latLng = LatLng(lat, lon)
                            return latLng
                        }else if (provinceStr.equals(province.id)){
                            var lat = province?.location?.lat ?: MapHelper.lat1
                            var lon = province?.location?.lon ?: MapHelper.lon2
                            var latLng = LatLng(lat, lon)
                            return latLng
                        }
                    }
                }
                // 以上都没匹配到，就需要返回ON的中心坐标来兜底
                // 没匹配到的原因可能是check_token未返回结果就执行了这段逻辑
                return LatLng(43.955259, -79.346008)
            }



            return null
        }


        // Get the median latitude and longitude of a list of LatLngs
        fun getMedianLatLng(latLngList: List<LatLng>): LatLng? {
            if (latLngList.isEmpty()) return null

            // Separate the latitudes and longitudes
            val latitudes = latLngList.map { it.latitude }.sorted()
            val longitudes = latLngList.map { it.longitude }.sorted()

            // Find the median latitude and longitude
            val medianLatitude = if (latitudes.size % 2 == 0) {
                (latitudes[latitudes.size / 2 - 1] + latitudes[latitudes.size / 2]) / 2
            } else {
                latitudes[latitudes.size / 2]
            }

            val medianLongitude = if (longitudes.size % 2 == 0) {
                (longitudes[longitudes.size / 2 - 1] + longitudes[longitudes.size / 2]) / 2
            } else {
                longitudes[longitudes.size / 2]
            }

            return LatLng(medianLatitude, medianLongitude)
        }
    }

}