package com.housesigma.android.utils

import android.Manifest.permission
import android.app.Activity
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.ActivityCompat

object PermissionUtils {
    fun requestNotificationPermission(activity: Activity?) {
        activity?.let {
            if (Build.VERSION.SDK_INT >= 33) {
                if (ActivityCompat.checkSelfPermission(activity, permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_DENIED ) {
                    ActivityCompat.requestPermissions(activity,arrayOf(permission.POST_NOTIFICATIONS),100)
                }
            }
        }
    }
}