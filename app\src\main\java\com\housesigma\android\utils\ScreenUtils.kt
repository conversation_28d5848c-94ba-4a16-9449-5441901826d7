package com.housesigma.android.utils

import android.app.Activity
import android.content.Context
import android.content.res.Resources
import android.graphics.Point
import android.graphics.Rect
import android.util.DisplayMetrics
import android.view.View

object ScreenUtils {

    fun dpToPx(dpValue:Float):Float{
       return Resources.getSystem().displayMetrics.density * dpValue + 0.5f
    }

    fun pxToDp(pxValue:Float):Float{
        return pxValue / Resources.getSystem().displayMetrics.density + 0.5f
    }

//    fun getScreenSize(context: Context): Point {
//        val point = Point()
//        context.display?.getRealSize(point)
//        return point
//    }

    fun getDeviceWidth(context: Context): Int {
        return context.resources.displayMetrics.widthPixels
    }

    fun getDeviceHeight(context: Context): Int {
        return context.resources.displayMetrics.heightPixels
    }

    fun getDeviceDpi(context: Context): Int {
        return context.resources.displayMetrics.densityDpi
    }
    fun getDeviceDensity(context: Context): Float {
        return context.resources.displayMetrics.density
    }
    fun getLocalVisibleRect(context: Context, view: View, offsetY: Int): Boolean {
        val p = Point()
        (context as Activity).windowManager.defaultDisplay.getSize(p)
        val screenWidth = p.x
        val screenHeight = p.y
        val rect = Rect(0, 0, screenWidth, screenHeight)
        val location = IntArray(2)
        location[1] = location[1] + dpToPx(offsetY.toFloat()).toInt()
        view.getLocationInWindow(location)
        view.tag = location[1] //存储y方向的位置
        return if (view.getLocalVisibleRect(rect)) {
            true
        } else {
            false
        }
    }

    fun getStatusBarHeight(context: Context): Int {
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        return if (resourceId > 0) {
            context.resources.getDimensionPixelSize(resourceId)
        } else {
            0
        }
    }

    /**
     * 获取view当前占据屏幕的百分比
     */
     fun getVisibilityPercents(currentView: View): Int {
        var percents = 100
        val rect = Rect()
        //防止出现视频已不在可见得范围之内仍然返回100（完全可见）
        val isVisible = currentView.getLocalVisibleRect(rect)
        if (isVisible) { //可见时做百分比的计算
            val height = currentView.height
            if (viewIsPartiallyHiddenTop(rect)) {
                // view is partially hidden behind the top edge
                percents = (height - rect.top) * 100 / height
            } else if (viewIsPartiallyHiddenBottom(rect, height)) {
                percents = rect.bottom * 100 / height
            }
        } else { //View已经不可见
            percents = 0
        }
        return percents
    }

    //view底部部分不可见
    private fun viewIsPartiallyHiddenBottom(rect: Rect, height: Int): Boolean {
        return rect.bottom > 0 && rect.bottom < height
    }

    //view顶部部分不可见
    private fun viewIsPartiallyHiddenTop(rect: Rect): Boolean {
        return rect.top > 0
    }


}