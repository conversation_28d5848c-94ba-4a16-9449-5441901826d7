package com.housesigma.android.utils

import android.view.Gravity
import com.hjq.toast.ToastParams
import com.hjq.toast.Toaster
import com.hjq.toast.style.CustomToastStyle
import com.housesigma.android.R


object ToastUtils {

    fun showLong(message: String?) {
        message?.let {
            if (message == "") return
            Toaster.show(message)
        }
    }

    fun showSuccess(message: String?) {
        message?.let {
            if (message == "") return
            val params = ToastParams()
            params.text = it
            params.style = CustomToastStyle(R.layout.toast_success_view,Gravity.TOP,0, ScreenUtils.dpToPx(20f).toInt())
            Toaster.show(params)
        }
    }

    fun showDel(message: String?) {
        message?.let {
            if (message == "") return
            val params = ToastParams()
            params.text = it
            params.style = CustomToastStyle(R.layout.toast_del_view,Gravity.TOP,0, ScreenUtils.dpToPx(20f).toInt())
            Toaster.show(params)
        }
    }

}