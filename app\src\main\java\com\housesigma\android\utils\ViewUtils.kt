package com.housesigma.android.utils

import android.content.Context
import android.util.DisplayMetrics
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import me.jessyan.autosize.utils.AutoSizeUtils

open class ViewUtils {

    companion object {
        fun setViewHeightParams(view: View, height: Int) {
            val lp = view.layoutParams
            if (lp.height != height) {
                lp.height = height
                view.layoutParams = lp
            }
        }


        fun setRelativeLayoutWidthAndMargin(context: Context, relativeLayout: View, rate: Float, marginLeftDp: Int) {
            val screenWidth = ScreenUtils.getDeviceWidth(context)
            val newWidth = (screenWidth * rate).toInt()
            val marginLeftPx = AutoSizeUtils.dp2px(context,marginLeftDp.toFloat())
            val layoutParams = RelativeLayout.LayoutParams(newWidth, ViewGroup.LayoutParams.WRAP_CONTENT)
            layoutParams.leftMargin = marginLeftPx
            relativeLayout.layoutParams = layoutParams
        }
    }
}