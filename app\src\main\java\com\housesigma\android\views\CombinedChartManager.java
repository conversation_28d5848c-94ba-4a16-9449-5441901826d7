package com.housesigma.android.views;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;

import com.github.mikephil.charting.charts.CombinedChart;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.CombinedData;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.formatter.ValueFormatter;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.listener.OnChartValueSelectedListener;
import com.housesigma.android.utils.Callback1;

import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class CombinedChartManager implements OnChartValueSelectedListener {

    private TouchCombinedChart mCombinedChart;
    private YAxis leftAxis;
    //    private YAxis rightAxis;// 无右边Y轴
    private XAxis xAxis;
    private List<String> mTimeDatas;
    private Context mContext;

    public CombinedChartManager(TouchCombinedChart combinedChart, Context context) {
        this.mCombinedChart = combinedChart;
        this.mContext = context;
        leftAxis = mCombinedChart.getAxisLeft();
        xAxis = mCombinedChart.getXAxis();
        mCombinedChart.setLogEnabled(false);
    }

    /**
     * 初始化Chart
     */
    private void initChart() {
        //不显示描述内容 false 为不显示
        mCombinedChart.getDescription().setEnabled(false);
        mCombinedChart.getAxisRight().setEnabled(false);//设置右边Y轴不显示
        mCombinedChart.setDrawOrder(new CombinedChart.DrawOrder[]{
                CombinedChart.DrawOrder.BAR,
                CombinedChart.DrawOrder.LINE
        });
        HomeCombinedChartMarkerView myMarkerImage = new HomeCombinedChartMarkerView(mContext);
        myMarkerImage.setChartView(mCombinedChart);
        mCombinedChart.setMarker(myMarkerImage);
//        mCombinedChart.setDrawMarkers(true);
//        mCombinedChart.setDrawMarkerViews(true);

        mCombinedChart.highlightValues(null);
        mCombinedChart.setScaleEnabled(false);//是否支持x轴缩放
        mCombinedChart.setScaleXEnabled(false);//是否支持x轴缩放
        mCombinedChart.setScaleYEnabled(false); //是否支持y轴缩放
        mCombinedChart.setPinchZoom(false);//是否支持x、y轴同时缩放，默认为false
        mCombinedChart.setBackgroundColor(Color.WHITE);
        mCombinedChart.setDrawGridBackground(false);
        mCombinedChart.setDrawBarShadow(false);
        mCombinedChart.setHighlightPerTapEnabled(true); // 单击高亮
        mCombinedChart.setHighlightFullBarEnabled(true);
//        mCombinedChart.setHighlightPerDragEnabled(false);
        mCombinedChart.setOnChartValueSelectedListener(this);
        mCombinedChart.setHighlightPerDragEnabled(true);
        //显示边界
        mCombinedChart.setDrawBorders(false);
        mCombinedChart.setExtraOffsets(0, 0, 20, 20);//给额外的偏移量，让旋转的x轴标签显示完整
        //图例说明
        Legend legend = mCombinedChart.getLegend();
        legend.setWordWrapEnabled(true);
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.CENTER);
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.TOP);
        legend.setOrientation(Legend.LegendOrientation.HORIZONTAL);
        legend.setDrawInside(false);
        legend.setForm(Legend.LegendForm.LINE);
        legend.setFormToTextSpace(10);
        legend.setFormLineWidth(4);
        legend.setFormSize(20);
        legend.setXEntrySpace(24);
        legend.setXOffset(-25);
        //Y轴设置
        leftAxis.setDrawLimitLinesBehindData(false);
        leftAxis.setDrawAxisLine(false);
        leftAxis.setDrawZeroLine(false);
        leftAxis.setDrawGridLines(true);
        leftAxis.enableGridDashedLine(12f, 4f, 2f);//-虚线样式
        leftAxis.setGridColor(Color.parseColor("#28A3B3"));
        leftAxis.setAxisMinimum(0f);
        leftAxis.setTextColor(Color.parseColor("#28A3B3"));
        leftAxis.setTextSize(13f);
        leftAxis.setValueFormatter(new ValueFormatter() {

            @Override
            public String getFormattedValue(float value) {
                if (value == 0) {
                    return "0";
                } else if ((value + 0.001) / 1000000 >= 1) {
                    @SuppressLint("DefaultLocale") String yValueByMUnit = String.format("%.1f", value / 1000000);
                    return "$" + yValueByMUnit + "M";
                } else {
                    @SuppressLint("DefaultLocale") String yValueByKUnit = String.format("%.0f", value / 1000);
                    return "$" + yValueByKUnit + "k";
                }
            }

        });

        // x轴的是竖线
        xAxis.setTextColor(Color.parseColor("#28A3B3"));
        xAxis.setAxisLineColor(Color.parseColor("#28A3B3"));
        xAxis.setDrawGridLines(false);
        xAxis.setDrawAxisLine(false);
        xAxis.setLabelRotationAngle(-45);//设置x轴标签的旋转角度
        xAxis.setTextSize(12f);
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
        xAxis.setGranularity(1f);
        xAxis.setLabelCount(6, true);
        xAxis.setAxisMinimum(0);
        xAxis.setValueFormatter(new ValueFormatter() {

            @Override
            public String getFormattedValue(float value) {
                if (mTimeDatas.size() == 0) {
                    return "";
                }
                int index = 0;
                if (value > 0) {
                    index = new BigDecimal(value).setScale(0, BigDecimal.ROUND_UP).intValue();
                }
                if (index >= mTimeDatas.size()) {
                    index = mTimeDatas.size() - 1;
                }
                return mTimeDatas.get(index);
            }
        });

//        mCombinedChart.animateX(0); // 立即执行的动画,x轴
    }

    /**
     * 设置X轴坐标值
     *
     * @param xAxisValues x轴坐标集合
     */
    public void setXAxis(final List<String> xAxisValues) {
        //设置X轴在底部
//        mCombinedChart.invalidate();
    }

    /**
     * 得到折线图(一条)
     *
     * @param lineChartY 折线Y轴值
     * @param lineName   折线图名字
     * @param lineColor  折线颜色
     * @return
     */
    private LineData getLineData(List<Float> lineChartY, String lineName, int lineColor) {
        LineData lineData = new LineData();

        ArrayList<Entry> yValue = new ArrayList<>();
        for (int i = 0; i < lineChartY.size(); i++) {
            if (lineChartY.get(i)==0){
                continue;
            }
            yValue.add(new Entry(i, lineChartY.get(i)));
        }
        LineDataSet dataSet = new LineDataSet(yValue, lineName);
        dataSet.setLineWidth(3);
        dataSet.setDrawCircles(false);
        dataSet.setMode(LineDataSet.Mode.CUBIC_BEZIER);//贝塞尔曲线样式
        dataSet.setDrawValues(false);//显示Y轴上的值
        dataSet.setColor(lineColor);
        dataSet.setCircleColor(lineColor);
        dataSet.setValueTextColor(lineColor);
        dataSet.setCircleSize(1);
        dataSet.setValueTextSize(10f);
        dataSet.setAxisDependency(YAxis.AxisDependency.LEFT);
        dataSet.setHighLightColor(Color.parseColor("#FF5F05"));
        dataSet.setHighlightEnabled(true);
        dataSet.setDrawVerticalHighlightIndicator(false);
        dataSet.setDrawHorizontalHighlightIndicator(false);
        lineData.addDataSet(dataSet);
        return lineData;
    }


    /**
     * 得到柱状图
     *
     * @param barChartY Y轴值
     * @param barName   柱状图名字
     * @param barColor  柱状图颜色
     * @return
     */

    private BarData getBarData(List<Float> barChartY, String barName, int barColor) {
        BarData barData = new BarData();
        ArrayList<BarEntry> yValues = new ArrayList<>();
        for (int i = 0; i < barChartY.size(); i++) {
            if (barChartY.get(i)==0) {
                continue;
            }
            yValues.add(new BarEntry(i, barChartY.get(i), mTimeDatas.get(i)));
        }

        BarDataSet barDataSet = new BarDataSet(yValues, barName);
        barDataSet.setColor(barColor);
        barDataSet.setHighLightColor(Color.parseColor("#FF5F05"));
        barDataSet.setHighlightEnabled(true);
//        barDataSet.setValueTextSize(10f);
//        barDataSet.setValueTextColor(barColor);
        barDataSet.setDrawValues(false);
        barDataSet.setAxisDependency(YAxis.AxisDependency.RIGHT);
        barData.addDataSet(barDataSet);
        //以下是为了解决 柱状图 左右两边只显示了一半的问题 根据实际情况 而定
//        xAxis.setAxisMinimum(-0.3f);
//        xAxis.setAxisMaximum((float) (barChartY.size() - 0.3));
        return barData;
    }


    /**
     * 显示混合图(柱状图+折线图)
     *
     * @param xAxisValues X轴坐标
     * @param barChartY   柱状图Y轴值
     * @param lineChartY  折线图Y轴值
     */

    public void showHomeCombinedChart(List<String> xAxisValues, List<Float> barChartY, List<Float> lineChartY) {
        initChart();
        setXAxis(xAxisValues);

        CombinedData combinedData = new CombinedData();
        combinedData.setData(getBarData(barChartY, "Total Sold", Color.parseColor("#B3B3B3")));
        combinedData.setData(getLineData(lineChartY, "Median Price", Color.parseColor("#28A3B3")));
        mCombinedChart.setData(combinedData);
        mCombinedChart.invalidate();
    }


    public void setCallback(Callback1 callback) {
        mCombinedChart.setSelectListener(callback);
    }

    public void setData(@NotNull List<String> timeDatas) {
        mTimeDatas = timeDatas;
    }

    @Override
    public void onValueSelected(Entry e, Highlight h) {
//        cb.onData((int) e.getX());
    }

    @Override
    public void onNothingSelected() {
//        cb.onData(-1);
    }
}
