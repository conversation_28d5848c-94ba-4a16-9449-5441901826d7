package com.housesigma.android.views

import android.app.Activity
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.text.style.URLSpan
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.gson.Gson
import com.housesigma.android.databinding.FooterDisclaimerBinding
import com.housesigma.android.helper.ProvinceHelper
import com.housesigma.android.model.DisclaimerInfo
import com.housesigma.android.model.HouseDetail
import com.housesigma.android.utils.HSURLSpan
import com.housesigma.android.utils.HSUtil
import com.housesigma.android.utils.MMKVUtils

class DisclaimerViewHelper {

    companion object {

        /**
         * 处理底部disclaimer区域
         */
        fun handleDisclaimer(
            adapter: BaseQuickAdapter<HouseDetail, BaseViewHolder>,
            activity: Activity
        ) {
            try {
                if (adapter.hasFooterLayout()) return
                adapter.removeAllFooterView()
                val footerBinding = FooterDisclaimerBinding.inflate(activity.layoutInflater)
                val disclaimersStr = MMKVUtils.getStr("disclaimers_v2")

                val disclaimerInfo = Gson().fromJson(disclaimersStr, DisclaimerInfo::class.java)
                if (disclaimerInfo.disclaimer==null){
                    return
                }

                val provinceAbb = ProvinceHelper.getAbbreviationFromCache("ON")
                var disclaimers =""

                disclaimerInfo.disclaimer[provinceAbb]?.let {
                    disclaimers = it.default?:""
                }

                val spanned = HSUtil.explainSourceToHtmlWithImage(activity,disclaimers,footerBinding.tvDisclaimer)
                val spannableStringBuilder = SpannableStringBuilder(spanned)
                val urls = spannableStringBuilder.getSpans(0, spanned.length, URLSpan::class.java)
                for (url in urls) {
                    val hsUrlSpan = HSURLSpan(activity, url.url)
                    val start = spannableStringBuilder.getSpanStart(url)
                    val end = spannableStringBuilder.getSpanEnd(url)
                    val flags = spannableStringBuilder.getSpanFlags(url)
                    spannableStringBuilder.setSpan(hsUrlSpan, start, end, flags)
                    //一定要加上这一句,看过很多网上的方法，都没加这一句，导致ClickableSpan的onClick方法没有回调，直接用浏览器打开了
                    spannableStringBuilder.removeSpan(url)
                }
                footerBinding.tvDisclaimer.text = spannableStringBuilder
                footerBinding.tvDisclaimer.movementMethod = LinkMovementMethod.getInstance()
                adapter.addFooterView(
                    footerBinding.root,
                    orientation = LinearLayout.VERTICAL
                )
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        /**
         * 处理底部disclaimer区域
         */
        fun handleDisclaimer(
            activity: Activity,
            tvDisclaimer: TextView
        ) {
            try {

                val disclaimersStr = MMKVUtils.getStr("disclaimers_v2")

                val disclaimerInfo = Gson().fromJson(disclaimersStr, DisclaimerInfo::class.java)
                if (disclaimerInfo.disclaimer==null){
                    return
                }

                val provinceAbb = ProvinceHelper.getAbbreviationFromCache("ON")
                var disclaimers = ""
                disclaimerInfo.disclaimer[provinceAbb]?.let {
                    disclaimers = it.default?:""
                }
                val spanned = HSUtil.explainSourceToHtmlWithImage(activity,disclaimers,tvDisclaimer)

                val spannableStringBuilder = SpannableStringBuilder(spanned)
                val urls = spannableStringBuilder.getSpans(0, spanned.length, URLSpan::class.java)
                for (url in urls) {
                    val hsUrlSpan = HSURLSpan(activity, url.url)
                    val start = spannableStringBuilder.getSpanStart(url)
                    val end = spannableStringBuilder.getSpanEnd(url)
                    val flags = spannableStringBuilder.getSpanFlags(url)
                    spannableStringBuilder.setSpan(hsUrlSpan, start, end, flags)
                    //一定要加上这一句,看过很多网上的方法，都没加这一句，导致ClickableSpan的onClick方法没有回调，直接用浏览器打开了
                    spannableStringBuilder.removeSpan(url)
                }
                if (!TextUtils.isEmpty(spannableStringBuilder)) {
                    tvDisclaimer.visibility = View.VISIBLE
                } else {
                    tvDisclaimer.visibility = View.GONE
                }
                tvDisclaimer.text = spannableStringBuilder
                tvDisclaimer.movementMethod = LinkMovementMethod.getInstance()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}