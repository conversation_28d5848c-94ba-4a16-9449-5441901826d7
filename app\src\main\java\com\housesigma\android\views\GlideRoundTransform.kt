package com.housesigma.android.views

import android.content.Context
import android.graphics.*
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import com.bumptech.glide.load.resource.bitmap.TransformationUtils
import java.security.MessageDigest


class GlideRoundTransform(context:Context, private var radius: Array<Float>): BitmapTransformation() {

    private var bitmapPool:BitmapPool = Glide.get(context).bitmapPool

    override fun updateDiskCacheKey(messageDigest: MessageDigest) {

    }

    override fun transform(
        pool: BitmapPool,
        toTransform: Bitmap,
        outWidth: Int,
        outHeight: Int
    ): Bitmap? {
        val bitmap = TransformationUtils.centerCrop(pool, toTransform, outWidth, outHeight)
        return roundCrop(bitmap)
    }

    private fun roundCrop(source: Bitmap?): Bitmap? {
        val outBitmap: Bitmap =
            this.bitmapPool.get(source!!.width, source.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(outBitmap)
        val paint = Paint()
        val shader =
            BitmapShader(source, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
        paint.shader = shader
        paint.isAntiAlias = true

        val rectF = RectF(0f, 0f, canvas.width.toFloat(), canvas.height.toFloat())
        // 左上
        // 左上
        var r: Float = radius[0]
        canvas.save()
        canvas.clipRect(0, 0, canvas.width / 2, canvas.height / 2)
        canvas.drawRoundRect(rectF, r, r, paint)
        canvas.restore()
        // 右上
        // 右上
        r = radius[1]
        canvas.save()
        canvas.clipRect(canvas.width / 2, 0, canvas.width, canvas.height / 2)
        canvas.drawRoundRect(rectF, r, r, paint)
        canvas.restore()
        // 右下
        // 右下
        r = radius[2]
        canvas.save()
        canvas.clipRect(
            canvas.width / 2,
            canvas.height / 2,
            canvas.width,
            canvas.height
        )
        canvas.drawRoundRect(rectF, r, r, paint)
        canvas.restore()
        // 左下
        // 左下
        r = radius[3]
        canvas.save()
        canvas.clipRect(0, canvas.height / 2, canvas.width / 2, canvas.height)
        canvas.drawRoundRect(rectF, r, r, paint)
        canvas.restore()
        return outBitmap
    }
}