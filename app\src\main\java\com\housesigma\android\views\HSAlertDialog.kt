package com.housesigma.android.views

import android.app.Dialog
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Html
import android.text.TextUtils
import android.text.method.ScrollingMovementMethod
import android.view.View
import android.view.Window
import android.view.WindowManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import com.google.gson.Gson
import com.housesigma.android.databinding.DialogHsAlertBinding
import com.housesigma.android.databinding.DialogTosBinding
import com.housesigma.android.model.InitApp
import com.housesigma.android.ui.tos.TosDialog
import com.housesigma.android.utils.MMKVUtils


class HSAlertDialog(
    context: Context,
    title: String,
    content: String,
    leftBtn: String,
    rightBtn: String,
    cb: HSAlertCallback,
    autoDismiss: Boolean? = true
) : Dialog(context) {

    interface HSAlertCallback {
        fun onSuccess()
    }

    interface HSBtnPressCallback {
        fun pressLeftBtn()
        fun pressRightBtn()
    }

    private var mCallback: HSAlertCallback = cb
    private var mHSBtnPressCallback: HSBtnPressCallback?=null
    private var mTitle = title
    private var mContent = content
    private var mLeftBtn = leftBtn
    private var mRightBtn = rightBtn
    private var mAutoDismiss = autoDismiss ?: true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.window?.requestFeature(Window.FEATURE_NO_TITLE)
        val binding = DialogHsAlertBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initViews(binding)
        this.window?.setBackgroundDrawable(ColorDrawable(0x00000000))
        this.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

     fun setHSLeftBtnPressCallback(hsCallback: HSBtnPressCallback){
        mHSBtnPressCallback = hsCallback
    }

    private fun initViews(binding: DialogHsAlertBinding) {
        try {
            binding.tvTitle.text = "$mTitle"
            binding.tvContent.text = "$mContent"

            if (TextUtils.isEmpty(mTitle)){
                binding.tvTitle.visibility = View.GONE
            }

            if (TextUtils.isEmpty(mContent)){
                binding.tvContent.visibility = View.GONE
            }
        } catch (exception: Exception) {
            exception.printStackTrace()
        }

        if (!TextUtils.isEmpty(mLeftBtn)) {
            binding.tvNegative.text = mLeftBtn
            binding.tvNegative.visibility = View.VISIBLE
        } else {
            binding.tvNegative.visibility = View.GONE
        }

        if (!TextUtils.isEmpty(mRightBtn)) {
            binding.tvPositive.text = mRightBtn
            binding.tvPositive.visibility = View.VISIBLE
        } else {
            binding.tvPositive.visibility = View.GONE
        }

        binding.tvNegative.setOnClickListener {
            mHSBtnPressCallback?.pressLeftBtn()
            if (mAutoDismiss) {
                dismiss()
            }
        }
        binding.tvPositive.setOnClickListener {
            mHSBtnPressCallback?.pressRightBtn()
            mCallback.onSuccess()
            if (mAutoDismiss) {
                dismiss()
            }
        }

    }


}