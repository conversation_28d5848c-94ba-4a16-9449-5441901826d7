package com.housesigma.android.views

import android.content.Context
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.text.style.URLSpan
import android.widget.TextView
import com.housesigma.android.utils.HSURLSpan
import com.housesigma.android.utils.HSUtil

class HSHtmlTextView : SpannableStringBuilder() {
    companion object {
        fun setHtml(
            context: Context,
            str: String,
            textView: TextView
        ): TextView {
            val spanned = HSUtil.explainSourceToHtmlWithImage(context, str, textView)
            val spannableStringBuilder = SpannableStringBuilder(spanned)
            val urls = spannableStringBuilder.getSpans(0, spanned.length, URLSpan::class.java)
            for (url in urls) {
                val hsUrlSpan = HSURLSpan(context, url.url)
                val start = spannableStringBuilder.getSpanStart(url)
                val end = spannableStringBuilder.getSpanEnd(url)
                val flags = spannableStringBuilder.getSpanFlags(url)
                spannableStringBuilder.setSpan(hsUrlSpan, start, end, flags)
                // ClickableSpan的onClick方法回调，在APP内部打开
                spannableStringBuilder.removeSpan(url)
            }
            textView.text = spannableStringBuilder
            textView.movementMethod = LinkMovementMethod.getInstance()
            return textView
        }
    }


}