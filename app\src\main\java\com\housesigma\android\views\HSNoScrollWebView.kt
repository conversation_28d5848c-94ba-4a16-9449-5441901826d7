package com.housesigma.android.views

import android.content.Context
import android.util.AttributeSet


class HSNoScrollWebView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : HSWebView(context, attrs) {

    override fun overScrollBy(
        deltaX: Int,
        deltaY: Int,
        scrollX: Int,
        scrollY: Int,
        scrollRangeX: Int,
        scrollRangeY: Int,
        maxOverScrollX: Int,
        maxOverScrollY: Int,
        isTouchEvent: Boolean
    ): Bo<PERSON>an {
        return false
    }

    override fun scrollTo(x: Int, y: Int) {
        super.scrollTo(0, 0)
    }
}