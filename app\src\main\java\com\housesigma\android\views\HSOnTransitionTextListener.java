package com.housesigma.android.views;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Typeface;
import android.util.TypedValue;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.res.ResourcesCompat;

import com.housesigma.android.HSApp;
import com.housesigma.android.R;
import com.housesigma.android.utils.log.Logger;
import com.housesigma.android.views.viewpagerindicator.view.indicator.Indicator;
import com.housesigma.android.views.viewpagerindicator.view.utils.ColorGradient;

public class HSOnTransitionTextListener implements Indicator.OnTransitionListener {

    private float selectSize = -1;
    private float unSelectSize = -1;
    private ColorGradient gradient;
    private float dFontFize = -1;

    private boolean isPxSize = false;

    public HSOnTransitionTextListener() {
        super();
    }

    public HSOnTransitionTextListener(float selectSize, float unSelectSize, int selectColor, int unSelectColor) {
        super();
        setColor(selectColor, unSelectColor);
        setSize(selectSize, unSelectSize);
    }

    public final HSOnTransitionTextListener setSize(float selectSize, float unSelectSize) {
        isPxSize = false;
        this.selectSize = selectSize;
        this.unSelectSize = unSelectSize;
        this.dFontFize = selectSize - unSelectSize;
        return this;
    }

    public final HSOnTransitionTextListener setValueFromRes(Context context, int selectColorId, int unSelectColorId, int selectSizeId,
                                                            int unSelectSizeId) {
        setColorId(context, selectColorId, unSelectColorId);
        setSizeId(context, selectSizeId, unSelectSizeId);
        return this;
    }

    public final HSOnTransitionTextListener setColorId(Context context, int selectColorId, int unSelectColorId) {
        Resources res = context.getResources();
        setColor(res.getColor(selectColorId), res.getColor(unSelectColorId));
        return this;
    }

    public final HSOnTransitionTextListener setSizeId(Context context, int selectSizeId, int unSelectSizeId) {
        Resources res = context.getResources();
        setSize(res.getDimensionPixelSize(selectSizeId), res.getDimensionPixelSize(unSelectSizeId));
        isPxSize = true;
        return this;
    }

    public final HSOnTransitionTextListener setColor(int selectColor, int unSelectColor) {
        gradient = new ColorGradient(unSelectColor, selectColor, 100);
        return this;
    }

    /**
     * 如果tabItemView 不是目标的TextView，那么你可以重写该方法返回实际要变化的TextView
     *
     * @param tabItemView Indicator的每一项的view
     * @param position    view在Indicator的位置索引
     * @return
     */
    public TextView getTextView(View tabItemView, int position) {
        return (TextView) tabItemView;
    }

    @Override
    public void onTransition(View view, int position, float selectPercent) {
        TextView selectTextView = getTextView(view, position);
        if (gradient != null) {
            selectTextView.setTextColor(gradient.getColor((int) (selectPercent * 100)));
        }
        if (unSelectSize > 0 && selectSize > 0) {
            if (isPxSize) {
                selectTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, unSelectSize + dFontFize * selectPercent);
            } else {
                selectTextView.setTextSize(unSelectSize + dFontFize * selectPercent);
            }

            // bugfix: 切换时，textview会跳动
            if (HSApp.Companion.getAppContext() != null) {
                if (selectPercent==1) {
                    selectTextView.setTypeface(ResourcesCompat.getFont(HSApp.Companion.getAppContext(), R.font.poppins_medium), Typeface.BOLD);
                } else if (selectPercent==0) {
                    selectTextView.setTypeface(ResourcesCompat.getFont(HSApp.Companion.getAppContext(), R.font.poppins_medium), Typeface.NORMAL);
                }
            }

        }
    }
}
