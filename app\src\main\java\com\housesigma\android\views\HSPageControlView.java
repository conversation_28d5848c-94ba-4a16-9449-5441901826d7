package com.housesigma.android.views;


import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.housesigma.android.R;
import com.housesigma.android.utils.GALog;
import com.housesigma.android.utils.ScreenUtils;
import com.housesigma.android.utils.log.Logger;

public class HSPageControlView extends LinearLayout implements View.OnClickListener {
    private Context context;
    private int maxPage = 1;//最大页
    private int curPage = 1;//当前页
    private int buttonCount = 5;//按钮数
    private int lastButton = 0;//最后一个按钮对应的页码
    private int firstButton = 0;//第一个按钮对应的页码
    private int totalCount = 0; //总页数
    public TextView upPage;//上一页
    public TextView downPage;//下一页
    private String UP = "<";
    private String DOWN = ">";


    public HSPageControlView(Context context) {
        this(context, null);
    }

    public HSPageControlView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public HSPageControlView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.context = context;
        this.setBackgroundResource(R.color.color_white);
        this.setGravity(Gravity.RIGHT);

    }

    private OnPageChangeListener mPageChangeLinstener = new OnPageChangeListener() {
        @Override
        public void pageChanged(HSPageControlView HSPageControlView, int numPerPage) {
            if(mPageChangeLinstener != null){
                mPageChangeLinstener.pageChanged(HSPageControlView,numPerPage);
            }
        }
    };

    public interface OnPageChangeListener {
        void pageChanged(HSPageControlView HSPageControlView, int numPerPage);
    }

    /**
     * @param tCount 总页数
     */
    public void setTotalPage(int tCount) {
        totalCount =  tCount;
        setSelectView(curPage);
        initPageComposite();
    }

    /**
     * 设置当前页
     * @param currentPage
     */
    public void setCurrentPage(int currentPage){
        curPage = currentPage;
    }

    /**
     * 创建view
     * @return
     */
    private TextView createView() {
        TextView page = new TextView(context);
        page.setTextColor(getResources().getColor(R.color.app_main_color));
        page.setBackgroundResource(R.drawable.shape_6radius_stroke1width_main_color);
        //如果page是一位数，如果page是两位数，如果page是三位数
        page.setPadding((int) ScreenUtils.INSTANCE.dpToPx(2), 0, (int) ScreenUtils.INSTANCE.dpToPx(2), 0);
//        page.setPadding((int) ScreenUtils.INSTANCE.dpToPx(6), 0, (int) ScreenUtils.INSTANCE.dpToPx(6), 0);
        page.setGravity(Gravity.CENTER);
        LayoutParams layoutParam = new LayoutParams(LayoutParams.WRAP_CONTENT, (int) ScreenUtils.INSTANCE.dpToPx(30f));
        page.setMinWidth((int) ScreenUtils.INSTANCE.dpToPx(30));
        layoutParam.setMargins(0, 0, (int) ScreenUtils.INSTANCE.dpToPx(5), 0);
        page.setLayoutParams(layoutParam);
        return page;
    }

    /**
     * 创建中间页码view
     * @param page
     */
    private void createView(int page) {
        TextView countPage = createView();
        countPage.setText(page + "");
        countPage.setTag(page);
        countPage.setOnClickListener(this);
        this.addView(countPage);
    }
    /**
     * 创建上一页view
     */
    private void createPrevious() {
        upPage = createView();
        upPage.setText(UP);
        upPage.setOnClickListener(this);
        this.addView(upPage, 0);
    }
    /**
     * 创建下一页view
     */
    private void createNext() {
        downPage = createView();
        downPage.setText(DOWN);
        downPage.setOnClickListener(this);
        this.addView(downPage);
    }
    /**
     * n个页 创建第一个view
     */
    private void first() {
        TextView first = createView();
        first.setText("1");
        first.setOnClickListener(this);
        this.addView(first);
    }

    /**
     * n个页 创建末页view
     */
    private void last() {
        TextView first = createView();
        first.setText(totalCount+"");
        first.setOnClickListener(this);
        this.addView(first);
    }
    /**
     * n个页 创建...
     */
    private void middleFirstView(Integer next) {
        TextView middleTextView = new TextView(context);
        middleTextView.setText("...");
        middleTextView.setTag("middleFirst"+" next:"+next);
        middleTextView.setOnClickListener(this);

        middleTextView.setTextColor(getResources().getColor(R.color.app_main_color));
        middleTextView.setBackgroundResource(R.drawable.shape_6radius_stroke1width_main_color);
        //如果page是一位数，如果page是两位数，如果page是三位数
        middleTextView.setPadding((int) ScreenUtils.INSTANCE.dpToPx(2), 0, (int) ScreenUtils.INSTANCE.dpToPx(2), 0);
        middleTextView.setGravity(Gravity.CENTER);
        LayoutParams layoutParam = new LayoutParams(LayoutParams.WRAP_CONTENT, (int) ScreenUtils.INSTANCE.dpToPx(30f));
        middleTextView.setMinWidth((int) ScreenUtils.INSTANCE.dpToPx(30));
        layoutParam.setMargins(0, 0, (int) ScreenUtils.INSTANCE.dpToPx(5), 0);
        middleTextView.setLayoutParams(layoutParam);
        this.addView(middleTextView);
    }

    /**
     * n个页 创建...
     */
    private void middleLastView(Integer next) {
        TextView middleTextView = new TextView(context);
        middleTextView.setText("...");
        middleTextView.setTag("middleLast"+" next:"+next);
        middleTextView.setOnClickListener(this);

        middleTextView.setTextColor(getResources().getColor(R.color.app_main_color));
        middleTextView.setBackgroundResource(R.drawable.shape_6radius_stroke1width_main_color);
        //如果page是一位数，如果page是两位数，如果page是三位数
        middleTextView.setPadding((int) ScreenUtils.INSTANCE.dpToPx(2), 0, (int) ScreenUtils.INSTANCE.dpToPx(2), 0);
        middleTextView.setGravity(Gravity.CENTER);
        LayoutParams layoutParam = new LayoutParams(LayoutParams.WRAP_CONTENT, (int) ScreenUtils.INSTANCE.dpToPx(30f));
        middleTextView.setMinWidth((int) ScreenUtils.INSTANCE.dpToPx(30));
        layoutParam.setMargins(0, 0, (int) ScreenUtils.INSTANCE.dpToPx(5), 0);
        middleTextView.setLayoutParams(layoutParam);
        this.addView(middleTextView);
    }



    public void initPageComposite() {
        int temp = maxPage;
        maxPage = totalCount % 1 == 0 ? totalCount / 1 : totalCount / 1 + 1;

        if (temp != maxPage || curPage >= 1) {
            createAllView();
        }
        if (maxPage == 0) {
            removeAllViews();
            return;
        }
        setSelectView(curPage);
        setActionStatus(curPage);
    }

    /**
     * 当前页码为1的时候上一页不可点击
     * 当前页码为末页的时候下一页不可点击
     * @param curPage
     */
    private void setActionStatus(int curPage) {
        if (curPage > 1) {
            upPage.setEnabled(true);
        }else {
            upPage.setEnabled(false);
        }
        if(totalCount > curPage){
            downPage.setEnabled(true);
        }else {
            downPage.setEnabled(false);
        }
    }
    private void setBtnGroup() {
        int n = maxPage / buttonCount;
        if (n == 0) {
            //只有一组
            firstButton = 1;
            lastButton = maxPage;
        } else {
            int i = curPage / buttonCount;
            //有n组
            firstButton = i * buttonCount + 1;
            if (firstButton > curPage) {
                firstButton = (i - 1) * buttonCount + 1;
            }
            lastButton = (firstButton - 1) + buttonCount;
            if (lastButton > maxPage && lastButton > 1) {
                lastButton = maxPage;
            }

        }
    }

    private void createAllView() {
        setBtnGroup();
        this.removeAllViews();
        this.setPadding((int) ScreenUtils.INSTANCE.dpToPx(6), (int) ScreenUtils.INSTANCE.dpToPx(6), (int) ScreenUtils.INSTANCE.dpToPx(6), (int) ScreenUtils.INSTANCE.dpToPx(6));
        createPrevious();
        if((firstButton >= buttonCount ||curPage == buttonCount)&& totalCount > buttonCount){
            firstButton = curPage - 2;
            lastButton = curPage + 2;
        }
        if(firstButton > 1 && totalCount > buttonCount){
            first();
            int next =  curPage-5;
            middleFirstView(next);
        }
        while (firstButton <= lastButton && firstButton <= totalCount) {
            createView(firstButton);
            firstButton++;
        }
        if(lastButton < totalCount){
            int next =  curPage+5;
            middleLastView(next);
            last();
        }
        createNext();
    }

    @Override
    public void onClick(View view) {
        if (mPageChangeLinstener == null) return;
        if (view instanceof TextView) {
            TextView tv = (TextView) view;
            String txt = tv.getText().toString();
            if (txt.equalsIgnoreCase(UP)) {
                GALog.Companion.log("list_mode_page_click","pre",null);
                curPage -= 1;
                curPage = curPage >= 1 ? curPage : 1;
                setSelectView(curPage);
                mPageChangeLinstener.pageChanged(this, curPage);
                initPageComposite();
                return;
            }
            if (txt.equalsIgnoreCase(DOWN)) {
                GALog.Companion.log("list_mode_page_click","next",null);
                curPage += 1;
                curPage = curPage <= maxPage ? curPage : maxPage;
                setSelectView(curPage);
                mPageChangeLinstener.pageChanged(this,curPage);
                initPageComposite();
                return;
            }
            if(txt.equalsIgnoreCase(String.valueOf(1))){
                GALog.Companion.log("list_mode_page_click","page_num",null);
                curPage = 1;
                setSelectView(curPage);
                mPageChangeLinstener.pageChanged(this,curPage);
                initPageComposite();
                return;
            }
            if(txt.equalsIgnoreCase(String.valueOf(totalCount))){
                GALog.Companion.log("list_mode_page_click","page_num",null);
                curPage = totalCount;
                setSelectView(curPage);
                mPageChangeLinstener.pageChanged(this,curPage);
                initPageComposite();
                return;
            }
        }
        if (view.getTag() != null) {
            Object tag = view.getTag();
            if (!tag.toString().contains("next")){
                curPage = Integer.parseInt(tag.toString());
                mPageChangeLinstener.pageChanged(this,curPage);
                setSelectView(curPage);
                initPageComposite();
            }else {
                GALog.Companion.log("list_mode_page_click","skip",null);
                Logger.e(""+tag.toString());
                if (tag.toString().contains("middleFirst")){
                    curPage = curPage-5;
                }else {
                    curPage = curPage+5;
                }
                mPageChangeLinstener.pageChanged(this,curPage);
                setSelectView(curPage);
                initPageComposite();

            }

        }
    }

    private void setSelectView(int n) {
        for (int i = 0; i < this.getChildCount(); i++) {
            TextView v = (TextView) this.getChildAt(i);
            Object tagObject = v.getTag();
            if (tagObject != null) {
                if (!tagObject.toString().contains("next")){
                    int tag = Integer.parseInt(v.getTag().toString());
                    if (tag == n) {
                        v.setTextColor(getResources().getColor(R.color.color_white));
                        v.setSelected(true);
                        v.setBackgroundResource(R.drawable.shape_6radius_main_color_fill);
                    } else {
                        v.setTextColor(getResources().getColor(R.color.app_main_color));
                        v.setSelected(false);
                        v.setBackgroundResource(R.drawable.shape_6radius_stroke1width_main_color);
                    }
                }else {
                    Logger.e(""+tagObject.toString());
                }
            }
        }
    }

    /**
     * 设置分页监听事件
     */
    public void setPageChangeListener(OnPageChangeListener pageChangeListener) {
        this.mPageChangeLinstener = pageChangeListener;
    }


}

