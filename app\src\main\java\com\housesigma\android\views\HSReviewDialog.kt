package com.housesigma.android.views

import android.app.Dialog
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Window
import android.view.WindowManager
import com.housesigma.android.databinding.DialogReviewBinding
import com.housesigma.android.utils.GALog


class HSReviewDialog(
    context: Context,
    cb: HSAlertCallback,
) : Dialog(context) {

    interface HSAlertCallback {
        fun onLike()
        fun onNotLike()
        fun onLater()
    }

    private var mCallback: HSAlertCallback = cb

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.window?.requestFeature(Window.FEATURE_NO_TITLE)
        val binding = DialogReviewBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initViews(binding)
        this.window?.setBackgroundDrawable(ColorDrawable(0x00000000))
        this.window?.setLayout(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT
        )
        this.setCancelable(false)
        GALog.log("app_review_prompt","displayed")
    }

    private fun initViews(binding: DialogReviewBinding) {
        binding.ivBad.setOnClickListener {
            GALog.log("app_review_prompt","enjoy_no")
            mCallback.onNotLike()
            dismiss()
        }

        binding.ivGood.setOnClickListener {
            GALog.log("app_review_prompt","enjoy_yes")
            mCallback.onLike()
            dismiss()
        }

        binding.tvLater.setOnClickListener {
            GALog.log("app_review_prompt","later")
            mCallback.onLater()
            dismiss()
        }
    }


}