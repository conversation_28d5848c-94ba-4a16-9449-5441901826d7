package com.housesigma.android.views

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.core.widget.NestedScrollView

class HSScrollView(context: Context?, attrs: AttributeSet?) : NestedScrollView(context!!, attrs) {
    private var mLastXIntercept = 0f
    private var mLastYIntercept = 0f
    override fun onInterceptTouchEvent(ev: MotionEvent): Bo<PERSON>an {
        val x = ev.x
        val y = ev.y
        val action = ev.action and MotionEvent.ACTION_MASK
        when (action) {
            MotionEvent.ACTION_DOWN -> {
                mLastXIntercept = x
                mLastYIntercept = y
            }
            MotionEvent.ACTION_MOVE -> {
                //横坐标位移增量
                val deltaX = Math.abs(x - mLastXIntercept)
                //纵坐标位移增量
                val deltaY = Math.abs(y - mLastYIntercept)

//                在三星部分手机上，会触发ACTION_MOVE
//                且横坐标或纵坐标的移动量不等于0的bug出现
//                导致自主接管事件传递后，无法触发click事件。这里设定了offset作为偏移量的阈值。
//                现在设置偏移量的阈值小于5就不接管处理
                val offset = 5
                if (deltaY < offset && deltaX < offset) {
                    return super.onInterceptTouchEvent(ev)
                } else {
                    return deltaX < deltaY
                }
            }
        }
        return super.onInterceptTouchEvent(ev)
    }
}