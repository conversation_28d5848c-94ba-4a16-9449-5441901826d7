package com.housesigma.android.views

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.webkit.WebView
import com.housesigma.android.utils.HSUtil
import me.jessyan.autosize.AutoSize


open class HSWebView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : WebView(context, attrs) {
    override fun setOverScrollMode(mode: Int) {
        super.setOverScrollMode(mode)
        if (!HSUtil.isPad(context)){
            if (context is Activity){
                AutoSize.autoConvertDensityOfGlobal(context as Activity)
            }
        }
    }

    interface RefreshStateListener {
        fun refreshState(canRefresh: Boolean)
    }

    private var refreshStateListener: RefreshStateListener? = null

    fun setRefreshStateListener(listener: RefreshStateListener) {
        this.refreshStateListener = listener
    }


    override fun onOverScrolled(scrollX: Int, scrollY: Int, clampedX: Boolean, clampedY: Boolean) {
        super.onOverScrolled(scrollX, scrollY, clampedX, clampedY)
        refreshStateListener?.refreshState(clampedY)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (refreshStateListener != null && event.action == MotionEvent.ACTION_DOWN) {
            refreshStateListener?.refreshState(false)
        }
        return super.onTouchEvent(event)
    }
}