package com.housesigma.android.views;

import android.content.Context;
import android.util.Log;

import com.github.mikephil.charting.components.MarkerView;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.utils.MPPointF;
import com.housesigma.android.R;

public class HomeCombinedChartMarkerView extends MarkerView {
    public HomeCombinedChartMarkerView(Context context) {
        super(context, R.layout.marker_view_home_combinedchart);
        initView();
    }

    private void initView() {
    }

    @Override
    public void refreshContent(Entry e, Highlight highlight) {
        super.refreshContent(e, highlight);
    }

    private MPPointF mOffset;

    @Override
    public MPPointF getOffset() {
        if (mOffset == null) {
            // center the marker horizontally and vertically
            mOffset = new MPPointF(-(getWidth() / 2), -(getHeight() / 2));
        }
        return mOffset;
    }
}
