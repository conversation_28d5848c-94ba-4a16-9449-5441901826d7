package com.housesigma.android.views

import android.content.Context
import android.widget.LinearLayout
import android.widget.TextView
import com.housesigma.android.R
import com.lxj.xpopup.core.BottomPopupView


class ListingMenuDialog(
    context: Context,
    isWatchList: <PERSON><PERSON><PERSON>,
    watchlistName:String,
    cb: ListingsMenuCallback
) : BottomPopupView(context) {

    private var mCallback: ListingsMenuCallback = cb
    private var watchlistName = watchlistName
    private var isWatchList = isWatchList

    interface ListingsMenuCallback {
        fun onDelete()
        fun onSaveToWatchlist()
    }

    override fun getImplLayoutId(): Int {
        return R.layout.dialog_listings_menu
    }

    override fun onCreate() {
        super.onCreate()
        initView()
    }


    private fun initView() {
        val tvRemoveWatchlistName = findViewById<TextView>(R.id.tv_remove_watchlist_name)
        val tvSaveToWatchlist = findViewById<TextView>(R.id.tv_save_to_watchlist)
        val llDelete = findViewById<LinearLayout>(R.id.ll_delete)

        if (isWatchList) {
            tvSaveToWatchlist.text = "Save to Watchlist"
            tvRemoveWatchlistName.text = "Remove from " + watchlistName
            llDelete.visibility = VISIBLE
        } else {
            tvSaveToWatchlist.text = "Save to my watchlist"
            tvRemoveWatchlistName.text = "Remove from my watchlist"
            llDelete.visibility = GONE
        }


        llDelete.setOnClickListener {
            mCallback.onDelete()
            dismiss()
        }

        val llShave = findViewById<LinearLayout>(R.id.ll_save_to_watchlist)
        llShave.setOnClickListener {
            mCallback.onSaveToWatchlist()
            dismiss()
        }
    }


}