package com.housesigma.android.views

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import com.housesigma.android.R


class LoadingDialog(context: Context) : Dialog(context, R.style.CustomAlertDialog) {

    var mDimAmount : Float = 0.0f

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.loading)

        setCancelable(false)
        val layoutParams = window!!.attributes
        layoutParams.dimAmount = mDimAmount
        window!!.attributes = layoutParams


        window?.setBackgroundDrawableResource(R.drawable.shape_loading_bg)
    }

    fun setDimAmount(dimAmount:Float): LoadingDialog {
        mDimAmount = dimAmount
        return this
    }

}