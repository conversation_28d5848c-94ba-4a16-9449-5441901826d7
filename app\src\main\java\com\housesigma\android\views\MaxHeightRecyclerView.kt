package com.housesigma.android.views


import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.RecyclerView
import kotlin.math.min

/**
 * 自定义View：MaxHeightRecyclerView
 * 用在点击marker后展示RecyclerView，Android api没有RecyclerView的最大高度限制，要自己实现
 */
class MaxHeightRecyclerView : RecyclerView {

    private var maxHeight: Int = -1

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        attrs?.let { attrSet ->
            val attrRes = intArrayOf(android.R.attr.maxHeight)
            val types = context.obtainStyledAttributes(attrSet, attrRes)
            maxHeight = types.getDimensionPixelSize(0, -1)
            types.recycle()
        }
    }

    override fun onMeasure(widthSpec: Int, heightSpec: Int) {
        val hSize = MeasureSpec.getSize(heightSpec)
        val heightMeasureSpec = if (maxHeight < 0) {
            heightSpec
        } else {
            when (MeasureSpec.getMode(heightSpec)) {
                MeasureSpec.AT_MOST -> MeasureSpec.makeMeasureSpec(min(hSize, maxHeight), MeasureSpec.AT_MOST)
                MeasureSpec.UNSPECIFIED -> MeasureSpec.makeMeasureSpec(maxHeight, MeasureSpec.AT_MOST)
                MeasureSpec.EXACTLY -> MeasureSpec.makeMeasureSpec(min(hSize, maxHeight), MeasureSpec.EXACTLY)
                else -> MeasureSpec.makeMeasureSpec(maxHeight, MeasureSpec.AT_MOST)
            }
        }
        super.onMeasure(widthSpec, heightMeasureSpec)
    }
}