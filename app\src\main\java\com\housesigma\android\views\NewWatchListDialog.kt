package com.housesigma.android.views

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import androidx.lifecycle.ViewModelProvider
import com.housesigma.android.R
import com.housesigma.android.base.BaseDialogFragment
import com.housesigma.android.databinding.DialogNewWatchListBinding
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.ui.watched.WatchedViewModel
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.ToastUtils
import org.greenrobot.eventbus.EventBus


class NewWatchListDialog : BaseDialogFragment() {

    private lateinit var watchedViewModel: WatchedViewModel
    private lateinit var binding: DialogNewWatchListBinding
    private var mPrivacy: Int = 1

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreen)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        watchedViewModel = ViewModelProvider(this).get(WatchedViewModel::class.java)
        binding = DialogNewWatchListBinding.inflate(inflater, container, false)
        initView()
        initData()
        dialog?.setCanceledOnTouchOutside(true)
        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.etWatchlistName.postDelayed({
            binding.etWatchlistName.requestFocus()
            try {
                val imm = requireActivity().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                imm?.showSoftInput(binding.etWatchlistName, InputMethodManager.SHOW_IMPLICIT)
            } catch (e:IllegalStateException) {
                e.printStackTrace()
            }
        },100)
    }
    private fun initData() {
        watchedViewModel.addWatchListMsg.observe(this) {
            GALog.log("watchlists_actions","save_watchlist")
            EventBus.getDefault().postSticky(MessageEvent(MessageType.WATCHED_MULTIPLE_WATCHLIST_NEWED))
            ToastUtils.showLong(it.message)
            dismiss()
        }

        watchedViewModel.loadingLiveData.observe(this) {
            dismissLoadingDialog()
        }
    }

    private fun initView() {
        binding.tvWatchlistSave.setOnClickListener {
            val watchlistName = binding.etWatchlistName.text.toString().trim()
            if (TextUtils.isEmpty(watchlistName)) return@setOnClickListener

            showLoadingDialog()
            watchedViewModel.addWatchlist(watchlistName,mPrivacy)
        }

        binding.llNewWatchListPrivate.setOnClickListener {
            mPrivacy = 0
            binding.ivPrivate.setBackgroundResource(R.drawable.ic_hs_radiobtn_select)
            binding.ivPublic.setBackgroundResource(R.drawable.ic_hs_radiobtn_normal)
        }

        binding.llNewWatchListPublic.setOnClickListener {
            mPrivacy = 1
            binding.ivPrivate.setBackgroundResource(R.drawable.ic_hs_radiobtn_normal)
            binding.ivPublic.setBackgroundResource(R.drawable.ic_hs_radiobtn_select)
        }

        binding.ivDel.setOnClickListener {
            binding.etWatchlistName.setText("")
        }

        binding.tvWatchlistSave.setBackgroundResource(R.drawable.shape_10radius_gray_fill)
        binding.tvWatchlistSave.setTextColor(resources.getColor(R.color.color_gray))
        binding.ivDel.visibility = View.INVISIBLE
        binding.etWatchlistName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                val feedbackStr = p0.toString()
                if (!TextUtils.isEmpty(feedbackStr)) {
                    binding.ivDel.visibility = View.VISIBLE
                    binding.tvWatchlistSave.setBackgroundResource(R.drawable.shape_10radius_main_color_fill)
                    binding.tvWatchlistSave.setTextColor(resources.getColor(R.color.color_white))
                } else {
                    binding.ivDel.visibility = View.INVISIBLE
                    binding.tvWatchlistSave.setBackgroundResource(R.drawable.shape_10radius_gray_fill)
                    binding.tvWatchlistSave.setTextColor(resources.getColor(R.color.color_gray))
                }
            }

            override fun afterTextChanged(p0: Editable?) {

            }

        })


    }


}