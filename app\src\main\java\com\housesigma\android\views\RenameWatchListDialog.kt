package com.housesigma.android.views

import android.content.Context
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import com.housesigma.android.R
import com.housesigma.android.databinding.DialogNewWatchListBinding
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.ui.watched.WatchedViewModel
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.ToastUtils
import com.lxj.xpopup.core.BottomPopupView
import org.greenrobot.eventbus.EventBus


class RenameWatchListDialog(
    viewModelStoreOwner: ViewModelStoreOwner,
    context: Context,
    cb: RenameWatchListCallback,
    watchlistId: String? = null,
    name: String? = null,
    privacy :Int = 0
) : BottomPopupView(context) {

    private var mContext: ViewModelStoreOwner = viewModelStoreOwner
    private lateinit var watchedViewModel: WatchedViewModel
    private var mCallback: RenameWatchListCallback = cb
    private var mWatchlistId: String? = watchlistId
    private var mName: String? = name
    private var mPrivacy: Int = privacy

    interface RenameWatchListCallback {
        fun onSuccess()
    }

    override fun getImplLayoutId(): Int {
        return R.layout.dialog_new_watch_list
    }

    override fun onCreate() {
        super.onCreate()
        watchedViewModel = ViewModelProvider(mContext).get(WatchedViewModel::class.java)
        initView()
        initData()
    }

    private fun initData() {
        watchedViewModel.updateWatchlistUpdateMsg.observe(this) {
            ToastUtils.showLong(it.message)
            mCallback.onSuccess()
            dismiss()
        }
    }

    private fun initView() {
        val binding = DialogNewWatchListBinding.bind(popupContentView)
        if (mPrivacy==0){
            binding.ivPrivate.setBackgroundResource(R.drawable.ic_hs_radiobtn_select)
            binding.ivPublic.setBackgroundResource(R.drawable.ic_hs_radiobtn_normal)
        } else {
            binding.ivPrivate.setBackgroundResource(R.drawable.ic_hs_radiobtn_normal)
            binding.ivPublic.setBackgroundResource(R.drawable.ic_hs_radiobtn_select)
        }


        binding.tvWatchlistSave.setOnClickListener {
            val watchlistName = binding.etWatchlistName.text.toString().trim()
            if (TextUtils.isEmpty(watchlistName)) return@setOnClickListener
            mWatchlistId?.let {
                watchedViewModel.updateWatchlistUpdate(id_user_watchlist = it, watchlistName, mPrivacy)
            }
        }
        binding.tvTitle.text = "Edit Watchlist"

        binding.etWatchlistName.setText(mName ?: "")
        binding.etWatchlistName.setSelection((mName ?: "").length)

        binding.ivDel.setOnClickListener {
            binding.etWatchlistName.setText("")
        }

        binding.llNewWatchListPrivate.setOnClickListener {
            mPrivacy = 0
            binding.ivPrivate.setBackgroundResource(R.drawable.ic_hs_radiobtn_select)
            binding.ivPublic.setBackgroundResource(R.drawable.ic_hs_radiobtn_normal)
        }

        binding.llNewWatchListPublic.setOnClickListener {
            mPrivacy = 1
            binding.ivPrivate.setBackgroundResource(R.drawable.ic_hs_radiobtn_normal)
            binding.ivPublic.setBackgroundResource(R.drawable.ic_hs_radiobtn_select)
        }


        binding.etWatchlistName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                val feedbackStr = p0.toString()
                if (!TextUtils.isEmpty(feedbackStr)) {
                    binding.ivDel.visibility = View.VISIBLE
                    binding.tvWatchlistSave.setBackgroundResource(R.drawable.shape_10radius_main_color_fill)
                    binding.tvWatchlistSave.setTextColor(resources.getColor(R.color.color_white))
                } else {
                    binding.ivDel.visibility = View.INVISIBLE
                    binding.tvWatchlistSave.setBackgroundResource(R.drawable.shape_10radius_gray_fill)
                    binding.tvWatchlistSave.setTextColor(resources.getColor(R.color.color_gray))
                }
            }

            override fun afterTextChanged(p0: Editable?) {

            }

        })
    }


}