package com.housesigma.android.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.RelativeLayout
import com.housesigma.android.R
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.HSUtil
import com.jaygoo.widget.OnRangeChangedListener
import com.jaygoo.widget.RangeSeekBar

/**
 *  房源售价的曲线
 */
class SaleSeekBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr) {

    var priceLeft: Int = 0
    var priceRight: Int = 6000000
    private var showPrice: String = ""


    interface OnChangeListener {
        fun onChange(showPrice: String)

        fun onStopTrackingTouch()
    }

    private var sbRange: RangeSeekBar

    init {
        LayoutInflater.from(context).inflate(R.layout.view_sale_seek_bar, this, true)
        sbRange = findViewById(R.id.sb_range)
        sbRange.setProgress(0f, 43f)
    }

    fun setDefaultValue() {
        sbRange.setProgress(0f, 43f)
    }


    fun setProgress(leftValue: Float, rightValue: Float) {
        sbRange.setProgress(leftValue, rightValue)
    }

    fun isDefaultValue(): Boolean {
        return priceLeft != 0 || priceRight != 6000000
    }


    fun setOnChangeListener(listener: OnChangeListener) {
        sbRange.setOnRangeChangedListener(object : OnRangeChangedListener {
            override fun onRangeChanged(
                view: RangeSeekBar?,
                leftValue: Float,
                rightValue: Float,
                isFromUser: Boolean
            ) {
                priceLeft = HSUtil.calSquareFootageCurve(leftValue.toInt())
                priceRight = HSUtil.calSquareFootageCurve(rightValue.toInt())
                if (rightValue.toInt() == 43) {

                    showPrice = "$".plus(
                        HSUtil.formatNumberThousandsSeparators(
                            HSUtil.calSquareFootageCurve(
                                leftValue.toInt()
                            )
                        )
                    ) + " - Max"


                } else {
                    showPrice = "$".plus(
                        HSUtil.formatNumberThousandsSeparators(
                            HSUtil.calSquareFootageCurve(
                                leftValue.toInt()
                            )
                        )
                    ) + " - " + "$".plus(
                        HSUtil.formatNumberThousandsSeparators(
                            HSUtil.calSquareFootageCurve(
                                rightValue.toInt()
                            )
                        )
                    )
                }

                listener.onChange(showPrice)
            }

            override fun onStartTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
            }

            override fun onStopTrackingTouch(view: RangeSeekBar?, isLeft: Boolean) {
                listener.onStopTrackingTouch()
                GALog.log("map_filters_click","price_range")
            }
        })

    }


}