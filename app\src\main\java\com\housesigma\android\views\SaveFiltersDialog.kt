package com.housesigma.android.views

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import com.housesigma.android.R
import com.housesigma.android.base.BaseDialogFragment
import com.housesigma.android.databinding.DialogNewWatchListBinding
import com.housesigma.android.databinding.DialogSaveFiltersBinding
import com.housesigma.android.model.MapFilters
import com.housesigma.android.model.MessageEvent
import com.housesigma.android.model.MessageType
import com.housesigma.android.model.SaveMapFilter
import com.housesigma.android.ui.map.MapViewModel
import com.housesigma.android.ui.watched.WatchedViewModel
import com.housesigma.android.utils.GALog
import com.housesigma.android.utils.ToastUtils
import com.lxj.xpopup.core.BottomPopupView
import org.greenrobot.eventbus.EventBus


class SaveFiltersDialog(
    cb: SaveFiltersCallback,
    selectSaveMapFilter: SaveMapFilter? = null,
) : BaseDialogFragment() {

//    private lateinit var mMapViewModel: MapViewModel
    private var mCallback: SaveFiltersCallback = cb
    private var mSelectSaveMapFilter: SaveMapFilter? = selectSaveMapFilter
    private lateinit var binding: DialogSaveFiltersBinding

    interface SaveFiltersCallback {
        fun onSuccess(filtersName: String)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.DialogFullScreen)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
//        mMapViewModel = ViewModelProvider(this).get(MapViewModel::class.java)
        binding = DialogSaveFiltersBinding.inflate(inflater, container, false)
        initView()
        initData()
        dialog?.setCanceledOnTouchOutside(true)
        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.etFilterName.postDelayed({
            binding.etFilterName.requestFocus()
            try {
                val imm = requireActivity().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                imm?.showSoftInput(binding.etFilterName, InputMethodManager.SHOW_IMPLICIT)
            } catch (e:IllegalStateException) {
                e.printStackTrace()
            }
        },100)
    }


    private fun initData() {
    }

    private fun initView() {
        binding.tvSave.setOnClickListener {
            GALog.log("saved_filters_save_click")
            val watchlistName = binding.etFilterName.text.toString().trim()
            if (TextUtils.isEmpty(watchlistName)) {
                ToastUtils.showLong("Filter name required")
                return@setOnClickListener
            }
            mCallback.onSuccess(watchlistName)
            dismiss()
        }

        binding.tvDelete.setOnClickListener {
            dismiss()
        }

        binding.etFilterName.setText(mSelectSaveMapFilter?.filterName ?: "")
        binding.etFilterName.setSelection((mSelectSaveMapFilter?.filterName ?: "").length)

        binding.ivDel.setOnClickListener {
            binding.etFilterName.setText("")
        }


        binding.etFilterName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                val feedbackStr = p0.toString()
                if (!TextUtils.isEmpty(feedbackStr)) {
                    binding.ivDel.visibility = View.VISIBLE
                } else {
                    binding.ivDel.visibility = View.INVISIBLE
                }
            }

            override fun afterTextChanged(p0: Editable?) {

            }

        })
    }


}