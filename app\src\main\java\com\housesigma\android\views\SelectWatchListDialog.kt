package com.housesigma.android.views

import android.app.Activity
import android.content.Context
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.housesigma.android.R
import com.housesigma.android.model.MultipleWatchItem
import com.housesigma.android.model.MultipleWatchList
import com.housesigma.android.ui.watched.SelectWatchListAdapter
import com.housesigma.android.utils.GALog
import com.lxj.xpopup.core.BottomPopupView


class SelectWatchListDialog(
    idListing: String,
    ml_num: String,
    selectWatchlists: MultipleWatchList? = null,
    context: Context,
    cb: NewWatchListCallback
) : BottomPopupView(context) {

    private var mCallback: NewWatchListCallback? = cb
    private lateinit var adapter: SelectWatchListAdapter
    private var mSelectWatchlists: ArrayList<MultipleWatchItem>? = selectWatchlists
    private var mIdListing: String = idListing
    private var mMlNum: String = ml_num

    interface NewWatchListCallback {
        fun onSuccess(id_listing: String, ml_num: String, ids_user_watchlist: List<String>)

        fun onCancel()
    }

    override fun getImplLayoutId(): Int {
        return R.layout.dialog_select_watch_list
    }

    override fun onCreate() {
        super.onCreate()
        initView()
        GALog.log("watch_listing_click","show_watchlist")
    }

    /**
     * 更新adapter数据，并把list第0个自动勾选，并自动保存
     * 在新建watchlist时，会调用此方法
     */
    fun notifyDataSetChangedAndAutoSelectTop1(selectWatchlists:MultipleWatchList?){
        selectWatchlists?.let {
            if (mCallback == null) return // 识别是否走的是新建watchlist的逻辑，否则不处理
            if (selectWatchlists.size==0) return
            selectWatchlists[0].is_watched = 1
            mSelectWatchlists?.add(0,selectWatchlists[0])

            val checkedList: MutableList<String> = ArrayList()
            checkedList.clear()
            mSelectWatchlists?.forEach {
                if (it.is_watched == 1) {
                    checkedList.add(it.id)
                }
            }

            mCallback?.onSuccess(mIdListing, mMlNum, checkedList)
            mCallback = null
        }
    }


    private fun initView() {
        val rv = findViewById<RecyclerView>(R.id.rv)
        adapter = SelectWatchListAdapter()
        rv.layoutManager =
            LinearLayoutManager(context, RecyclerView.VERTICAL, false)
        rv.adapter = adapter
        adapter.data.clear()
        mSelectWatchlists?.let { adapter.addData(it) }
        val tvWatchlistSave = findViewById<TextView>(R.id.tv_watchlist_save)
        tvWatchlistSave.setOnClickListener {
            val checkedList: MutableList<String> = ArrayList()
            adapter.data.forEach {
                if (it.is_watched == 1) {
                    checkedList.add(it.id)
                }
            }

            mCallback?.onSuccess(mIdListing, mMlNum, checkedList)
            mCallback = null//回调成功后要把回调对象置为null，避免重复调用
        }

        val tvWatchlistCancel = findViewById<TextView>(R.id.tv_watchlist_cancel)
        tvWatchlistCancel.setOnClickListener {
            mCallback?.onCancel()
            dismiss()
        }


        val rvNewWatchlist = findViewById<View>(R.id.rl_new_watchlist)
        rvNewWatchlist.setOnClickListener {
            GALog.log("watchlists_actions","new_watchlist")
            val newWatchListDialog = NewWatchListDialog()
            (context as AppCompatActivity).supportFragmentManager.let {
                newWatchListDialog.show(it,"")
            }
        }
    }


}