package com.housesigma.android.views

import android.content.Context
import android.content.Intent
import android.widget.LinearLayout
import android.widget.TextView
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.housesigma.android.R
import com.housesigma.android.model.WatchListMeta
import com.housesigma.android.utils.log.Logger
import com.lxj.xpopup.core.BottomPopupView
import java.lang.Exception


class ShareWatchListMenuDialog(
    context: Context,
    isShowRemove:Boolean?=true,
    isShowSave:Boolean?=true,
    isShowShare:Boolean?=true,
    cb: ShareWatchListMenuCallback
) : BottomPopupView(context) {

    private var mCallback: ShareWatchListMenuCallback = cb
    private var isShowRemove: Boolean = isShowRemove == true
    private var isShowSave: Boolean = isShowSave == true
    private var isShowShare: Boolean = isShowShare == true

    interface ShareWatchListMenuCallback {
        fun onShare()
        fun onSave()
        fun onRemove()
    }

    override fun getImplLayoutId(): Int {
        return R.layout.dialog_share_watchlist_menu
    }

    override fun onCreate() {
        super.onCreate()
        initView()
    }


    private fun initView() {
        val llRemove = findViewById<LinearLayout>(R.id.ll_remove)
        if (isShowRemove) {
            llRemove.visibility = VISIBLE
        } else {
            llRemove.visibility = GONE
        }
        llRemove.setOnClickListener {
            mCallback.onRemove()
            dismiss()
        }

        val llSave = findViewById<LinearLayout>(R.id.ll_save)
        if (isShowSave) {
            llSave.visibility = VISIBLE
        } else {
            llSave.visibility = GONE
        }
        llSave.setOnClickListener {
            mCallback.onSave()
            dismiss()
        }

        val llShare = findViewById<LinearLayout>(R.id.ll_share)
        if (isShowShare) {
            llShare.visibility = VISIBLE
        } else {
            llShare.visibility = GONE
        }
        llShare.setOnClickListener {
            mCallback.onShare()
            dismiss()
        }
    }


}