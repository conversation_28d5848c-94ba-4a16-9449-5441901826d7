package com.housesigma.android.views;

import android.util.Log;
import android.view.MotionEvent;
import android.view.View;

import androidx.fragment.app.DialogFragment;

import com.housesigma.android.utils.log.Logger;


public class Sheet {
    private float startY;
    private float moveY = 0;

    public void setTouch(View touchView, View decorView, DialogFragment dialogFragment) {
        touchView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent ev) {
                switch (ev.getAction() & MotionEvent.ACTION_MASK) {
                    case MotionEvent.ACTION_DOWN:
                        startY = ev.getY();
                        break;
                    case MotionEvent.ACTION_MOVE:
                        moveY = ev.getY() - startY;
                        if (moveY > 0) {
                            decorView.scrollBy(0, -(int) moveY);
                            startY = ev.getY();
                        }
                        if (decorView.getScrollY() > 0) {
                            decorView.scrollTo(0, 0);
                        }
                        break;
                    case MotionEvent.ACTION_UP:
                        if (decorView.getScrollY() < -decorView.getHeight()
                                / 7 && moveY > 0) {
                            dialogFragment.dismiss();
                            Logger.d( "dismiss。。。。。。。。");
                        }
                        decorView.scrollTo(0, 0);
                        break;
                }
                return true;

            }
        });

    }
}
