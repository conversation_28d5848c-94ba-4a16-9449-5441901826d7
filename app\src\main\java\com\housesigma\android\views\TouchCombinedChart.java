package com.housesigma.android.views;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.PointF;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.TouchDelegate;

import com.github.mikephil.charting.charts.CombinedChart;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.datasets.IDataSet;
import com.github.mikephil.charting.listener.ChartTouchListener;
import com.github.mikephil.charting.utils.MPPointD;
import com.github.mikephil.charting.utils.MPPointF;
import com.housesigma.android.utils.Callback1;

public class TouchCombinedChart extends CombinedChart {
    private PointF downPoint = new PointF();
    private Callback1 mCb;
    public TouchCombinedChart(Context context) {
        super(context);
    }

    public TouchCombinedChart(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public TouchCombinedChart(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    public ChartTouchListener getOnTouchListener() {
        return super.getOnTouchListener();
    }

    @Override
    public TouchDelegate getTouchDelegate() {
        return super.getTouchDelegate();
    }

    @Override
    public void setOnTouchListener(ChartTouchListener l) {
        super.setOnTouchListener(l);
    }


    @Override
    public Highlight getHighlightByTouchPoint(float x, float y) {

        if (mData == null) {
            Log.e(LOG_TAG, "Can't select by touch. No data set.");
            return null;
        } else {
            Highlight h = getHighlighter().getHighlight(x, y);
            if (h == null || !isHighlightFullBarEnabled()) return h;

            // For isHighlightFullBarEnabled, remove stackIndex
            Highlight highlightByTouchPoint =  new Highlight(h.getX(), h.getY(),
                    h.getXPx(), h.getYPx(),
                    h.getDataSetIndex(), -1, h.getAxis());


            float x1 = highlightByTouchPoint.getX();

            Highlight highlight = new Highlight(x1,0,-1);

            highlight.setDataIndex(0);

            Highlight highlight2 = new Highlight(x1,0,-1);
            highlight2.setDataIndex(1);

            Highlight[] highlights = new Highlight[]{highlight,highlight2};
            highlightValues(highlights);

            return highlightByTouchPoint;
        }


    }



    @Override
    protected void drawMarkers(Canvas canvas) {
        // if there is no marker view or drawing marker is disabled
        if (mMarker == null || !isDrawMarkersEnabled() || !valuesToHighlight())
            return;

        for (int i = 0; i < mIndicesToHighlight.length; i++) {

            Highlight highlight = mIndicesToHighlight[i];

            IDataSet set = mData.getDataSetByHighlight(highlight);

            Entry e = mData.getEntryForHighlight(highlight);
            if (e == null)
                continue;

            int entryIndex = set.getEntryIndex(e);

            // make sure entry not null
            if (entryIndex > set.getEntryCount() * mAnimator.getPhaseX())
                continue;
            int dataIndex = highlight.getDataIndex();
            if (dataIndex!=0) return;
            float[] pos = getMarkerPosition(highlight);
            // check bounds
            if (!mViewPortHandler.isInBounds(pos[0], pos[1]))
                continue;
            // callbacks to update the content
            mMarker.refreshContent(e, highlight);

//            Logger.d("position x:: "+pos[0]+" y:: "+ pos[1]);

            // draw the marker
            mMarker.draw(canvas, pos[0], pos[1]);

            try {
                mCb.onData((int) e.getX());
            }catch (Exception ex){
                ex.printStackTrace();
            }

        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent evt) {
        try {
            switch (evt.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    downPoint.y = evt.getY();
                    break;
                case MotionEvent.ACTION_MOVE:
//                Log.i("hkj ",  "getY "+evt.getY() );
                    if (Math.abs(evt.getY() - downPoint.y) > 90) {
                        getParent().requestDisallowInterceptTouchEvent(false);
                    }
                    break;
            }
            return super.onTouchEvent(evt);
        }catch (Exception e){
//            e.printStackTrace();
        }
        return false;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        getParent().requestDisallowInterceptTouchEvent(true);
        return super.dispatchTouchEvent(ev);
    }

    public void setSelectListener(Callback1 cb) {
        mCb = cb;
    }
}
