package com.housesigma.android.views

import android.content.Context
import android.widget.LinearLayout
import com.housesigma.android.R
import com.lxj.xpopup.core.BottomPopupView


class WatchedAreaMenuDialog(
    context: Context,
    cb: MultipleWatchListMenuCallback
) : BottomPopupView(context) {

    private var mCallback: MultipleWatchListMenuCallback = cb

    interface MultipleWatchListMenuCallback {
        fun onDelete()

        fun onEdit()
    }

    override fun getImplLayoutId(): Int {
        return R.layout.dialog_watched_area_menu
    }

    override fun onCreate() {
        super.onCreate()
        initView()
    }


    private fun initView() {
        val llDelete = findViewById<LinearLayout>(R.id.ll_delete)
        llDelete.setOnClickListener {
            mCallback.onDelete()
            dismiss()
        }

        val llEdit = findViewById<LinearLayout>(R.id.ll_edit)
        llEdit.setOnClickListener {
            mCallback.onEdit()
            dismiss()
        }
    }


}