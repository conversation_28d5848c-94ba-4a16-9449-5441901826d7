package com.housesigma.android.views.selectlistdialog

import android.widget.ImageView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.housesigma.android.R
import com.housesigma.android.model.AgentBoard
import com.housesigma.android.utils.MMKVUtils

class SelectListAdapter<T> :
    BaseQuickAdapter<T, BaseViewHolder>(R.layout.item_simple_select_list) {

        init {
            addChildClickViewIds(
                R.id.ll
            )
        }
        override fun convert(holder: BaseViewHolder, item: T) {
            holder.setText(R.id.tv_select_name, item.toString())


            val simpleSelectListItem = item as SimpleSelectListItem
            if (simpleSelectListItem.isSelect){
                holder.setBackgroundResource(R.id.iv_select,R.drawable.ic_filter_point_selected)
                holder.setTextColor(R.id.tv_select_name, context.resources.getColor(R.color.app_main_color))
            } else {
                holder.setTextColor(R.id.tv_select_name, context.resources.getColor(R.color.color_dark))
                holder.setBackgroundResource(R.id.iv_select,R.drawable.ic_filter_point_select)
            }


        }
    }