package com.housesigma.android.views.selectlistdialog

import android.content.Context
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.housesigma.android.R
import com.housesigma.android.model.AgentBoard
import com.lxj.xpopup.core.BottomPopupView

class SelectListDialog<T>(
    title: String,
    list: List<SimpleSelectListItem>,
    context: Context,
    cb: SelectCallback<T>
) : BottomPopupView(context) {

    private var mCallback: SelectCallback<T> = cb
    private lateinit var adapter: SelectListAdapter<SimpleSelectListItem>
    private var mList: List<SimpleSelectListItem> = list
    private var mTitle: String = title

    interface SelectCallback<T> {
        fun onSuccess(selectItem: T)

        fun onCancel()
    }

    override fun getImplLayoutId(): Int {
        return R.layout.dialog_select_list
    }

    override fun onCreate() {
        super.onCreate()
        initView()
    }


    private fun initView() {
        val title = findViewById<TextView>(R.id.tv_title)
        mTitle?.let {
            title.text = it
        }

        val rv = findViewById<RecyclerView>(R.id.rv)
        adapter = SelectListAdapter()
        rv.layoutManager =
            LinearLayoutManager(context, RecyclerView.VERTICAL, false)
        rv.adapter = adapter
        adapter.data.clear()
        adapter.addData(mList)
        adapter.setOnItemChildClickListener { adapter, view, position ->
            when (view.id) {
                R.id.ll -> {
                    val selectItem = mList[position]
                    selectItem.isSelect = true
                    mList.forEachIndexed { index, simpleSelectListItem ->
                        simpleSelectListItem.isSelect = index == position
                    }
                    adapter.notifyDataSetChanged()
                    mCallback.onSuccess(selectItem as T)
                    dismiss()
                }
            }
        }
    }


}