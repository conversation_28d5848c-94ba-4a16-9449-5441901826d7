package com.housesigma.android.views.tip

import android.app.Activity
import android.view.LayoutInflater
import android.view.ViewGroup
import com.housesigma.android.databinding.PopwindowHsTipBinding
import com.housesigma.android.utils.GALog
import com.housesigma.android.views.HSPopWindow

/**
 * TipHelper 是一个管理提示弹窗显示的工具类。
 * 它维护一个 TipModel 列表，并负责以递归方式显示这些弹窗，确保同一时间只显示一个弹窗。
 */
object TipHelper {
    const val FIRST_SHOW_BUILDING_AGE_FILTER_TIP = "first_show_building_age_filter_tip"
    // 维护所有待显示的 TipModel 列表
    private val tipModels = mutableListOf<TipModel>()
    // 标志位，用于指示当前是否有弹窗正在显示
    private var isShowing = false

    /**
     * 添加一个 Tip 到列表中。
     * @param tipModel 要添加的 TipModel。
     */
    fun addTip(tipModel: TipModel) {
        tipModels.add(tipModel)
    }

    /**
     * 从列表中移除一个 Tip。
     * @param tipModel 要移除的 TipModel。
     */
    fun removeTip(tipModel: TipModel) {
        tipModels.remove(tipModel)
    }

    /**
     * 清空所有Tip。
     */
    fun clearTip() {
        tipModels.clear()
    }

    /**
     * 递归显示 TipModel 列表中的弹窗。
     * 如果当前有弹窗正在显示或列表为空，则不执行任何操作。
     * @param activity 当前的 Activity 实例。
     * @param layoutInflater 用于创建视图的 LayoutInflater 实例。
     */
    fun showTip(
        activity: Activity,
        layoutInflater: LayoutInflater
    ) {
        if (isShowing || tipModels.isEmpty()) return

        isShowing = true
        val tipModel = tipModels[0]
        val binding = PopwindowHsTipBinding.inflate(layoutInflater)
        val hintView = binding.root
        var popupViewTip: HSPopWindow? = null
        val tvTipContent = binding.tvTipContent

        tvTipContent.text = tipModel.tipContent

        tipModel.ga?.let {
            GALog.log(it.eventName,it.hsLabel)
        }

        hintView.setOnClickListener {
            popupViewTip?.dissmiss()
        }

        popupViewTip = HSPopWindow.PopupWindowBuilder(activity)
            .setView(hintView)
            .size(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            .enableBackgroundDark(true)
            .setBgDarkAlpha(0.5f)
            .create()
            .showAsDropDown(tipModel.showAtView, 0, 0)

        popupViewTip.popupWindow.setOnDismissListener {
            popupViewTip?.dissmiss()
            removeTip(tipModel)
            isShowing = false
            // 递归调用showTip处理下一个Tip弹窗
            showTip(activity, layoutInflater)
        }
    }
}