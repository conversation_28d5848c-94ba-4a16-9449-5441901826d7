<?xml version="1.0" encoding="UTF-8"?><!--<shape xmlns:android="http://schemas.android.com/apk/res/android">--><!--<stroke--><!--android:width="1dp"--><!--android:color="@color/divider_color" />--><!--<solid android:color="@color/pink_color" />--><!--<corners android:radius="4dp" />--><!--</shape>-->
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 标签选中时的背景 -->
    <item android:state_selected="true">
        <shape>
            <stroke android:width="1dp" android:color="@color/app_main_color" />
            <corners android:radius="4dp" />
            <solid android:color="@color/app_main_color" />
        </shape>
    </item>
    <!-- 标签的正常背景 -->
    <item>
        <shape>
            <stroke android:width="1dp" android:color="#1A000000" />
            <corners android:radius="4dp" />
            <solid android:color="@android:color/white" />
        </shape>
    </item>
</selector>