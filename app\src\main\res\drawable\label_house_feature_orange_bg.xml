<?xml version="1.0" encoding="UTF-8"?><!--<shape xmlns:android="http://schemas.android.com/apk/res/android">--><!--<stroke--><!--android:width="1dp"--><!--android:color="@color/divider_color" />--><!--<solid android:color="@color/pink_color" />--><!--<corners android:radius="4dp" />--><!--</shape>-->
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 标签选中时的背景 -->
    <item>
        <shape>
            <corners
                android:bottomLeftRadius="13.5dp"
                android:bottomRightRadius="0dp"
                android:topLeftRadius="13.5dp"
                android:topRightRadius="0dp" />
            <solid android:color="@color/app_main_color" />
        </shape>
    </item>
</selector>