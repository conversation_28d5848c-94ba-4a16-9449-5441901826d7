<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- item 的top left right bottom 相当于margin 设置不需要的边为负值 -->
    <item android:left="0dp">
        <shape>
            <!-- 背景颜色 -->
            <solid android:color="@color/app_main_color" />
            <!-- 边框颜色 -->
            <stroke
                android:width="1dp"
                android:color="@color/app_main_color" />
            <corners
                android:bottomLeftRadius="0dp"
                android:bottomRightRadius="6dp"
                android:topLeftRadius="0dp"
                android:topRightRadius="6dp" />
        </shape>
    </item>
</layer-list>
