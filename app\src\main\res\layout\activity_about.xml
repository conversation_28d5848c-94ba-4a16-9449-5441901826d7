<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="About HouseSigma"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="110dp"
        android:orientation="vertical">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/ic_about_logo"></ImageView>

        <TextView
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="30dp"
            android:layout_marginRight="16dp"
            android:gravity="center_horizontal"
            android:text="HouseSigma is a leading technology platform that utilizes artificial intelligence technology to correctly estimate Canadian home values in real time. With one click, home buyers can obtain an accurate automated home valuation for every listing within seconds. The HouseSigma algorithm also correctly identifies similar nearby sold properties to help buyers determine their final offer price!
\n\nEmail: <EMAIL>
\nPhone Number: +18885247297
"
            android:textColor="@color/color_dark"></TextView>


        <TextView
            style="@style/H2Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="30dp"
            android:text="Press &#038; Media"
            android:textColor="@color/color_dark"></TextView>

        <TextView
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="For all media inquiries, please contact us at: <EMAIL>"
            android:textColor="@color/color_gray_dark"></TextView>


        <TextView
            style="@style/Subtitles2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="30dp"
            android:text="Operated by HouseSigma Inc. Brokerage"
            android:textColor="@color/color_gray"
            android:textSize="13sp"></TextView>
    </LinearLayout>


</LinearLayout>