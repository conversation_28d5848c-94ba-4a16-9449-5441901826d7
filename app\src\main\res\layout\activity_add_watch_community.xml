<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_white"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close_main_color"></ImageView>


        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_logo_head_main_color"
            android:drawablePadding="10dp"
            android:text="Add Watch Community"
            android:textColor="@color/app_main_color"
            android:textSize="18sp"></TextView>

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">


        <LinearLayout
            android:id="@+id/ll_filter_region"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_filter_region"
                style="@style/Subtitles1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="16dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="center_horizontal"
                android:lines="1"
                android:maxLines="1"
                android:singleLine="true"
                android:text="Toronto City All"
                android:textColor="@color/color_dark"
                android:textSize="14sp"></TextView>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="16dp"
                android:background="@drawable/ic_map_arrow_down"></ImageView>

        </LinearLayout>


    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#C4C4C4"></View>

    <LinearLayout
        android:id="@+id/ll_filter2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:baselineAligned="false">


        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">


            <TextView
                android:id="@+id/tv_filter2"
                style="@style/Subtitles1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="16dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:lines="1"
                android:text="Toronto"
                android:textColor="@color/color_dark"
                android:textSize="14sp"></TextView>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="16dp"
                android:background="@drawable/ic_map_arrow_down"></ImageView>

        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_filter3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">


            <TextView
                android:id="@+id/tv_filter3"
                style="@style/Subtitles1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="16dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:lines="1"
                android:text="Annex"
                android:textColor="@color/color_dark"
                android:textSize="14sp"></TextView>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="16dp"
                android:background="@drawable/ic_map_arrow_down"></ImageView>

        </LinearLayout>
    </LinearLayout>

    <View
        android:id="@+id/v_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#C4C4C4"></View>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.housesigma.android.views.viewpagerindicator.view.indicator.ScrollIndicatorView
            android:id="@+id/tab_main_indicator"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_alignParentTop="true"
            android:background="@color/color_white" />

        <org.maplibre.android.maps.MapView
            android:id="@+id/mapView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/tab_main_indicator" />

        <View
            android:id="@+id/v_no_data"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="50dp"
            android:background="#3f000000"
            android:visibility="gone"></View>

        <TextView
            android:id="@+id/tv_no_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@drawable/shape_10radiuis_white"
            android:paddingLeft="10dp"
            android:paddingTop="5dp"
            android:paddingRight="10dp"
            android:paddingBottom="5dp"
            android:text="No coordinate especified for community"
            android:textColor="@color/color_dark"
            android:textSize="14sp"
            android:visibility="gone"></TextView>

        <ImageView
            android:id="@+id/ic_map_tool_location"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/tv_add_watch_community"
            android:layout_alignRight="@id/tv_add_watch_community"
            android:layout_marginBottom="16dp"
            android:background="@drawable/ic_map_tool_location"></ImageView>

        <ImageView
            android:id="@+id/ic_map_tool_satellite"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/ic_map_tool_location"
            android:layout_alignLeft="@id/ic_map_tool_location"
            android:layout_marginBottom="16dp"
            android:background="@drawable/ic_map_tool_satellite"></ImageView>


        <TextView
            android:id="@+id/tv_add_watch_community"
            style="@style/Button1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="36dp"
            android:background="@drawable/shape_10radius_main_color_fill"
            android:gravity="center"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Add Watch Community"
            android:textColor="@color/color_white"
            android:textSize="16sp"></TextView>
    </RelativeLayout>


</LinearLayout>