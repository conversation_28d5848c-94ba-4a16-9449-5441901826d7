<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="Recent Experience"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

    </RelativeLayout>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <org.maplibre.android.maps.MapView
            android:id="@+id/mapView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <TextView
            android:id="@+id/tv_attribution"
            style="@style/Subtitles2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="6dp"
            android:layout_marginBottom="6dp"
            android:text="© OpenStreetMap contributors"
            android:textSize="10sp"></TextView>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/sv_list_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ll_type_list_view_root"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_centerHorizontal="true"
                    android:layout_gravity="center"
                    android:layout_marginTop="16dp"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingLeft="1dp"
                    android:paddingRight="1dp"></LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_list_view_total"
                        style="@style/H1Header"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="18dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginBottom="16dp"
                        android:layout_weight="1"
                        android:text="0 Listing"
                        android:textColor="@color/color_black"></TextView>

                    <TextView
                        android:id="@+id/tv_map_list_view_sort_type"
                        style="@style/Body1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginRight="16dp"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/shape_5radius_gray"
                        android:drawableRight="@drawable/ic_map_listview_arrow_down"
                        android:drawablePadding="6dp"
                        android:gravity="left"
                        android:inputType="text"
                        android:paddingLeft="10dp"
                        android:paddingTop="6dp"
                        android:paddingRight="8dp"
                        android:paddingBottom="6dp"
                        android:text="Newest"
                        android:textColor="@color/color_black"
                        android:textColorHint="@color/color_gray_dark"
                        android:textSize="14sp"></TextView>
                </LinearLayout>


                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="18dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_breadcrumb_navigation_first"
                        style="@style/SemiBold"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textColor="@color/app_main_color"
                        android:textSize="16sp"></TextView>

                    <TextView
                        android:id="@+id/tv_breadcrumb_navigation_final"
                        style="@style/SemiBold"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textColor="@color/color_black"
                        android:textSize="16sp"></TextView>

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_listing_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:overScrollMode="never"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:visibility="gone"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:listitem="@layout/item_map_listing" />


                <LinearLayout
                    android:id="@+id/ll_page_control"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <com.housesigma.android.views.HSPageControlView
                        android:id="@+id/page_control_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"></com.housesigma.android.views.HSPageControlView>
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_change_map"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="20dp"
                    android:background="@drawable/shape_25radius_main_color_fill"
                    android:drawableLeft="@drawable/ic_change_map"
                    android:drawablePadding="10dp"
                    android:paddingLeft="22dp"
                    android:paddingTop="12dp"
                    android:paddingRight="22dp"
                    android:paddingBottom="12dp"
                    android:text="Map"
                    android:textColor="@color/color_white"
                    android:textSize="16sp"></TextView>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="30dp"
                    android:layout_marginBottom="16dp"
                    android:background="@color/color_gray_light" />

                <TextView
                    android:id="@+id/tv_list_view_municipality_name"
                    style="@style/H1Header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="16dp"
                    android:text=""
                    android:textColor="@color/color_dark"></TextView>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_period_name"
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="January 2023"
                            android:textColor="@color/color_gray_dark"></TextView>

                        <TextView
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Median Price"
                            android:textColor="@color/color_gray_dark"></TextView>

                        <TextView
                            android:id="@+id/tv_price_sold"
                            style="@style/H1Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="7dp"
                            android:text="$-"
                            android:textColor="@color/color_dark"></TextView>

                    </LinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="match_parent"
                        android:background="@color/color_EBEBEB"></View>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_period_name_new"
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="January 2023"
                            android:textColor="@color/color_gray_dark"></TextView>

                        <TextView
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="New Listings"
                            android:textColor="@color/color_gray_dark"></TextView>

                        <TextView
                            android:id="@+id/tv_list_new"
                            style="@style/H1Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="7dp"
                            android:text="-"
                            android:textColor="@color/color_dark"></TextView>


                    </LinearLayout>
                </LinearLayout>

                <TextView
                    style="@style/Subtitles2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="37dp"
                    android:layout_marginBottom="20dp"
                    android:text="Median Price Change"
                    android:textColor="@color/color_gray_dark"></TextView>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1 Year"
                            android:textColor="@color/color_gray_dark"></TextView>


                        <TextView
                            android:id="@+id/tv_price_sold_change_1_years"
                            style="@style/H1Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="3dp"
                            android:text="-17.3%"
                            android:textColor="@color/color_green_dark"></TextView>


                    </LinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="match_parent"
                        android:background="@color/color_EBEBEB"></View>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="5 Years"
                            android:textColor="@color/color_gray_dark"></TextView>


                        <TextView
                            android:id="@+id/tv_price_sold_change_5_years"
                            style="@style/H1Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="3dp"
                            android:text="-17.3%"
                            android:textColor="@color/color_green_dark"></TextView>


                    </LinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="match_parent"
                        android:background="@color/color_EBEBEB"></View>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="10 Years"
                            android:textColor="@color/color_gray_dark"></TextView>

                        <TextView
                            android:id="@+id/tv_price_sold_change_10_years"
                            style="@style/H1Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="3dp"
                            android:text="-17.3%"
                            android:textColor="@color/color_green_dark"></TextView>

                    </LinearLayout>
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="16dp"
                    android:background="@color/color_EBEBEB" />

                <TextView
                    style="@style/H1Header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="18dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:text="Municipalities"
                    android:textColor="@color/color_black"></TextView>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_municipalities"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:overScrollMode="never"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:listitem="@layout/item_municipalities_listing" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/color_EBEBEB" />

                <LinearLayout
                    android:id="@+id/ll_list_view_show_more_municipalities"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal">

                    <TextView
                        android:id="@+id/tv_list_view_show_more_municipalities"
                        style="@style/Button1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:drawableStart="@drawable/ic_map_list_view_show_more_arrow_down"
                        android:drawablePadding="10dp"
                        android:gravity="center"
                        android:paddingTop="18dp"
                        android:paddingBottom="18dp"
                        android:text="Show More"
                        android:textColor="@color/app_main_color"
                        android:textSize="16sp"></TextView>
                </LinearLayout>


            </LinearLayout>


        </androidx.core.widget.NestedScrollView>



        <LinearLayout
            android:id="@+id/ll_no_result_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="60dp"
            android:layout_marginBottom="30dp"
            android:gravity="center_horizontal"
            android:orientation="horizontal"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:background="@drawable/shape_10radius_cyan_color_fill"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_no_result_tip"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:layout_marginLeft="12dp"
                    android:layout_weight="1"
                    android:drawableLeft="@drawable/ic_map_tip_info"
                    android:drawablePadding="10dp"
                    android:textColor="@color/color_dark"
                    android:textSize="14sp"></TextView>

                <TextView
                    android:id="@+id/tv_tip_clear_filters"
                    style="@style/Button2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/shape_10radius_main_color_fill"
                    android:paddingLeft="10dp"
                    android:paddingTop="5dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="5dp"
                    android:text="Clear filters"
                    android:textColor="@color/color_white"
                    android:visibility="visible"></TextView>

            </LinearLayout>


        </LinearLayout>

        <TextView
            android:id="@+id/tv_change_map_outer"
            style="@style/Body1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="35dp"
            android:background="@drawable/shape_25radius_main_color_fill"
            android:drawableLeft="@drawable/ic_change_map"
            android:drawablePadding="10dp"
            android:paddingLeft="22dp"
            android:paddingTop="12dp"
            android:paddingRight="22dp"
            android:paddingBottom="12dp"
            android:text="Map"
            android:textColor="@color/color_white"
            android:textSize="16sp"
            android:visibility="gone"></TextView>

        <TextView
            android:id="@+id/tv_test_zoom"
            style="@style/H1Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_red_light"></TextView>

        <LinearLayout
            android:id="@+id/ll_filter_type"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="16dp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_for_sale"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="38dp"
                android:background="@drawable/shape_map_left"
                android:elevation="2dp"
                android:gravity="center"
                android:paddingLeft="16dp"
                android:paddingTop="4dp"
                android:paddingRight="16dp"
                android:paddingBottom="4dp"
                android:text="For Sale"
                android:textColor="@color/color_gray_dark"
                android:textSize="16sp"></TextView>

            <View
                android:id="@+id/tv_type_divider1"
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/color_gray_a30"></View>

            <TextView
                android:id="@+id/tv_sold"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="38dp"
                android:background="@drawable/shape_map_center"
                android:elevation="2dp"
                android:gravity="center"
                android:paddingLeft="16dp"
                android:paddingTop="4dp"
                android:paddingRight="16dp"
                android:paddingBottom="4dp"
                android:text="Sold"
                android:textColor="@color/color_gray_dark"
                android:textSize="16sp"></TextView>

            <View
                android:id="@+id/tv_type_divider2"
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/color_gray_a30"></View>

            <TextView
                android:id="@+id/tv_delisted"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="38dp"
                android:background="@drawable/shape_map_right"
                android:elevation="2dp"
                android:gravity="center"
                android:paddingLeft="16dp"
                android:paddingTop="4dp"
                android:paddingRight="16dp"
                android:paddingBottom="4dp"
                android:text="De-listed"
                android:textColor="@color/color_gray_dark"
                android:textSize="16sp"></TextView>

        </LinearLayout>

        <ImageView
            android:id="@+id/iv_tool_location"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="30dp"
            android:background="@drawable/ic_map_tool_location"></ImageView>

        <ImageView
            android:id="@+id/iv_tool_settings"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/iv_tool_location"
            android:layout_alignLeft="@id/iv_tool_location"
            android:layout_marginBottom="16dp"
            android:background="@drawable/ic_map_tool_settings"></ImageView>

        <ImageView
            android:id="@+id/iv_tool_school"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/iv_tool_settings"
            android:layout_alignLeft="@id/iv_tool_settings"
            android:layout_marginBottom="16dp"
            android:background="@drawable/ic_map_tool_school"
            android:visibility="gone"></ImageView>

        <ImageView
            android:id="@+id/iv_tool_list_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/iv_tool_school"
            android:layout_alignLeft="@id/iv_tool_school"
            android:layout_marginBottom="16dp"
            android:background="@drawable/ic_map_tool_list_view"
            android:visibility="gone"></ImageView>

        <LinearLayout
            android:id="@+id/ll_school_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@color/color_white"
            android:orientation="vertical"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_map_school_name"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_horizontal"
                    android:text="Three Valleys Public School"
                    android:textColor="@color/color_black"
                    android:textSize="18sp"></TextView>

                <ImageView
                    android:id="@+id/iv_close_map_school"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:layout_marginRight="8dp"
                    android:padding="8dp"
                    android:src="@drawable/ic_map_listing_close"></ImageView>
            </LinearLayout>

            <View
                android:id="@+id/line"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/color_EBEBEB" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginLeft="8dp"
                    android:layout_marginTop="8dp"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_score"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="-"
                        android:textColor="@color/color_red_light"
                        android:textSize="20sp"></TextView>

                    <TextView
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="Out of 10"
                        android:textColor="@color/color_979797"
                        android:textSize="14sp"></TextView>


                </LinearLayout>

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:background="@color/color_EBEBEB" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginRight="12dp"
                    android:layout_marginBottom="8dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        style="@style/H2Header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="Basic Information"
                        android:textColor="@color/color_black"
                        android:textSize="16sp"></TextView>

                    <TextView
                        android:id="@+id/tv_address"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Address: 76 Three Valleys Dr, North York, Ontario"
                        android:textColor="@color/color_dark"
                        android:textSize="14sp"></TextView>

                    <TextView
                        android:id="@+id/tv_phone_number"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Phone Number:"
                        android:textColor="@color/color_dark"
                        android:textSize="14sp"></TextView>

                    <TextView
                        android:id="@+id/tv_school_type"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="School Type:"
                        android:textColor="@color/color_dark"
                        android:textSize="14sp"></TextView>

                    <TextView
                        android:id="@+id/tv_school_board"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="School Board:"
                        android:textColor="@color/color_dark"
                        android:textSize="14sp"></TextView>

                    <TextView
                        android:id="@+id/tv_website"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Web Site:"
                        android:textColor="@color/color_dark"
                        android:textSize="14sp"></TextView>

                    <TextView
                        android:id="@+id/tv_score_head"
                        style="@style/H2Header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Academic Performance"
                        android:textColor="@color/color_black"
                        android:textSize="16sp"></TextView>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_score"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/item_map_score" />


                </LinearLayout>


            </LinearLayout>


        </LinearLayout>

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="70dp">

            <LinearLayout
                android:id="@+id/ll_listings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:background="@drawable/shape_white_login_bg_dialog"
                android:orientation="vertical"
                android:visibility="gone"
                app:behavior_draggable="true"
                app:behavior_hideable="true"
                app:behavior_peekHeight="300dp"
                app:layout_behavior="@string/bottom_sheet_behavior">

                <ImageView
                    android:layout_marginTop="16dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ic_sheet_bar"></ImageView>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">


                    <TextView
                        android:id="@+id/tv_listings_size"
                        style="@style/H1Header"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_weight="1"
                        android:text="2 Listing"
                        android:textColor="@color/color_black"
                        android:textSize="18sp"></TextView>


                    <ImageView
                        android:id="@+id/iv_close_map_preview_list"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="8dp"
                        android:layout_marginRight="8dp"
                        android:padding="8dp"
                        android:src="@drawable/ic_map_listing_close"></ImageView>
                </LinearLayout>
                <com.housesigma.android.views.MaxHeightRecyclerView
                    android:id="@+id/rv_listings"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxHeight="1200dp">

                </com.housesigma.android.views.MaxHeightRecyclerView>

            </LinearLayout>

        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </RelativeLayout>


</LinearLayout>