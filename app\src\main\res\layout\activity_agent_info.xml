<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="I am a real estate agent"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:orientation="vertical">

        <TextView
            style="@style/H2Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="Province"
            android:textColor="@color/color_dark"
            android:textSize="16sp"></TextView>


        <EditText
            android:id="@+id/et_province"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/shape_10radius_gray_fill"
            android:enabled="false"
            android:paddingLeft="16dp"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text=""
            android:textColor="@color/color_gray_dark"
            android:textColorHint="@color/color_gray"
            android:textSize="16sp"></EditText>

        <TextView
            style="@style/H2Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Board name"
            android:textColor="@color/color_dark"
            android:textSize="16sp"></TextView>

        <EditText
            android:id="@+id/et_board_name"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/shape_10radius_gray_fill"
            android:enabled="false"
            android:paddingLeft="16dp"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text=""
            android:textColor="@color/color_gray_dark"
            android:textColorHint="@color/color_gray"
            android:textSize="16sp"></EditText>


        <TextView
            style="@style/H2Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Brokerage name"
            android:textColor="@color/color_dark"
            android:textSize="16sp"></TextView>


        <EditText
            android:id="@+id/et_brokerage_name"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/shape_10radius_gray_fill"
            android:enabled="false"
            android:paddingLeft="16dp"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text=""
            android:textColor="@color/color_gray_dark"
            android:textColorHint="@color/color_gray"
            android:textSize="16sp"></EditText>

        <TextView
            style="@style/Subtitles1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="This is the information you declared at sign up.\nTo update the above info, please contact support."></TextView>

    </LinearLayout>


</LinearLayout>