<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="Change Account"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/ll_verify"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_verify_send_tip"
            style="@style/H2Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="80dp"
            android:text="We sent you a code to verify your email"
            android:textColor="@color/color_dark"
            android:textSize="16sp">

        </TextView>


        <TextView
            android:id="@+id/tv_send_to"
            style="@style/Subtitles2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="10dp"
            android:text="Sent to "
            android:textColor="@color/color_gray_dark"></TextView>

        <TextView
            style="@style/H2Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="25dp"
            android:layout_marginBottom="10dp"
            android:text="Code"
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>

        <com.housesigma.android.views.verifycodelib.VerifyCodeView
            android:id="@+id/verifyCodeView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <TextView
            style="@style/Subtitles2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="13dp"
            android:text="Enter 4 digit code"
            android:textColor="@color/color_gray"
            android:textSize="14sp"></TextView>

        <TextView
            android:layout_marginTop="30dp"
            android:id="@+id/tv_submit"
            style="@style/Button1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/shape_10radius_main_color_fill"
            android:gravity="center_horizontal"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Submit"
            android:textColor="@color/color_white"
            android:textSize="16sp"></TextView>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_enter"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="visible">


        <TextView
            android:id="@+id/tv_current"
            style="@style/Body1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:visibility="gone"
            android:layout_marginTop="20dp"
            android:layout_marginRight="16dp"
            android:text="Current: "
            android:textColor="@color/color_dark"></TextView>

        <LinearLayout
            android:id="@+id/ll_phone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="visible">


            <LinearLayout
                android:id="@+id/ll_input_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="17dp"
                android:background="@drawable/shape_5radius_blue"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_country_code"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:drawableRight="@drawable/ic_signup_down"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:paddingLeft="16dp"
                    android:paddingRight="10dp"
                    android:text=""
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>

                <View
                    android:layout_width="1dp"
                    android:layout_height="36dp"
                    android:layout_gravity="center_vertical"
                    android:background="@color/color_gray"></View>

                <EditText
                    android:id="@+id/et_phone"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="left"
                    android:hint="Enter your new phone"
                    android:inputType="phone"
                    android:lines="1"
                    android:maxLines="1"
                    android:paddingLeft="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:textColor="@color/color_black"
                    android:textColorHint="@color/color_gray"
                    android:textSize="16sp"></EditText>

                <ImageView
                    android:id="@+id/iv_del"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="14dp"
                    android:paddingTop="8dp"
                    android:paddingRight="14dp"
                    android:paddingBottom="8dp"
                    android:src="@drawable/ic_serach_del"></ImageView>


            </LinearLayout>


        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rl_email"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="16dp"
            android:background="@drawable/shape_5radius_blue"
            android:visibility="gone">

            <EditText
                android:id="@+id/et_email"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Enter your new email"
                android:inputType="textEmailAddress"
                android:lines="1"
                android:maxLines="1"
                android:paddingLeft="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray_dark"
                android:textSize="14sp"></EditText>

            <ImageView
                android:id="@+id/iv_del_email"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:paddingLeft="14dp"
                android:paddingTop="8dp"
                android:paddingRight="14dp"
                android:paddingBottom="8dp"
                android:src="@drawable/ic_serach_del"></ImageView>

        </RelativeLayout>

        <TextView
            android:layout_marginTop="30dp"
            android:id="@+id/tv_send_code"
            style="@style/Button1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="17dp"
            android:layout_marginRight="17dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/shape_10radius_main_color_fill"
            android:gravity="center_horizontal"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Send Code"
            android:textColor="@color/color_white"
            android:textSize="16sp"></TextView>

        <TextView
            android:id="@+id/tv_change_sign_in"
            style="@style/Button1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="17dp"
            android:layout_marginRight="17dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/shape_10radius_main_color"
            android:gravity="center_horizontal"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Change sign-in account to phone number"
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>
    </LinearLayout>


</LinearLayout>