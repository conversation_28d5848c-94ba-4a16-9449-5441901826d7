<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="Change password"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/shape_5radius_blue">

            <EditText
                android:id="@+id/et_search_term"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:hint="Enter your new password"
                android:inputType="textPassword"
                android:lines="1"
                android:maxLines="1"
                android:paddingLeft="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray_dark"
                android:textSize="16sp"></EditText>

            <ImageView
                android:id="@+id/iv_del"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:paddingLeft="14dp"
                android:paddingTop="8dp"
                android:paddingRight="14dp"
                android:paddingBottom="8dp"
                android:src="@drawable/ic_serach_del"></ImageView>

        </RelativeLayout>


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="4dp"
                android:layout_height="4dp"
                android:layout_marginLeft="16dp"
                android:background="@drawable/shape_black_point"></ImageView>

            <TextView
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:text="Password must consist of at least 6 characteres."
                android:textColor="@color/color_dark"
                android:textSize="14sp"></TextView>

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="4dp"
                android:layout_height="4dp"
                android:layout_marginTop="7dp"
                android:layout_gravity="top"
                android:layout_marginLeft="16dp"
                android:background="@drawable/shape_black_point"></ImageView>

            <TextView
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:text="Password must consist 2 of: Alphabet, Number digit, Special character."
                android:textColor="@color/color_dark"
                android:textSize="14sp"></TextView>

        </LinearLayout>
    </LinearLayout>


    <TextView
        android:id="@+id/tv_save"
        style="@style/Button1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="17dp"
        android:layout_marginRight="17dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/shape_10radius_main_color_fill"
        android:gravity="center_horizontal"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:text="Submit"
        android:textColor="@color/color_white"
        android:textSize="16sp"></TextView>

</LinearLayout>