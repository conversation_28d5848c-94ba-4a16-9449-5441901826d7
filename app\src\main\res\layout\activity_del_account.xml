<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="Are you absolutely sure?"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_tip"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="16dp"
            android:gravity="left"
            android:text="This action cannot be undone. This will permanently delete the  account, watched property, watched area and all data associated."
            android:textColor="@color/color_dark"
            android:textSize="16sp"></TextView>

        <!--        <TextView-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_marginLeft="16dp"-->
        <!--            android:gravity="left"-->
        <!--            android:layout_marginTop="16dp"-->
        <!--            android:layout_marginRight="16dp"-->
        <!--            android:textColor="@color/color_dark"-->
        <!--            android:text="Once you delete the account, there is no going back, please be certain.\nEnter your password to confirm."></TextView>-->

        <EditText
            android:id="@+id/et_password"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="17dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="17dp"
            android:background="@drawable/shape_btn_gray"
            android:gravity="left"
            android:hint="Enter password"
            android:inputType="textPassword"
            android:lines="1"
            android:maxLines="1"
            android:paddingLeft="16dp"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:textColor="@color/color_black"
            android:textColorHint="@color/color_gray"
            android:textSize="16sp"></EditText>

        <TextView
            android:id="@+id/tv_del_my_account"
            style="@style/Button1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="36dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="35dp"
            android:background="@drawable/shape_btn_red"
            android:gravity="center"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Delete my account"
            android:textColor="#FB1815"
            android:textSize="16sp"></TextView>

    </LinearLayout>


</LinearLayout>