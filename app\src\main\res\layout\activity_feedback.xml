<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="Give Us Feedback"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

    </RelativeLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="16dp"
                android:text="Check our FAQ section for commonly asked questions."
                android:textColor="@color/color_dark"
                android:textSize="16sp"></TextView>

            <TextView
                android:id="@+id/tv_faq"
                style="@style/Button1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/shape_10radius_main_color"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="Knowledge Base - FAQ"
                android:textColor="@color/app_main_color"
                android:textSize="16sp"></TextView>


            <TextView
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:text="@string/feedback_tip"
                android:textColor="@color/color_dark"
                android:textSize="16sp"></TextView>

            <EditText
                android:id="@+id/et_feedback"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="16dp"
                android:scrollbars="vertical"
                android:background="@drawable/shape_5radius_gray"
                android:gravity="left"
                android:hint="@string/feedback_hint"
                android:inputType="textMultiLine"
                android:lines="3"
                android:maxLines="20"
                android:paddingLeft="16dp"
                android:paddingTop="13dp"
                android:paddingRight="16dp"
                android:paddingBottom="13dp"
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray_dark"></EditText>


            <TextView
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="16dp"
                android:text="@string/feedback_content_tip"
                android:textColor="@color/color_dark"
                android:textSize="16sp"></TextView>


            <EditText
                android:id="@+id/et_email"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="16dp"
                android:background="@drawable/shape_5radius_gray"
                android:gravity="left"
                android:hint="@string/feedback_email_hint"
                android:inputType="textEmailAddress"
                android:lines="1"
                android:maxLines="1"
                android:paddingLeft="16dp"
                android:paddingTop="13dp"
                android:paddingRight="16dp"
                android:paddingBottom="13dp"
                android:singleLine="true"
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray_dark"></EditText>

            <TextView
                style="@style/Subtitles2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="16dp"
                android:text="For brokerage related issues, please contact our \nBroker of Record:\<EMAIL>"
                android:textColor="@color/color_black"
                android:textSize="14sp"></TextView>

            <TextView
                android:id="@+id/tv_save"
                style="@style/Button1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="30dp"
                android:background="@drawable/shape_10radius_main_color_fill"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="Send Feedback"
                android:textColor="@color/color_white"
                android:textSize="16sp"></TextView>

        </LinearLayout>
    </ScrollView>


</LinearLayout>