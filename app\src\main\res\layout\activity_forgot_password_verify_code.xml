<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@drawable/ic_home_logo"></ImageView>


    </RelativeLayout>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="30dp"
        android:background="@drawable/ic_signup_step3"></ImageView>


    <TextView
        style="@style/H1Header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="30dp"
        android:text="Verify it's you"
        android:textColor="@color/app_main_color"
        android:textSize="18sp"></TextView>


    <TextView
        android:id="@+id/tv_send_to"
        style="@style/Subtitles2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginLeft="28dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="28dp"
        android:text="A verification code has been <NAME_EMAIL>, please enter it below"
        android:textColor="@color/color_gray"
        android:textSize="14sp"></TextView>

    <TextView
        style="@style/H2Header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="30dp"
        android:text="Code"
        android:textColor="@color/app_main_color"
        android:textSize="16sp"></TextView>

    <com.housesigma.android.views.verifycodelib.VerifyCodeView
        android:id="@+id/verifyCodeView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="13dp"
        android:text="Enter 4 digit code"
        android:textColor="@color/color_gray_dark"
        android:textSize="14sp"></TextView>

    <EditText
        android:id="@+id/et_password"
        style="@style/PlaceHolder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="17dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="17dp"
        android:background="@drawable/shape_btn_gray5"
        android:gravity="left"
        android:hint="Enter a password"
        android:inputType="textPassword"
        android:lines="1"
        android:maxLines="1"
        android:paddingLeft="16dp"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:textColor="@color/color_black"
        android:textColorHint="@color/color_gray"
        android:textSize="16sp"></EditText>

    <TextView
        style="@style/Subtitles2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="17dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="17dp"
        android:text="Passwords must consist of at least 6 characteres.
Passwords must consist 2 of: Alphabet, Number digit, Special character."
        android:textColor="@color/color_dark"
        android:textSize="14sp"></TextView>

    <TextView
        android:id="@+id/tv_submit"
        style="@style/Button1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="17dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="17dp"
        android:background="@drawable/shape_10radius_main_color_fill"
        android:gravity="center_horizontal"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:text="Submit"
        android:textColor="@color/color_white"
        android:textSize="16sp"></TextView>

</LinearLayout>