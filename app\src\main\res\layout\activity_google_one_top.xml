<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@drawable/ic_home_logo"></ImageView>

    </RelativeLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fadeScrollbars="false"
        android:scrollbarSize="2dp"
        android:scrollbars="vertical"
        android:scrollbarThumbVertical="@color/app_main_color">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <TextView
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="36dp"
                android:text="Finish setting up your account"
                android:textColor="@color/app_main_color"
                android:textSize="18sp"></TextView>


            <EditText
                android:id="@+id/et_email"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="30dp"
                android:layout_marginRight="16dp"
                android:background="@drawable/shape_btn_gray"
                android:clickable="false"
                android:cursorVisible="false"
                android:enabled="false"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:gravity="left"
                android:hint="Enter your Email"
                android:inputType="textEmailAddress"
                android:paddingLeft="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray"
                android:textSize="16sp"></EditText>

            <LinearLayout
                android:id="@+id/ll_input_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="17dp"
                android:background="@drawable/shape_btn_gray"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_country_code"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:drawableRight="@drawable/ic_signup_down"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:paddingLeft="16dp"
                    android:paddingRight="10dp"
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>

                <View
                    android:layout_width="1dp"
                    android:layout_height="36dp"
                    android:layout_gravity="center_vertical"
                    android:background="@color/color_gray"></View>

                <EditText
                    android:id="@+id/et_phone"
                    style="@style/Body1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="left"
                    android:hint="Enter your Mobile Phone"
                    android:inputType="phone"
                    android:lines="1"
                    android:maxLines="1"
                    android:paddingLeft="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:textColor="@color/color_black"
                    android:textColorHint="@color/color_gray"
                    android:textSize="16sp"></EditText>


            </LinearLayout>


            <EditText
                android:id="@+id/et_password"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="16dp"
                android:background="@drawable/shape_btn_gray"
                android:gravity="left"
                android:hint="Enter a password"
                android:inputType="textPassword"
                android:paddingLeft="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray"
                android:textSize="16sp"></EditText>

            <TextView
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="18dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="30dp"
                android:text="- Passwords must consist of at least 6 characters.
        \n- Passwords must consist 2 of: Alphabet, Number digit, Special character."
                android:textColor="@color/color_gray_dark"
                android:textSize="14sp"></TextView>

            <TextView
                android:id="@+id/tv_next"
                style="@style/Button1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="60dp"
                android:background="@drawable/shape_10radius_main_color_fill"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="Complete Signup"
                android:textColor="@color/color_white"
                android:textSize="16sp"></TextView>


            <TextView
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginBottom="2dp"
                android:text="Disclosure:"
                android:textColor="@color/color_dark"
                android:textSize="14dp"></TextView>

            <androidx.appcompat.widget.AppCompatCheckBox
                android:id="@+id/cb_is_agent"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="12dp"
                android:text="I am a licensed real estate agent"
                android:textColor="@color/color_black"
                android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>

            <LinearLayout
                android:id="@+id/ll_agent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/Body1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="16dp"
                        android:layout_weight="1"
                        android:text="Province"
                        android:textColor="@color/color_dark"></TextView>

                    <LinearLayout
                        android:id="@+id/ll_province"
                        android:layout_width="220dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:background="@drawable/shape_5radius_gray"
                        android:orientation="horizontal"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/tv_select_province"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:hint="Select"
                            android:lines="1"
                            android:paddingLeft="10dp"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp"
                            android:textColor="@color/color_dark"
                            android:textColorHint="@color/color_gray"></TextView>

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="16dp"
                            android:background="@drawable/ic_signup_down"></ImageView>

                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/Body1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="16dp"
                        android:layout_weight="1"
                        android:text="Board name"
                        android:textColor="@color/color_dark"></TextView>

                    <LinearLayout
                        android:id="@+id/ll_board_name"
                        android:layout_width="220dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:background="@drawable/shape_5radius_gray"
                        android:orientation="horizontal"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/tv_board_select_name"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:hint="Select"
                            android:lines="1"
                            android:paddingLeft="10dp"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp"
                            android:textColor="@color/color_dark"
                            android:textColorHint="@color/color_gray"></TextView>

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="16dp"
                            android:background="@drawable/ic_signup_down"></ImageView>

                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginBottom="30dp"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/Body1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="16dp"
                        android:layout_weight="1"
                        android:text="Brokerage name"
                        android:textColor="@color/color_dark"></TextView>

                    <LinearLayout
                        android:layout_width="220dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:background="@drawable/shape_5radius_gray"
                        android:orientation="horizontal"
                        android:visibility="visible">

                        <EditText
                            android:id="@+id/et_brokerage_name"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:paddingLeft="10dp"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp"
                            android:textColor="@color/color_dark"
                            android:textColorHint="@color/color_gray"></EditText>


                    </LinearLayout>
                </LinearLayout>



            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="80dp"
                android:background="@drawable/edittext_gray_color_border"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_referral_code"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    android:gravity="left"
                    android:hint="Referral Code (Optional)"
                    android:inputType="text"
                    android:lines="1"
                    android:paddingLeft="16dp"
                    android:paddingTop="12dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="12dp"
                    android:textColor="@color/color_dark"
                    android:textColorHint="@color/color_gray"
                    android:textSize="16sp"></EditText>

                <ImageView
                    android:id="@+id/iv_del"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:paddingLeft="14dp"
                    android:paddingTop="8dp"
                    android:paddingRight="14dp"
                    android:paddingBottom="8dp"
                    android:src="@drawable/ic_serach_del"></ImageView>

            </LinearLayout>
        </LinearLayout>


    </androidx.core.widget.NestedScrollView>



</LinearLayout>