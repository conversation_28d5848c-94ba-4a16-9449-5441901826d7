<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.housesigma.android.views.FingerDragHelper
        android:id="@+id/fingerDragHelper"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_black"
        android:gravity="center"
        android:orientation="vertical">

        <com.housesigma.android.views.subsampling.SubsamplingScaleImageView
            android:id="@+id/photo_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </com.housesigma.android.views.FingerDragHelper>

    <FrameLayout
        android:id="@+id/loading_container"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_centerInParent="true"
        android:background="@drawable/bg_loading_ios"
        android:visibility="visible">

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center"
            android:indeterminateTint="@android:color/white" />

    </FrameLayout>
</RelativeLayout>