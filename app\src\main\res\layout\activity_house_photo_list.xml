<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <TextView
            android:id="@+id/tv_title"
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="Photos"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

        <ImageView
            android:id="@+id/iv_share"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_webview_share"
            android:visibility="visible"></ImageView>

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/ll_bottom_tool"
            android:clipToPadding="false"
            android:paddingBottom="20dp" />

        <LinearLayout
            android:id="@+id/ll_bottom_tool"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="-20dp"
            android:orientation="vertical"
            android:visibility="invisible">

            <View
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:background="@drawable/shape_photo_list_top"></View>

            <LinearLayout
                android:id="@+id/btn_left"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/color_white"
                android:orientation="horizontal"
                android:paddingTop="10dp"
                android:paddingBottom="20dp">

                <RelativeLayout
                    android:id="@+id/rl_left"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="16dp"
                    android:layout_weight="1"
                    android:background="@drawable/shape_10radius_broder_main_color_fill_cyan"
                    android:orientation="horizontal"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp">

                    <TextView
                        android:id="@+id/tv_left"
                        style="@style/Button1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:drawableLeft="@drawable/ic_listing_save"
                        android:drawablePadding="10dp"
                        android:text="Save"
                        android:textColor="@color/app_main_color"
                        android:textSize="16sp"></TextView>
                </RelativeLayout>

                <TextView
                    android:id="@+id/tv_right_btn"
                    style="@style/Button1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="16dp"
                    android:layout_weight="1"
                    android:background="@drawable/shape_10radius_main_color_fill"
                    android:gravity="center_horizontal"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:text="@string/contact_agent"
                    android:textColor="@color/color_white"
                    android:textSize="16sp"></TextView>

            </LinearLayout>
        </LinearLayout>


    </RelativeLayout>


</LinearLayout>