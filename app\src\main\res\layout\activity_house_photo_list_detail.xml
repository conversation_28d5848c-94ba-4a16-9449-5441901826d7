<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <androidx.viewpager.widget.ViewPager
        android:id="@+id/vp"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    </androidx.viewpager.widget.ViewPager>

    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:src="@drawable/ic_photo_close"></ImageView>

    <TextView
        android:id="@+id/tvPage"
        style="@style/Button2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:shadowColor="@color/color_gray_dark"
        android:shadowDx="2"
        android:shadowDy="2"
        android:shadowRadius="2"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="16dp"
        android:textColor="@color/color_white"
        android:textSize="18sp"></TextView>

    <TextView
        android:visibility="gone"
        android:id="@+id/tv_page_landscape"
        style="@style/Button2"
        android:background="@drawable/shape_25radius_white_color_fill"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="1/10"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="16dp"
        android:textColor="@color/color_black"
        android:textSize="16sp"></TextView>

</RelativeLayout>