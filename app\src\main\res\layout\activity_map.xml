<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="16dp"
                android:paddingTop="14dp"
                android:paddingRight="16dp"
                android:paddingBottom="14dp"
                android:src="@drawable/ic_close"></ImageView>

            <LinearLayout
                android:id="@+id/ll_search"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/shape_map_search"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="12dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:src="@drawable/ic_map_search"></ImageView>

                <TextView
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:lines="1"
                    android:maxLines="1"
                    android:text="@string/map_search_hint"
                    android:textColor="@color/color_white"
                    android:textColorHint="@color/color_white"></TextView>

            </LinearLayout>

            <View
                android:layout_width="0.5dp"
                android:layout_height="29dp"
                android:layout_marginRight="4dp"
                android:background="@color/color_gray_a30"></View>

            <LinearLayout
                android:id="@+id/ll_watch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="0.5dp"
                    android:layout_height="29dp"
                    android:layout_marginRight="8dp"
                    android:background="@color/color_gray_a30"></View>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ic_map_watch"></ImageView>

                <TextView
                    style="@style/Subtitles1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="16dp"
                    android:text="Watch"
                    android:textColor="@color/color_white"
                    android:textSize="14sp"></TextView>

            </LinearLayout>


        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#44A6B3"></View>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:baselineAligned="false">


            <LinearLayout
                android:id="@+id/ll_property_types"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="145"
                android:gravity="center"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:layout_marginTop="9dp"
                    android:layout_marginRight="8dp"
                    android:layout_marginBottom="9dp"
                    android:background="@drawable/shape_map_all_property_types"
                    android:orientation="horizontal"
                    android:paddingTop="5dp"
                    android:paddingBottom="4dp">

                    <TextView
                        android:id="@+id/tv_property_types"
                        style="@style/Subtitles1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="8dp"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:lines="1"
                        android:maxLines="1"
                        android:text="All property types"
                        android:textColor="@color/color_white"
                        android:textSize="14sp"></TextView>

                    <TextView
                        android:id="@+id/tv_property_more"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginRight="4dp"
                        android:background="@drawable/shape_5radius_main_color_fill"
                        android:lines="1"
                        android:maxLines="1"
                        android:paddingLeft="2dp"
                        android:paddingRight="2dp"
                        android:text="1+"
                        android:textColor="@color/color_white"
                        android:textSize="14sp"
                        android:visibility="gone"></TextView>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginRight="10dp"
                        android:background="@drawable/ic_sign_hidden"></ImageView>

                </LinearLayout>


            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_for_sale_delised"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_map_all_property_types"
                    android:paddingTop="5dp"
                    android:paddingBottom="4dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="4dp"
                        android:background="@drawable/ic_map_point"></ImageView>


                    <TextView
                        android:id="@+id/tv_filter_sale"
                        style="@style/Subtitles1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1"
                        android:paddingRight="4dp"
                        android:gravity="center_vertical"
                        android:lines="1"
                        android:maxLines="1"
                        android:text="All"
                        android:textColor="@color/color_white"
                        android:textSize="14sp"></TextView>


                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/ic_filter_point"></ImageView>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="-9dp"
                        android:background="@drawable/ic_filter_gray_point"
                        android:visibility="gone"></ImageView>

                    <TextView
                        android:id="@+id/tv_filter_sold_delisted"
                        style="@style/Subtitles1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:lines="1"
                        android:maxLines="1"
                        android:text="90d"
                        android:layout_marginRight="12dp"
                        android:textColor="@color/color_white"
                        android:textSize="14sp"></TextView>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginRight="8dp"
                        android:background="@drawable/ic_sign_hidden"></ImageView>

                </LinearLayout>


            </LinearLayout>


            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="8dp"
                android:layout_weight="70"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/ll_filters_more"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:layout_marginRight="8dp"
                    android:layout_marginBottom="9dp"
                    android:background="@drawable/shape_map_all_property_types"
                    android:orientation="horizontal">

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/tv_filters_more"
                            style="@style/Subtitles1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:lines="1"
                            android:maxLines="1"
                            android:paddingLeft="10dp"
                            android:paddingTop="4dp"
                            android:paddingBottom="4dp"
                            android:text="Filters"
                            android:textColor="@color/color_white"
                            android:textSize="14sp"></TextView>

                        <ImageView
                            android:id="@+id/iv_filter_more_point"
                            android:layout_width="8dp"
                            android:layout_height="8dp"
                            android:layout_alignTop="@id/tv_filters_more"
                            android:layout_marginTop="4dp"
                            android:layout_marginRight="4dp"
                            android:layout_toRightOf="@+id/tv_filters_more"
                            android:background="@drawable/ic_red_point"
                            android:visibility="gone"></ImageView>

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:layout_gravity="center_vertical"
                            android:layout_marginRight="8dp"
                            android:background="@drawable/ic_sign_hidden"></ImageView>

                    </RelativeLayout>


                </LinearLayout>


            </LinearLayout>


        </LinearLayout>


    </LinearLayout>

    <View
        android:id="@+id/v_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#C4C4C4"></View>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <org.maplibre.android.maps.MapView
            android:id="@+id/mapView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <TextView
            android:id="@+id/tv_attribution"
            android:layout_marginBottom="6dp"
            android:layout_marginLeft="6dp"
            android:layout_alignParentBottom="true"
            android:layout_alignParentLeft="true"
            android:textSize="10sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/Subtitles2"
            android:text="© OpenStreetMap contributors"></TextView>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/sv_list_view"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ll_type_list_view_root"
                    android:paddingLeft="1dp"
                    android:paddingRight="1dp"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_centerHorizontal="true"
                    android:layout_gravity="center"
                    android:layout_marginTop="16dp"
                    android:gravity="center"
                    android:orientation="horizontal"></LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/tv_list_view_total"
                        android:layout_marginLeft="18dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginBottom="16dp"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        style="@style/H1Header"
                        android:textColor="@color/color_black"
                        android:layout_height="wrap_content"
                        android:text="0 Listing"></TextView>

                    <TextView
                        android:id="@+id/tv_map_list_view_sort_type"
                        style="@style/Body1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginBottom="16dp"
                        android:layout_marginRight="16dp"
                        android:background="@drawable/shape_5radius_gray"
                        android:gravity="left"
                        android:text="Newest"
                        android:inputType="text"
                        android:paddingLeft="10dp"
                        android:drawablePadding="6dp"
                        android:drawableRight="@drawable/ic_map_listview_arrow_down"
                        android:paddingTop="6dp"
                        android:paddingRight="8dp"
                        android:textSize="14sp"
                        android:paddingBottom="6dp"
                        android:textColor="@color/color_black"
                        android:textColorHint="@color/color_gray_dark"></TextView>
                </LinearLayout>




                <LinearLayout
                    android:layout_marginLeft="18dp"
                    android:layout_width="wrap_content"
                    android:orientation="horizontal"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tv_breadcrumb_navigation_first"
                        android:layout_width="wrap_content"
                        style="@style/SemiBold"
                        android:textSize="16sp"
                        android:textColor="@color/app_main_color"
                        android:layout_height="wrap_content"
                        android:text=""></TextView>

                    <TextView
                        android:id="@+id/tv_breadcrumb_navigation_final"
                        android:layout_width="wrap_content"
                        style="@style/SemiBold"
                        android:textSize="16sp"
                        android:textColor="@color/color_black"
                        android:layout_height="wrap_content"
                        android:text=""></TextView>

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_listing_view"
                    android:overScrollMode="never"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:layout_height="match_parent"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:listitem="@layout/item_map_listing" />


                <LinearLayout
                    android:id="@+id/ll_page_control"
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:gravity="center_horizontal"
                    android:layout_height="wrap_content">

                    <com.housesigma.android.views.HSPageControlView
                        android:id="@+id/page_control_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"></com.housesigma.android.views.HSPageControlView>
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_change_map"
                    style="@style/Body1"
                    android:layout_marginTop="20dp"
                    android:layout_width="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:paddingLeft="22dp"
                    android:paddingRight="22dp"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:layout_height="wrap_content"
                    android:drawableLeft="@drawable/ic_change_map"
                    android:background="@drawable/shape_25radius_main_color_fill"
                    android:drawablePadding="10dp"
                    android:text="Map"
                    android:textColor="@color/color_white"
                    android:textSize="16sp"></TextView>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="30dp"
                    android:layout_marginBottom="16dp"
                    android:background="@color/color_gray_light" />

                <TextView
                    android:id="@+id/tv_list_view_municipality_name"
                    style="@style/H1Header"
                    android:layout_gravity="center_horizontal"
                    android:layout_width="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:textColor="@color/color_dark"
                    android:layout_height="wrap_content"
                    android:text=""></TextView>



                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:orientation="vertical"
                        android:gravity="center_horizontal"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/tv_period_name"
                            android:layout_width="wrap_content"
                            android:textColor="@color/color_gray_dark"
                            style="@style/Subtitles2"
                            android:layout_height="wrap_content"
                            android:text="January 2023"></TextView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:textColor="@color/color_gray_dark"
                            style="@style/Subtitles2"
                            android:layout_height="wrap_content"
                            android:text="Median Price"></TextView>

                        <TextView
                            android:id="@+id/tv_price_sold"
                            android:layout_marginTop="7dp"
                            android:layout_width="wrap_content"
                            android:textColor="@color/color_dark"
                            android:layout_height="wrap_content"
                            style="@style/H1Header"
                            android:text="$-"></TextView>

                    </LinearLayout>
                    <View
                        android:layout_width="1dp"
                        android:background="@color/color_EBEBEB"
                        android:layout_height="match_parent"></View>
                    <LinearLayout
                        android:gravity="center_horizontal"
                        android:orientation="vertical"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/tv_period_name_new"
                            android:layout_width="wrap_content"
                            android:textColor="@color/color_gray_dark"
                            style="@style/Subtitles2"
                            android:layout_height="wrap_content"
                            android:text="January 2023"></TextView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:textColor="@color/color_gray_dark"
                            style="@style/Subtitles2"
                            android:layout_height="wrap_content"
                            android:text="New Listings"></TextView>

                        <TextView
                            android:id="@+id/tv_list_new"
                            android:layout_width="wrap_content"
                            android:layout_marginTop="7dp"
                            android:textColor="@color/color_dark"
                            style="@style/H1Header"
                            android:layout_height="wrap_content"
                            android:text="-"></TextView>



                    </LinearLayout>
                </LinearLayout>

                <TextView
                    style="@style/Subtitles2"
                    android:layout_gravity="center_horizontal"
                    android:layout_width="wrap_content"
                    android:layout_marginBottom="20dp"
                    android:textColor="@color/color_gray_dark"
                    android:layout_marginTop="37dp"
                    android:layout_height="wrap_content"
                    android:text="Median Price Change"></TextView>

                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:orientation="vertical"
                        android:gravity="center_horizontal"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content">

                        <TextView
                            android:layout_width="wrap_content"
                            android:textColor="@color/color_gray_dark"
                            android:layout_height="wrap_content"
                            style="@style/Subtitles2"
                            android:text="1 Year"></TextView>


                        <TextView
                            android:id="@+id/tv_price_sold_change_1_years"
                            android:layout_width="wrap_content"
                            style="@style/H1Header"
                            android:layout_marginTop="3dp"
                            android:textColor="@color/color_green_dark"
                            android:layout_height="wrap_content"
                            android:text="-17.3%"></TextView>



                    </LinearLayout>
                    <View
                        android:layout_width="1dp"
                        android:background="@color/color_EBEBEB"
                        android:layout_height="match_parent"></View>
                    <LinearLayout
                        android:gravity="center_horizontal"
                        android:orientation="vertical"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content">

                        <TextView
                            android:layout_width="wrap_content"
                            android:textColor="@color/color_gray_dark"
                            style="@style/Subtitles2"
                            android:layout_height="wrap_content"
                            android:text="5 Years"></TextView>


                        <TextView
                            android:id="@+id/tv_price_sold_change_5_years"
                            style="@style/H1Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="3dp"
                            android:text="-17.3%"
                            android:textColor="@color/color_green_dark"></TextView>



                    </LinearLayout>
                    <View
                        android:layout_width="1dp"
                        android:background="@color/color_EBEBEB"
                        android:layout_height="match_parent"></View>
                    <LinearLayout
                        android:gravity="center_horizontal"
                        android:orientation="vertical"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content">

                        <TextView
                            android:layout_width="wrap_content"
                            android:textColor="@color/color_gray_dark"
                            android:layout_height="wrap_content"
                            style="@style/Subtitles2"
                            android:text="10 Years"></TextView>

                        <TextView
                            android:id="@+id/tv_price_sold_change_10_years"
                            android:layout_width="wrap_content"
                            style="@style/H1Header"
                            android:layout_marginTop="3dp"
                            android:textColor="@color/color_green_dark"
                            android:layout_height="wrap_content"
                            android:text="-17.3%"></TextView>

                    </LinearLayout>
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="16dp"
                    android:background="@color/color_EBEBEB" />

                <TextView
                    android:layout_marginLeft="18dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:layout_width="wrap_content"
                    style="@style/H1Header"
                    android:textColor="@color/color_black"
                    android:layout_height="wrap_content"
                    android:text="Municipalities"></TextView>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_municipalities"
                    android:overScrollMode="never"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:listitem="@layout/item_municipalities_listing" />
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/color_EBEBEB" />

                <LinearLayout
                    android:id="@+id/ll_list_view_show_more_municipalities"
                    android:layout_width="match_parent"
                    android:gravity="center_horizontal"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tv_list_view_show_more_municipalities"
                        android:paddingTop="18dp"
                        android:paddingBottom="18dp"
                        style="@style/Button1"
                        android:textColor="@color/app_main_color"
                        android:layout_width="wrap_content"
                        android:drawablePadding="10dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:textSize="16sp"
                        android:drawableStart="@drawable/ic_map_list_view_show_more_arrow_down"
                        android:text="Show More"
                        android:layout_height="wrap_content"></TextView>
                </LinearLayout>


                <TextView
                    android:id="@+id/tv_disclaimer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="left"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="16dp"
                    android:layout_marginBottom="14dp"
                    style="@style/Regular"
                    android:gravity="left"
                    android:textColor="@color/color_gray_dark"
                    android:textSize="14sp"></TextView>


            </LinearLayout>




        </androidx.core.widget.NestedScrollView>



        <com.housesigma.android.views.MaterialProgressBar
            android:id="@+id/progress"
            android:layout_width="match_parent"
            android:layout_height="6dp"
            android:indeterminate="true"
            android:visibility="visible"
            app:backgroundColour="@color/color_gray"
            app:duration="1000"
            app:progressColour="@color/color_cyan_strong" />


        <LinearLayout
            android:id="@+id/ll_no_result_tip"
            android:visibility="gone"
            android:layout_alignParentBottom="true"
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_marginRight="60dp"
            android:layout_marginBottom="30dp"
            android:gravity="center_horizontal"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_marginRight="16dp"
                android:layout_marginLeft="16dp"
                android:background="@drawable/shape_10radius_cyan_color_fill"
                android:orientation="horizontal"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_no_result_tip"
                    style="@style/Body3"
                    android:layout_margin="10dp"
                    android:layout_marginLeft="12dp"
                    android:drawablePadding="10dp"
                    android:textColor="@color/color_dark"
                    android:textSize="14sp"
                    android:drawableLeft="@drawable/ic_map_tip_info"
                    android:layout_width="wrap_content"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"></TextView>

                <TextView
                    android:id="@+id/tv_tip_clear_filters"
                    style="@style/Button2"
                    android:layout_width="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:visibility="visible"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:paddingTop="5dp"
                    android:paddingBottom="5dp"
                    android:layout_marginRight="10dp"
                    android:textColor="@color/color_white"
                    android:background="@drawable/shape_10radius_main_color_fill"
                    android:layout_height="wrap_content"
                    android:text="Clear filters"></TextView>

            </LinearLayout>


        </LinearLayout>

        <TextView
            android:id="@+id/tv_change_map_outer"
            style="@style/Body1"
            android:layout_marginTop="20dp"
            android:layout_width="wrap_content"
            android:layout_gravity="center_horizontal"
            android:paddingLeft="22dp"
            android:paddingRight="22dp"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:visibility="gone"
            android:layout_centerHorizontal="true"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="35dp"
            android:layout_height="wrap_content"
            android:drawableLeft="@drawable/ic_change_map"
            android:background="@drawable/shape_25radius_main_color_fill"
            android:drawablePadding="10dp"
            android:text="Map"
            android:textColor="@color/color_white"
            android:textSize="16sp"></TextView>



        <LinearLayout
            android:layout_marginTop="50dp"
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tv_test_zoom"
                style="@style/H1Header"
                android:textSize="12sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_red_light"></TextView>

            <TextView
                android:id="@+id/tv_test_marker_duration"
                style="@style/H1Header"
                android:textSize="12sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_red_light"></TextView>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_filter_type"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_for_sale"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="38dp"
                android:gravity="center"
                android:elevation="2dp"
                android:background="@drawable/shape_map_left"
                android:paddingLeft="16dp"
                android:paddingTop="4dp"
                android:paddingRight="16dp"
                android:paddingBottom="4dp"
                android:text="For Sale"
                android:textColor="@color/color_gray_dark"
                android:textSize="16sp"></TextView>

            <View
                android:id="@+id/tv_type_divider1"
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/color_gray_a30"></View>

            <TextView
                android:id="@+id/tv_sold"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="38dp"
                android:elevation="2dp"
                android:gravity="center"
                android:background="@drawable/shape_map_center"
                android:paddingLeft="16dp"
                android:paddingTop="4dp"
                android:paddingRight="16dp"
                android:paddingBottom="4dp"
                android:text="Sold"
                android:textColor="@color/color_gray_dark"
                android:textSize="16sp"></TextView>

            <View
                android:id="@+id/tv_type_divider2"
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/color_gray_a30"></View>

            <TextView
                android:id="@+id/tv_delisted"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="38dp"
                android:gravity="center"
                android:elevation="2dp"
                android:background="@drawable/shape_map_right"
                android:paddingLeft="16dp"
                android:paddingTop="4dp"
                android:paddingRight="16dp"
                android:paddingBottom="4dp"
                android:text="De-listed"
                android:textColor="@color/color_gray_dark"
                android:textSize="16sp"></TextView>

        </LinearLayout>

        <ImageView
            android:id="@+id/iv_tool_location"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="30dp"
            android:background="@drawable/ic_map_tool_location"></ImageView>

        <ImageView
            android:id="@+id/iv_tool_settings"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/iv_tool_location"
            android:layout_alignLeft="@id/iv_tool_location"
            android:layout_marginBottom="16dp"
            android:background="@drawable/ic_map_tool_settings"></ImageView>

        <ImageView
            android:id="@+id/iv_tool_school"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/iv_tool_settings"
            android:layout_alignLeft="@id/iv_tool_settings"
            android:layout_marginBottom="16dp"
            android:background="@drawable/ic_map_tool_school"></ImageView>

        <ImageView
            android:id="@+id/iv_tool_list_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/iv_tool_school"
            android:layout_alignLeft="@id/iv_tool_school"
            android:layout_marginBottom="16dp"
            android:background="@drawable/ic_map_tool_list_view"></ImageView>

        <LinearLayout
            android:id="@+id/ll_school_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@color/color_white"
            android:orientation="vertical"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_map_school_name"
                    style="@style/Body2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_horizontal"
                    android:text="Three Valleys Public School"
                    android:textColor="@color/color_black"
                    android:textSize="18sp"></TextView>

                <ImageView
                    android:id="@+id/iv_close_map_school"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:layout_marginRight="8dp"
                    android:padding="8dp"
                    android:src="@drawable/ic_map_listing_close"></ImageView>
            </LinearLayout>

            <View
                android:id="@+id/line"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/color_EBEBEB" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginLeft="8dp"
                    android:layout_marginTop="8dp"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_score"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="-"
                        android:textColor="@color/color_red_light"
                        android:textSize="20sp"></TextView>

                    <TextView
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="Out of 10"
                        android:textColor="@color/color_979797"
                        android:textSize="14sp"></TextView>


                </LinearLayout>

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:background="@color/color_EBEBEB" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="12dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginRight="12dp"
                    android:layout_marginBottom="8dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        style="@style/H2Header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="Basic Information"
                        android:textColor="@color/color_black"
                        android:textSize="16sp"></TextView>

                    <TextView
                        android:id="@+id/tv_address"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Address: 76 Three Valleys Dr, North York, Ontario"
                        android:textColor="@color/color_dark"
                        android:textSize="14sp"></TextView>

                    <TextView
                        android:id="@+id/tv_phone_number"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Phone Number:"
                        android:textColor="@color/color_dark"
                        android:textSize="14sp"></TextView>

                    <TextView
                        android:id="@+id/tv_school_type"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="School Type:"
                        android:textColor="@color/color_dark"
                        android:textSize="14sp"></TextView>

                    <TextView
                        android:id="@+id/tv_school_board"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="School Board:"
                        android:textColor="@color/color_dark"
                        android:textSize="14sp"></TextView>

                    <TextView
                        android:id="@+id/tv_website"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Web Site:"
                        android:textColor="@color/color_dark"
                        android:textSize="14sp"></TextView>

                    <TextView
                        android:id="@+id/tv_score_head"
                        style="@style/H2Header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Academic Performance"
                        android:textColor="@color/color_black"
                        android:textSize="16sp"></TextView>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_score"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/item_map_score" />


                </LinearLayout>


            </LinearLayout>


        </LinearLayout>

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_marginTop="70dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/ll_listings"
                app:behavior_draggable="true"
                app:behavior_hideable="true"
                app:behavior_peekHeight="300dp"
                app:layout_behavior="@string/bottom_sheet_behavior"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_alignParentBottom="true"
                android:background="@drawable/shape_white_login_bg_dialog"
                android:orientation="vertical">

                <ImageView
                    android:layout_marginTop="16dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ic_sheet_bar"></ImageView>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">


                    <TextView
                        android:id="@+id/tv_listings_size"
                        style="@style/H1Header"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_weight="1"
                        android:text="2 Listing in total"
                        android:textColor="@color/color_black"
                        android:textSize="18sp"></TextView>


                    <ImageView
                        android:id="@+id/iv_close_map_listings"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="8dp"
                        android:layout_marginRight="8dp"
                        android:padding="8dp"
                        android:src="@drawable/ic_map_listing_close"></ImageView>
                </LinearLayout>

                <com.housesigma.android.views.MaxHeightRecyclerView
                    android:id="@+id/rv_listings"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxHeight="1200dp">

                </com.housesigma.android.views.MaxHeightRecyclerView>

            </LinearLayout>

        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </RelativeLayout>


</LinearLayout>