<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="@string/account_my_agent"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            style="@style/H1Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="Your HouseSigma Agent"
            android:textColor="@color/color_black"
            android:textSize="18sp"></TextView>

        <RelativeLayout
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/shape_oval_main_color_5_border">

            <ImageView
                android:id="@+id/iv_avatar"
                android:layout_width="140dp"
                android:layout_height="140dp"
                android:layout_centerInParent="true"></ImageView>
        </RelativeLayout>


        <TextView
            android:id="@+id/tv_agent_name"
            style="@style/H1Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="18dp"
            android:layout_marginBottom="10dp"
            android:text="[Agent Name]"
            android:textColor="@color/color_dark"></TextView>

        <TextView
            android:id="@+id/tv_agent_email"
            style="@style/Body1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="[agent_email]"
            android:textColor="@color/color_cyan_strong"></TextView>

        <TextView
            android:id="@+id/tv_agent_phone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="[agent_phone]"
            android:textColor="@color/color_cyan_strong"></TextView>

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/ll_contact_agent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/shape_10radius_main_color_fill"
        android:orientation="horizontal"
        android:paddingTop="16dp"
        android:paddingBottom="16dp">

        <TextView
            style="@style/Button1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:drawablePadding="10dp"
            android:text="@string/contact_agent"
            android:textColor="@color/color_white"
            android:textSize="16sp"></TextView>
    </RelativeLayout>


</LinearLayout>