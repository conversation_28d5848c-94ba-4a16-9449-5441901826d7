<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="@string/profile_title"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/tv_sign_out"
            android:fillViewport="true"
            android:scrollbarThumbVertical="@color/app_main_color"
            android:scrollbars="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ll_premium_account"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingTop="10dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="10dp"
                    android:visibility="gone">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile_premium_account"
                            android:textColor="@color/color_gray_dark"
                            android:textSize="16sp"></TextView>

                        <TextView
                            android:id="@+id/tv_premium_account"
                            style="@style/H2Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/color_dark"
                            android:textSize="16sp"></TextView>


                    </LinearLayout>


                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_sign_in_user"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ripple_bright_rectangle"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingTop="10dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="10dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile_sign_in_user"
                            android:textColor="@color/color_gray_dark"
                            android:textSize="16sp"></TextView>

                        <TextView
                            android:id="@+id/tv_sign_in_user"
                            style="@style/H2Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/color_dark"
                            android:textSize="16sp"></TextView>


                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/ic_account_right"></ImageView>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingTop="10dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="10dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile_registered_on"
                            android:textColor="@color/color_gray_dark"
                            android:textSize="16sp"></TextView>

                        <TextView
                            android:id="@+id/tv_register_time"
                            style="@style/H2Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/color_dark"
                            android:textSize="16sp"></TextView>


                    </LinearLayout>


                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ripple_bright_rectangle"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingTop="10dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="10dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile_name"
                            android:textColor="@color/color_gray_dark"
                            android:textSize="16sp"></TextView>

                        <TextView
                            android:id="@+id/tv_name"
                            style="@style/H2Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/color_dark"
                            android:textSize="16sp"></TextView>


                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/ic_account_right"></ImageView>

                </LinearLayout>


                <LinearLayout
                    android:id="@+id/ll_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ripple_bright_rectangle"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingTop="10dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="10dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile_email"
                            android:textColor="@color/color_gray_dark"
                            android:textSize="16sp"></TextView>

                        <TextView
                            android:id="@+id/tv_email"
                            style="@style/H2Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/color_dark"
                            android:textSize="16sp"></TextView>


                    </LinearLayout>


                    <ImageView
                        android:id="@+id/iv_del_email"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:paddingLeft="10dp"
                        android:paddingTop="10dp"
                        android:paddingRight="24dp"
                        android:paddingBottom="10dp"
                        android:src="@drawable/ic_my_profile_del"
                        android:visibility="gone"></ImageView>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/ic_account_right"></ImageView>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_phone_number"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ripple_bright_rectangle"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingTop="10dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="10dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile_phone_number"
                            android:textColor="@color/color_gray_dark"
                            android:textSize="16sp"></TextView>

                        <TextView
                            android:id="@+id/tv_phone_number"
                            style="@style/H2Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/color_dark"
                            android:textSize="16sp"></TextView>


                    </LinearLayout>

                    <ImageView
                        android:id="@+id/iv_del_phone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:paddingLeft="10dp"
                        android:paddingTop="10dp"
                        android:paddingRight="24dp"
                        android:paddingBottom="10dp"
                        android:src="@drawable/ic_my_profile_del"
                        android:visibility="gone"></ImageView>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/ic_account_right"></ImageView>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ripple_bright_rectangle"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingTop="20dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="15dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">


                        <TextView
                            style="@style/H2Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile_password"
                            android:textColor="@color/color_dark"
                            android:textSize="16sp"></TextView>


                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/ic_account_right"></ImageView>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_notification"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ripple_bright_rectangle"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingTop="20dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="15dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">


                        <TextView
                            style="@style/H2Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile_notification"
                            android:textColor="@color/color_dark"
                            android:textSize="16sp"></TextView>


                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/ic_account_right"></ImageView>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_referral_code"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ripple_bright_rectangle"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingTop="20dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="15dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">


                        <TextView
                            style="@style/H2Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/profile_referral_code"
                            android:textColor="@color/color_dark"
                            android:textSize="16sp"></TextView>


                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/ic_account_right"></ImageView>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_agent_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ripple_bright_rectangle"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingTop="20dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="15dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">


                        <TextView
                            style="@style/H2Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="I am a real estate agent"
                            android:textColor="@color/color_dark"
                            android:textSize="16sp"></TextView>


                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/ic_account_right"></ImageView>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_del_my_account"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ripple_bright_rectangle"
                    android:orientation="horizontal"
                    android:paddingLeft="16dp"
                    android:paddingTop="20dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="15dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">


                        <TextView
                            style="@style/H2Header"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Delete my account"
                            android:textColor="@color/color_dark"
                            android:textSize="16sp"></TextView>


                    </LinearLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/ic_account_right"></ImageView>

                </LinearLayout>


            </LinearLayout>
        </ScrollView>

        <TextView
            android:id="@+id/tv_sign_out"
            style="@style/Button1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="35dp"
            android:background="@drawable/shape_5radius_main_color_fill"
            android:gravity="center"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Sign out"
            android:textColor="@color/color_white"
            android:textSize="16sp"></TextView>
    </RelativeLayout>


</LinearLayout>