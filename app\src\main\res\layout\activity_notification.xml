<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="Notification"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <TextView
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="8dp"
                android:text="Email"
                android:textColor="@color/color_dark"></TextView>

            <TextView
                android:id="@+id/tv_email"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="no email provided"
                android:textColor="@color/color_dark"></TextView>
        </LinearLayout>


        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_recommend"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@android:color/transparent"
            android:button="@null"
            android:buttonTint="@color/color_black"
            android:drawableRight="?android:attr/listChoiceIndicatorMultiple"
            android:paddingLeft="16dp"
            android:paddingTop="8dp"
            android:paddingRight="16dp"
            android:paddingBottom="8dp"
            android:text="Recommend"
            android:textColor="@color/color_black"
            android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_watched_listing"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:button="@null"
            android:drawableRight="?android:attr/listChoiceIndicatorMultiple"
            android:paddingLeft="16dp"
            android:paddingTop="8dp"
            android:paddingRight="16dp"
            android:paddingBottom="8dp"
            android:text="Watched property"
            android:textColor="@color/color_black"
            android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_watched_community"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:button="@null"
            android:drawableRight="?android:attr/listChoiceIndicatorMultiple"
            android:paddingLeft="16dp"
            android:paddingTop="8dp"
            android:paddingRight="16dp"
            android:paddingBottom="8dp"
            android:text="Watched community"
            android:textColor="@color/color_black"
            android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_watched_area"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:button="@null"
            android:drawableRight="?android:attr/listChoiceIndicatorMultiple"
            android:paddingLeft="16dp"
            android:paddingTop="8dp"
            android:paddingRight="16dp"
            android:paddingBottom="8dp"
            android:text="Watched area"
            android:textColor="@color/color_black"
            android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>


        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_recommend_disable"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@android:color/transparent"
            android:button="@null"
            android:buttonTint="@color/color_black"
            android:clickable="false"
            android:drawableRight="?android:attr/listChoiceIndicatorMultiple"
            android:paddingLeft="16dp"
            android:paddingTop="8dp"
            android:paddingRight="16dp"
            android:paddingBottom="8dp"
            android:text="Recommend"
            android:textColor="@color/color_gray_dark"
            android:theme="@style/MyDisableCheckBox"
            android:visibility="gone"></androidx.appcompat.widget.AppCompatCheckBox>

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_watched_listing_disable"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:button="@null"
            android:clickable="false"
            android:drawableRight="?android:attr/listChoiceIndicatorMultiple"
            android:paddingLeft="16dp"
            android:paddingTop="8dp"
            android:paddingRight="16dp"
            android:paddingBottom="8dp"
            android:text="Watched property"
            android:textColor="@color/color_gray_dark"
            android:theme="@style/MyDisableCheckBox"
            android:visibility="gone"></androidx.appcompat.widget.AppCompatCheckBox>

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_watched_community_disable"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:button="@null"
            android:clickable="false"
            android:drawableRight="?android:attr/listChoiceIndicatorMultiple"
            android:paddingLeft="16dp"
            android:paddingTop="8dp"
            android:paddingRight="16dp"
            android:paddingBottom="8dp"
            android:text="Watched community"
            android:textColor="@color/color_gray_dark"
            android:theme="@style/MyDisableCheckBox"
            android:visibility="gone"></androidx.appcompat.widget.AppCompatCheckBox>

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_watched_area_disable"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:button="@null"
            android:clickable="false"
            android:drawableRight="?android:attr/listChoiceIndicatorMultiple"
            android:paddingLeft="16dp"
            android:paddingTop="8dp"
            android:paddingRight="16dp"
            android:paddingBottom="8dp"
            android:text="Watched area"
            android:textColor="@color/color_gray_dark"
            android:theme="@style/MyDisableCheckBox"
            android:visibility="gone"></androidx.appcompat.widget.AppCompatCheckBox>


        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="16dp"
            android:background="@color/color_divider_style2"></View>

        <TextView
            style="@style/H1Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="8dp"
            android:text="Push Notification"
            android:textColor="@color/color_dark"></TextView>


        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_watched_listing_push"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@android:color/transparent"
            android:button="@null"
            android:drawableRight="?android:attr/listChoiceIndicatorMultiple"
            android:paddingLeft="16dp"
            android:paddingTop="8dp"
            android:paddingRight="16dp"
            android:paddingBottom="8dp"
            android:text="Watched property"
            android:textColor="@color/color_black"
            android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="11dp"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:layout_marginTop="6dp"
                android:layout_marginRight="6dp"
                android:background="@drawable/ic_notification_notice"></ImageView>

            <TextView
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="This changes how you would like to receive notifications. You can change receiving email in the &quot;contact email&quot; user setting."
                android:textColor="@color/color_dark"
                android:textSize="14sp"></TextView>

        </LinearLayout>


    </LinearLayout>


</LinearLayout>