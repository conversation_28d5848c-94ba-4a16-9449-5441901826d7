<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="Personalize Listings"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

    </RelativeLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="16dp"
            android:orientation="vertical">

            <TextView
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Price range"
                android:textColor="@color/color_dark"
                android:textSize="18sp"></TextView>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_price"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="$0 - Max"
                    android:textColor="@color/color_black"
                    android:textSize="16sp"></TextView>

            </LinearLayout>

            <com.housesigma.android.views.SaleSeekBar
                android:id="@+id/sale_sb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"></com.housesigma.android.views.SaleSeekBar>

            <TextView
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:text="Property type"
                android:textColor="@color/color_dark"
                android:textSize="18sp"></TextView>

            <com.donkingliang.labels.LabelsView
                app:labelTextSize="16sp"
                android:id="@+id/labels"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                app:labelBackground="@drawable/label_bg"
                app:labelTextColor="@drawable/label_text_color"
                app:lineMargin="10dp"
                app:wordMargin="20dp"></com.donkingliang.labels.LabelsView>

            <TextView
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:text="City"
                android:textColor="@color/color_dark"
                android:textSize="18sp"></TextView>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />


        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_clear"
            style="@style/Button1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="17dp"
            android:layout_marginRight="17dp"
            android:layout_weight="1"
            android:background="@drawable/shape_10radius_main_color"
            android:gravity="center_horizontal"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Clear"
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>

        <TextView
            android:id="@+id/tv_save"
            style="@style/Button1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="17dp"
            android:layout_marginRight="17dp"
            android:layout_weight="1"
            android:background="@drawable/shape_10radius_main_color_fill"
            android:gravity="center_horizontal"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Save"
            android:textColor="@color/color_white"
            android:textSize="16sp"></TextView>

    </LinearLayout>
</LinearLayout>