<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:padding="16dp"
            android:src="@drawable/ic_close"></ImageView>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/shape_serach">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:paddingLeft="16dp"
                android:paddingTop="16dp"
                android:paddingRight="8dp"
                android:paddingBottom="16dp"
                android:src="@drawable/ic_home_search"></ImageView>

            <EditText
                android:id="@+id/et_search_term"
                style="@style/PlaceHolder"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:hint="@string/search_search_hint"
                android:imeOptions="actionDone"
                android:inputType="text"
                android:lines="1"
                android:maxLines="1"
                android:paddingRight="6dp"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray_dark"
                android:textSize="16sp"></EditText>

            <ImageView
                android:id="@+id/iv_del"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:layout_gravity="center_vertical"
                android:paddingLeft="14dp"
                android:paddingTop="8dp"
                android:paddingRight="14dp"
                android:paddingBottom="8dp"
                android:src="@drawable/ic_serach_del"
                android:visibility="gone"></ImageView>
        </LinearLayout>


    </LinearLayout>


    <RelativeLayout
        android:id="@+id/rl_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_search_history"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            android:paddingLeft="16dp"
            android:paddingRight="16dp" />


    </RelativeLayout>


    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_open_in_map"
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="5dp"
                android:layout_weight="1"
                android:text="@string/search_locations"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_open_in_map"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                android:paddingLeft="16dp"
                android:paddingRight="16dp" />


            <TextView
                android:id="@+id/tv_preconstruction"
                style="@style/H1Header"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="6dp"
                android:layout_weight="1"
                android:text="@string/search_preconstruction"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_preconstruction"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                android:paddingLeft="16dp"
                android:paddingRight="16dp" />

            <View
                android:id="@+id/view_precon_line"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:layout_height="1dp"
                android:background="@color/color_divider_style2"></View>

            <LinearLayout
                android:visibility="gone"
                android:id="@+id/ll_list_view_show_more_municipalities"
                android:layout_width="match_parent"
                android:gravity="center_horizontal"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="5dp"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_list_view_show_more_municipalities"
                    style="@style/Button1"
                    android:textColor="@color/app_main_color"
                    android:layout_width="wrap_content"
                    android:drawablePadding="10dp"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:textSize="16sp"
                    android:drawableStart="@drawable/ic_map_list_view_show_more_arrow_down"
                    android:text="Show More"
                    android:layout_height="wrap_content"></TextView>
            </LinearLayout>





            <TextView
                android:id="@+id/tv_listings"
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="5dp"
                android:layout_weight="1"
                android:text="@string/search_listings"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_listings"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                android:paddingLeft="16dp"
                android:paddingRight="16dp" />


            <TextView
                android:id="@+id/tv_community"
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="5dp"
                android:layout_weight="1"
                android:text="@string/search_community_market_trends"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_community"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                android:paddingLeft="16dp"
                android:paddingRight="16dp" />

            <TextView
                android:id="@+id/tv_disclaimer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="14dp"
                style="@style/Regular"
                android:gravity="left"
                android:textColor="@color/color_gray_dark"
                android:textSize="14sp"></TextView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>


</LinearLayout>