<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@drawable/ic_home_logo"></ImageView>


    </RelativeLayout>

    <ScrollView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fadeScrollbars="false"
        android:scrollbarThumbVertical="@color/app_main_color">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="33dp"
                android:background="@drawable/ic_signup_step1"></ImageView>


            <TextView
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="40dp"
                android:text="Create an Account"
                android:textColor="@color/app_main_color"
                android:textSize="18sp"></TextView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:orientation="horizontal"
                android:baselineAligned="false">

                <LinearLayout
                    android:id="@+id/ll_email"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_email"
                        style="@style/H2Header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="6dp"
                        android:text="Email"
                        android:textColor="@color/app_main_color"
                        android:textSize="16sp"></TextView>

                    <View
                        android:id="@+id/v_line_email"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/app_main_color"></View>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_phone"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_phone"
                        style="@style/H2Header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="6dp"
                        android:text="Mobile Phone"
                        android:textColor="@color/color_black"
                        android:textSize="16sp"></TextView>

                    <View
                        android:id="@+id/v_line_phone"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/color_cccccc"></View>

                </LinearLayout>
            </LinearLayout>

            <EditText
                android:id="@+id/et_name"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="17dp"
                android:background="@drawable/shape_btn_gray"
                android:gravity="left"
                android:hint="Full Name"
                android:inputType="textPersonName"
                android:lines="1"
                android:maxLines="1"
                android:paddingLeft="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray"
                android:textSize="16sp"></EditText>

            <EditText
                android:id="@+id/et_email"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="17dp"
                android:background="@drawable/shape_btn_gray"
                android:gravity="left"
                android:hint="Enter your Email"
                android:inputType="textEmailAddress"
                android:lines="1"
                android:maxLines="1"
                android:paddingLeft="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray"
                android:textSize="16sp"></EditText>

            <LinearLayout
                android:id="@+id/ll_input_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="17dp"
                android:background="@drawable/shape_btn_gray"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_country_code"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:drawableRight="@drawable/ic_signup_down"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:paddingLeft="16dp"
                    android:paddingRight="10dp"
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>

                <View
                    android:layout_width="1dp"
                    android:layout_height="36dp"
                    android:layout_gravity="center_vertical"
                    android:background="@color/color_gray"></View>

                <EditText
                    android:id="@+id/et_phone"
                    style="@style/Body1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="left"
                    android:hint="Enter your Mobile Phone"
                    android:inputType="phone"
                    android:lines="1"
                    android:maxLines="1"
                    android:paddingLeft="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:textColor="@color/color_black"
                    android:textColorHint="@color/color_gray"
                    android:textSize="16sp"></EditText>


            </LinearLayout>


            <EditText
                android:id="@+id/et_password"
                style="@style/PlaceHolder"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="17dp"
                android:background="@drawable/shape_btn_gray"
                android:gravity="left"
                android:hint="Enter a password"
                android:inputType="textPassword"
                android:lines="1"
                android:maxLines="1"
                android:paddingLeft="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray"
                android:textSize="16sp"></EditText>

            <TextView
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="17dp"
                android:layout_marginBottom="10dp"
                android:text="Passwords must consist of at least 6 characteres.
Passwords must consist 2 of: Alphabet, Number digit, Special character."
                android:textColor="@color/color_dark"
                android:textSize="14sp"></TextView>

            <TextView
                android:id="@+id/tv_next"
                style="@style/Button1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17dp"
                android:layout_marginRight="17dp"
                android:background="@drawable/shape_10radius_main_color_fill"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="Next"
                android:textColor="@color/color_white"
                android:textSize="16sp"></TextView>


            <LinearLayout
                android:id="@+id/ll_or"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="17dp"
                    android:layout_marginRight="24dp"
                    android:layout_weight="1"
                    android:background="@color/color_gray"></View>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Or"
                    android:textColor="@color/color_gray"></TextView>

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="24dp"
                    android:layout_marginRight="17dp"
                    android:layout_weight="1"
                    android:background="@color/color_gray"></View>

            </LinearLayout>

            <RelativeLayout
                android:id="@+id/rl_sign_in_google"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="16dp"
                android:background="@drawable/shape_10radius_main_color">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="30dp"
                    android:background="@drawable/ic_login_google"></ImageView>

                <TextView
                    style="@style/Button1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:text="Sign up with Google"
                    android:textColor="@color/app_main_color"></TextView>

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:layout_marginBottom="30dp"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <TextView
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Already registered? "
                    android:textColor="@color/color_black"
                    android:textSize="16sp"></TextView>

                <TextView
                    android:id="@+id/tv_sign_in"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Sign in"
                    android:textColor="@color/app_main_color"
                    android:textSize="16sp"></TextView>


            </LinearLayout>
        </LinearLayout>

    </ScrollView>

</LinearLayout>