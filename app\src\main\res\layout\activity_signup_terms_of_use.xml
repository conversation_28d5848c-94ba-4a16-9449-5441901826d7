<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@drawable/ic_home_logo"></ImageView>


    </RelativeLayout>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="33dp"
        android:background="@drawable/ic_signup_step2"></ImageView>



    <ScrollView
        android:layout_marginTop="18dp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:layout_weight="1"
        android:background="@drawable/shape_term"
        android:fadeScrollbars="false"
        android:scrollbarThumbVertical="@color/app_main_color">

        <TextView
            android:id="@+id/tv_terms"
            style="@style/Body2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="8dp"
            android:paddingTop="12dp"
            android:paddingRight="8dp"
            android:text=""
            android:textColor="@color/color_black"
            android:textSize="16sp"></TextView>

    </ScrollView>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="8dp"
        android:layout_weight="1"
        android:background="@drawable/shape_term"
        android:fadeScrollbars="false"
        android:scrollbarThumbVertical="@color/app_main_color">

        <TextView
            android:id="@+id/tv_terms2"
            style="@style/Body2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="8dp"
            android:paddingTop="12dp"
            android:paddingRight="8dp"
            android:text=""
            android:textColor="@color/color_black"
            android:textSize="16sp"></TextView>

    </ScrollView>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="17dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="17dp"
        android:text="Mandatory disclosure, failure to disclose will result in computer fraud  trespass"
        android:textColor="@color/color_black"
        android:textSize="14sp"
        android:visibility="gone"></TextView>


    <TextView
        style="@style/Subtitles2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="26dp"
        android:text="By clicking agree, you agree to our terms of use and privacy agreement"
        android:textColor="@color/color_dark"></TextView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_reject"
            style="@style/Button1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_weight="1"
            android:background="@drawable/shape_10radius_main_color"
            android:gravity="center_horizontal"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Reject"
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>

        <TextView
            android:id="@+id/tv_accept"
            style="@style/Button1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_weight="1"
            android:background="@drawable/shape_10radius_main_color_fill"
            android:gravity="center_horizontal"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Accept"
            android:textColor="@color/color_white"
            android:textSize="16sp"></TextView>

    </LinearLayout>

</LinearLayout>