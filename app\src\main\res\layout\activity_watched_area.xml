<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <RelativeLayout
        android:id="@+id/rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="Watched Area"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

    </RelativeLayout>


    <View
        android:id="@+id/v_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#C4C4C4"></View>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <org.maplibre.android.maps.MapView
            android:id="@+id/mapView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />


        <LinearLayout
            android:id="@+id/ll_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_watched_area_tip"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="10dp">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ic_watched_drag_tip" />

                <TextView
                    android:id="@android:id/message"
                    style="@style/H2Header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:text="Fit shape to desired area"
                    android:textColor="@color/app_main_color"
                    android:textSize="16sp" />
            </LinearLayout>


        </LinearLayout>

        <ImageView
            android:id="@+id/iv_tool_zoom_min"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="30dp"
            android:background="@drawable/ic_watchedarea_zoom_min"></ImageView>

        <ImageView
            android:id="@+id/iv_tool_zoom_plus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/iv_tool_zoom_min"
            android:layout_alignLeft="@id/iv_tool_zoom_min"
            android:layout_marginBottom="16dp"
            android:background="@drawable/ic_watchedarea_zoom_plus"></ImageView>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="30dp"
            android:layout_toLeftOf="@+id/iv_tool_zoom_min"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_cancel"
                style="@style/Button1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_weight="1"
                android:background="@drawable/shape_10radius_main_color"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="Cancel"
                android:textColor="@color/app_main_color"
                android:textSize="16sp"></TextView>

            <TextView
                android:id="@+id/tv_done"
                style="@style/Button1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:layout_weight="1"
                android:background="@drawable/shape_10radius_main_color_fill"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="Done"
                android:textColor="@color/color_white"
                android:textSize="16sp"></TextView>

        </LinearLayout>

    </RelativeLayout>


</LinearLayout>