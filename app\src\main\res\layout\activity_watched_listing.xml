<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"></ImageView>


        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="Watchlist"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

        <ImageView
            android:id="@+id/iv_share"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_webview_share"
            android:visibility="gone"></ImageView>

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingLeft="16dp">

            <TextView
                android:id="@+id/tv_watchlist_name"
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginBottom="3dp"
                android:layout_weight="1"
                android:textColor="@color/color_dark"></TextView>

            <TextView
                android:id="@+id/tv_owner_name"
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_gray_dark"
                android:textSize="14sp"></TextView>

            <TextView
                android:id="@+id/tv_updated_time"
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:textColor="@color/color_gray_dark"
                android:textSize="14sp"></TextView>

        </LinearLayout>


        <TextView
            android:id="@+id/tv_follow"
            style="@style/Button1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_gravity="center"
            android:layout_marginRight="22dp"
            android:background="@drawable/shape_10radius_main_color"
            android:gravity="center"
            android:paddingLeft="26dp"
            android:paddingTop="6dp"
            android:paddingRight="26dp"
            android:paddingBottom="6dp"
            android:text="Follow"
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>


    </LinearLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:id="@+id/sr_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">


            <LinearLayout
                android:id="@+id/ll_listing_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_for_sale"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="38dp"
                    android:background="@drawable/shape_watch_left_selected"
                    android:gravity="center"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:text="For Sale"
                    android:textColor="@color/color_white"
                    android:textSize="16sp"></TextView>

                <TextView
                    android:id="@+id/tv_sold"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="38dp"
                    android:layout_marginLeft="-1dp"
                    android:background="@drawable/shape_watch_center_selected"
                    android:gravity="center"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:text="Sold"
                    android:textColor="@color/color_white"
                    android:textSize="16sp"></TextView>

                <TextView
                    android:id="@+id/tv_de_listed"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="38dp"
                    android:layout_marginLeft="-1dp"
                    android:background="@drawable/shape_watch_right_selected"
                    android:gravity="center"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:text="De-listed"
                    android:textColor="@color/color_white"
                    android:textSize="16sp"></TextView>

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="16dp" />

        </LinearLayout>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</LinearLayout>