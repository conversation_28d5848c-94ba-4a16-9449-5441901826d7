<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_dialog_location_choose"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:background="@drawable/shape_white_10_corners_dialog"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            style="@style/H1Header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Add Email Later"
            android:gravity="center_horizontal"
            android:textColor="@color/color_dark"
            android:layout_marginBottom="20dp"
            android:textSize="18sp"></TextView>


        <TextView
            android:id="@+id/tv_content"
            style="@style/Body2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="16dp"
            android:text="Add your email at any time by going to your account settings and adding it to
your profile."
            android:layout_marginRight="16dp"
            android:gravity="left"
            android:textColor="@color/color_dark"
            android:textSize="16sp"></TextView>

        <LinearLayout
            android:layout_marginTop="20dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_okay"
                style="@style/Button1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="17dp"
                android:layout_marginRight="17dp"
                android:layout_weight="1"
                android:background="@drawable/shape_10radius_main_color_fill"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="Okay"
                android:textColor="@color/color_white"
                android:textSize="16sp"></TextView>

            <TextView
                android:id="@+id/tv_go_back_and_add_email_now"
                style="@style/Body2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="17dp"
                android:layout_marginRight="17dp"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="Go back and add email now"
                android:textColor="@color/app_main_color"
                android:textSize="16sp"></TextView>

        </LinearLayout>


    </LinearLayout>
</LinearLayout>