<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_login_bg_dialog"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_contact_us_title"
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="30dp"
            android:text="@string/schedule_viewing"
            android:textColor="@color/color_black"
            android:textSize="18sp"></TextView>

        <TextView
            android:id="@+id/tv_contact_us_subtitle"
            style="@style/Subtitles2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="Tour with Housesigma Agent"
            android:textColor="@color/color_gray"
            android:textSize="14sp"></TextView>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="16dp">

            <EditText
                android:id="@+id/et_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_5radius_gray_color"
                android:gravity="left"
                android:inputType="textPersonName"
                android:lines="1"
                android:padding="16dp"
                android:textColor="@color/color_black"
                android:textSize="16sp"></EditText>

            <TextView
                style="@style/Regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="26dp"
                android:layout_marginTop="7dp"
                android:background="@color/color_white"
                android:paddingLeft="4dp"
                android:paddingRight="4dp"
                android:text="Your Name *"
                android:textColor="@color/color_gray_dark"
                android:textSize="14sp"></TextView>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="14dp"
            android:layout_marginRight="16dp">

            <EditText
                android:id="@+id/et_contact_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_5radius_gray_color"
                android:gravity="left"
                android:inputType="phone"
                android:lines="1"
                android:padding="16dp"
                android:textColor="@color/color_black"
                android:textSize="16sp"></EditText>

            <TextView
                style="@style/Regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="26dp"
                android:layout_marginTop="7dp"
                android:background="@color/color_white"
                android:paddingLeft="4dp"
                android:paddingRight="4dp"
                android:text="Your Contact Number*"
                android:textColor="@color/color_gray_dark"
                android:textSize="14sp"></TextView>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="14dp"
            android:layout_marginRight="16dp">

            <EditText
                android:id="@+id/et_contact_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_5radius_gray_color"
                android:gravity="left"
                android:inputType="textEmailAddress"
                android:lines="1"
                android:padding="16dp"
                android:textColor="@color/color_black"
                android:textSize="16sp"></EditText>

            <TextView
                style="@style/Regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="26dp"
                android:layout_marginTop="7dp"
                android:background="@color/color_white"
                android:paddingLeft="4dp"
                android:paddingRight="4dp"
                android:text="Email*"
                android:textColor="@color/color_gray_dark"
                android:textSize="14sp"></TextView>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="14dp"
            android:layout_marginRight="16dp">

            <EditText
                android:id="@+id/et_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_5radius_gray_color"
                android:gravity="left"
                android:inputType="textMultiLine"
                android:lines="3"
                android:padding="16dp"
                android:textColor="@color/color_black"
                android:textSize="16sp"></EditText>

            <TextView
                style="@style/Regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="26dp"
                android:layout_marginTop="7dp"
                android:background="@color/color_white"
                android:paddingLeft="4dp"
                android:paddingRight="4dp"
                android:text="Message*"
                android:textColor="@color/color_gray_dark"
                android:textSize="14sp"></TextView>

        </RelativeLayout>

        <TextView
            style="@style/Regular"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="10dp"
            android:background="@color/color_white"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:text="* Required field"
            android:textColor="@color/color_gray_dark"
            android:textSize="14sp"></TextView>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatCheckBox
                android:id="@+id/cb_tour_via_video_chat"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:lines="1"
                android:maxLines="1"
                android:paddingTop="6dp"
                android:paddingRight="10dp"
                android:paddingBottom="6dp"
                android:text="Tour Via Video Chat"
                android:textColor="@color/color_dark"
                android:textSize="16sp"
                android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="10dp"
                android:src="@drawable/ic_zoom"></ImageView>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:src="@drawable/ic_whats"></ImageView>
        </LinearLayout>


    </LinearLayout>

    <TextView
        android:id="@+id/tv_submit"
        style="@style/Button1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/shape_10radius_main_color_fill"
        android:gravity="center_horizontal"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:text="Submit Request"
        android:textColor="@color/color_white"
        android:textSize="16sp"></TextView>


</LinearLayout>