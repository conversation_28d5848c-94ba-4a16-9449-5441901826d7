<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_login_bg_dialog"
    android:orientation="vertical">


    <LinearLayout
        android:layout_marginTop="20dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_contact_us_title"
            style="@style/H1Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="HouseSigma Agent"
            android:textColor="@color/color_black"
            android:textSize="18sp"></TextView>

        <TextView
            android:id="@+id/tv_contact_us_subtitle"
            style="@style/Body1"
            android:layout_width="wrap_content"
            android:gravity="center"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="4dp"
            android:text="Housesigma Agent"
            android:textColor="@color/color_gray"></TextView>

        <FrameLayout
            android:id="@+id/webview_container"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:layout_height="55dp" />

        <LinearLayout
            android:id="@+id/ll_agent_info"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_agent_avatar"
                android:layout_width="73dp"
                android:layout_height="73dp"
                android:layout_marginRight="26dp"
                android:background="@drawable/shape_oval_main_color_2_border">

                <ImageView
                    android:id="@+id/iv_avatar"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:layout_centerInParent="true"></ImageView>
            </RelativeLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_contact_us_agent_title"
                    style="@style/H1Header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:text="HouseSigma Agent"
                    android:textColor="@color/color_black"
                    android:textSize="18sp"></TextView>

                <TextView
                    android:id="@+id/tv_contact_us_agent_name"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:gravity="center"
                    android:layout_height="wrap_content"
                    android:maxWidth="200dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="4dp"
                    android:text="Housesigma Agent"
                    android:textColor="@color/color_dark"></TextView>
            </LinearLayout>
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="16dp">

            <ImageView
                android:id="@+id/iv_agent_name"
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:layout_alignParentLeft="true"
                android:background="@drawable/shape_5radius_gray_color_left"
                android:padding="14dp"
                android:src="@drawable/ic_agent_name"></ImageView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="-2dp"
                android:background="@drawable/shape_5radius_gray_color_right"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    android:gravity="left"
                    android:hint="Your Name*"
                    style="@style/PlaceHolder"
                    android:inputType="textPersonName"
                    android:lines="1"
                    android:padding="16dp"
                    android:textColor="@color/color_black"
                    android:textColorHint="@color/color_gray"
                    android:textSize="16sp"></EditText>

                <ImageView
                    android:id="@+id/iv_del_name"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="14dp"
                    android:paddingTop="8dp"
                    android:paddingRight="14dp"
                    android:paddingBottom="8dp"
                    android:src="@drawable/ic_serach_del"
                    android:visibility="gone"></ImageView>

            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="16dp">

            <ImageView
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:layout_alignParentLeft="true"
                android:background="@drawable/shape_5radius_gray_color_left"
                android:padding="14dp"
                android:src="@drawable/ic_agent_phone"></ImageView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="-2dp"
                android:background="@drawable/shape_5radius_gray_color_right"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_contact_phone"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    android:gravity="left"
                    android:hint="Your Contact Number*"
                    style="@style/PlaceHolder"
                    android:inputType="phone"
                    android:lines="1"
                    android:padding="16dp"
                    android:textColor="@color/color_black"
                    android:textColorHint="@color/color_gray"
                    android:textSize="16sp"></EditText>

                <ImageView
                    android:id="@+id/iv_del_phone"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="14dp"
                    android:paddingTop="8dp"
                    android:paddingRight="14dp"
                    android:paddingBottom="8dp"
                    android:src="@drawable/ic_serach_del"
                    android:visibility="gone"></ImageView>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="16dp">

            <ImageView
                android:layout_width="50dp"
                android:layout_height="match_parent"
                android:layout_alignParentLeft="true"
                android:background="@drawable/shape_5radius_gray_color_left"
                android:padding="14dp"
                android:src="@drawable/ic_agent_email"></ImageView>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="-2dp"
                android:background="@drawable/shape_5radius_gray_color_right"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_contact_email"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    android:gravity="left"
                    android:hint="Your Email Address*"
                    style="@style/PlaceHolder"
                    android:inputType="textEmailAddress"
                    android:lines="1"
                    android:padding="16dp"
                    android:textColor="@color/color_black"
                    android:textColorHint="@color/color_gray"
                    android:textSize="16sp"></EditText>

                <ImageView
                    android:id="@+id/iv_del_email"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="14dp"
                    android:paddingTop="8dp"
                    android:paddingRight="14dp"
                    android:paddingBottom="8dp"
                    android:src="@drawable/ic_serach_del"
                    android:visibility="gone"></ImageView>

            </LinearLayout>

        </LinearLayout>


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="14dp"
            android:layout_marginRight="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_5radius_gray_color"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_message"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    style="@style/PlaceHolder"
                    android:gravity="left"
                    android:inputType="textMultiLine"
                    android:hint="Your message"
                    android:lines="3"
                    android:padding="16dp"
                    android:textColor="@color/color_black"
                    android:textColorHint="@color/color_gray"
                    android:textSize="16sp"></EditText>

                <ImageView
                    android:id="@+id/iv_del_message"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="14dp"
                    android:paddingTop="8dp"
                    android:paddingRight="14dp"
                    android:paddingBottom="8dp"
                    android:src="@drawable/ic_serach_del"></ImageView>

            </LinearLayout>


        </RelativeLayout>


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            android:visibility="gone">

            <androidx.appcompat.widget.AppCompatCheckBox
                android:id="@+id/cb_tour_via_video_chat"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:lines="1"
                android:maxLines="1"
                android:paddingTop="6dp"
                android:paddingRight="10dp"
                android:paddingBottom="6dp"
                android:text="Tour Via Video Chat"
                android:textColor="@color/color_dark"
                android:textSize="16sp"
                android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="10dp"
                android:src="@drawable/ic_zoom"></ImageView>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:src="@drawable/ic_whats"></ImageView>
        </LinearLayout>


    </LinearLayout>

    <TextView
        android:id="@+id/tv_submit"
        style="@style/Button1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/shape_10radius_main_color_fill"
        android:gravity="center_horizontal"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:text="@string/contact_agent"
        android:textColor="@color/color_white"
        android:textSize="16sp"></TextView>


</LinearLayout>