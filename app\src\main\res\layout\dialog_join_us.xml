<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_login_bg_dialog"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_contact_us_title"
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="30dp"
            android:text="Contact HouseSigma Agent"
            android:textColor="@color/color_black"
            android:textSize="18sp"></TextView>

        <TextView
            android:id="@+id/tv_tour_with"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:text="Tour with HouseSigma Agent"
            android:textColor="@color/color_gray"
            android:textSize="14sp"></TextView>

        <FrameLayout
            android:id="@+id/webview_container"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="55dp" />

        <TextView
            android:id="@+id/tv_join_us_intro"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="16dp"
            android:text=""
            android:textColor="@color/color_dark"
            android:textSize="16sp"></TextView>


    </LinearLayout>


    <RelativeLayout
        android:id="@+id/tv_join_us"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/shape_10radius_main_color_fill"
        android:orientation="horizontal"
        android:paddingTop="16dp"
        android:paddingBottom="16dp">

        <TextView
            style="@style/Button1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:drawableLeft="@drawable/ic_join_us"
            android:drawablePadding="10dp"
            android:text="Join Us"
            android:textColor="@color/color_white"
            android:textSize="16sp"></TextView>
    </RelativeLayout>


</LinearLayout>