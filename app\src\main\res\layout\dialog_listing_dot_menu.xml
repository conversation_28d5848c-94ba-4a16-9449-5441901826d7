<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_login_bg_dialog"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="30dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="30dp">

            <TextView
                android:drawablePadding="8dp"
                android:drawableLeft="@drawable/ic_mark_not_interested"
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="Mark as Not Interested"
                android:textColor="@color/color_dark"
                android:textSize="18sp"></TextView>


            <com.housesigma.android.views.SwitchButton
                android:id="@+id/sb_not_interested"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"></com.housesigma.android.views.SwitchButton>

        </RelativeLayout>


    </LinearLayout>


</RelativeLayout>