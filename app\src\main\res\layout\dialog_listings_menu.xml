<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_15_corners_top"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="14dp"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_delete"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/ripple_bright_rectangle"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_margin="18dp"
                android:background="@drawable/ic_listings_menu_del"></ImageView>

            <TextView
                android:id="@+id/tv_remove_watchlist_name"
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Remove from [watchlist]"
                android:textColor="@color/color_dark"></TextView>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_save_to_watchlist"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:background="@drawable/ripple_bright_rectangle"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_margin="18dp"
                android:background="@drawable/ic_listings_menu_edit"></ImageView>

            <TextView
                android:id="@+id/tv_save_to_watchlist"
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Save to Watchlist"
                android:textColor="@color/color_dark"></TextView>
        </LinearLayout>

    </LinearLayout>


</LinearLayout>