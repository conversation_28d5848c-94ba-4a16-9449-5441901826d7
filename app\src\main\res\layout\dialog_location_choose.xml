<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_dialog_location_choose"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:background="@drawable/shape_10radiuis_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            style="@style/H1Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:layout_marginBottom="28dp"
            android:text="Choose your location"
            android:textColor="@color/color_black"
            android:textSize="18sp"></TextView>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fadeScrollbars="false"
            android:scrollbarSize="2dp"
            android:scrollbarThumbVertical="@color/app_main_color"
            tools:listitem="@layout/item_choose_province"
            android:scrollbars="vertical" />

    </LinearLayout>


</LinearLayout>