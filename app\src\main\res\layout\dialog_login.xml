<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_login_bg_dialog"
    android:gravity="center_horizontal"
    android:orientation="vertical">


    <ScrollView
        android:layout_width="wrap_content"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@drawable/ic_login_top"></ImageView>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/ic_logo_head_main_color"></ImageView>

                <TextView
                    android:id="@+id/tv_login_title"
                    style="@style/H1Header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_marginLeft="10dp"
                    android:text="Login"
                    android:textColor="@color/app_main_color"
                    android:textSize="18sp"></TextView>
            </LinearLayout>

            <TextView
                android:id="@+id/tv_explain_text"
                android:layout_marginTop="10dp"
                android:textColor="@color/color_black"
                android:textSize="14sp"
                android:layout_gravity="left"
                style="@style/H3Header"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"></TextView>

            <TextView
                android:id="@+id/tv_explain_subtext"
                android:textColor="@color/color_gray_dark"
                android:textSize="14sp"
                android:layout_gravity="left"
                android:layout_marginTop="4dp"
                android:layout_marginLeft="16dp"
                style="@style/Body1"
                android:layout_marginRight="16dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"></TextView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:baselineAligned="false"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/ll_email"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_email"
                        style="@style/H2Header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="6dp"
                        android:text="Email"
                        android:textColor="@color/app_main_color"
                        android:textSize="16sp"></TextView>

                    <View
                        android:id="@+id/v_line_email"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/app_main_color"></View>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_phone"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_phone"
                        style="@style/H2Header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="6dp"
                        android:text="Mobile Phone"
                        android:textColor="@color/color_black"
                        android:textSize="16sp"></TextView>

                    <View
                        android:id="@+id/v_line_phone"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/color_cccccc"></View>

                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_input_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="17dp"
                android:background="@drawable/shape_btn_gray"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_country_code"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:drawableRight="@drawable/ic_signup_down"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:paddingLeft="16dp"
                    android:paddingRight="10dp"
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>

                <View
                    android:layout_width="1dp"
                    android:layout_height="36dp"
                    android:layout_gravity="center_vertical"
                    android:background="@color/color_gray"></View>

                <EditText
                    android:id="@+id/et_phone"
                    style="@style/Body1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:autofillHints="phoneNumber"
                    android:background="@null"
                    android:gravity="left"
                    android:hint="Enter your Mobile Phone"
                    android:inputType="phone"
                    android:lines="1"
                    android:maxLines="1"
                    android:paddingLeft="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:textColor="@color/color_black"
                    android:textColorHint="@color/color_gray"
                    android:textSize="16sp"></EditText>


            </LinearLayout>

            <EditText
                android:id="@+id/et_email"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="17dp"
                android:autofillHints="username"
                android:background="@drawable/shape_btn_gray"
                android:gravity="left"
                android:hint="Enter your email"
                android:inputType="textEmailAddress"
                android:lines="1"
                android:maxLines="1"
                android:paddingLeft="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray"
                android:textSize="16sp"></EditText>


            <EditText
                android:id="@+id/et_password"
                style="@style/Body1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="17dp"
                android:autofillHints="password"
                android:background="@drawable/shape_btn_gray"
                android:gravity="left"
                android:hint="Enter password"
                android:inputType="textPassword"
                android:lines="1"
                android:maxLines="1"
                android:paddingLeft="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray"
                android:textSize="16sp"></EditText>


            <TextView
                android:id="@+id/tv_sign_in"
                style="@style/Button1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17dp"
                android:layout_marginTop="30dp"
                android:layout_marginRight="17dp"
                android:background="@drawable/shape_10radius_main_color_fill"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="Log in"
                android:textColor="@color/color_white"
                android:textSize="16sp"></TextView>

            <TextView
                android:id="@+id/tv_forgot_password"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:padding="10dp"
                android:text="Forgot Password?"
                android:textColor="@color/app_main_color"></TextView>

            <LinearLayout
                android:id="@+id/ll_or"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="17dp"
                    android:layout_marginRight="24dp"
                    android:layout_weight="1"
                    android:background="@color/color_gray"></View>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Or"
                    android:textColor="@color/color_gray"></TextView>

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginLeft="24dp"
                    android:layout_marginRight="17dp"
                    android:layout_weight="1"
                    android:background="@color/color_gray"></View>

            </LinearLayout>

            <RelativeLayout
                android:id="@+id/rl_sign_in_google"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginBottom="10dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="16dp"
                android:background="@drawable/shape_10radius_main_color">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="30dp"
                    android:background="@drawable/ic_login_google"></ImageView>

                <TextView
                    style="@style/Button1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:text="Sign in with Google"
                    android:textColor="@color/app_main_color"></TextView>

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/ll_sign_up"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                android:paddingBottom="20dp">

                <TextView
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="New user? "
                    android:textColor="@color/color_black"
                    android:textSize="16sp"></TextView>

                <TextView
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" Sign up here"
                    android:textColor="@color/app_main_color"
                    android:textSize="16sp"></TextView>


            </LinearLayout>

            <View
                android:id="@+id/v_bottom"
                android:layout_width="wrap_content"
                android:layout_height="400dp"
                android:visibility="gone"></View>
        </LinearLayout>
    </ScrollView>


</LinearLayout>