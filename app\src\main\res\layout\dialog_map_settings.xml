<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_login_bg_dialog"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_close_dialog"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="10dp"
        android:padding="16dp"
        android:src="@drawable/ic_gray_close"></ImageView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_contact_us_title"
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:text="Map Settings"
            android:textColor="@color/color_black"
            android:textSize="18sp"></TextView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/fl_street"
                android:layout_width="0dp"
                android:layout_height="43dp"
                android:layout_weight="1"
                android:background="@drawable/shape_map_settings_left_selected">

                <TextView
                    android:id="@+id/tv_street"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:drawableLeft="@drawable/ic_map_settings_street_select"
                    android:drawablePadding="5dp"
                    android:text="Street"
                    android:textColor="@color/color_white"
                    android:textSize="14sp"></TextView>

            </FrameLayout>

            <FrameLayout
                android:id="@+id/fl_satellite"
                android:layout_width="0dp"
                android:layout_height="43dp"
                android:layout_weight="1"
                android:background="@drawable/shape_map_settings_right_normal">

                <TextView
                    android:id="@+id/tv_satellite"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:drawableLeft="@drawable/ic_map_settings_satellite_normal"
                    android:drawablePadding="5dp"
                    android:text="Satellite"
                    android:textColor="@color/color_black"
                    android:textSize="14sp"></TextView>

            </FrameLayout>

        </LinearLayout>


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="30dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="30dp">

            <TextView
                android:id="@+id/tv_sold_listings"
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="Previously Sold Listings"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <ImageView
                android:id="@+id/iv_sold_listings_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@+id/tv_sold_listings"
                android:padding="10dp"
                android:src="@drawable/ic_info"></ImageView>

            <com.housesigma.android.views.SwitchButton
                android:id="@+id/sb_nearby_sold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"></com.housesigma.android.views.SwitchButton>

        </RelativeLayout>


    </LinearLayout>


</RelativeLayout>