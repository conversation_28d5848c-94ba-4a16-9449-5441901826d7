<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_dialog_location_choose"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:background="@drawable/shape_white_10_corners_dialog"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            style="@style/H1Header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Don´t Miss Any Updates"
            android:gravity="center_horizontal"
            android:textColor="@color/color_dark"
            android:layout_marginBottom="20dp"
            android:textSize="18sp"></TextView>


        <TextView
            android:id="@+id/tv_content"
            style="@style/Body2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="16dp"
            android:text="Add your email to your account to receive Watched updates right to your inbox."
            android:layout_marginRight="16dp"
            android:gravity="left"
            android:textColor="@color/color_dark"
            android:textSize="16sp"></TextView>

        <EditText
            android:id="@+id/et_email"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="17dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:layout_marginRight="17dp"
            android:autofillHints="username"
            android:background="@drawable/shape_btn_gray"
            android:gravity="left"
            android:hint="Enter your email"
            android:inputType="textEmailAddress"
            android:lines="1"
            android:maxLines="1"
            android:paddingLeft="16dp"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:textColor="@color/color_black"
            android:textColorHint="@color/color_gray"
            android:textSize="16sp"></EditText>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_send_code"
                style="@style/Button1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="17dp"
                android:layout_marginRight="17dp"
                android:layout_weight="1"
                android:background="@drawable/shape_10radius_main_color_fill"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="Send Verification code"
                android:textColor="@color/color_white"
                android:textSize="16sp"></TextView>

            <TextView
                android:id="@+id/tv_not_now"
                style="@style/Body2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="17dp"
                android:layout_marginRight="17dp"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="Not now"
                android:textColor="@color/app_main_color"
                android:textSize="16sp"></TextView>

        </LinearLayout>


    </LinearLayout>
</LinearLayout>