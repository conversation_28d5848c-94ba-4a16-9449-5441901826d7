<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/shape_10radiuis_white"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <TextView
        style="@style/H1Header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingRight="26dp"
        android:paddingLeft="26dp"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="30dp"
        android:text="Enjoying HouseSigma?"
        android:textColor="@color/color_black"
        android:textSize="18sp"></TextView>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_good"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="30dp"
            android:background="@drawable/ic_review_good"></ImageView>

        <ImageView
            android:id="@+id/iv_bad"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ic_review_bad"></ImageView>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_gravity="center_vertical"
        android:layout_marginTop="30dp"
        android:background="@color/color_gray"></View>

    <TextView
        android:id="@+id/tv_later"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:gravity="center"
        android:text="Later"
        android:textColor="#007AFF"
        android:textSize="17sp"></TextView>


</LinearLayout>