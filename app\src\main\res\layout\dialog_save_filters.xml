<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_login_bg_dialog"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            style="@style/H1Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="30dp"
            android:layout_marginBottom="20dp"
            android:text="Save Filters"
            android:textColor="@color/color_black"></TextView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/edittext_main_color_border"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/et_filter_name"
                style="@style/Body1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@null"
                android:gravity="left"
                android:hint="Filter Name"
                android:inputType="text"
                android:maxLength="60"
                android:lines="1"
                android:paddingLeft="16dp"
                android:paddingTop="12dp"
                android:paddingRight="16dp"
                android:paddingBottom="12dp"
                android:textColor="@color/color_dark"
                android:textColorHint="@color/color_gray"
                android:textSize="16sp"></EditText>

            <ImageView
                android:id="@+id/iv_del"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:paddingLeft="14dp"
                android:paddingTop="8dp"
                android:paddingRight="14dp"
                android:paddingBottom="8dp"
                android:src="@drawable/ic_serach_del"></ImageView>

        </LinearLayout>


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_delete"
            style="@style/Button1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginBottom="16dp"
            android:layout_weight="1"
            android:background="@drawable/shape_10radius_main_color"
            android:gravity="center_horizontal"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Cancel"
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>

        <TextView
            android:id="@+id/tv_save"
            style="@style/Button1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="16dp"
            android:layout_weight="1"
            android:background="@drawable/shape_10radius_main_color_fill"
            android:gravity="center_horizontal"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Save"
            android:textColor="@color/color_white"
            android:textSize="16sp"></TextView>
    </LinearLayout>


</LinearLayout>