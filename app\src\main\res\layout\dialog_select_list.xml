<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_15_corners_top"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            style="@style/H1Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="30dp"
            android:layout_marginBottom="20dp"
            android:text="Title"
            android:textColor="@color/color_black"></TextView>


        <com.housesigma.android.views.MaxHeightRecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:fadeScrollbars="false"
            android:maxHeight="300dp"
            android:scrollbarSize="2dp"
            android:scrollbarThumbVertical="@color/app_main_color" />

    </LinearLayout>


</LinearLayout>