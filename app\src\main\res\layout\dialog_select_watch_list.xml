<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/shape_white_15_corners_top"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="26dp"
            android:layout_marginBottom="14dp"
            android:orientation="horizontal">

            <TextView
                style="@style/H1Header"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:text="Save property to..."
                android:textColor="@color/color_black"></TextView>

            <RelativeLayout
                android:id="@+id/rl_new_watchlist"
                android:layout_width="wrap_content"
                android:layout_centerVertical="true"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_alignParentRight="true"
                android:layout_marginRight="16dp"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:background="@drawable/shape_10radius_broder_main_color_fill_white"
                android:orientation="horizontal"
                android:paddingTop="8dp"
                android:paddingBottom="8dp">

                <TextView
                    android:id="@+id/tv_left"
                    style="@style/Button1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:drawablePadding="10dp"
                    android:text="+ New watchlist"
                    android:textColor="@color/app_main_color"
                    android:textSize="16sp"></TextView>
            </RelativeLayout>

        </RelativeLayout>
        <View
            android:layout_width="match_parent"
            android:background="@color/color_EBEBEB"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_height="1dp"></View>
        <com.housesigma.android.views.MaxHeightRecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_marginTop="16dp"
            android:layout_height="wrap_content"
            android:maxHeight="300dp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_watchlist_cancel"
            style="@style/Button1"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/shape_10radius_main_color"
            android:gravity="center_horizontal"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Cancel"
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>

        <TextView
            android:id="@+id/tv_watchlist_save"
            style="@style/Button1"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/shape_10radius_main_color_fill"
            android:gravity="center_horizontal"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Save"
            android:textColor="@color/color_white"
            android:textSize="16sp"></TextView>
    </LinearLayout>


</LinearLayout>