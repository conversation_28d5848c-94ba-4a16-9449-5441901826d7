<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_15_corners_top"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="14dp"
        android:layout_marginBottom="24dp"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_remove"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/ripple_bright_rectangle"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_margin="18dp"
                android:background="@drawable/ic_listings_menu_del"></ImageView>

            <TextView
                android:id="@+id/tv_remove_watchlist_name"
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Unfollow"
                android:textColor="@color/color_dark"></TextView>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_save"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/ripple_bright_rectangle"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_margin="18dp"
                android:background="@drawable/ic_listings_menu_edit"></ImageView>

            <TextView
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Follow"
                android:textColor="@color/color_dark"></TextView>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_share"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/ripple_bright_rectangle"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_margin="18dp"
                android:background="@drawable/ic_listings_menu_share"></ImageView>

            <TextView
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Share"
                android:textColor="@color/color_dark"></TextView>
        </LinearLayout>

    </LinearLayout>


</LinearLayout>