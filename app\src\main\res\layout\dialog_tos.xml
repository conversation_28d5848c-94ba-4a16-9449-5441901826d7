<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_dialog_location_choose"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:background="@drawable/shape_white_dialog"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            style="@style/H1Header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="center_horizontal"
            android:lines="2"
            android:textColor="@color/color_black"
            android:textSize="18sp"></TextView>

        <TextView
            style="@style/Subtitles2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center_horizontal"
            android:text="Terms of Use"
            android:textColor="@color/color_gray"
            android:textSize="16sp"></TextView>


        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="250dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:fillViewport="true"
            android:scrollbarThumbVertical="@color/app_main_color"
            android:scrollbars="vertical">

            <TextView
                android:id="@+id/tv_content"
                style="@style/Body2"
                android:layout_width="match_parent"
                android:layout_height="250dp"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:text=""
                android:textColor="@color/color_black"
                android:textSize="16sp"></TextView>

        </ScrollView>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_reject"
                style="@style/Button1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="17dp"
                android:layout_marginRight="17dp"
                android:layout_weight="1"
                android:background="@drawable/shape_10radius_main_color"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="Reject"
                android:textColor="@color/app_main_color"
                android:textSize="16sp"></TextView>

            <TextView
                android:id="@+id/tv_accept"
                style="@style/Button1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="17dp"
                android:layout_marginRight="17dp"
                android:layout_weight="1"
                android:background="@drawable/shape_10radius_main_color_fill"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="Accept"
                android:textColor="@color/color_white"
                android:textSize="16sp"></TextView>

        </LinearLayout>


    </LinearLayout>
</LinearLayout>