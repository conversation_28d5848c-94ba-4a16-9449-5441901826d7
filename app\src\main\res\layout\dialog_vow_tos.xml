<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_dialog_location_choose"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:background="@drawable/shape_white_dialog"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:text=""
            android:layout_gravity="center"
            style="@style/H1Header"
            android:background="@drawable/shape_main_color_corners_20_top"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:fillViewport="true"
            android:scrollbarThumbVertical="@color/app_main_color"
            android:scrollbars="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_content"
                    style="@style/Body2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:text=""
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>

                <LinearLayout
                    android:layout_marginTop="6dp"
                    android:layout_marginBottom="6dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_center_up"
                        android:visibility="gone"
                        style="@style/Button1"
                        android:layout_width="130dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:background="@drawable/shape_10radius_main_color_fill"
                        android:gravity="center_horizontal"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        android:text="Live Chat"
                        android:textColor="@color/color_white"
                        android:textSize="16sp"></TextView>


                    <TextView
                        android:id="@+id/tv_center_up_subtitle"
                        style="@style/Body2"
                        android:visibility="gone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="16dp"
                        android:text="(Mon - Fri 10am ~ 4pm EST)"
                        android:textColor="@color/color_dark"></TextView>

                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_center_bottom"
                        android:layout_marginTop="6dp"
                        android:layout_marginBottom="6dp"
                        style="@style/Button1"
                        android:visibility="gone"
                        android:layout_width="130dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:background="@drawable/shape_10radius_main_color_fill"
                        android:gravity="center_horizontal"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        android:text="Email Support"
                        android:textColor="@color/color_white"
                        android:textSize="16sp"></TextView>

                    <TextView
                        android:id="@+id/tv_center_bottom_subtitle"
                        android:layout_marginTop="6dp"
                        android:layout_marginBottom="6dp"
                        style="@style/Body2"
                        android:visibility="gone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="16dp"
                        android:text="(24/7)"
                        android:textColor="@color/color_dark"></TextView>

                </LinearLayout>
            </LinearLayout>


        </ScrollView>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:layout_marginBottom="16dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_bottom_left_button"
                style="@style/Button1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:layout_weight="1"
                android:background="@drawable/shape_10radius_main_color"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="FAQ Page"
                android:textColor="@color/app_main_color"
                android:textSize="16sp"></TextView>

            <TextView
                android:id="@+id/tv_bottom_right_button"
                style="@style/Button1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:layout_weight="1"
                android:background="@drawable/shape_10radius_main_color_fill"
                android:gravity="center_horizontal"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="Close"
                android:textColor="@color/color_white"
                android:textSize="16sp"></TextView>

        </LinearLayout>


    </LinearLayout>
</LinearLayout>