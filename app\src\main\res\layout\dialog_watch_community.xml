<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_dialog_location_choose"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:background="@drawable/shape_10radiuis_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            style="@style/H1Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:layout_marginBottom="28dp"
            android:text="Watch Community"
            android:textColor="@color/color_black"
            android:textSize="18sp"></TextView>

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_new"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:paddingLeft="16dp"
            android:paddingTop="6dp"
            android:paddingRight="16dp"
            android:paddingBottom="12dp"
            android:text="New Listings"
            android:textColor="@color/color_black"
            android:textSize="16sp"

            android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_sold"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:paddingLeft="16dp"
            android:paddingTop="6dp"
            android:paddingRight="16dp"
            android:paddingBottom="12dp"
            android:text="Sold Listings"
            android:textColor="@color/color_black"
            android:textSize="16sp"

            android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_delisted"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:paddingLeft="16dp"
            android:paddingTop="6dp"
            android:paddingRight="16dp"
            android:paddingBottom="12dp"
            android:text="Delisted Listings"
            android:textColor="@color/color_black"
            android:textSize="16sp"

            android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>

        <TextView
            android:id="@+id/tv_add_watch_community"
            style="@style/Button1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/shape_10radius_main_color_fill"
            android:gravity="center"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Add Watch Community"
            android:textColor="@color/color_white"
            android:textSize="16sp"></TextView>


    </LinearLayout>

</LinearLayout>