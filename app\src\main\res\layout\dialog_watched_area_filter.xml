<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_login_bg_dialog"
    android:orientation="vertical">


    <ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="500dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="20dp"
            android:paddingTop="20dp"
            android:paddingRight="20dp">

            <TextView
                style="@style/SemiBold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:text="Watched Area"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <TextView
                android:id="@+id/tv_watched_area_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/Body1"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="10dp"
                android:textColor="@color/color_black"></TextView>

            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="Notify for"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <com.donkingliang.labels.LabelsView
                app:labelTextSize="16sp"
                android:id="@+id/labels_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="18dp"
                app:labelBackground="@drawable/label_bg"
                app:labelTextColor="@drawable/label_text_color"
                app:lineMargin="10dp"
                app:wordMargin="20dp"></com.donkingliang.labels.LabelsView>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="Property type"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <com.donkingliang.labels.LabelsView
                app:labelTextSize="16sp"
                android:id="@+id/labels_property_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="18dp"
                app:labelBackground="@drawable/label_bg"
                app:labelTextColor="@drawable/label_text_color"
                app:lineMargin="10dp"
                app:wordMargin="20dp"></com.donkingliang.labels.LabelsView>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="Price range"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>


            <TextView
                android:id="@+id/tv_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="$0 - Max"
                android:textColor="@color/color_black"
                android:textSize="16sp"></TextView>


            <com.housesigma.android.views.SaleSeekBar
                android:id="@+id/sale_sb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"></com.housesigma.android.views.SaleSeekBar>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="Description Contains Keywords"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <EditText
                android:id="@+id/et_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/shape_btn_gray7"
                android:gravity="left"
                android:hint="Waterfront, Pool, Fireplace..."
                android:lines="1"
                android:inputType="text"
                android:maxLines="1"
                android:paddingLeft="10dp"
                android:paddingTop="13dp"
                android:paddingBottom="13dp"
                android:singleLine="true"
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray_dark"
                android:textSize="16sp"></EditText>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="Bedrooms"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <com.donkingliang.labels.LabelsView
                app:labelTextSize="16sp"
                android:id="@+id/labels_beadroom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:labelBackground="@drawable/label_bg"
                app:labelTextColor="@drawable/label_text_color"
                app:lineMargin="10dp"
                app:wordMargin="16dp"></com.donkingliang.labels.LabelsView>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="Bathrooms"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <com.donkingliang.labels.LabelsView
                app:labelTextSize="16sp"
                android:id="@+id/labels_bathroom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:labelBackground="@drawable/label_bg"
                app:labelTextColor="@drawable/label_text_color"
                app:lineMargin="10dp"
                app:wordMargin="16dp"></com.donkingliang.labels.LabelsView>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="Garage/Covered Parking"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <com.donkingliang.labels.LabelsView
                app:labelTextSize="16sp"
                android:id="@+id/labels_garage_parking"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                app:labelBackground="@drawable/label_bg"
                app:labelTextColor="@drawable/label_text_color"
                app:lineMargin="10dp"
                app:wordMargin="16dp"></com.donkingliang.labels.LabelsView>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="Open House"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <com.donkingliang.labels.LabelsView
                app:labelTextSize="16sp"
                android:id="@+id/labels_open_house"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                app:labelBackground="@drawable/label_bg"
                app:labelTextColor="@drawable/label_text_color"
                app:lineMargin="10dp"
                app:wordMargin="16dp"></com.donkingliang.labels.LabelsView>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="Basement"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <com.donkingliang.labels.LabelsView
                app:labelTextSize="16sp"
                android:id="@+id/labels_basement"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                app:labelBackground="@drawable/label_bg"
                app:labelTextColor="@drawable/label_text_color"
                app:lineMargin="10dp"
                app:wordMargin="16dp"></com.donkingliang.labels.LabelsView>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>


            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="Max Maintenance Fee"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <EditText
                android:id="@+id/et_fee"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/shape_btn_gray7"
                android:gravity="left"
                android:inputType="number"
                android:lines="1"
                android:maxLines="1"
                android:paddingLeft="10dp"
                android:paddingTop="13dp"
                android:paddingBottom="13dp"
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray_dark"
                android:textSize="16sp"></EditText>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

            <TextView
                android:id="@+id/tv_feet"
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="Lot Front (feet): Unspecified - 100+"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>


            <com.jaygoo.widget.RangeSeekBar
                android:id="@+id/rsb_feet"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:rsb_gravity="center"
                app:rsb_max="100"
                app:rsb_min="0"
                app:rsb_mode="range"
                app:rsb_progress_color="@color/app_main_color"
                app:rsb_step_auto_bonding="true"
                app:rsb_step_color="@color/color_transparent"
                app:rsb_step_height="10dp"
                app:rsb_step_width="3dp"
                app:rsb_steps="10"
                app:rsb_tick_mark_gravity="center"
                app:rsb_tick_mark_layout_gravity="bottom"
                app:rsb_tick_mark_mode="number"
                app:rsb_tick_mark_text_margin="20dp" />


            <TextView
                android:id="@+id/tv_square"
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Square Footage: Unspecified - Max"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <com.jaygoo.widget.RangeSeekBar
                android:id="@+id/rsb_square"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:rsb_gravity="center"
                app:rsb_max="4000"
                app:rsb_min="0"
                app:rsb_mode="range"
                app:rsb_progress_color="@color/app_main_color"
                app:rsb_step_auto_bonding="true"
                app:rsb_step_color="@color/color_transparent"
                app:rsb_step_height="10dp"
                app:rsb_step_width="3dp"
                app:rsb_steps="40"
                app:rsb_tick_mark_gravity="center"
                app:rsb_tick_mark_layout_gravity="bottom"
                app:rsb_tick_mark_mode="number"
                app:rsb_tick_mark_text_margin="20dp" />

            <TextView
                android:id="@+id/tv_building_age"
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Building Age (years): Unspecified - 0"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <com.jaygoo.widget.RangeSeekBar
                android:id="@+id/rsb_building_age"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:rsb_gravity="center"
                app:rsb_max="24"
                app:rsb_min="0"
                app:rsb_mode="range"
                app:rsb_progress_color="@color/app_main_color"
                app:rsb_step_auto_bonding="true"
                app:rsb_step_color="@color/color_transparent"
                app:rsb_step_height="10dp"
                app:rsb_step_width="3dp"
                app:rsb_steps="16"
                app:rsb_tick_mark_gravity="center"
                app:rsb_tick_mark_layout_gravity="bottom"
                app:rsb_tick_mark_mode="number"
                app:rsb_tick_mark_text_margin="20dp" />

            <TextView
                android:id="@+id/tv_lot_size"
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Lot Size: Unspecified - Max"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <com.jaygoo.widget.RangeSeekBar
                android:id="@+id/rsb_lot_size"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:rsb_gravity="center"
                app:rsb_max="15"
                app:rsb_min="0"
                app:rsb_mode="range"
                app:rsb_progress_color="@color/app_main_color"
                app:rsb_step_auto_bonding="true"
                app:rsb_step_color="@color/color_transparent"
                app:rsb_step_height="10dp"
                app:rsb_step_width="3dp"
                app:rsb_steps="16"
                app:rsb_tick_mark_gravity="center"
                app:rsb_tick_mark_layout_gravity="bottom"
                app:rsb_tick_mark_mode="number"
                app:rsb_tick_mark_text_margin="20dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

        </LinearLayout>


    </ScrollView>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_white"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_skip"
            style="@style/Button1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="17dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="17dp"
            android:layout_marginBottom="16dp"
            android:layout_weight="1"
            android:background="@drawable/shape_10radius_main_color"
            android:gravity="center_horizontal"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Skip"
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>

        <TextView
            android:id="@+id/tv_save"
            style="@style/Button1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="17dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="17dp"
            android:layout_marginBottom="16dp"
            android:layout_weight="1"
            android:background="@drawable/shape_10radius_main_color_fill"
            android:gravity="center_horizontal"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Save"
            android:textColor="@color/color_white"
            android:textSize="16sp"></TextView>

    </LinearLayout>
</LinearLayout>
