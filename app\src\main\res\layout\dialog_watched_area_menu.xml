<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_15_corners_top"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_delete"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:background="@drawable/ripple_bright_rectangle"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_margin="18dp"
                android:src="@drawable/ic_listings_menu_del"></ImageView>

            <TextView
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Delete"
                android:textColor="@color/color_dark"></TextView>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_edit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:background="@drawable/ripple_bright_rectangle"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_margin="18dp"
                android:src="@drawable/ic_listings_menu_edit"></ImageView>

            <TextView
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Edit watch setting"
                android:textColor="@color/color_dark"></TextView>
        </LinearLayout>

    </LinearLayout>


</LinearLayout>