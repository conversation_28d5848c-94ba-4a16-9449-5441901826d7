<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_login_bg_dialog"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_marginLeft="17dp"
        android:layout_marginTop="30dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            style="@style/H2Header"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@color/color_black"
            android:text="Name"></TextView>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/H2Header"
            android:textSize="16sp"
            android:textColor="@color/color_red_light"
            android:text="*"></TextView>

    </LinearLayout>




    <EditText
        android:id="@+id/et_area_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="17dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="17dp"
        style="@style/Body1"
        android:background="@drawable/shape_btn_gray"
        android:gravity="left"
        android:inputType="text"
        android:hint="Name Your Area"
        android:paddingLeft="16dp"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:textColor="@color/color_black"
        android:textColorHint="@color/color_gray"
        android:textSize="16sp"></EditText>


    <LinearLayout
        android:layout_marginTop="20dp"
        android:layout_marginLeft="17dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:layout_gravity="center_vertical"
            android:layout_width="wrap_content"
            android:layout_marginRight="5dp"
            android:layout_height="wrap_content"
            android:background="@drawable/ic_dialog_wathed_area_email_me"></ImageView>

        <TextView
            android:layout_gravity="center_vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/H2Header"
            android:text="Email Me Daily"
            android:textColor="@color/color_dark"
            android:textSize="16sp"></TextView>
    </LinearLayout>

    <TextView
        android:layout_marginTop="4dp"
        android:layout_marginLeft="17dp"
        android:layout_width="wrap_content"
        style="@style/Subtitles2"
        android:layout_height="wrap_content"
        android:text="* Each watched area will receive up to 100 listing updates per day."
        android:textColor="@color/color_gray_dark"
        android:textSize="14sp"></TextView>


    <TextView
        android:id="@+id/tv_save"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="17dp"
        android:layout_marginTop="30dp"
        style="@style/Button1"
        android:layout_marginRight="17dp"
        android:background="@drawable/shape_10radius_main_color_fill"
        android:gravity="center_horizontal"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:layout_marginBottom="16dp"
        android:text="Save"
        android:textColor="@color/color_white"
        android:textSize="16sp"></TextView>





</LinearLayout>