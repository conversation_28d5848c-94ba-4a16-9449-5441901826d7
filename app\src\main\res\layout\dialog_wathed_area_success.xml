<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_white_login_bg_dialog"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="30dp"
        android:layout_marginRight="16dp"
        android:orientation="horizontal">


        <ImageView
            android:id="@+id/iv_pic"
            android:layout_width="112dp"
            android:layout_height="80dp"
            android:layout_marginRight="10dp"></ImageView>


        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_toast"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="3dp">

                <ImageView
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:background="@drawable/ic_toast_left" />

                <TextView
                    style="@style/Subtitles1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:paddingRight="4dp"
                    android:text="Watched area"
                    android:textColor="@color/app_main_color"
                    android:textSize="14sp"></TextView>
            </LinearLayout>

            <TextView
                android:layout_alignParentBottom="true"
                android:id="@+id/tv_save_name"
                style="@style/Body2"
                android:layout_width="wrap_content"
                android:lines="2"
                android:layout_height="wrap_content"
                android:textColor="@color/color_black"
                android:textSize="16sp"></TextView>
        </RelativeLayout>

    </LinearLayout>


    <TextView
        android:id="@+id/tv_manage_watch_area"
        style="@style/Button1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="17dp"
        android:layout_marginTop="30dp"
        android:layout_marginRight="17dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/shape_10radius_main_color_fill"
        android:gravity="center_horizontal"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:text="Manage Watch Area"
        android:textColor="@color/color_white"
        android:textSize="16sp"></TextView>


</LinearLayout>