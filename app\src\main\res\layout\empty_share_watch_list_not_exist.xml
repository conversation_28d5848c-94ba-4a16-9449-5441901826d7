<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:src="@drawable/ic_share_watchlist_not_exist"></ImageView>

    <TextView
        style="@style/H1Header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="24dp"
        android:layout_marginTop="28dp"
        android:paddingRight="24dp"
        android:gravity="center_horizontal"
        android:textSize="18sp"
        android:text="The watchlist you are trying to access has been deleted or made private"
        android:textColor="@color/color_dark"></TextView>

    <TextView
        android:id="@+id/tv_visit_homepage"
        style="@style/Button1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:paddingLeft="26dp"
        android:paddingRight="26dp"
        android:background="@drawable/shape_10radius_main_color_fill"
        android:gravity="center"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:text="Visit Homepage"
        android:textColor="@color/color_white"
        android:textSize="16sp"></TextView>

</LinearLayout>

