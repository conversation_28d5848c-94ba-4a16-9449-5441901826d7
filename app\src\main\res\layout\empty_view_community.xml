<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:layout_height="wrap_content">


    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E2E2E2"></View>

    <TextView
        android:id="@+id/tv_not_found"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="5dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:gravity="left"
        android:textColor="@color/color_gray"
        android:text="community not found"
        android:textSize="16sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#E2E2E2"></View>

</LinearLayout>