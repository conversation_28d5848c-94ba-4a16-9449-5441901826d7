<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tv_add_watch_community"
        style="@style/Button1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/shape_10radius_main_color_fill"
        android:gravity="center"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:text="Add Watch Community"
        android:textColor="@color/color_white"
        android:textSize="16sp"></TextView>

</LinearLayout>