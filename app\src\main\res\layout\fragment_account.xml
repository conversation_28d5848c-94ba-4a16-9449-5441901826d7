<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:id="@+id/v_top"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/app_main_color" />

    <RelativeLayout
        android:id="@+id/ll_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_account_header"
        android:orientation="vertical"
        android:paddingBottom="16dp">

        <TextView
            android:id="@+id/tv_title"
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="4dp"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:padding="12dp"
            android:text="@string/account_title"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_premium"
                style="@style/SemiBold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="8dp"
                android:background="@drawable/shape_premium"
                android:paddingLeft="6dp"
                android:paddingTop="2dp"
                android:paddingRight="6dp"
                android:paddingBottom="2dp"
                android:text="Premium"
                android:textColor="@color/color_dark"
                android:visibility="gone"></TextView>

            <TextView
                android:id="@+id/tv_name"
                style="@style/SemiBold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:textColor="@color/color_white"
                android:textSize="27sp"></TextView>

            <TextView
                android:id="@+id/tv_email"
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:textColor="@color/color_white"
                android:textSize="14sp"></TextView>

            <TextView
                android:id="@+id/tv_phone"
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="2dp"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:textColor="@color/color_white"
                android:textSize="14sp"></TextView>

            <TextView
                android:id="@+id/tv_my_profile"
                style="@style/Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="16dp"
                android:background="@drawable/shape_white"
                android:paddingLeft="18dp"
                android:paddingTop="6dp"
                android:paddingRight="18dp"
                android:paddingBottom="6dp"
                android:text="My Profile"
                android:textColor="@color/color_white"
                android:textSize="16sp"></TextView>

        </LinearLayout>


    </RelativeLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="-16dp"
        android:background="@drawable/shape_home_search_bg">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginTop="10dp"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/ll_my_agent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:background="@drawable/ripple_bright_rectangle"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="22dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="17dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginRight="14dp"
                    android:layout_marginBottom="22dp"
                    android:src="@drawable/ic_my_agent"></ImageView>


                <TextView
                    style="@style/H2Header"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:text="@string/account_my_agent"
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>


                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="17dp"
                    android:background="@drawable/ic_account_right"></ImageView>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_account_watched"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/ripple_bright_rectangle"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="22dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="17dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginRight="14dp"
                    android:layout_marginBottom="22dp"
                    android:src="@drawable/ic_account_watched"></ImageView>


                <TextView
                    style="@style/H2Header"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:text="@string/account_watched"
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>


                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="17dp"
                    android:background="@drawable/ic_account_right"></ImageView>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_account_language"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/ripple_bright_rectangle"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="22dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="17dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginRight="14dp"
                    android:layout_marginBottom="22dp"
                    android:src="@drawable/ic_language"></ImageView>


                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1">

                    <TextView
                        style="@style/H2Header"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/account_language"
                        android:textColor="@color/color_dark"
                        android:textSize="16sp"></TextView>

                    <TextView
                        android:id="@+id/tv_language"
                        style="@style/Regular"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginRight="16dp"
                        android:text="English"
                        android:textColor="@color/color_gray_dark"
                        android:textSize="14sp"></TextView>

                </LinearLayout>


                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="17dp"
                    android:background="@drawable/ic_account_right"></ImageView>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_account_recommend"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/ripple_bright_rectangle"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="22dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="17dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginRight="14dp"
                    android:layout_marginBottom="22dp"
                    android:src="@drawable/ic_account_recommend"></ImageView>


                <TextView
                    style="@style/H2Header"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:text="@string/account_recommend_houseSigma_to_friends"
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>


                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="17dp"
                    android:background="@drawable/ic_account_right"></ImageView>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_account_about"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/ripple_bright_rectangle"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="22dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="17dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginRight="14dp"
                    android:layout_marginBottom="22dp"
                    android:src="@drawable/ic_about"></ImageView>


                <TextView
                    style="@style/H2Header"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:text="@string/account_about_houseSigma"
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>


                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="17dp"
                    android:background="@drawable/ic_account_right"></ImageView>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_account_feedback"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/ripple_bright_rectangle"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="22dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="17dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginRight="14dp"
                    android:layout_marginBottom="22dp"
                    android:src="@drawable/ic_feedback"></ImageView>


                <TextView
                    style="@style/H2Header"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:text="@string/account_give_us_feedback"
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>


                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="17dp"
                    android:background="@drawable/ic_account_right"></ImageView>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_livechat"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:background="@drawable/ripple_bright_rectangle"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="22dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="17dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginRight="14dp"
                    android:layout_marginBottom="22dp"
                    android:src="@drawable/ic_account_chat"></ImageView>


                <TextView
                    style="@style/H2Header"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:text="Live Chat"
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>


                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="17dp"
                    android:background="@drawable/ic_account_right"></ImageView>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_career"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/ripple_bright_rectangle"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="22dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="17dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginRight="14dp"
                    android:layout_marginBottom="22dp"
                    android:src="@drawable/ic_career"></ImageView>


                <TextView
                    style="@style/H2Header"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:text="@string/account_careers"
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>


                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="17dp"
                    android:background="@drawable/ic_account_right"></ImageView>
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>


</LinearLayout>