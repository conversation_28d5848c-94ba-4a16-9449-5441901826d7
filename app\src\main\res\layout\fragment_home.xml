<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:id="@+id/v_top"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/app_main_color" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:id="@+id/sr_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />


        <LinearLayout
            android:id="@+id/ll_home_root"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/rl_root"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/app_main_color"
                android:orientation="horizontal"
                android:paddingLeft="16dp"
                android:paddingTop="16dp"
                android:paddingRight="16dp"
                android:paddingBottom="16dp">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="bottom"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="7dp"
                        android:background="@drawable/ic_home_logo_left"></ImageView>

                    <ImageView
                        android:id="@+id/iv_logo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_centerVertical="true"
                        android:background="@drawable/ic_home_logo_2"></ImageView>

                </LinearLayout>


                <LinearLayout
                    android:id="@+id/ll_location_choose"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:background="@drawable/shape_home_right"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_location"
                        style="@style/H3Header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableRight="@drawable/ic_arrow_down"
                        android:drawablePadding="4dp"
                        android:paddingLeft="12dp"
                        android:paddingTop="4dp"
                        android:paddingRight="12dp"
                        android:paddingBottom="4dp"
                        android:text="Ontario"
                        android:textColor="@color/color_white"></TextView>

                </LinearLayout>


            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@color/app_main_color"
                android:paddingBottom="10dp">

                <LinearLayout
                    android:id="@+id/iv_search"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="16dp"
                    android:background="@drawable/shape_serach">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:paddingLeft="16dp"
                        android:paddingTop="16dp"
                        android:paddingRight="10dp"
                        android:paddingBottom="16dp"
                        android:src="@drawable/ic_home_search"></ImageView>

                    <TextView
                        android:id="@+id/et_search_term"
                        style="@style/PlaceHolder"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:hint="@string/home_search_hint"
                        android:text=""
                        android:textColor="@color/color_black"
                        android:textColorHint="@color/color_gray_dark"
                        android:textSize="16sp"></TextView>
                </LinearLayout>

                <ImageView
                    android:id="@+id/iv_live_chat"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="10dp"
                    android:paddingTop="10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:src="@drawable/ic_home_msg"></ImageView>
            </LinearLayout>

            <com.housesigma.android.views.HSScrollView
                android:id="@+id/hs_scroll_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:id="@+id/ll_card"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">


                    <LinearLayout
                        android:id="@+id/ll_btn_line1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="4dp"
                        android:layout_marginRight="4dp"
                        android:orientation="horizontal"
                        android:baselineAligned="false">

                        <LinearLayout
                            android:id="@+id/ll_sale"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"

                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:paddingTop="10dp">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_home_for_sale_and_sold"></ImageView>

                            <TextView
                                android:id="@+id/tv_sale_sold"
                                style="@style/H3Header"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="6dp"
                                android:gravity="center_horizontal"
                                android:text="@string/home_btn_for_sale_sold"
                                android:textColor="@color/color_dark"></TextView>
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/ll_precon"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:paddingTop="10dp">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_home_precon"></ImageView>

                            <TextView
                                android:id="@+id/tv_precon"
                                style="@style/H3Header"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="6dp"
                                android:text="@string/home_btn_precon"
                                android:textColor="@color/color_dark"></TextView>
                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/ll_rental"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:paddingTop="10dp">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_home_rental"></ImageView>

                            <TextView
                                android:id="@+id/tv_rental"
                                style="@style/H3Header"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="6dp"
                                android:text="@string/home_btn_rental"
                                android:textColor="@color/color_dark"></TextView>
                        </LinearLayout>


                        <LinearLayout
                            android:id="@+id/ll_btn_more"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:paddingTop="10dp">

                            <ImageView
                                android:id="@+id/iv_more"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:background="@drawable/ic_home_more"></ImageView>

                            <TextView
                                android:id="@+id/tv_more_less"
                                style="@style/H3Header"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="6dp"
                                android:text="@string/home_btn_more"
                                android:textColor="@color/color_dark"></TextView>
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_btn_line2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="4dp"
                        android:layout_marginRight="4dp"
                        android:orientation="horizontal"
                        android:visibility="gone"
                        android:baselineAligned="false">

                        <LinearLayout
                            android:id="@+id/ll_estimate"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:paddingTop="10dp">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_home_estimate"></ImageView>

                            <TextView
                                style="@style/H3Header"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="6dp"
                                android:gravity="center_horizontal"
                                android:text="@string/home_btn_estimate"
                                android:textColor="@color/color_dark"></TextView>
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/ll_recommend"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:paddingTop="10dp">

                            <ImageView
                                android:id="@+id/iv_recommend"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_recommend"
                                app:tint="@color/app_main_color"></ImageView>

                            <TextView
                                android:id="@+id/tv_recommend"
                                style="@style/H3Header"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="6dp"
                                android:gravity="center_horizontal"
                                android:lines="2"
                                android:text="@string/home_btn_recomemend"
                                android:textColor="@color/color_dark"></TextView>
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/ll_reports"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:paddingTop="10dp">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_home_reports"></ImageView>

                            <TextView
                                style="@style/H3Header"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="6dp"
                                android:text="@string/home_btn_report"
                                android:textColor="@color/color_dark"
                                android:textSize="14sp"></TextView>
                        </LinearLayout>
                        <LinearLayout
                            android:id="@+id/ll_agents"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:paddingTop="10dp">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_home_agents"></ImageView>

                            <TextView
                                style="@style/H3Header"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="6dp"
                                android:text="@string/home_btn_agents"
                                android:textColor="@color/color_dark"
                                android:textSize="14sp"></TextView>
                        </LinearLayout>
                        <View
                            android:id="@+id/view_placeholder"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:visibility="invisible">

                        </View>


                    </LinearLayout>

                    <com.lihang.ShadowLayout
                        android:id="@+id/mShadowLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="6dp"
                        android:layout_marginBottom="4dp"
                        app:hl_cornerRadius="10dp"
                        app:hl_shadowOffsetY="5dp"
                        app:hl_shadowColor="#2a373A40"
                        app:hl_shadowLimit="16dp">
                        <com.youth.banner.Banner
                            xmlns:banner="http://schemas.android.com/apk/res-auto"
                            android:id="@+id/banner"
                            android:layout_width="match_parent"
                            android:layout_height="60dp"
                            banner:banner_indicator_normal_color="#E9F6F7"
                            banner:banner_indicator_selected_color="#28A3B3">

                        </com.youth.banner.Banner>
                    </com.lihang.ShadowLayout>

                    <LinearLayout
                        android:id="@+id/ll_watched_community_updates"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="14dp"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            style="@style/H1Header"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="16dp"
                            android:layout_weight="1"
                            android:lines="1"
                            android:maxLines="1"
                            android:text="@string/home_listing_watched_area_community"
                            android:textColor="@color/color_dark"
                            android:textSize="18sp"></TextView>

                        <TextView
                            android:id="@+id/tv_see_more_watched_community_updates"
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingRight="16dp"
                            android:paddingLeft="16dp"
                            android:text="@string/home_listing_see_more"
                            android:textColor="@color/app_main_color"
                            android:textSize="14sp"></TextView>

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_home_watched_community_updates"
                        android:layout_width="match_parent"
                        android:layout_height="230dp"
                        android:layout_marginBottom="20dp"
                        android:clipToPadding="false"
                        android:paddingRight="18dp"
                        android:visibility="gone" />

                    <LinearLayout
                        android:id="@+id/ll_recommend_for_you"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="14dp"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            style="@style/H1Header"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="16dp"
                            android:layout_weight="1"
                            android:lines="1"
                            android:maxLines="1"
                            android:text="@string/home_listing_recommend_for_you"
                            android:textColor="@color/color_dark"
                            android:textSize="18sp"></TextView>

                        <TextView
                            android:id="@+id/tv_see_more_recommend_for_you"
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingRight="16dp"
                            android:paddingLeft="16dp"
                            android:text="@string/home_listing_see_more"
                            android:textColor="@color/app_main_color"
                            android:textSize="14sp"></TextView>

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_home_recommend_for_you"
                        android:layout_width="match_parent"
                        android:layout_height="230dp"
                        android:layout_marginBottom="20dp"
                        android:clipToPadding="false"
                        android:paddingRight="18dp"
                        android:visibility="gone" />


                    <RelativeLayout
                        android:id="@+id/rl_customize"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:background="@drawable/shape_10radius_main_color">

                        <LinearLayout
                            android:id="@+id/ll_personlize_listing"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_centerVertical="true"
                            android:gravity="center_horizontal"
                            android:orientation="horizontal">

                            <RelativeLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginRight="16dp">

                                <ImageView
                                    android:id="@+id/iv_personalize_changed"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:background="@drawable/ic_customize"></ImageView>


                            </RelativeLayout>


                            <TextView
                                android:id="@+id/tv_personalize_listing"
                                style="@style/Body1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:layout_marginTop="16dp"
                                android:layout_marginBottom="16dp"
                                android:text="@string/home_personalize_listings"
                                android:textColor="@color/app_main_color"></TextView>

                        </LinearLayout>


                    </RelativeLayout>

                    <LinearLayout
                        android:id="@+id/ll_recommend_root"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:id="@+id/ll_home_exclusive_precon_assignment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="14dp"
                            android:orientation="horizontal"
                            android:visibility="gone">

                            <TextView
                                style="@style/H1Header"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="16dp"
                                android:layout_weight="1"
                                android:text="@string/home_listing_exclusive_precon_assignment"
                                android:textColor="@color/color_black"
                                android:textSize="18sp"></TextView>

                            <TextView
                                android:id="@+id/tv_see_more_exclusive_precon_assignment"
                                style="@style/Subtitles2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingRight="16dp"
                                android:paddingLeft="16dp"
                                android:text="@string/home_listing_see_more"
                                android:textColor="@color/app_main_color"
                                android:textSize="14sp"></TextView>

                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_home_exclusive_precon_assignment"
                            android:layout_width="match_parent"
                            android:layout_height="230dp"
                            android:clipToPadding="false"
                            android:paddingRight="18dp"
                            android:visibility="gone" />

                        <LinearLayout
                            android:id="@+id/ll_home_sold_below_bought"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="14dp"
                            android:orientation="horizontal"
                            android:visibility="gone">

                            <TextView
                                style="@style/H1Header"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="16dp"
                                android:layout_weight="1"
                                android:text="@string/home_listing_sold_below_bought"
                                android:textColor="@color/color_black"
                                android:textSize="18sp"></TextView>

                            <TextView
                                android:id="@+id/tv_see_more_sold_below_bought"
                                style="@style/Subtitles2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingRight="16dp"
                                android:paddingLeft="16dp"
                                android:text="@string/home_listing_see_more"
                                android:textColor="@color/app_main_color"
                                android:textSize="14sp"></TextView>

                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_home_sold_below_bought"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:paddingRight="18dp"
                            android:visibility="gone" />

                        <LinearLayout
                            android:id="@+id/ll_home_high_returns_type"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="14dp"
                            android:orientation="horizontal"
                            android:visibility="gone">

                            <TextView
                                style="@style/H1Header"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="16dp"
                                android:layout_weight="1"
                                android:text="@string/home_listing_high_returns_type"
                                android:textColor="@color/color_dark"
                                android:textSize="18sp"></TextView>

                            <TextView
                                android:id="@+id/tv_see_more_high_returns_type"
                                style="@style/Subtitles2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingRight="16dp"
                                android:paddingLeft="16dp"
                                android:text="@string/home_listing_see_more"
                                android:textColor="@color/app_main_color"
                                android:textSize="14sp"></TextView>

                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_home_high_returns_type"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:paddingRight="18dp"
                            android:visibility="gone" />

                        <LinearLayout
                            android:id="@+id/ll_home_featured_listings"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="14dp"
                            android:orientation="horizontal"
                            android:visibility="gone">

                            <TextView
                                style="@style/H1Header"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="16dp"
                                android:layout_weight="1"
                                android:text="@string/home_listing_featured_listings"
                                android:textColor="@color/color_dark"
                                android:textSize="18sp"></TextView>

                            <TextView
                                android:id="@+id/tv_see_more_featured_listings"
                                style="@style/Subtitles2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingRight="16dp"
                                android:paddingLeft="16dp"
                                android:text="@string/home_listing_see_more"
                                android:textColor="@color/app_main_color"
                                android:textSize="14sp"></TextView>

                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_home_featured_listings"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:paddingRight="18dp"
                            android:visibility="gone" />


                        <LinearLayout
                            android:id="@+id/ll_home_just_sold"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="14dp"
                            android:orientation="horizontal"
                            android:visibility="gone">

                            <TextView
                                style="@style/H1Header"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="16dp"
                                android:layout_weight="1"
                                android:text="@string/home_listing_just_sold"
                                android:textColor="@color/color_dark"
                                android:textSize="18sp"></TextView>

                            <TextView
                                android:id="@+id/tv_see_more_just_sold"
                                style="@style/Subtitles2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingRight="16dp"
                                android:paddingLeft="16dp"
                                android:text="@string/home_listing_see_more"
                                android:textColor="@color/app_main_color"
                                android:textSize="14sp"></TextView>

                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_home_just_sold"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:paddingRight="18dp"
                            android:visibility="gone" />

                        <LinearLayout
                            android:id="@+id/ll_home_newly_listed"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="14dp"
                            android:orientation="horizontal"
                            android:visibility="gone">

                            <TextView
                                style="@style/H1Header"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="16dp"
                                android:layout_weight="1"
                                android:text="@string/home_listing_newly_listed"
                                android:textColor="@color/color_dark"
                                android:textSize="18sp"></TextView>

                            <TextView
                                android:id="@+id/tv_see_more_newly_listed"
                                style="@style/Subtitles2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingRight="16dp"
                                android:paddingLeft="16dp"
                                android:text="@string/home_listing_see_more"
                                android:textColor="@color/app_main_color"
                                android:textSize="14sp"></TextView>

                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_home_newly_listed"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:paddingRight="18dp"
                            android:visibility="gone" />

                        <LinearLayout
                            android:id="@+id/ll_best_for_rental_investment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="14dp"
                            android:orientation="horizontal"
                            android:visibility="gone">

                            <TextView
                                style="@style/H1Header"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="16dp"
                                android:layout_weight="1"
                                android:text="@string/home_listing_best_for_rental_investment"
                                android:textColor="@color/color_dark"
                                android:textSize="18sp"></TextView>

                            <TextView
                                android:id="@+id/tv_see_more_best_for_rental_investment"
                                style="@style/Subtitles2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingRight="16dp"
                                android:paddingLeft="16dp"
                                android:text="@string/home_listing_see_more"
                                android:textColor="@color/app_main_color"
                                android:textSize="14sp"></TextView>

                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_best_for_rental_investment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:paddingRight="18dp"
                            android:visibility="gone" />

                        <LinearLayout
                            android:id="@+id/ll_home_best_for_school"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="14dp"
                            android:orientation="horizontal"
                            android:visibility="gone">

                            <TextView
                                style="@style/H1Header"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="16dp"
                                android:layout_weight="1"
                                android:text="@string/home_listing_best_for_school"
                                android:textColor="@color/color_dark"
                                android:textSize="18sp"></TextView>

                            <TextView
                                android:id="@+id/tv_see_more_best_for_school"
                                style="@style/Subtitles2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingRight="16dp"
                                android:paddingLeft="16dp"
                                android:text="@string/home_listing_see_more"
                                android:textColor="@color/app_main_color"
                                android:textSize="14sp"></TextView>

                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_home_best_for_school"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:paddingRight="18dp"
                            android:visibility="gone" />

                        <LinearLayout
                            android:id="@+id/ll_home_1year_price_growth"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="14dp"
                            android:orientation="horizontal"
                            android:visibility="gone">

                            <TextView
                                style="@style/H1Header"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="16dp"
                                android:layout_weight="1"
                                android:text="@string/home_listing_1_year_price_growth"
                                android:textColor="@color/color_dark"
                                android:textSize="18sp"></TextView>

                            <TextView
                                android:id="@+id/tv_see_more_1year_price_growth"
                                style="@style/Subtitles2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:paddingRight="16dp"
                                android:paddingLeft="16dp"
                                android:text="@string/home_listing_see_more"
                                android:textColor="@color/app_main_color"
                                android:textSize="14sp"></TextView>

                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_home_1year_price_growth"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:paddingRight="18dp"
                            android:visibility="gone" />

                    </LinearLayout>




                    <RelativeLayout
                        android:id="@+id/rl_chart"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="10dp"
                        android:paddingRight="10dp">

                        <TextView
                            android:id="@+id/tv_echart_title"
                            style="@style/H1Header"
                            android:layout_centerHorizontal="true"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="16dp"
                            android:text="GTA Statistics *"
                            android:textColor="@color/color_dark"
                            android:textSize="18sp"></TextView>

                        <TextView
                            android:layout_below="@+id/tv_echart_title"
                            android:id="@+id/tv_chart_subtitle"
                            style="@style/Subtitles2"
                            android:layout_centerHorizontal="true"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="2dp"
                            android:layout_marginBottom="8dp"
                            android:text="(All property types)"
                            android:textColor="@color/color_gray_dark"
                            android:textSize="14sp"></TextView>

                        <TextView
                            android:layout_below="@+id/tv_chart_subtitle"
                            android:id="@+id/tv_chart_no_data"
                            style="@style/Subtitles2"
                            android:layout_width="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="50dp"
                            android:layout_marginBottom="50dp"
                            android:text="Data Not Available"
                            android:visibility="gone"
                            android:textColor="@color/color_gray"
                            android:textSize="14sp"></TextView>

                        <com.housesigma.android.views.TouchCombinedChart
                            android:id="@+id/chart"
                            android:layout_marginTop="16dp"
                            android:layout_below="@+id/tv_chart_no_data"
                            android:layout_width="match_parent"
                            android:layout_height="300dp" />

                        <androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
                            xmlns:app="http://schemas.android.com/apk/res-auto"
                            android:id="@+id/card_view"
                            android:layout_centerHorizontal="true"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="5dp"
                            android:visibility="gone"
                            app:cardCornerRadius="4dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@color/color_white"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tv_chart_date"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="left"
                                    android:layout_marginLeft="14dp"
                                    android:layout_marginTop="12dp"
                                    android:layout_marginBottom="2dp"
                                    android:text="2020-01"
                                    android:textColor="@color/color_dark"
                                    android:textSize="15sp"></TextView>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <View
                                        android:layout_width="20dp"
                                        android:layout_height="4dp"
                                        android:layout_marginLeft="14dp"
                                        android:background="@drawable/shape_chart_marker_median_price_lable">

                                    </View>


                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginLeft="10dp"
                                            android:text="Median Price: "
                                            android:textColor="@color/color_dark"
                                            android:textSize="15sp"></TextView>

                                        <TextView
                                            android:id="@+id/tv_chart_media_price"
                                            android:layout_width="0dp"
                                            android:layout_height="wrap_content"
                                            android:layout_marginRight="10dp"
                                            android:layout_weight="1"
                                            android:gravity="right"
                                            android:text="725,000"
                                            android:textColor="@color/color_dark"
                                            android:textSize="15sp"></TextView>
                                    </LinearLayout>

                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginBottom="12dp"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <View
                                        android:layout_width="20dp"
                                        android:layout_height="4dp"
                                        android:layout_marginLeft="14dp"
                                        android:background="@drawable/shape_chart_marker_total_sold_lable">

                                    </View>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginLeft="10dp"
                                            android:text="Total sold: "
                                            android:textColor="@color/color_dark"
                                            android:textSize="15sp"></TextView>

                                        <TextView
                                            android:id="@+id/tv_chart_total_sold"
                                            android:layout_width="0dp"
                                            android:layout_height="wrap_content"
                                            android:layout_marginRight="10dp"
                                            android:layout_weight="1"
                                            android:gravity="right"
                                            android:text="9,953"
                                            android:textColor="@color/color_dark"
                                            android:textSize="15sp"></TextView>
                                    </LinearLayout>

                                </LinearLayout>


                            </LinearLayout>
                        </androidx.cardview.widget.CardView>
                    </RelativeLayout>


                    <TextView
                        android:id="@+id/tv_view_more_stats"
                        style="@style/Button1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginRight="16dp"
                        android:background="@drawable/shape_10radius_main_color"
                        android:gravity="center_horizontal"
                        android:paddingTop="16dp"
                        android:paddingBottom="16dp"
                        android:text="@string/home_view_more_stats"
                        android:textColor="@color/app_main_color"
                        android:textSize="16sp"></TextView>

                    <TextView
                        android:id="@+id/tv_chart_source"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginLeft="16dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginRight="16dp"
                        android:gravity="center_horizontal"
                        android:text=" * Source: Based on analysis of information from past listings from respective real estate boards. "
                        android:textColor="@color/color_gray_dark"
                        android:textSize="14sp"></TextView>

                    <RelativeLayout
                        android:visibility="gone"
                        android:id="@+id/rl_agent_team_header"
                        android:layout_width="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/tv_agent_team_title"
                            android:layout_marginLeft="13dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            style="@style/H1Header"
                            android:textColor="@color/color_dark"></TextView>


                        <TextView
                            android:layout_marginRight="19dp"
                            android:layout_alignParentRight="true"
                            android:id="@+id/tv_agent_team_see_more"
                            android:layout_width="wrap_content"
                            style="@style/Subtitles2"
                            android:textColor="@color/app_main_color"
                            android:layout_height="wrap_content"
                            android:text="See More"/>

                    </RelativeLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_agent_team"
                        android:visibility="gone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clipToPadding="false"
                        android:layout_marginBottom="20dp"
                        android:paddingRight="18dp"/>

                    <LinearLayout
                        android:layout_marginTop="4dp"
                        android:id="@+id/ll_bottom"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="#F6F6F6"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_tip"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="16dp"
                            android:text="Find historical listing information about any home in Ontario. Sold, for sale, price trend and more!"
                            android:textColor="@color/color_dark"
                            android:textSize="16sp"></TextView>


                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="4dp"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tv_feed_back"
                                style="@style/HyperLink"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginRight="12dp"
                                android:background="@drawable/text_underline"
                                android:text="Contact Us"
                                android:textColor="@color/color_dark"
                                android:textSize="14sp"></TextView>

                            <TextView
                                android:id="@+id/tv_about"
                                style="@style/HyperLink"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginRight="12dp"
                                android:background="@drawable/text_underline"
                                android:text="About Us"
                                android:textColor="@color/color_dark"
                                android:textSize="14sp"></TextView>

                            <TextView
                                android:id="@+id/tv_english"
                                style="@style/HyperLink"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/text_underline"
                                android:text="@string/home_language"
                                android:textColor="@color/color_dark"
                                android:textSize="14sp"></TextView>
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_version"
                            style="@style/HyperLink"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="4dp"
                            android:text="Version 5.3.1"
                            android:textColor="@color/color_dark"
                            android:textSize="14sp"></TextView>

                        <TextView
                            android:id="@+id/tv_ab_test"
                            style="@style/HyperLink"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:visibility="gone"
                            android:layout_marginTop="4dp"
                            android:textColor="@color/color_dark"
                            android:textSize="14sp"></TextView>

                        <TextView
                            style="@style/HyperLink"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="4dp"
                            android:text="HouseSigma Inc. Brokerage"
                            android:textColor="@color/color_dark"
                            android:textSize="14sp"></TextView>


                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="4dp"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tv_legal"
                                style="@style/HyperLink"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginRight="12dp"
                                android:visibility="gone"
                                android:background="@drawable/text_underline"
                                android:text="Legal"
                                android:textColor="@color/color_dark"
                                android:textSize="14sp"></TextView>

                            <TextView
                                android:id="@+id/tv_privacy"
                                style="@style/HyperLink"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginRight="12dp"
                                android:background="@drawable/text_underline"
                                android:text="Privacy &#038; Security"
                                android:textColor="@color/color_dark"
                                android:textSize="14sp"></TextView>


                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_accessibility"
                            style="@style/HyperLink"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="4dp"
                            android:layout_marginBottom="12dp"
                            android:background="@drawable/text_underline"
                            android:text="Accessibility"
                            android:visibility="gone"
                            android:textColor="@color/color_dark"
                            android:textSize="14sp"></TextView>

                        <TextView
                            android:id="@+id/tv_terms"
                            style="@style/HyperLink"
                            android:layout_width="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="4dp"
                            android:layout_marginBottom="12dp"
                            android:layout_height="wrap_content"
                            android:background="@drawable/text_underline"
                            android:text="Terms &#038; Conditions"
                            android:textColor="@color/color_dark"
                            android:textSize="14sp"></TextView>

                        <TextView
                            android:id="@+id/tv_disclaimer"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="left"
                            android:layout_marginLeft="16dp"
                            android:layout_marginRight="16dp"
                            style="@style/Regular"
                            android:gravity="left"
                            android:textColor="@color/color_gray_dark"
                            android:textSize="14sp"></TextView>

                    </LinearLayout>

                </LinearLayout>


            </com.housesigma.android.views.HSScrollView>
        </LinearLayout>


    </com.scwang.smart.refresh.layout.SmartRefreshLayout>


</LinearLayout>