<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:id="@+id/v_top"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/app_main_color" />

    <RelativeLayout
        android:id="@+id/rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="12dp"
            android:src="@drawable/ic_close"
            android:visibility="gone"></ImageView>


        <TextView
            android:id="@+id/tv_title"
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:text="Market Trends"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

        <ImageView
            android:id="@+id/iv_search"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@+id/iv_share"
            android:padding="16dp"
            android:src="@drawable/ic_webview_search"
            android:visibility="gone"></ImageView>

        <ImageView
            android:id="@+id/iv_share"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:padding="16dp"
            android:src="@drawable/ic_webview_share"></ImageView>
    </RelativeLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <com.scwang.smart.refresh.header.ClassicsHeader
        android:id="@+id/sr_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <FrameLayout
        android:id="@+id/webview_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>
</LinearLayout>