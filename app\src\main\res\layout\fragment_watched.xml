<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:id="@+id/v_top"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/app_main_color" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/app_main_color"
        android:orientation="horizontal">

        <TextView
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:drawableLeft="@drawable/ic_head_logo"
            android:drawablePadding="10dp"
            android:padding="12dp"
            android:text="@string/watched_watched"
            android:textColor="@color/color_white"
            android:textSize="18sp"></TextView>

        <ImageView
            android:id="@+id/iv_notification"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:paddingLeft="16dp"
            android:paddingTop="12dp"
            android:paddingRight="16dp"
            android:paddingBottom="12dp"
            android:src="@drawable/ic_watched_notification"></ImageView>

    </RelativeLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.housesigma.android.views.viewpagerindicator.view.indicator.ScrollIndicatorView
            android:id="@+id/tab_main_indicator"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/app_main_color" />

        <com.housesigma.android.views.viewpagerindicator.view.viewpager.SViewPager
            android:id="@+id/tab_main_viewPager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />


    </LinearLayout>

</LinearLayout>