<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:id="@+id/sr_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_for_sale"
                    android:layout_width="wrap_content"
                    android:layout_height="38dp"
                    android:background="@drawable/shape_watch_left_selected"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    style="@style/Body1"
                    android:gravity="center"
                    android:text="For Sale"
                    android:textColor="@color/color_white"
                    android:textSize="16sp"></TextView>

                <TextView
                    android:id="@+id/tv_sold"
                    android:layout_width="wrap_content"
                    android:layout_height="38dp"
                    android:layout_marginLeft="-1dp"
                    android:background="@drawable/shape_watch_center_selected"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:gravity="center"
                    style="@style/Body1"
                    android:text="Sold"
                    android:textColor="@color/color_white"
                    android:textSize="16sp"></TextView>

                <TextView
                    android:id="@+id/tv_de_listed"
                    android:layout_width="wrap_content"
                    android:layout_height="38dp"
                    android:layout_marginLeft="-1dp"
                    android:background="@drawable/shape_watch_right_selected"
                    android:paddingLeft="16dp"
                    style="@style/Body1"
                    android:gravity="center"
                    android:paddingRight="16dp"
                    android:text="De-listed"
                    android:textColor="@color/color_white"
                    android:textSize="16sp"></TextView>

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="16dp" />

        </LinearLayout>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</LinearLayout>