<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:paddingTop="10dp"
    android:paddingBottom="5dp">

    <TextView
        style="@style/H1Header"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="@string/search_history"
        android:textColor="@color/color_black"
        android:textSize="18sp"></TextView>

    <TextView
        android:id="@+id/tv_clear_search"
        style="@style/Subtitles2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="16dp"
        android:text="Clear Search"
        android:textColor="@color/app_main_color"></TextView>

</LinearLayout>