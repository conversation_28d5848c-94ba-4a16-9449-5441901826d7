<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_agent_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="16dp"
    android:layout_gravity="center_horizontal"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_agent_avatar"
        android:layout_width="110dp"
        android:layout_height="110dp"
        android:background="@drawable/shape_oval_gray_color_4_border">

        <ImageView
            android:id="@+id/iv_avatar"
            android:layout_width="108dp"
            android:layout_height="108dp"
            android:layout_centerInParent="true"></ImageView>
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_agent_name"
        style="@style/Body1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:text="[Agent Name]"
        android:textColor="@color/color_dark"></TextView>

    <TextView
        android:id="@+id/tv_designation"
        style="@style/Subtitles2"
        android:layout_width="match_parent"
        android:gravity="center_horizontal"
        android:layout_height="wrap_content"
        android:text="[Designation]"
        android:textColor="@color/color_gray_dark"></TextView>

</LinearLayout>