<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tv_province_name"
        style="@style/Button1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="17dp"
        android:layout_marginRight="17dp"
        android:layout_marginBottom="18dp"
        android:background="@drawable/shape_10radius_main_color_fill"
        android:gravity="center_horizontal"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:textColor="@color/color_white"
        android:textSize="16sp"></TextView>

</LinearLayout>