<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_more_list"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:paddingLeft="16dp">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@drawable/ic_more_list"></ImageView>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@drawable/bg_more_list"></ImageView>

        <TextView
            style="@style/Button1"
            android:layout_width="110dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:background="@drawable/shape_10radius_main_color_fill"
            android:gravity="center"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="More"
            android:textColor="@color/color_white"
            android:textSize="16sp"></TextView>

    </RelativeLayout>


</RelativeLayout>