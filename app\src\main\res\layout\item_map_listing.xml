<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">


        <LinearLayout
            android:id="@+id/ll_open_house"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/shape_map_listings_open_house_time"
            android:orientation="horizontal"
            android:paddingLeft="13dp"
            android:paddingTop="5dp"
            android:paddingRight="13dp"
            android:paddingBottom="5dp">

            <TextView
                style="@style/Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:lines="1"
                android:text="OPEN HOUSE: "
                android:textColor="@color/color_green_dark"
                android:textSize="13sp"></TextView>

            <TextView
                android:id="@+id/tv_open_house_time"
                style="@style/Bold"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:fontFamily="@font/poppins_bold"
                android:textColor="@color/color_green_dark"
                android:textSize="13sp"></TextView>

            <TextView
                android:id="@+id/tv_open_house_time_more"
                style="@style/Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:fontFamily="@font/poppins_medium"
                android:text="+ more"
                android:textColor="@color/color_green_dark"
                android:textSize="13sp"></TextView>

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="100dp"
            android:layout_marginTop="6dp"
            android:layout_height="90dp">

            <ImageView
                android:id="@+id/iv_house_pic"
                android:layout_width="100dp"
                android:layout_height="90dp"></ImageView>

            <TextView
                android:id="@+id/tv_watched_status"
                style="@style/Medium"
                android:paddingLeft="8dp"
                android:layout_marginLeft="4dp"
                android:layout_marginTop="4dp"
                android:paddingTop="3dp"
                android:paddingRight="8dp"
                android:paddingBottom="3dp"
                android:background="@drawable/shape_14radius_main_color_fill"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:text="Watched"
                android:ellipsize="end"
                android:textColor="@color/color_white"
                android:textSize="14sp"></TextView>


        </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top"
                    android:gravity="top"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_alignParentLeft="true"
                        android:layout_alignParentTop="true"
                        android:id="@+id/tv_price"
                        style="@style/H1Header"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:lines="1"
                        android:maxLines="1"
                        android:text="$4,459,000"
                        android:textColor="@color/app_main_color"
                        android:textSize="18sp"></TextView>

                    <LinearLayout
                        android:layout_below="@+id/tv_price"
                        android:id="@+id/ll_sold"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_sold_tag"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Sold: "
                            android:textColor="@color/color_black"
                            android:textSize="16sp"></TextView>

                        <TextView
                            android:id="@+id/tv_price_sold"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="$1,550,000"
                            android:textColor="#FB1815"
                            android:textSize="16sp"></TextView>

                    </LinearLayout>

                    <TextView
                        android:layout_alignParentRight="true"
                        android:layout_alignParentTop="true"
                        android:id="@+id/tv_list_status"
                        style="@style/Body1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="4dp"
                        android:lines="1"
                        android:maxLines="1"
                        android:text="For sale"
                        android:textColor="@color/app_main_color"></TextView>

                </RelativeLayout>



                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_data_private1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Body2"
                        android:text="Data not available to"
                        android:textColor="@color/color_black"
                        android:textSize="16sp"></TextView>

                    <TextView
                        android:id="@+id/tv_data_private2"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="the public."
                        android:textColor="@color/color_black"
                        android:textSize="16sp"></TextView>

                    <TextView
                        android:id="@+id/tv_address"
                        style="@style/Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:text="Michelangelo Blvd, Brampton Toronto Gore Rural Estate"
                        android:textColor="@color/color_black"
                        android:textSize="16sp"></TextView>
                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_house_type_name"
                        style="@style/Body1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:lines="1"
                        android:maxLines="1"
                        android:text="Detached"
                        android:textColor="@color/color_black"
                        android:textSize="14sp"></TextView>

                    <TextView
                        android:id="@+id/tv_bedroom_string"
                        style="@style/Body1"
                        android:layout_marginLeft="12dp"
                        android:drawablePadding="6dp"
                        android:drawableLeft="@drawable/ic_bedroom"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="-"
                        android:textColor="@color/color_black"></TextView>


                    <TextView
                        android:id="@+id/tv_washroom"
                        style="@style/Body1"
                        android:layout_marginLeft="12dp"
                        android:drawablePadding="6dp"
                        android:drawableLeft="@drawable/ic_bathroom"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="-"
                        android:textColor="@color/color_black"></TextView>

                    <TextView
                        android:id="@+id/tv_garage"
                        style="@style/Body1"
                        android:layout_marginLeft="12dp"
                        android:drawablePadding="6dp"
                        android:drawableLeft="@drawable/ic_garage"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="-"
                        android:textColor="@color/color_black"></TextView>
                </LinearLayout>


            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_date_times"
                style="@style/Subtitles2"
                android:layout_width="100dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:layout_gravity="center"
                android:textColor="@color/color_dark"
                android:textSize="14sp"
                android:visibility="visible"></TextView>

            <LinearLayout
                android:id="@+id/ll_score"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_assignment"
                    style="@style/Medium"
                    android:layout_width="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/shape_8radius_green_light_fill"
                    android:paddingLeft="10dp"
                    android:paddingTop="6dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="6dp"
                    android:textColor="@color/color_green_dark"
                    android:textSize="14sp"></TextView>

                <TextView
                    android:id="@+id/tv_school"
                    style="@style/Medium"
                    android:layout_width="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/shape_map_listings_school_blue"
                    android:paddingLeft="10dp"
                    android:paddingTop="6dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="6dp"
                    android:textColor="@color/color_blue_blue"
                    android:textSize="14sp"></TextView>

                <TextView
                    android:id="@+id/tv_rental"
                    style="@style/Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/shape_map_listings_rental_red"
                    android:paddingLeft="10dp"
                    android:paddingTop="6dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="6dp"
                    android:textColor="@color/color_red_red"
                    android:textSize="14sp"></TextView>

                <TextView
                    android:id="@+id/tv_growth"
                    style="@style/Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/shape_listings_growth_green"
                    android:paddingLeft="10dp"
                    android:paddingTop="6dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="6dp"
                    android:textColor="@color/color_green_dark"
                    android:textSize="14sp"></TextView>

            </LinearLayout>

            <TextView
                android:id="@+id/tv_contact_agent"
                style="@style/Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:background="@drawable/shape_8radius_main_color_fill"
                android:paddingLeft="10dp"
                android:paddingTop="6dp"
                android:paddingRight="10dp"
                android:paddingBottom="6dp"
                android:text="@string/contact_agent"
                android:textColor="@color/color_white"
                android:textSize="14sp"></TextView>

        </LinearLayout>

        <TextView
            android:id="@+id/tv_bc_brokerage_text"
            style="@style/Light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="-8dp"
            android:textColor="#869099"
            android:textSize="12sp"></TextView>

        <View
            android:id="@+id/line"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="7dp" />


    </LinearLayout>

    <com.housesigma.android.views.blur.widget.RealtimeBlurView
        android:id="@+id/masked"
        android:layout_width="match_parent"
        android:layout_height="185dp"
        android:visibility="gone" />

    <View
        android:id="@+id/view_not_interested"
        android:layout_width="match_parent"
        android:layout_height="155dp"
        android:background="#80FFFFFF"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_not_interested"
        style="@style/Body1"
        android:layout_width="144dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:background="@drawable/shape_8radius_gray_dark_fill"
        android:gravity="center"
        android:paddingRight="4dp"
        android:paddingTop="4dp"
        android:paddingLeft="6dp"
        android:drawableLeft="@drawable/ic_not_interested"
        android:paddingBottom="4dp"
        android:text="Not interested"
        android:textColor="@color/color_white"
        android:textSize="16sp"
        android:visibility="gone"></TextView>

    <include layout="@layout/layout_sub_map_treb_expired"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_agreement_required"
        style="@style/Button1"
        android:layout_width="180dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:background="@drawable/shape_10radius_main_color_fill"
        android:gravity="center"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:text="Tap to View"
        android:textColor="@color/color_white"
        android:textSize="16sp"
        android:visibility="gone"></TextView>

    <TextView
        android:id="@+id/tv_not_available"
        style="@style/Button1"
        android:layout_width="180dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:background="@drawable/shape_10radius_main_color_fill"
        android:gravity="center"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:text="Not Available"
        android:textColor="@color/color_white"
        android:textSize="16sp"
        android:visibility="gone"></TextView>
</RelativeLayout>


