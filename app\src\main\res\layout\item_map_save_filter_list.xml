<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_filter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="8dp"
        android:background="@drawable/ic_filter_point_select"></ImageView>

    <TextView
        android:id="@+id/tv_filter"
        style="@style/Body2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_marginRight="8dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:textColor="@color/color_dark"
        android:textSize="15sp"
        android:theme="@style/MyCheckBox"></TextView>

</LinearLayout>