<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_house_pic"
        android:layout_width="88dp"
        android:layout_height="78dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="8dp"></ImageView>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_watched_name"
            style="@style/H2Header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="3dp"
            android:text="Default"
            android:textColor="@color/color_dark"></TextView>

        <TextView
            android:id="@+id/tv_watched_count"
            style="@style/Subtitles2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="- Listings"
            android:textColor="@color/color_gray_dark"
            android:textSize="14sp"></TextView>

        <LinearLayout
            android:id="@+id/ll_privacy"
            android:layout_width="wrap_content"
            android:layout_marginTop="3dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_privacy"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginRight="8dp"
                android:src="@drawable/ic_watched_private"></ImageView>

            <TextView
                android:id="@+id/tv_privacy"
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Private"
                android:textColor="@color/color_gray_dark"
                android:textSize="14sp"></TextView>
        </LinearLayout>


    </LinearLayout>

    <ImageView
        android:id="@+id/iv_watchlist_menu"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:paddingLeft="26dp"
        android:paddingTop="16dp"
        android:paddingRight="26dp"
        android:paddingBottom="16dp"
        android:src="@drawable/ic_listings_menu"></ImageView>

</LinearLayout>

