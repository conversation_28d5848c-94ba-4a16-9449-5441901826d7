<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:id="@+id/ll_root"
    android:orientation="horizontal"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_municipalities_name"
        style="@style/SemiBold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginLeft="18dp"
        android:layout_marginBottom="8dp"
        android:lines="1"
        android:maxLines="1"
        android:text="Toronto"
        android:textColor="@color/app_main_color"
        android:textSize="16sp"></TextView>
    <TextView
        android:id="@+id/tv_type"
        style="@style/Regular"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="8dp"
        android:lines="1"
        android:maxLines="1"
        android:text=" homes for lease"
        android:textColor="@color/app_main_color"
        android:textSize="16sp"></TextView>
</LinearLayout>


