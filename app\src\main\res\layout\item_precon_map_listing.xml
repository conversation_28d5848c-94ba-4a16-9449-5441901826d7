<?xml version="1.0" encoding="utf-8"?>


<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/rl"
        android:layout_width="match_parent"
        android:layout_height="230dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="8dp"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_house_pic"
            android:layout_width="match_parent"
            android:layout_height="match_parent"></ImageView>


        <LinearLayout
            android:id="@+id/ll_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:orientation="vertical">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:background="@drawable/shape_pic_top20"></ImageView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_pic_bottom80"
                android:orientation="vertical"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:paddingBottom="8dp">

                <TextView
                    android:id="@+id/tv_project_name"
                    style="@style/H1Header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Allure Condos"
                    android:textColor="@color/color_white"
                    android:textSize="18sp"></TextView>

                <LinearLayout
                    android:id="@id/ll_price"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_what_for"
                        style="@style/Subtitles2"
                        android:layout_width="wrap_content"
                        android:layout_marginRight="4dp"
                        android:layout_height="wrap_content"
                        android:text="From"
                        android:textColor="@color/color_white"
                        android:textSize="14sp"></TextView>

                    <TextView
                        android:id="@+id/tv_price"
                        style="@style/H1Header"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text=" $ -"
                        android:textColor="@color/color_white"
                        android:textSize="18sp"></TextView>

                    <TextView
                        android:id="@+id/tv_days_ago"
                        style="@style/Subtitles1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="10dp"
                        android:paddingTop="3dp"
                        android:paddingRight="10dp"
                        android:paddingBottom="3dp"
                        android:textColor="@color/color_white"
                        android:textSize="14sp"></TextView>

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_project_status_text"
                    style="@style/Subtitles2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Registration"
                    android:textColor="@color/color_white"
                    android:textSize="14sp"></TextView>


                <TextView
                    android:id="@+id/tv_address"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:text="3 GRAYSTONE Court, Brant - Paris"
                    android:textColor="@color/color_white"
                    android:textSize="16sp"></TextView>

                <View
                    android:id="@+id/line"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="7dp"
                    android:background="@color/color_gray_dark" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_house_type_name"
                        style="@style/Subtitles1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text=""
                        android:textColor="@color/color_white"></TextView>


                    <TextView
                        android:id="@+id/tv_bedroom_string"
                        style="@style/Subtitles1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableLeft="@drawable/ic_bedroom"
                        android:drawablePadding="6dp"
                        android:text="-"
                        android:textColor="@color/color_white"></TextView>


                    <TextView
                        android:id="@+id/tv_sqft"
                        style="@style/Subtitles1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:drawableLeft="@drawable/ic_sqft"
                        android:drawablePadding="6dp"
                        android:text="-"
                        android:textColor="@color/color_white"></TextView>

                </LinearLayout>



            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/tv_login_required"
            style="@style/Button1"
            android:layout_width="180dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:background="@drawable/shape_10radius_main_color_fill"
            android:gravity="center"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Login Required"
            android:textColor="@color/color_white"
            android:textSize="16sp"
            android:visibility="gone"></TextView>

        <TextView
            android:id="@+id/tv_agreement_required"
            style="@style/Button1"
            android:layout_width="180dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:background="@drawable/shape_10radius_main_color_fill"
            android:gravity="center"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Tap to View"
            android:textColor="@color/color_white"
            android:textSize="16sp"
            android:visibility="gone"></TextView>

        <TextView
            android:id="@+id/tv_not_available"
            style="@style/Button1"
            android:layout_width="180dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:background="@drawable/shape_10radius_main_color_fill"
            android:gravity="center"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Not Available"
            android:textColor="@color/color_white"
            android:textSize="16sp"
            android:visibility="gone"></TextView>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_assignment"
                style="@style/Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:ellipsize="end"
                android:background="@drawable/shape_14radius_green_dark_fill"
                android:paddingLeft="10dp"
                android:paddingTop="4dp"
                android:text="Precon Project"
                android:paddingRight="10dp"
                android:layout_marginTop="16dp"
                android:layout_marginLeft="16dp"
                android:paddingBottom="4dp"
                android:textColor="@color/color_white"
                android:textSize="14sp"></TextView>

            <TextView
                android:id="@+id/tv_tag"
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginTop="20dp"
                android:paddingLeft="10dp"
                android:paddingTop="4dp"
                android:paddingRight="10dp"
                android:paddingBottom="4dp"
                android:text=""
                android:textColor="@color/color_white"
                android:textSize="14sp"
                android:visibility="gone"></TextView>

        </RelativeLayout>


    </RelativeLayout>

</LinearLayout>
