<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="16dp"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="20dp"

            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_address"
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Old Oakville - Oakville"
                android:textColor="@color/color_dark"
                android:textSize="18sp"></TextView>


            <TextView
                android:id="@+id/tv_house_type_name"
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Att/Row/Twnhouse"
                android:textColor="@color/color_gray_dark"
                android:textSize="14sp"></TextView>


        </LinearLayout>

        <TextView
            android:id="@+id/tv_listings_size"
            style="@style/Subtitles2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:text="View Listings (3)"
            android:textColor="@color/app_main_color"
            android:textSize="14sp"></TextView>


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="12dp">

        <ImageView
            android:id="@+id/iv_house_pic1"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_marginRight="10dp"
            android:layout_weight="1"></ImageView>

        <ImageView
            android:id="@+id/iv_house_pic2"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_marginRight="10dp"
            android:layout_weight="1"></ImageView>

        <ImageView
            android:id="@+id/iv_house_pic3"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_weight="1"></ImageView>

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Median Price: "
            android:textColor="@color/color_black"
            android:textSize="14sp"></TextView>

        <TextView
            android:id="@+id/tv_median_price"
            style="@style/SemiBold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="$ "
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>


        <LinearLayout
            android:id="@+id/ll_market_trends"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_btn_market_trend"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:background="@drawable/ic_market_trends"></ImageView>

            <TextView
                android:id="@+id/tv_market_trends"
                style="@style/Medium"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="8dp"
                android:layout_weight="1"
                android:lines="1"
                android:maxLines="1"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:singleLine="true"
                android:text="Market Trends"
                android:textColor="@color/color_gray_dark"
                android:textSize="14sp"></TextView>
        </LinearLayout>


    </LinearLayout>


    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp"
        android:background="#E2E2E2"></View>


</LinearLayout>

