<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_city_name"
            style="@style/H1Header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:layout_weight="1"
            android:text="GTA - Central"
            android:textColor="@color/color_dark"
            android:textSize="16sp"></TextView>

        <TextView
            android:id="@+id/tv_select_all"
            style="@style/Subtitles2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="6dp"
            android:paddingRight="6dp"
            android:text="Select all"
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>


    </LinearLayout>


    <com.donkingliang.labels.LabelsView
        app:labelTextSize="16sp"
        android:id="@+id/labels"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp"
        app:labelBackground="@drawable/label_bg"
        app:labelTextColor="@drawable/label_text_color"
        app:lineMargin="10dp"
        app:wordMargin="20dp"></com.donkingliang.labels.LabelsView>


</LinearLayout>