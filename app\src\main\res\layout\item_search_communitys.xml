<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="5dp"
    android:orientation="horizontal">


    <ImageView
        android:id="@+id/iv_house_pic"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/ic_search_community"
        android:scaleType="fitXY"></ImageView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginBottom="5dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_community_name"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Red Rock Ontario"
            android:textColor="@color/color_black"
            android:textSize="16sp"></TextView>

        <TextView
            android:id="@+id/tv_municipality_name"
            style="@style/Subtitles2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Red Rock Ontario"
            android:textColor="@color/color_gray"
            android:textSize="14sp"></TextView>


    </LinearLayout>


</LinearLayout>