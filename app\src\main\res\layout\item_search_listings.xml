<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="10dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_house_pic"
            android:layout_width="120dp"
            android:layout_height="104dp"></ImageView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:gravity="top"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_price"
                    style="@style/H1Header"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="$4,459,000"
                    android:textColor="@color/app_main_color"
                    android:textSize="18sp"></TextView>

                <TextView
                    android:id="@+id/tv_type_text"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:gravity="right"
                    android:lines="1"
                    android:maxLines="1"
                    android:layout_height="wrap_content"
                    android:text="For sale"
                    android:textColor="@color/app_main_color"></TextView>


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_address"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_marginTop="4dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="Michelangelo Blvd, Brampton Toronto Gore Rural Estate"
                    android:textColor="@color/color_black"
                    android:textSize="16sp"></TextView>

                <TextView
                    android:id="@+id/tv_municipality_name"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_alignParentBottom="true"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="Michelangelo Blvd, Brampton Toronto Gore Rural Estate"
                    android:textColor="@color/color_black"
                    android:textSize="16sp"></TextView>
            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_house_type_name"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:lines="1"
                    android:maxLines="1"
                    android:text="Detached"
                    android:textColor="@color/color_black"
                    android:textSize="16sp"></TextView>

                <TextView
                    android:id="@+id/tv_bedroom_string"
                    style="@style/Body1"
                    android:drawableLeft="@drawable/ic_bedroom"
                    android:drawablePadding="6dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-"
                    android:textColor="@color/color_black"
                    android:textSize="16sp"></TextView>

                <TextView
                    android:id="@+id/tv_washroom"
                    style="@style/Body1"
                    android:drawableLeft="@drawable/ic_bathroom"
                    android:drawablePadding="6dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:text="-"
                    android:textColor="@color/color_black"
                    android:textSize="16sp"></TextView>

                <TextView
                    android:id="@+id/tv_garage"
                    style="@style/Body1"
                    android:drawableLeft="@drawable/ic_garage"
                    android:drawablePadding="6dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:text="-"
                    android:textColor="@color/color_black"
                    android:textSize="16sp"></TextView>
            </LinearLayout>


        </LinearLayout>


    </LinearLayout>

    <TextView
        android:id="@+id/tv_date_times"
        style="@style/Subtitles2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="10dp"
        android:text="Listing Date: 2021-07-29, Listed 1 times"
        android:textColor="@color/color_dark"
        android:textSize="14sp"></TextView>


</LinearLayout>