<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="5dp"
    android:layout_marginBottom="5dp"
    android:orientation="horizontal">


    <ImageView
        android:id="@+id/iv_type"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/ic_search_open_in_map_address"
        android:scaleType="fitXY"></ImageView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="10dp"
        android:orientation="horizontal">


        <TextView
            android:id="@+id/tv_address"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Red Rock Ontario"
            android:textColor="@color/color_black"
            android:textSize="16sp"></TextView>

        <TextView
            android:id="@+id/tv_type"
            style="@style/Subtitles2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Address"
            android:textColor="@color/color_gray"
            android:textSize="14sp"></TextView>

    </LinearLayout>


</LinearLayout>