<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="10dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_house_pic"
            android:layout_width="120dp"
            android:layout_height="104dp"></ImageView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dp"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/ll_price"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:gravity="top"
                android:orientation="horizontal">

                <TextView
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="From"
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>

                <TextView
                    android:id="@+id/tv_price"
                    style="@style/Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="4dp"
                    android:gravity="right"
                    android:lines="1"
                    android:maxLines="1"
                    android:text="$600,000"
                    android:textColor="@color/color_cyan_strong"
                    android:textSize="16sp"></TextView>


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_project_name"
                    style="@style/Bold"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_marginTop="4dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="[Project Name]"
                    android:textColor="@color/color_black"
                    android:textSize="16sp"></TextView>

                <TextView
                    android:id="@+id/tv_address"
                    style="@style/Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_alignParentBottom="true"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="Michelangelo Blvd, Brampton Toronto Gore Rural Estate"
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>
            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_house_type_name"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:lines="1"
                    android:maxLines="1"
                    android:text="Detached"
                    android:textColor="@color/color_dark"
                    android:textSize="16sp"></TextView>


            </LinearLayout>


        </LinearLayout>


    </LinearLayout>

    <LinearLayout
        android:layout_marginTop="8dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_completion_date"
            android:gravity="center_horizontal"
            style="@style/Subtitles1"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="Summer 2025"
            android:textColor="@color/color_dark"
            android:textSize="14sp"></TextView>

        <TextView
            android:layout_marginLeft="10dp"
            android:id="@+id/tv_bedroom_string"
            style="@style/Body1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@drawable/ic_bedroom"
            android:drawablePadding="6dp"
            android:text="-"
            android:textColor="@color/color_black"
            android:textSize="16sp"></TextView>

        <TextView
            android:id="@+id/tv_sqft"
            style="@style/Body1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:drawableLeft="@drawable/ic_sqft"
            android:drawablePadding="6dp"
            android:text="-"
            android:textColor="@color/color_black"
            android:textSize="16sp"></TextView>


    </LinearLayout>


</LinearLayout>