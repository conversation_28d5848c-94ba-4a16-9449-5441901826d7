<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="16dp"
        android:layout_marginTop="4dp"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_description"
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_dark"
                android:textSize="14sp"></TextView>


            <TextView
                android:id="@+id/tv_notification_text"
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_gray_dark"
                android:textSize="14sp"></TextView>


        </LinearLayout>

        <ImageView
            android:id="@+id/iv_del_watched_area"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/ic_watch_area_menu"></ImageView>


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="18dp"
        android:baselineAligned="false">


        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="180dp"
            android:layout_marginRight="18dp"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/iv_house_pic1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"></ImageView>

            <TextView
                android:id="@+id/tv_view"
                style="Regular"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="8dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/shape_5radius_main_color_fill"
                android:gravity="center_horizontal"
                android:paddingLeft="16dp"
                android:paddingTop="6dp"
                android:paddingRight="16dp"
                android:paddingBottom="6dp"
                android:text="View Map"
                android:textColor="@color/color_white"
                android:textSize="14sp"></TextView>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_view_all"
            android:layout_width="0dp"
            android:layout_height="180dp"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/iv_house_pic2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"></ImageView>


            <LinearLayout
                android:id="@+id/ll_no_pic"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/shape_10radius_gray_fill"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">


                <ImageView
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/ic_no_history"></ImageView>


                <TextView
                    style="@style/Subtitles1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:text="No update in this \narea"
                    android:textColor="#ccc"></TextView>
            </LinearLayout>

            <TextView
                android:id="@+id/tv_view_all"
                style="Regular"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="8dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/shape_5radius_main_color_fill"
                android:gravity="center_horizontal"
                android:paddingLeft="16dp"
                android:paddingTop="6dp"
                android:paddingRight="16dp"
                android:paddingBottom="6dp"
                android:text="View Listings"
                android:textColor="@color/color_white"
                android:textSize="14sp"></TextView>

        </RelativeLayout>


    </LinearLayout>


    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp"
        android:background="#E2E2E2"></View>


</LinearLayout>

