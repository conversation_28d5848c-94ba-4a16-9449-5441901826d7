<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="16dp"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"

            android:layout_marginLeft="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="16dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_address"
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_dark"
                android:textSize="14sp"></TextView>


            <TextView
                android:id="@+id/tv_house_type_name"
                style="@style/Subtitles2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_gray_dark"
                android:textSize="14sp"></TextView>


        </LinearLayout>

        <ImageView
            android:id="@+id/iv_watched_community_menu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/ic_watch_area_menu"></ImageView>


    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_pics"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:layout_marginBottom="12dp">

        <ImageView
            android:id="@+id/iv_house_pic1"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_marginRight="10dp"
            android:layout_weight="1"></ImageView>

        <ImageView
            android:id="@+id/iv_house_pic2"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_marginRight="10dp"
            android:layout_weight="1"></ImageView>

        <ImageView
            android:id="@+id/iv_house_pic3"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_weight="1"></ImageView>

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="16dp"
        android:orientation="horizontal">

        <TextView
            style="@style/Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Median Price: "
            android:textColor="@color/color_black"
            android:textSize="14sp"></TextView>

        <TextView
            android:id="@+id/tv_median_price"
            style="@style/SemiBold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="$ "
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>

        <TextView
            android:id="@+id/tv_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_gray_dark"
            android:textSize="14sp"></TextView>

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="16dp"
        android:orientation="horizontal">

        <TextView
            style="@style/Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Notify for: "
            android:textColor="@color/color_black"
            android:textSize="14sp"></TextView>

        <TextView
            android:id="@+id/tv_type"
            style="@style/SemiBold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="New, Sold, Delisted"
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>


    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_watched_community_view_listing"
            style="@style/Medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginRight="16dp"
            android:layout_weight="1"
            android:background="@drawable/shape_5radius_main_color"
            android:drawablePadding="5dp"
            android:gravity="center"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:text="View Listings"
            android:textColor="@color/app_main_color"
            android:textSize="15sp"></TextView>


        <TextView
            android:id="@+id/tv_watched_community_market_trend"
            style="@style/Medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_weight="1"
            android:background="@drawable/shape_5radius_main_color"
            android:drawablePadding="5dp"
            android:gravity="center"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:text="Market Trends"
            android:textColor="@color/app_main_color"
            android:textSize="15sp"></TextView>

    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp"
        android:background="#E2E2E2"></View>


</LinearLayout>

