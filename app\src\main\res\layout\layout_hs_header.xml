<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/app_main_color"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:padding="12dp"
        android:src="@drawable/ic_close"></ImageView>


    <TextView
        style="@style/SemiBold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:drawableLeft="@drawable/ic_head_logo"
        android:drawablePadding="10dp"
        android:text="HouseSigma"
        android:textColor="@color/color_white"
        android:textSize="18sp"></TextView>

</RelativeLayout>