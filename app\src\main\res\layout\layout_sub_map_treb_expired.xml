<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical">


        <TextView
            android:id="@+id/tv_trreb_expired_body"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="12dp"
            android:text=""
            android:textColor="@color/color_dark"></TextView>


        <TextView
            android:id="@+id/tv_login_required"
            style="@style/Button1"
            android:layout_width="180dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:background="@drawable/shape_10radius_main_color_fill"
            android:gravity="center"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="Login Required"
            android:textColor="@color/color_white"
            android:textSize="16sp"
            android:visibility="gone"></TextView>
    </LinearLayout>
</merge>