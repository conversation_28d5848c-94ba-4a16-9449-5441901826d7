<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/rl_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="4dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/bg_marker_de_listed_viewed">

        <TextView
            android:id="@+id/tv_number"
            style="@style/Regular"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:paddingLeft="6dp"
            android:paddingRight="6dp"
            android:paddingTop="1dp"
            android:text="D 1.2M"
            android:textColor="@color/color_white"
            android:textSize="15sp" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tv_marker_feature_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignRight="@+id/rl_label"
        android:layout_marginRight="-4dp"
        android:background="@drawable/shape_map_marker_de_listed_watched_tip"
        android:paddingLeft="2dp"
        android:paddingTop="1dp"
        android:paddingRight="2dp"
        android:paddingBottom="1dp"
        android:text="Watched"
        android:textColor="#C0C4CC"
        android:textSize="8sp" />

</RelativeLayout>