<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/rl_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_marker_feature_tip"
                android:background="@drawable/shape_map_marker_nearby_select">

                <TextView
                    android:id="@+id/tv_number"
                    style="@style/SemiBold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:paddingLeft="6dp"
                    android:paddingTop="2dp"
                    android:paddingRight="6dp"
                    android:paddingBottom="2dp"
                    android:text="1.2M"
                    android:textColor="@color/color_white"
                    android:textSize="14sp"></TextView>

            </RelativeLayout>

        </LinearLayout>


    </RelativeLayout>


</LinearLayout>
