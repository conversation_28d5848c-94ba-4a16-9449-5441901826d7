<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/rl_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <RelativeLayout
                android:layout_width="6dp"
                android:layout_height="6dp"
                android:layout_below="@+id/tv_marker_feature_tip"
                android:background="@drawable/shape_map_marker_dot_purple">

            </RelativeLayout>

        </LinearLayout>




    </RelativeLayout>


</LinearLayout>
