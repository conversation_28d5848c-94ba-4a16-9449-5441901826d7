<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/rl_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/ic_marker_bg_blue">

        <TextView
            android:id="@+id/tv_number"
            style="@style/Regular"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:paddingLeft="6dp"
            android:paddingRight="6dp"
            android:paddingTop="2dp"
            android:text="D 1.2M"
            android:textColor="@color/color_white"
            android:textSize="15sp" />

    </RelativeLayout>


</RelativeLayout>