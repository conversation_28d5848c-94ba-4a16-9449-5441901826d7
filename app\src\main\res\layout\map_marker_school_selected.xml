<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/rl_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_marker_feature_tip"
                android:background="@drawable/shape_map_marker_school_selected">

                <TextView
                    android:id="@+id/tv_number"
                    style="@style/Regular"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:drawableLeft="@drawable/ic_map_marker_school2"
                    android:drawablePadding="3dp"
                    android:paddingLeft="6dp"
                    android:paddingTop="2dp"
                    android:paddingRight="6dp"
                    android:paddingBottom="2dp"
                    android:text="  -  "
                    android:textColor="@color/color_white"
                    android:textSize="15sp"></TextView>

            </RelativeLayout>

<!--            <ImageView-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_gravity="center_horizontal"-->
<!--                android:layout_marginTop="-1dp"-->
<!--                android:src="@drawable/ic_map_marker_down"-->
<!--                app:tint="#EFB92B"></ImageView>-->

        </LinearLayout>


    </RelativeLayout>


</LinearLayout>
