<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_marker_feature_tip"
        android:background="@drawable/ic_marker_bg_orange">

        <TextView
            android:id="@+id/tv_number"
            style="@style/Regular"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:paddingLeft="6dp"
            android:paddingTop="1dp"
            android:paddingRight="6dp"
            android:text="D 1.2M"
            android:textColor="@color/color_white"
            android:textSize="15sp" />

    </RelativeLayout>

</LinearLayout>
