<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/rl_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_marker_feature_tip"
                android:background="@drawable/shape_map_marker_white">

                <TextView
                    android:id="@+id/tv_number"
                    style="@style/SemiBold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:paddingLeft="20dp"
                    android:paddingTop="10dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="10dp"
                    android:text="Old Oadfdsf"
                    android:textColor="@color/color_black"
                    android:textSize="14sp"></TextView>

            </RelativeLayout>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="-1dp"
                android:src="@drawable/ic_map_marker_down"
                app:tint="@color/color_white"></ImageView>


            <ImageView
                android:layout_marginTop="-24dp"
                android:layout_width="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_height="wrap_content"
                android:background="@drawable/ic_marker_recommend"></ImageView>

        </LinearLayout>


    </RelativeLayout>


</LinearLayout>
