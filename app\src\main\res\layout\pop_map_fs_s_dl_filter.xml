<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_white"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">


        <TextView
            style="@style/H2Header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:layout_weight="1"
            android:text="For Sale"
            android:textColor="@color/color_dark"
            android:textSize="16sp"></TextView>

        <TextView
            android:id="@+id/tv_sold_delist_title"
            style="@style/H2Header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:layout_weight="1"
            android:text="Sold &#038; De-listed"
            android:textColor="@color/color_dark"
            android:textSize="16sp"></TextView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_left"
            android:layout_width="0dp"
            android:layout_height="230dp"
            android:layout_weight="1"
            android:fadeScrollbars="false"
            android:scrollbarSize="2dp"
            android:scrollbarThumbVertical="@color/app_main_color"
            android:scrollbars="vertical" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_right"
            android:layout_width="0dp"
            android:layout_height="230dp"
            android:layout_weight="1"
            android:fadeScrollbars="false"
            android:scrollbarSize="2dp"
            android:scrollbarThumbVertical="@color/app_main_color"
            android:scrollbars="vertical" />


    </LinearLayout>


</LinearLayout>