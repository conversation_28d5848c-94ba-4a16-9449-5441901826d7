<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="260dp"
    android:layout_height="wrap_content"
    android:layout_gravity="right"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_triangle"
        android:layout_width="10dp"
        android:layout_height="8dp"
        android:layout_alignParentRight="true"
        android:layout_marginRight="20dp"
        android:rotation="-90"
        app:srcCompat="@drawable/ic_triangle_right" />

    <LinearLayout
        android:background="@drawable/shape_10radiuis_white"
        android:layout_width="260dp"
        android:layout_below="@+id/iv_triangle"
        android:layout_alignParentRight="true"
        android:layout_marginRight="10dp"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll"
            android:layout_width="260dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="10dp"
            android:paddingBottom="6dp"
            android:paddingRight="10dp"
            android:paddingLeft="10dp">

            <TextView
                android:id="@+id/tv_tip_content"
                style="@style/H3Header"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Tap here to set up a Watched Area and get alerted when there are listing updates."
                android:textColor="@color/color_dark"></TextView>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_x"></ImageView>
        </LinearLayout>

        <TextView
            style="@style/Button1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/shape_5radius_main_color"
            android:gravity="center_horizontal"
            android:paddingLeft="10dp"
            android:paddingTop="4dp"
            android:paddingRight="10dp"
            android:paddingBottom="4dp"
            android:text="Got it"
            android:textColor="@color/app_main_color"
            android:textSize="14sp"></TextView>
    </LinearLayout>


</RelativeLayout>
