<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="wrap_content"
    android:background="@color/color_white"
    android:orientation="vertical"
    android:paddingLeft="10dp"
    android:paddingTop="10dp"
    android:paddingRight="10dp">

    <com.donkingliang.labels.LabelsView
        app:labelTextSize="16sp"
        android:id="@+id/labels"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="18dp"
        app:labelBackground="@drawable/label_bg"
        app:labelTextColor="@drawable/label_text_color"
        app:lineMargin="10dp"
        app:wordMargin="20dp"></com.donkingliang.labels.LabelsView>


    <View
        android:id="@+id/v_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#C4C4C4"></View>


    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_apply"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/shape_5radius_main_color"
            android:gravity="center_horizontal"
            android:paddingLeft="8dp"
            android:paddingTop="8dp"
            android:paddingRight="8dp"
            android:paddingBottom="8dp"
            android:text="Apply"
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>

        <TextView
            android:id="@+id/tv_clear_all_filters"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/shape_5radius_main_color"
            android:gravity="center_horizontal"
            android:paddingLeft="8dp"
            android:paddingTop="8dp"
            android:paddingRight="8dp"
            android:paddingBottom="8dp"
            android:text="Clear all filters"
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>

    </RelativeLayout>
</LinearLayout>