<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="right"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_triangle"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:layout_alignParentRight="true"
        android:layout_marginRight="30dp"
        android:rotation="-90"
        app:srcCompat="@drawable/ic_triangle_right" />


    <LinearLayout
        android:id="@+id/ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_triangle"
        android:layout_alignParentRight="true"
        android:layout_marginRight="10dp"
        android:background="@drawable/shape_10radiuis_white"

        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/rl_date"
            android:layout_marginTop="8dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="16dp"
                android:background="@drawable/ic_filter_point_selected"></ImageView>

            <TextView
                style="@style/Subtitles2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:lines="1"
                android:maxLines="1"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:text="Newest"
                android:textColor="@color/color_dark"
                android:textSize="16sp"></TextView>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/rl_price_ascending"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_price_ascending"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="16dp"
                android:background="@drawable/ic_filter_point_select"></ImageView>

            <TextView
                style="@style/Subtitles2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:lines="1"
                android:maxLines="1"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:text="Price Ascending"
                android:textColor="@color/color_dark"
                android:textSize="16sp"></TextView>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/rl_price_descending"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_price_descending"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="16dp"
                android:background="@drawable/ic_filter_point_select"></ImageView>

            <TextView
                style="@style/Subtitles2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginRight="16dp"
                android:layout_marginLeft="10dp"
                android:lines="1"
                android:maxLines="1"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:text="Price Descending"
                android:layout_marginBottom="8dp"
                android:textColor="@color/color_dark"
                android:textSize="16sp"
                android:theme="@style/MyCheckBox"></TextView>

        </LinearLayout>
    </LinearLayout>
</RelativeLayout>