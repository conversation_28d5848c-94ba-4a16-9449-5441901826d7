<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:background="@drawable/shape_white_15_corners_top"
        android:orientation="vertical"
        android:paddingLeft="16dp"
        android:paddingRight="16dp">

        <RelativeLayout
            android:layout_marginTop="10dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                style="@style/H1Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="School Settings"
                android:layout_centerVertical="true"
                android:layout_centerHorizontal="true"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <ImageView
                android:id="@+id/iv_close_school_settings"
                android:layout_alignParentRight="true"
                android:paddingLeft="10dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:layout_centerVertical="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_school_settings_close"></ImageView>
        </RelativeLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="20dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/H1Header"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Show Schools "
                android:textColor="@color/color_dark"
                android:textSize="18sp"></TextView>

            <com.housesigma.android.views.SwitchButton
                android:id="@+id/sb_show_schools"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"></com.housesigma.android.views.SwitchButton>

        </LinearLayout>


        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#e2e2e2"></View>


        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_elementary"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="6dp"
            android:layout_marginTop="14dp"
            android:paddingTop="6dp"
            android:paddingRight="16dp"
            android:paddingBottom="6dp"
            android:text="Elementary"
            android:textColor="@color/color_dark"
            android:textSize="16sp"
            android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_secondary"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="6dp"
            android:paddingTop="6dp"
            android:paddingRight="16dp"
            android:paddingBottom="6dp"
            android:text="Secondary"
            android:layout_marginBottom="14dp"
            android:textColor="@color/color_dark"
            android:textSize="16sp"
            android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#e2e2e2"></View>

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_public"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:paddingLeft="6dp"
            android:paddingTop="6dp"
            android:paddingRight="16dp"
            android:paddingBottom="6dp"
            android:text="Public"
            android:textColor="@color/color_dark"
            android:textSize="16sp"
            android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_catholic"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="14dp"
            android:paddingLeft="6dp"
            android:paddingTop="6dp"
            android:paddingRight="16dp"
            android:paddingBottom="6dp"
            android:text="Catholic"
            android:textColor="@color/color_dark"
            android:textSize="16sp"
            android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#e2e2e2"></View>

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_match_score"
            style="@style/Body1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:paddingLeft="6dp"
            android:paddingTop="6dp"
            android:paddingRight="16dp"
            android:paddingBottom="6dp"
            android:layout_marginBottom="26dp"
            android:text="Only show score >=7"
            android:textColor="@color/color_dark"
            android:textSize="15.4sp"
            android:theme="@style/MyCheckBox"></androidx.appcompat.widget.AppCompatCheckBox>
    </LinearLayout>

</RelativeLayout>

