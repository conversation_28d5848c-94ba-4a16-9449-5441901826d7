<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_white"
    android:orientation="vertical">


    <ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fadeScrollbars="false"
        android:paddingLeft="10dp"
        android:paddingTop="10dp"
        android:paddingRight="10dp"
        android:scrollbarSize="2dp"
        android:scrollbarThumbVertical="@color/app_main_color"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/color_white"
            android:orientation="vertical"
            android:paddingLeft="10dp"
            android:paddingTop="10dp"
            android:paddingRight="10dp">

            <TextView
                android:visibility="gone"
                android:id="@+id/tv_save_filters_title"
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_marginBottom="16dp"
                android:layout_height="wrap_content"
                android:text="Save Filters"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:visibility="gone"
                    android:id="@+id/tv_save_filter_create"
                    style="@style/Button1"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:background="@drawable/shape_5radius_main_color"
                    android:gravity="center_horizontal"
                    android:paddingLeft="20dp"
                    android:paddingTop="14dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="14dp"
                    android:text="Save Filters"
                    android:textColor="@color/app_main_color"
                    android:textSize="16sp"></TextView>

                <TextView
                    android:id="@+id/spinner_save_filter"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="match_parent"
                    android:layout_marginRight="14dp"
                    android:background="@drawable/shape_5radius_gray"
                    android:gravity="left"
                    android:text="---"
                    android:inputType="text"
                    android:paddingLeft="10dp"
                    android:drawablePadding="6dp"
                    android:drawableRight="@drawable/ic_map_listview_arrow_down"
                    android:paddingTop="12dp"
                    android:paddingRight="8dp"
                    android:textSize="14sp"
                    android:paddingBottom="12dp"
                    android:textColor="@color/color_black"
                    android:textColorHint="@color/color_gray_dark"></TextView>

                <TextView
                    android:id="@+id/tv_save_filter_delete"
                    style="@style/Button1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:background="@drawable/shape_10radius_gray_fill"
                    android:gravity="center_horizontal"
                    android:paddingLeft="16dp"
                    android:layout_marginRight="8dp"
                    android:paddingTop="12dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="12dp"
                    android:text="Delete"
                    android:textColor="@color/color_gray"
                    android:textSize="16sp"></TextView>

                <TextView
                    android:id="@+id/tv_save_filter_save"
                    style="@style/Button1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_10radius_main_color_fill"
                    android:gravity="center_horizontal"
                    android:paddingLeft="16dp"
                    android:paddingTop="12dp"
                    android:paddingRight="16dp"
                    android:paddingBottom="12dp"
                    android:text="Save"
                    android:textColor="@color/color_white"
                    android:textSize="16sp"></TextView>

            </LinearLayout>

            <View
                android:id="@+id/view_line_save_filters"
                android:visibility="gone"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Price range"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>


            <TextView
                android:id="@+id/tv_price"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="Unspecified - Max"
                android:textColor="@color/color_black"
                android:textSize="16sp"></TextView>

            <com.housesigma.android.views.PreconSeekBar
                android:id="@+id/sale_sb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"></com.housesigma.android.views.PreconSeekBar>

            <com.housesigma.android.views.RentalSeekBar
                android:id="@+id/rental_sb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"></com.housesigma.android.views.RentalSeekBar>

            <com.jaygoo.widget.RangeSeekBar
                android:id="@+id/rsb_price"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:rsb_gravity="center"
                app:rsb_max="6000000"
                app:rsb_min="0"
                app:rsb_mode="range"
                app:rsb_progress_color="@color/app_main_color"
                app:rsb_step_auto_bonding="true"
                app:rsb_step_color="@color/color_transparent"
                app:rsb_step_height="10dp"
                app:rsb_step_width="3dp"
                app:rsb_steps="120"
                app:rsb_tick_mark_gravity="center"
                app:rsb_tick_mark_layout_gravity="bottom"
                app:rsb_tick_mark_mode="number"
                app:rsb_tick_mark_text_margin="20dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="Description Contains Keywords"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <EditText
                android:id="@+id/et_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/shape_btn_gray7"
                android:gravity="left"
                android:hint="Waterfront, Pool, Fireplace..."
                android:lines="1"
                android:inputType="text"
                android:maxLines="1"
                android:paddingLeft="10dp"
                android:paddingTop="13dp"
                android:paddingBottom="13dp"
                android:singleLine="true"
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_gray_dark"
                android:textSize="16sp"></EditText>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="Bedrooms"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <com.donkingliang.labels.LabelsView
                android:id="@+id/labels_beadroom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:labelBackground="@drawable/label_bg"
                app:labelTextColor="@drawable/label_text_color"
                app:labelTextSize="16sp"
                app:lineMargin="10dp"
                app:labelTextPaddingLeft="15dp"
                app:labelTextPaddingRight="15dp"
                app:wordMargin="10dp"></com.donkingliang.labels.LabelsView>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="Bathrooms"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <com.donkingliang.labels.LabelsView
                android:id="@+id/labels_bathroom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:labelBackground="@drawable/label_bg"
                app:labelTextColor="@drawable/label_text_color"
                app:labelTextSize="16sp"
                app:lineMargin="10dp"
                app:labelTextPaddingLeft="15dp"
                app:labelTextPaddingRight="15dp"
                app:wordMargin="10dp"></com.donkingliang.labels.LabelsView>


            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

            <TextView
                style="@style/H2Header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="Est. Completion Year"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <com.donkingliang.labels.LabelsView
                android:id="@+id/labels_est_completion_year"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:labelBackground="@drawable/label_bg"
                app:labelTextColor="@drawable/label_text_color"
                app:labelTextSize="16sp"
                app:lineMargin="10dp"
                app:wordMargin="16dp"></com.donkingliang.labels.LabelsView>


            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>


            <TextView
                android:id="@+id/tv_square"
                style="@style/H2Header"
                android:layout_marginTop="16dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Square Footage: Unspecified - Max"
                android:textColor="@color/color_black"
                android:textSize="18sp"></TextView>

            <com.jaygoo.widget.RangeSeekBar
                android:id="@+id/rsb_square"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:rsb_gravity="center"
                app:rsb_max="4000"
                app:rsb_min="0"
                app:rsb_mode="range"
                app:rsb_progress_color="@color/app_main_color"
                app:rsb_step_auto_bonding="true"
                app:rsb_step_color="@color/color_transparent"
                app:rsb_step_height="10dp"
                app:rsb_step_width="3dp"
                app:rsb_steps="40"
                app:rsb_tick_mark_gravity="center"
                app:rsb_tick_mark_layout_gravity="bottom"
                app:rsb_tick_mark_mode="number"
                app:rsb_tick_mark_text_margin="20dp" />




            <View
                android:layout_marginTop="10dp"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#E2E2E2"></View>

        </LinearLayout>


    </ScrollView>


    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_apply"
            style="@style/Button1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="17dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="17dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/shape_5radius_main_color"
            android:gravity="center_horizontal"
            android:paddingLeft="8dp"
            android:paddingTop="8dp"
            android:paddingRight="8dp"
            android:paddingBottom="8dp"
            android:text="Apply"
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>

        <TextView
            android:id="@+id/tv_clear_all_filters"
            style="@style/Button1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="17dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="17dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/shape_5radius_main_color"
            android:gravity="center_horizontal"
            android:paddingLeft="8dp"
            android:paddingTop="8dp"
            android:paddingRight="8dp"
            android:paddingBottom="8dp"
            android:text="Clear all filters"
            android:textColor="@color/app_main_color"
            android:textSize="16sp"></TextView>

    </RelativeLayout>
</LinearLayout>
