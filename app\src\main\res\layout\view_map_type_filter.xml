<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="38dp"
    android:layout_centerHorizontal="true"
    android:layout_marginTop="16dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tv_for_sale_list_view"
        style="@style/Body1"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:background="@drawable/shape_map_left"
        android:gravity="center"
        android:paddingLeft="16dp"
        android:paddingTop="4dp"
        android:paddingRight="16dp"
        android:paddingBottom="4dp"
        android:text="For Sale"
        android:textColor="@color/color_gray_dark"
        android:textSize="16sp"></TextView>

    <View
        android:layout_width="0.5dp"
        android:layout_height="match_parent"
        android:background="@color/color_gray_a30"></View>

    <TextView
        android:id="@+id/tv_sold_list_view"
        style="@style/Body1"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:background="@drawable/shape_map_center"
        android:gravity="center"
        android:paddingLeft="16dp"
        android:paddingTop="4dp"
        android:paddingRight="16dp"
        android:paddingBottom="4dp"
        android:text="Sold"
        android:textColor="@color/color_gray_dark"
        android:textSize="16sp"></TextView>

    <View
        android:layout_width="0.5dp"
        android:layout_height="match_parent"
        android:background="@color/color_gray_a30"></View>

    <TextView
        android:id="@+id/tv_delisted_list_view"
        style="@style/Body1"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:background="@drawable/shape_map_right"
        android:gravity="center"
        android:paddingLeft="16dp"
        android:paddingTop="4dp"
        android:paddingRight="16dp"
        android:paddingBottom="4dp"
        android:text="De-listed"
        android:textColor="@color/color_gray_dark"
        android:textSize="16sp"></TextView>

</LinearLayout>