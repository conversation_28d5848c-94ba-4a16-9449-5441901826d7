<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.jaygoo.widget.RangeSeekBar
        android:id="@+id/sb_range"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:rsb_gravity="center"
        app:rsb_max="70"
        app:rsb_min="0"
        app:rsb_mode="range"
        app:rsb_progress_color="@color/app_main_color"
        app:rsb_step_auto_bonding="true"
        app:rsb_step_color="@color/color_transparent"
        app:rsb_step_height="10dp"
        app:rsb_step_width="3dp"
        app:rsb_steps="71"
        app:rsb_tick_mark_gravity="center"
        app:rsb_tick_mark_layout_gravity="bottom"
        app:rsb_tick_mark_mode="number"
        app:rsb_tick_mark_text_margin="20dp" />

</RelativeLayout>