<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.HouseSigma" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/app_main_color</item>
        <item name="colorAccent">@color/app_main_color</item>
        <item name="android:windowBackground">@color/color_white</item>
        <item name="android:statusBarColor">@color/color_white</item>
        <item name="android:editTextStyle">@style/App_EditTextStyle</item>
        <item name="editTextStyle">@style/App_EditTextStyle</item>

        <item name="android:listDivider">@drawable/divider_bg</item>
    </style>

    <style name="HSChat.Theme.HouseSigma" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/app_main_color</item>
        <item name="colorAccent">@color/app_main_color</item>
        <item name="android:windowBackground">@color/color_white</item>
        <item name="android:statusBarColor">@color/app_main_color</item>
        <item name="android:editTextStyle">@style/App_EditTextStyle</item>
        <item name="editTextStyle">@style/App_EditTextStyle</item>

        <item name="android:listDivider">@drawable/divider_bg</item>
    </style>

    <style name="Theme.ImagePreview" parent="Theme.AppCompat.NoActionBar">
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>

        <item name="android:windowContentOverlay">@null</item>
        <item name="background">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="bottom_bar_normal_text">
        <item name="android:fontFamily">@font/poppins_regular</item>
        <item name="android:textColor">@color/color_gray_dark</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="bottom_bar_selected_text">
        <item name="android:fontFamily">@font/poppins_semi_bold</item>
        <item name="android:textColor">@color/app_main_color</item>
        <item name="android:textSize">14sp</item>
    </style>


    <style name="App_EditTextStyle" parent="@android:style/Widget.EditText">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:textColor">#808080</item>
        <item name="android:layout_height">50dp</item>
    </style>

    <style name="MyPopupWindow_anim_style">
        <item name="android:windowEnterAnimation">@anim/popupwindow_show_anim</item>
        <item name="android:windowExitAnimation">@anim/popupwindow_hidden_anim</item>
    </style>

    <style name="CustomAlertDialog" parent="Base.Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/white</item>
    </style>

    <style name="DialogFullScreen" parent="Theme.AppCompat.Dialog">
        <!--        <item name="android:windowIsFloating">false</item>-->
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowSoftInputMode">stateVisible|adjustPan</item>
    </style>

    <style name="MyCheckBox" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/app_main_color</item>
        <item name="colorControlActivated">@color/app_main_color</item>
    </style>

    <style name="MyDisableCheckBox" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">#87CCD4</item>
        <item name="colorControlActivated">#87CCD4</item>
    </style>


    <style name="VerifyCodeViewStyle">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:textSize">43sp</item>
        <item name="android:textColor">@color/verifyCodeTextColor</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_margin">5dp</item>
        <item name="android:padding">3dp</item>
    </style>

    <style name="CheckCodeLineStyle">
        <item name="android:layout_width">1dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:background">@color/verifyCodeLineDefault</item>
    </style>

    <!--    Header -->
    <style name="H1Header">
        <item name="android:fontFamily">@font/poppins_semi_bold</item>
        <item name="android:textSize">18sp</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="H2Header">
        <item name="android:fontFamily">@font/poppins_semi_bold</item>
        <item name="android:textSize">16sp</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="H3Header">
        <item name="android:fontFamily">@font/poppins_medium</item>
        <item name="android:textSize">14sp</item>
        <item name="android:includeFontPadding">false</item>
    </style>


    <!--    Body -->
    <style name="Body1">
        <item name="android:fontFamily">@font/poppins_medium</item>
        <item name="android:textSize">16sp</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Body2">
        <item name="android:fontFamily">@font/poppins_regular</item>
        <item name="android:textSize">16sp</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Body3">
        <item name="android:fontFamily">@font/poppins_medium</item>
        <item name="android:textSize">14sp</item>
        <item name="android:includeFontPadding">false</item>
    </style>
    <!--Subtitles-->
    <style name="Subtitles1">
        <item name="android:fontFamily">@font/poppins_semi_bold</item>
        <item name="android:textSize">14sp</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Subtitles2">
        <item name="android:fontFamily">@font/poppins_regular</item>
        <item name="android:textSize">14sp</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <!-- Other-->
    <style name="Button1">
        <item name="android:fontFamily">@font/poppins_medium</item>
        <item name="android:textSize">16sp</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Button2">
        <item name="android:fontFamily">@font/poppins_semi_bold</item>
        <item name="android:textSize">14sp</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="PlaceHolder">
        <item name="android:fontFamily">@font/poppins_regular</item>
        <item name="android:textSize">16sp</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="HyperLink">
        <item name="android:fontFamily">@font/poppins_light</item>
        <item name="android:textSize">14sp</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Medium">
        <item name="android:fontFamily">@font/poppins_medium</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Bold">
        <item name="android:fontFamily">@font/poppins_bold</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Light">
        <item name="android:fontFamily">@font/poppins_light</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Regular">
        <item name="android:fontFamily">@font/poppins_regular</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="SemiBold">
        <item name="android:fontFamily">@font/poppins_semi_bold</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Black">
        <item name="android:fontFamily">@font/poppins_black</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="BlackItalic">
        <item name="android:fontFamily">@font/poppins_black_italic</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Thin">
        <item name="android:fontFamily">@font/poppins_thin</item>
        <item name="android:includeFontPadding">false</item>
    </style>

</resources>