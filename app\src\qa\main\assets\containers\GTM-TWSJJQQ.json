{"exportFormatVersion": 2, "exportTime": "2022-09-07 07:46:50", "containerVersion": {"path": "accounts/**********/containers/********/versions/3", "accountId": "**********", "containerId": "********", "containerVersionId": "3", "name": "del", "container": {"path": "accounts/**********/containers/********", "accountId": "**********", "containerId": "********", "name": "SDX-HS-GTM-TEST-ANDROID", "publicId": "GTM-TWSJJQQ", "usageContext": ["ANDROID_SDK_5"], "fingerprint": "*************", "tagManagerUrl": "https://tagmanager.google.com/#/container/accounts/**********/containers/********/workspaces?apiLink=container"}, "trigger": [{"accountId": "**********", "containerId": "********", "triggerId": "4", "name": "All_EVENT", "type": "ALWAYS", "fingerprint": "*************"}], "builtInVariable": [{"accountId": "**********", "containerId": "********", "type": "APP_ID", "name": "App ID"}, {"accountId": "**********", "containerId": "********", "type": "APP_NAME", "name": "App Name"}, {"accountId": "**********", "containerId": "********", "type": "APP_VERSION_CODE", "name": "App Version Code"}, {"accountId": "**********", "containerId": "********", "type": "APP_VERSION_NAME", "name": "App Version Name"}, {"accountId": "**********", "containerId": "********", "type": "EVENT_NAME", "name": "Event Name"}], "fingerprint": "*************", "tagManagerUrl": "https://tagmanager.google.com/#/versions/accounts/**********/containers/********/versions/3?apiLink=version"}}