package com.housesigma.android.ui.map.filters

import com.housesigma.android.model.DefaultFilter
import com.housesigma.android.model.MapFilters
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import org.mockito.Mock
import org.mockito.MockitoAnnotations

/**
 * 测试SeekBarManager中新增的租金回报率和学校评分功能
 */
class SeekBarManagerTest {

    private lateinit var seekBarManager: SeekBarManager

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        seekBarManager = SeekBarManager()
    }

    @Test
    fun testRentalYieldRangeDefault() {
        // 测试默认值
        val result = seekBarManager.getRentalYieldRange()
        assertNull("默认情况下应该返回null", result)
    }

    @Test
    fun testRentalYieldRangeWithValues() {
        // 设置非默认值
        seekBarManager.rentalYieldLeft = 2.5f
        seekBarManager.rentalYieldRight = 7.0f
        
        val result = seekBarManager.getRentalYieldRange()
        assertNotNull("设置值后应该返回非null", result)
        assertEquals("左值应该正确", "2.5", result!![0])
        assertEquals("右值应该正确", "7.0", result[1])
    }

    @Test
    fun testSchoolScoreRangeDefault() {
        // 测试默认值
        val result = seekBarManager.getSchoolScoreRange()
        assertNull("默认情况下应该返回null", result)
    }

    @Test
    fun testSchoolScoreRangeWithValues() {
        // 设置非默认值
        seekBarManager.schoolScoreLeft = 3.0f
        seekBarManager.schoolScoreRight = 8.5f
        
        val result = seekBarManager.getSchoolScoreRange()
        assertNotNull("设置值后应该返回非null", result)
        assertEquals("左值应该正确", "3.0", result!![0])
        assertEquals("右值应该正确", "8.5", result[1])
    }

    @Test
    fun testRentalYieldRangeAtMaxValue() {
        // 测试最大值情况
        seekBarManager.rentalYieldLeft = 0.0f
        seekBarManager.rentalYieldRight = 10.0f // 等于最大值
        
        val result = seekBarManager.getRentalYieldRange()
        assertNull("当值为默认范围时应该返回null", result)
    }

    @Test
    fun testSchoolScoreRangeAtMaxValue() {
        // 测试最大值情况
        seekBarManager.schoolScoreLeft = 0.0f
        seekBarManager.schoolScoreRight = 10.0f // 等于最大值
        
        val result = seekBarManager.getSchoolScoreRange()
        assertNull("当值为默认范围时应该返回null", result)
    }

    @Test
    fun testMapFiltersWithNewFields() {
        // 测试MapFilters数据模型
        val mapFilters = MapFilters()
        mapFilters.rental_yield_min = "2.0"
        mapFilters.rental_yield_max = "8.0"
        mapFilters.school_score_min = "4.0"
        mapFilters.school_score_max = "9.0"
        
        assertEquals("租金回报率最小值应该正确", "2.0", mapFilters.rental_yield_min)
        assertEquals("租金回报率最大值应该正确", "8.0", mapFilters.rental_yield_max)
        assertEquals("学校评分最小值应该正确", "4.0", mapFilters.school_score_min)
        assertEquals("学校评分最大值应该正确", "9.0", mapFilters.school_score_max)
    }

    @Test
    fun testDefaultFilterWithNewFields() {
        // 测试DefaultFilter数据模型
        val defaultFilter = DefaultFilter(
            list_type = emptyList(),
            rent_list_type = emptyList(),
            listing_days = "0",
            sold_days = "90",
            de_list_days = "90",
            basement = emptyList(),
            open_house_date = "0",
            description = "",
            max_maintenance_fee = "0",
            bedroom = emptyList(),
            bathroom = "0",
            garage = "0",
            house_type = emptyList(),
            price_sale_min = "0",
            price_sale_max = "6000000",
            price_rent_min = "0",
            price_rent_max = "10000",
            square_footage_min = "0",
            square_footage_max = "4000",
            lot_front_feet_min = "0",
            lot_front_feet_max = "100",
            listing_type = emptyList(),
            lot_size_max = "10000000",
            lot_size_min = "0",
            building_age_max = "0",
            building_age_min = "999",
            houseTypeLocal = ArrayList(),
            rental_yield_min = "1.5",
            rental_yield_max = "6.5",
            school_score_min = "3.0",
            school_score_max = "8.0"
        )
        
        assertEquals("租金回报率最小值应该正确", "1.5", defaultFilter.rental_yield_min)
        assertEquals("租金回报率最大值应该正确", "6.5", defaultFilter.rental_yield_max)
        assertEquals("学校评分最小值应该正确", "3.0", defaultFilter.school_score_min)
        assertEquals("学校评分最大值应该正确", "8.0", defaultFilter.school_score_max)
    }

    @Test
    fun testRentalYieldBoundaryValues() {
        // 测试边界值
        seekBarManager.rentalYieldLeft = 0.0f
        seekBarManager.rentalYieldRight = 0.5f
        
        val result = seekBarManager.getRentalYieldRange()
        assertNotNull("边界值应该返回非null", result)
        assertEquals("左值应该正确", "0.0", result!![0])
        assertEquals("右值应该正确", "0.5", result[1])
    }

    @Test
    fun testSchoolScoreBoundaryValues() {
        // 测试边界值
        seekBarManager.schoolScoreLeft = 9.5f
        seekBarManager.schoolScoreRight = 10.0f
        
        val result = seekBarManager.getSchoolScoreRange()
        assertNotNull("边界值应该返回非null", result)
        assertEquals("左值应该正确", "9.5", result!![0])
        assertEquals("右值应该正确", "10.0", result[1])
    }
}
