package com.housesigma.android.ui.watched

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.housesigma.android.utils.MMKVUtils
import com.tencent.mmkv.MMKV
import io.mockk.*
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import android.content.Context

class WatchedHelperTest {

    private val watchedListKey = "watched_list"
    private lateinit var mockMMKV: MMKV
    private lateinit var mockContext: Context
    
    @Before
    fun setUp() {
        // Setup static mocks
        mockkStatic(MMKV::class)
        
        mockMMKV = mockk(relaxed = true)
        mockContext = mockk(relaxed = true)
        
        every { MMKV.defaultMMKV() } returns mockMMKV
        
        // Clear data before each test
        WatchedHelper.clearAllData()
    }

    @After
    fun tearDown() {
        WatchedHelper.clearAllData()
        unmockkAll()
    }

    @Test
    fun testSaveWatchedBatch() {
        val testListingIds = listOf("123", "456", "789")
        
        WatchedHelper.saveWatchedBatch(testListingIds)
        
        val slot = slot<String>()
        verify { mockMMKV.encode(watchedListKey, capture(slot)) }
        
        val gson = Gson()
        val savedIds: List<String> = gson.fromJson(slot.captured, object : TypeToken<List<String>>() {}.type)
        assertEquals(3, savedIds.size)
        assertTrue(savedIds.containsAll(testListingIds))
    }
    
    @Test
    fun testFindWatchedByListingId() {
        val testListingIds = listOf("123", "456", "789")

        WatchedHelper.saveWatchedBatch(testListingIds)
        
        // Test existing IDs
        assertTrue(WatchedHelper.findWatchedByListingId("123"))
        assertTrue(WatchedHelper.findWatchedByListingId("456"))
        assertTrue(WatchedHelper.findWatchedByListingId("789"))
        
        // Test non-existing ID
        assertFalse(WatchedHelper.findWatchedByListingId("999"))
        
        // Test empty ID
        assertFalse(WatchedHelper.findWatchedByListingId(""))
    }
    
    @Test
    fun testClearAllData() {
        WatchedHelper.clearAllData()
        
        verify { mockMMKV.remove(watchedListKey) }
    }
    
    @Test
    fun testLoadWatchedListFromStorage_WithValidData() {
        val testListingIds = listOf("123", "456", "789")

        WatchedHelper.saveWatchedBatch(testListingIds)

        assertTrue(WatchedHelper.findWatchedByListingId("123"))
    }
    
    @Test
    fun testLoadWatchedListFromStorage_WithInvalidData() {
        // Mock invalid JSON response
        every { mockMMKV.decodeString(watchedListKey) } returns "invalid json"
        
        assertFalse(WatchedHelper.findWatchedByListingId("any"))
    }
    
    @Test
    fun testLoadWatchedListFromStorage_WithNull() {
        // Mock null response
        every { mockMMKV.decodeString(watchedListKey) } returns null
        
        assertFalse(WatchedHelper.findWatchedByListingId("any"))
    }
    
    @Test
    fun testLoadWatchedListFromStorage_WithEmptyValues() {
        val testListingIds = listOf("123", "", "456", "789")

        WatchedHelper.saveWatchedBatch(testListingIds)

        assertTrue(WatchedHelper.findWatchedByListingId("123"))
        assertTrue(WatchedHelper.findWatchedByListingId("456"))
        assertTrue(WatchedHelper.findWatchedByListingId("789"))
        assertFalse(WatchedHelper.findWatchedByListingId(""))
    }
} 