// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    //other things....
    dependencies {
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
        classpath 'com.google.firebase:firebase-appdistribution-gradle:4.2.0'
    }
}

plugins {
    id 'com.android.application' version '8.5.0' apply false
    id 'com.android.library' version '8.5.0' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.23' apply false
    id 'com.google.gms.google-services' version '4.4.1' apply false
}

task clean(type: Delete) {
    delete rootProject.buildDir
}