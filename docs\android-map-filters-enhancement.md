# Android地图筛选功能增强 - 租金回报率和学校评分

## 概述

根据产品需求文档 PLAN-3565，本次开发为Android地图页面增加了两个新的筛选功能：
- **租金回报率 (Rental Yield)**：范围滑块，0% - 10%+，步进0.5%
- **学校评分 (School Score)**：范围滑块，0 - 10，步进0.5

## 技术实现

### 1. 数据模型扩展

#### MapFilters
```kotlin
class MapFilters {
    // 新增字段
    var rental_yield_min: String? = null
    var rental_yield_max: String? = null
    var school_score_min: String? = null
    var school_score_max: String? = null
}
```

#### DefaultFilter
```kotlin
data class DefaultFilter(
    // 现有字段...
    // 新增字段
    val rental_yield_min: String? = null,
    val rental_yield_max: String? = null,
    val school_score_min: String? = null,
    val school_score_max: String? = null,
)
```

### 2. API接口更新

所有相关的地图搜索API都已添加新参数：
- `getMapsearchv3List`
- `getMapListing22`
- `getMapSearchv2Feature`
- `getSearchV2NearBySold`

```kotlin
@Field("rental_yield_min") rental_yield_min: String? = null,
@Field("rental_yield_max") rental_yield_max: String? = null,
@Field("school_score_min") school_score_min: String? = null,
@Field("school_score_max") school_score_max: String? = null,
```

### 3. UI组件

#### 布局文件 (popwindow_map_filter_more.xml)
```xml
<!-- 租金回报率滑块 -->
<TextView android:id="@+id/tv_rental_yield" ... />
<com.jaygoo.widget.RangeSeekBar android:id="@+id/rsb_rental_yield" ... />

<!-- 学校评分滑块 -->
<TextView android:id="@+id/tv_school_score" ... />
<com.jaygoo.widget.RangeSeekBar android:id="@+id/rsb_school_score" ... />
```

#### 滑块配置
- **最大值**: 20 (对应租金回报率10%和学校评分10)
- **步数**: 20 (实现0.5的步进)
- **转换**: 滑块值 ÷ 2 = 实际值

### 4. 业务逻辑

#### SeekBarManager扩展
```kotlin
// 新增字段
var rentalYieldLeft: Float = 0.0f
var rentalYieldRight: Float = 10.0f
var schoolScoreLeft: Float = 0.0f
var schoolScoreRight: Float = 10.0f

// 获取筛选范围
fun getRentalYieldRange(): ArrayList<String>?
fun getSchoolScoreRange(): ArrayList<String>?
```

#### MapFiltersView扩展
```kotlin
// 获取筛选参数
fun getRentalYieldRange(): ArrayList<String>?
fun getSchoolScoreRange(): ArrayList<String>?
```

### 5. 数据持久化

使用MMKV保存用户筛选偏好：
```kotlin
// 保存
MMKVUtils.saveFloat("${isSale}_rental_yield_left", rentalYieldLeft)
MMKVUtils.saveFloat("${isSale}_rental_yield_right", rentalYieldRight)
MMKVUtils.saveFloat("${isSale}_school_score_left", schoolScoreLeft)
MMKVUtils.saveFloat("${isSale}_school_score_right", schoolScoreRight)

// 恢复
rentalYieldLeft = MMKVUtils.getFloat("${isSale}_rental_yield_left", 0.0f)
rentalYieldRight = MMKVUtils.getFloat("${isSale}_rental_yield_right", 10.0f)
```

## 使用方法

### 1. 用户交互
1. 用户打开地图筛选面板
2. 滑动"Rental Yield"滑块设置租金回报率范围
3. 滑动"School Score"滑块设置学校评分范围
4. 点击"Apply"应用筛选
5. 地图显示符合条件的房源

### 2. 重置功能
- 点击"Clear all filters"按钮重置所有筛选条件
- 新字段会恢复到默认值 (0%-10%+ 和 0-10)

### 3. 数据同步
- 筛选状态自动保存到本地
- 应用重启后恢复用户上次的筛选设置
- 支持售房和租房模式的独立设置

## API调用示例

```kotlin
mapViewModel.getMapSearchV3List(
    // 现有参数...
    rental_yield_range = mapFiltersView.getRentalYieldRange(),
    school_score_range = mapFiltersView.getSchoolScoreRange(),
)
```

## 测试

### 单元测试
- `SeekBarManagerTest`: 测试新字段的逻辑
- `MapFiltersIntegrationTest`: 测试完整流程

### 测试用例
1. 默认值测试
2. 边界值测试
3. 参数传递测试
4. 数据持久化测试
5. 重置功能测试

## 兼容性

- **向后兼容**: 新参数为可选，不影响现有功能
- **API兼容**: 后端需要支持新的筛选参数
- **数据兼容**: 新字段默认为null，不影响现有数据

## 注意事项

1. **滑块值转换**: UI滑块值需要除以2得到实际值
2. **空值处理**: 当筛选范围为默认值时返回null，表示不筛选
3. **缓存键名**: 使用`${isSale}_`前缀区分售房和租房模式
4. **显示格式**: 租金回报率显示为百分比，学校评分显示为数字

## 后续优化

1. 添加更多的数值验证
2. 优化滑块的用户体验
3. 添加筛选结果统计
4. 支持更精细的步进控制
