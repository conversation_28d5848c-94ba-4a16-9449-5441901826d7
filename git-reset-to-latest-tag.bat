@echo off
REM Navigate to the repository directory
cd /d "path\to\your\repo"

REM Fetch all tags and branches with debug information
echo Fetching all tags and branches...
git fetch --all --tags
if errorlevel 1 (
    echo Failed to fetch tags and branches.
    goto end
)

REM List all fetched tags for debugging
echo Listing all tags...
git tag
if errorlevel 1 (
    echo Failed to list tags.
    goto end
)

REM Get the latest tag on the master branch
echo Getting the latest tag on the master branch...
set LAST_TAG=
for /f "delims=" %%i in ('git rev-list --tags --max-count=1') do set LAST_TAG=%%i

REM Check if LAST_TAG is set
if "%LAST_TAG%"=="" (
    echo No tags found on master branch.
    goto end
)

echo Latest tag found: %LAST_TAG%

REM Get the commit hash for the latest tag
echo Getting commit hash for the tag %LAST_TAG%...
set COMMIT_HASH=
for /f "delims=" %%i in ('git rev-list -n 1 %LAST_TAG% 2^>nul') do set COMMIT_HASH=%%i

REM Check if COMMIT_HASH is set
if "%COMMIT_HASH%"=="" (
    echo Unable to find commit hash for tag %LAST_TAG%.
    goto end
)

echo Commit hash for %LAST_TAG% is %COMMIT_HASH%

REM Reset master branch to the latest tag's commit
echo Resetting master branch to commit %COMMIT_HASH%...
git checkout master
if errorlevel 1 (
    echo Failed to checkout master branch.
    goto end
)

git reset --hard %COMMIT_HASH%
if errorlevel 1 (
    echo Failed to reset master branch.
    goto end
)

git push origin HEAD --force
REM Optional: Push the reset branch to remote (use with caution)
REM echo Pushing reset branch to remote...
REM git push origin HEAD --force
REM if errorlevel 1 (
REM     echo Failed to push branch to remote.
REM     goto end
REM )

echo Master branch has been reset to the commit of the last tag: %LAST_TAG%

:end
pause
