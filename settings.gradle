pluginManagement {
    repositories {
//        https://developer.android.com/studio/build/optimize-your-build?hl=zh-cn
        gradlePluginPortal()
        google()
        mavenCentral()
        maven{ url 'https://maven.aliyun.com/repository/public' }
        maven{ url 'https://maven.aliyun.com/repository/google'}
        maven { url 'https://jitpack.io' }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven{ url 'https://maven.aliyun.com/repository/public' }
        maven{ url 'https://maven.aliyun.com/repository/google'}
        maven { url 'https://jitpack.io' }
    }
}
rootProject.name = "HouseSigma"
include ':app'
