# Android Testing Libraries Guide

This document provides basic usage instructions for two testing libraries used in the project: JUnit 4 and MockK.

## JUnit 4 (junit:junit:4.13.2)

JUnit is one of the most commonly used unit testing frameworks in the Java ecosystem.

### Basic Usage

```kotlin
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import org.junit.After

class ExampleUnitTest {
    
    @Before
    fun setUp() {
        // Runs before each test method
    }
    
    @Test
    fun addition_isCorrect() {
        // Test example
        assertEquals(4, 2 + 2)
    }
    
    @Test
    fun testString() {
        val str = "Hello World"
        assertTrue(str.contains("Hello"))
        assertFalse(str.isEmpty())
    }
    
    @After
    fun tearDown() {
        // Runs after each test method
    }
}
```

### Common Assertion Methods

- `assertEquals(expected, actual)`: Verifies if two values are equal
- `assertTrue(condition)`: Verifies if condition is true
- `assertFalse(condition)`: Verifies if condition is false
- `assertNull(object)`: Verifies if object is null
- `assertNotNull(object)`: Verifies if object is not null
- `assertSame(expected, actual)`: Verifies if two references point to the same object

## MockK (io.mockk:mockk:1.13.9)

MockK is a powerful mocking library specifically designed for Kotlin, helping to isolate code under test from its dependencies.

### Basic Usage

```kotlin
import io.mockk.*
import org.junit.Test

class UserServiceTest {

    @Test
    fun testUserService() {
        // Create mock object
        val userRepository = mockk<UserRepository>()
        
        // Configure mock behavior
        every { userRepository.findUserById(1) } returns User(1, "Zhang San")
        
        // Create service under test
        val userService = UserService(userRepository)
        
        // Call method under test
        val user = userService.getUserById(1)
        
        // Assert result
        assertEquals("Zhang San", user.name)
        
        // Verify mock method was called
        verify { userRepository.findUserById(1) }
    }
}
```

### Common Features

1. **Creating Mock Objects**:
   ```kotlin
   val mock = mockk<YourClass>()
   val relaxedMock = mockk<YourClass>(relaxed = true) // Uses relaxed mock, unstubbed methods won't throw exceptions
   ```

2. **Configuring Mock Behavior**:
   ```kotlin
   every { mock.someMethod(any()) } returns "result"
   every { mock.someFunction(eq(5)) } answers { "parameter is ${arg<Int>(0)}" }
   ```

3. **Verifying Calls**:
   ```kotlin
   verify { mock.someMethod(any()) }
   verify(exactly = 2) { mock.someMethod(any()) } // Verifies method was called twice
   ```

4. **Capturing Arguments**:
   ```kotlin
   val slot = slot<String>()
   every { mock.process(capture(slot)) } returns Unit
   // After method call
   println("Captured argument: ${slot.captured}")
   ```

5. **Mocking Coroutines**:
   ```kotlin
   coEvery { mock.suspendFunction() } returns "result"
   coVerify { mock.suspendFunction() }
   ```

### Common Issues

1. **Mocking Singletons or Objects**:
   ```kotlin
   mockkObject(MySingleton)
   every { MySingleton.someMethod() } returns "mock result"
   ```

2. **Mocking Static Methods**:
   ```kotlin
   mockkStatic(UtilityClass::class)
   every { UtilityClass.staticMethod() } returns "mock result"
   ```

## Running Tests in Android Projects

1. In Android Studio, right-click the test class and select "Run"
2. Using command line: `./gradlew test`
3. Running specific tests: `./gradlew test --tests "package.name.ClassName.methodName"`

## Reference Resources

- [JUnit 4 Official Documentation](https://junit.org/junit4/)
- [MockK Official Documentation](https://mockk.io/)